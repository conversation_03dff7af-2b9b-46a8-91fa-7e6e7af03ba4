<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import type { Cc3_Soc_Specialties } from '$generated-types';

    export type DailyTelehealthThreshold = {
        [key: string]: {
            amberThreshold: number;
            redThreshold: number;
            remarks: string;
            selected: boolean;
        } & Cc3_Soc_Specialties;
    };

    export type DailyTeleHealthConfig = {
        title?: string;
        lowThreshold?: number;
        mediumThreshold?: number;
        highThreshold?: number;
        lowStatusText?: string;
        mediumStatusText?: string;
        highStatusText?: string;
        lowColor?: string;
        mediumColor?: string;
        highColor?: string;
        SelectedSOCThresholds: DailyTelehealthThreshold;
        SelectedAHSThresholds: DailyTelehealthThreshold;
    };

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'Daily Telehealth';
    const DEFAULT_LOW_STATUS_TEXT = 'Low';
    const DEFAULT_LOW_THRESHOLD = 10;
    const DEFAULT_MEDIUM_STATUS_TEXT = 'Med';
    const DEFAULT_MEDIUM_THRESHOLD = 100;
    const DEFAULT_HIGH_STATUS_TEXT = 'High';

    export const configDefaults: DailyTeleHealthConfig = {
        title: DEFAULT_TITLE,
        lowThreshold: DEFAULT_LOW_THRESHOLD,
        mediumThreshold: DEFAULT_MEDIUM_THRESHOLD,
        lowStatusText: DEFAULT_LOW_STATUS_TEXT,
        mediumStatusText: DEFAULT_MEDIUM_STATUS_TEXT,
        highStatusText: DEFAULT_HIGH_STATUS_TEXT,
        lowColor: '#FFFFFF',
        mediumColor: '#FFBF00',
        highColor: '#FF0000',
        SelectedSOCThresholds: {},
        SelectedAHSThresholds: {},
    };
</script>

<script lang="ts">
    import { onMount } from 'svelte';
    import { type Unsubscriber, get } from 'svelte/store';
    import { Input, Label } from 'flowbite-svelte';
    import { user } from '$lib/stores/Auth';

    export let config: DailyTeleHealthConfig;
    let user_id = '';
    let subscriptions: Unsubscriber[] = [];
    let group = 'SOC';

    onMount(() => {
        user_id = get(user).claims.hasura_access['x-hasura-user-id']

        return () => {
            subscriptions.forEach(s => s);
        };
    });
</script>

<div class="text-on-surface flex flex-col gap-3">
    <Label for="title">Title</Label>
    <Input name="title" placeholder={'Title'} bind:value={config.title} />

    <section class="flex flex-col items-center gap-2 border-y-2 border-y-outline py-2">
        <!-- <TextField
            placeholder={'Medium Threshold'}
            type="number"
            bind:value={config.mediumThreshold}
            setClass="{INPUT_CLASS} h-10"
            setLabelClass="text-primary text-xs" /> -->

        <div class="flex w-full items-center gap-2">
            <div class="flex flex-col gap-3">
                <Label for="medium">Medium Status Text</Label>
                <Input name="medium" required placeholder={'Amber Status Text'} bind:value={config.mediumStatusText} />
            </div>
            <Input type="color" class="mt-auto h-12 w-12 rounded !bg-transparent" bind:value={config.mediumColor} />
        </div>
    </section>

    <section class="flex flex-col items-center gap-2 border-y-2 border-y-outline py-2">
        <!-- <TextField
            placeholder={'High Threshold'}
            type="number"
            bind:value={config.highThreshold}
            setClass="{INPUT_CLASS} h-10"
            setLabelClass="text-primary text-xs" /> -->

        <div class="flex w-full items-center gap-2">
            <div class="flex flex-col gap-3">
                <Label for="high">High Status Text</Label>
                <Input name="high" required placeholder={'Red Status Text'} bind:value={config.highStatusText} />
            </div>
            <Input type="color" class="mt-auto h-12 w-12 rounded !bg-transparent" bind:value={config.highColor} />
        </div>
    </section>
</div>
