import 'gridstack/dist/gridstack.min.css';

import { SvelteComponent } from 'svelte';

export declare const GRID_STACK_CONTEXT = 'gridstack';
export declare type IndexProps = typeof __propDef.props;
export declare type IndexEvents = typeof __propDef.events;
export declare type IndexSlots = typeof __propDef.slots;
export default class Index extends SvelteComponent<IndexProps, IndexEvents, IndexSlots> {
    get resetGrid(): (options: any) => void;
}
export {};
