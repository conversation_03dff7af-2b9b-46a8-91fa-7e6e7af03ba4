<!-- @component
    This component is used to provide a grid using the GridJS library. It is meant to be used in conjunction with
    'WidgetItem' components. It is primarily used in the Dashboard components to create interactive dashboards.

    Props:
        - options: A list of options to be forwarded to GridStack.addGrid(), these options determine the behaviour
        of the Grid. See the GridStack API for more details: https://gridstackjs.com/
        - editable: True if the widgets in the grid can be moved and resized
        - showGrid: True if a grid of squares should be rendered to make the grid cells visible
        - showBackground: True if the default background color is to be used, otherwise it will be transparent

    // TODO: Write documentation for exported functions when finalized (save/reset grid)
    Usage:
        ```tsx
        <

        > <WidgetItem> <OtherComponents props={'Hi Mom'}/> </WidgetItem> </WidgetGrid>
        ```
 -->
<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import type { GridStack, GridStackOptions, GridStackNode } from 'gridstack';
    import { onMount, setContext, getContext, createEventDispatcher } from 'svelte';
    import { writable, type Writable } from 'svelte/store';
    import { isDirty } from '$lib/core/components/PageTitle/index.svelte';
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';

    export const GRID_STACK_CONTEXT = 'gridstack';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    type ComponentEvents = {
        init: WidgetGridInitializedDetail;
    };

    export type WidgetGridInitializedDetail = {
        gridstack: GridStack;
    };

    export type WidgetGridinitializedEvent = CustomEvent<WidgetGridInitializedDetail>;
</script>

<script lang="ts">
    import 'gridstack/dist/gridstack.min.css';
    import 'gridstack/dist/gridstack-extra.min.css';
    import { findWidgetById, filterWidgetByExcludeId } from '$lib/core/modules/dashboard/utils/utils';
    import { displayStateStore, loadedWidgetItemsStore, DisplayState } from '$lib/core/modules/dashboard/module';

    /**
     * The list of {@link GridStackOptions} to be based to the GridStack library for initialization
     */
    export let options: GridStackOptions = {};

    /**
     * Allow editing of the widget grid (moving widgets etc.)
     *
     * @defaultValue `true`
     * */
    export let editable = true;

    /**
     * Whether or not to show the grid in the background
     *
     * @defaultValue `true`
     * */
    export let showGrid = true;

    /**
     * Whether to use the default background or to keep it transparent
     *
     * @defaultValue `true`
     * */
    export let showBackground = true;

    export let gridChangeBroadcast = true;

    const dispatch = createEventDispatcher<ComponentEvents>();

    let gridContainer: HTMLElement;
    let gridstack: GridStack;

    let gridstackStore = writable<GridStack>();

    setContext(GRID_STACK_CONTEXT, gridstackStore);

    onMount(() => {
        $isDirty = false;

        (async () => {
            // Gridstack import references the DOM, must be dynamically imported
            await import('gridstack/dist/gridstack-all');
            const { GridStack } = await import('gridstack');

            // set styleInHead true in options if gs-style-id CSS leaks
            gridstack = GridStack.addGrid(gridContainer, options);
            gridstackStore.set(gridstack);

            gridstack.on('resizestop', function (event, element) {
                resize(event, element);
            });

            gridstack.on('added removed change', function (event, element) {
                resize(event, element);

                if ((editable && event.type === 'change') || event.type === 'added') {
                    // KIV: Unverified assumption that detail is always there...
                    let el = event['detail'][0]['el'];

                    if (el && el.broadcast && gridChangeBroadcast && $displayStateStore != DisplayState.ViewingDashboard) {
                        $isDirty = true;
                    }
<<<<<<< HEAD
                });
                gridstack.on('dropped', function(event: Event, previousWidget: GridStackNode, newWidget: GridStackNode) {
                    let gs = newWidget.el.closest('.grid-stack')?.gridstack;
                    gs?.removeWidget(newWidget.el, false);
                    let widgetItem = findWidgetById($loadedWidgetItemsStore, previousWidget.wId);
                    widgetItem.widgetGridStackOptions = { ...widgetItem.widgetGridStackOptions, x: newWidget.x, y: newWidget.y, w: newWidget.w, h: newWidget.h };
                    $loadedWidgetItemsStore = filterWidgetByExcludeId($loadedWidgetItemsStore, previousWidget.wId);
                    widgetItem.parentId = null;
                    $loadedWidgetItemsStore.push(widgetItem);
                    $loadedWidgetItemsStore = $loadedWidgetItemsStore;
                });
            } else {
                gridstack.on('added removed change', function (event, element) {
                    resize(event, element);
                });
            }
=======
                }
            });
>>>>>>> aoh-web/develop

            gridstack.on('enable', function (event, element) {
                $isDirty = false;
            });

            window.addEventListener('resize', event => {
                resize(event);
            });

            resize();

            dispatch('init', {
                gridstack,
            });
        })();

        // Run when destroyed (but not on server)
        return () => {
            if (gridstack) {
                gridstack.off('added removed changed');
                gridstack.removeAll(false);
                // gridStack.destroy(false);
            }
        };
    });

    // Trigger resizing of the grid
    export const resize = (_event?: Event, _element?: HTMLElement) => {
        browserBroadcaster.pub(Channel.AOH_WIDGET_RESIZED, {
            type: Channel.AOH_WIDGET_RESIZED,
            data: 'Gridstack resize stopped event.',
        });
    };

    export const resetGrid = async options => {
        if (gridstack) {
            gridstack.removeAll(false);

            // If you don't merge with gridstack.opts, you'll lose all defaults
            gridstack.opts = { ...gridstack.opts, ...options };
        } else {
            log.debug('Widget Grid - resetGrid was called to no effect.');
        }
    };

    /**
     * Deprecated: The grid is now designed to grow indefinitely along the Y axis (indefinite number of rows)
     * @param ratio
     */
    export const setCellHeight = (ratio: number) => {
        if (gridstack) {
            gridstack.cellHeight(gridstack.cellWidth() * ratio);
        }
    };

    export const getGridStackStore = () => {
        return gridstackStore;
    };
</script>

<div
    bind:this={gridContainer}
    class:grid-display={showGrid}
    class:bg-background={showBackground}
    class="grid-stack transition-transform">
    <slot />
</div>
