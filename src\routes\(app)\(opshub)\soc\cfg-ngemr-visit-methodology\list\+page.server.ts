import { RESOURCE } from "$lib/shared/enum/general";
import { SCOPE } from "$lib/shared/enum/general";
import { checkPermissions } from "$lib/shared/util/acl";
import type { PageServerLoad } from "./$types";

export const load: PageServerLoad = async ({ locals, params, fetch }) => {
    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const access_token = locals.user.access_token;
    let permissionNeedToCheck = [
        {
            scopeId: SCOPE.READ,
            resourceId: RESOURCE.SOC_VISIT_TYPE,
        },
        {
            scopeId: SCOPE.CREATE,
            resourceId: RESOURCE.SOC_VISIT_TYPE,
        },
        {
            scopeId: SCOPE.UPDATE,
            resourceId: RESOURCE.SOC_VISIT_TYPE,
        },
        {
            scopeId: SCOPE.DELETE,
            resourceId: RESOURCE.SOC_VISIT_TYPE,
        },
        {
            scopeId: SCOPE.TOGGLE,
            resourceId: RESOURCE.SOC_VISIT_TYPE,
        },
    ];
    const abilities = await checkPermissions({
        token: access_token,
        tenantId,
        userId,
        permissions: permissionNeedToCheck,
    });
    return {
        abilities: abilities,
    };
};