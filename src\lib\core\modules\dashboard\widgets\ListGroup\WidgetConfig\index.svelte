<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { TYPEOF_COMPARATOR } from '$lib/core/modules/dashboard/widgets/utils';

    export interface IndicatorItemStyle {
        textColor?: string;
        bgColor?: string;
    }

    export interface ListGroupItem {
        name: string;
        label: string;
        numerator: number;
        denominator: number;
        isManual: boolean;
    }

    export interface TableListConfig {
        dataSource?: string;
        title?: string;
        textSize: number;
        isShowDenominator: boolean;
        listGroup: Array<ListGroupItem>;
        thresholdConfigList: Array<ThresholdConfig>;
    }

    export const amberThreshold: ThresholdItem = {
        titleThresholdItem: 'Amber Threshold',
        comparatorThreshold: TYPEOF_COMPARATOR[3].value,
        valueOfThreshold: 70,
        textColor: '#FFFFFF',
        bgColor: '#000000',
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
    };

    export const redThreshold: ThresholdItem = {
        titleThresholdItem: 'Red Threshold',
        comparatorThreshold: TYPEOF_COMPARATOR[3].value,
        valueOfThreshold: 100,
        textColor: '#FFFFFF',
        bgColor: '#000000',
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
    };

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'c3hoc_VW_SCDF_Amb_aggregate';
    const DEFAULT_TITLE = '';
    const DEFAULT_TEXT_SIZE = 18;
    const DEFAULT_ENABLE_TITLE = false;
    const INDICATOR_THRESHOLD_CONFIG_ITEMS: Array<ThresholdItem> = [amberThreshold, redThreshold];

    export const configThresholdConfigs: ThresholdConfig = {
        isEnableThreshold: DEFAULT_ENABLE_TITLE,
        thresholdConfigItems: INDICATOR_THRESHOLD_CONFIG_ITEMS,
    };

    export const configDefaults: TableListConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        textSize: DEFAULT_TEXT_SIZE,
        isShowDenominator: true,
        listGroup: null,
        thresholdConfigList: null,
    };
</script>

<script lang="ts">
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { onMount } from 'svelte';
    import { Accordion, AccordionItem } from 'flowbite-svelte';
    import WidgetItem from '../../../components/WidgetConfig/WidgetItem.svelte';
    import ThresholdConfigComponent, {
        type ThresholdItem,
        type ThresholdConfig,
    } from '$lib/core/components/ThresholdConfig/index.svelte';

    export let config: TableListConfig;
    let datasources: Array<DropdownItem> = [];
    let configElement: HTMLElement;
    let isDisplayBgColor = false;

    const widgetProperties = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: datasources,
                setClass: 'border-b border-b-outline bg-transparent mb-6',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
            },
        },
        {
            id: 'textSize',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Body Text Size',
                type: 'number',
                required: true,
                customClass: 'mb-6 w-full',
                inputClass: 'w-24',
            },
        },
    ];

    onMount(() => {
        // if (configElement) {
        //     let configurationPanel = configElement.parentElement.parentElement.parentElement;
        //     configurationPanel.classList.remove('w-1/5');
        //     configurationPanel.classList.add('w-2/5');
        // }
    });
</script>

<div bind:this={configElement}>
    <Accordion>
        <AccordionItem>
            <span slot="header">Properties</span>
            <div class="flex flex-col justify-center">
                <Accordion flush>
                    <AccordionItem>
                        <span slot="header" class="text-white">Widget Properties</span>
                        <WidgetItem bind:config widgetFields={widgetProperties} />
                    </AccordionItem>
                    <AccordionItem>
                        <span slot="header" class="text-white">Row Properties</span>
                        <div>
                            <WidgetToggle bind:checked={config.isShowDenominator} title="Show Denominator" />
                        </div>
                        <div
                            class="mb-2 grid gap-x-2 text-sm text-white"
                            style="grid-template-columns: 25% 25% 25% 25%;">
                            <div>Name</div>
                            <div>Numerator</div>
                            <div>Denominator</div>
                            <div />
                        </div>
                        {#each config?.listGroup as dataItem}
                            <div
                                class="mb-2 flex grid items-center gap-x-2"
                                style="grid-template-columns: 25% 25% 25% 25%;">
                                <div>
                                    <WidgetConfigText bind:value={dataItem.label} disabled={true} />
                                </div>
                                <div>
                                    <WidgetConfigText
                                        bind:value={dataItem.numerator}
                                        disabled={dataItem.isManual ? false : true}
                                        type="number" />
                                </div>
                                <div>
                                    <WidgetConfigText
                                        bind:value={dataItem.denominator}
                                        disabled={dataItem.isManual && config.isShowDenominator ? false : true}
                                        type="number" />
                                </div>
                                <div>
                                    <WidgetToggle bind:checked={dataItem.isManual} title="Manual" />
                                </div>
                            </div>
                        {/each}
                    </AccordionItem>
                </Accordion>
            </div>
        </AccordionItem>
        <AccordionItem open>
            <span slot="header">Thresholds</span>
            <div class="flex flex-col justify-center">
                <Accordion flush>
                    {#each config.thresholdConfigList as dataItem}
                        <AccordionItem>
                            <span slot="header" class="text-white"
                                >{config.listGroup?.find(el => el.name == dataItem.name).label} Threshold</span>
                            <ThresholdConfigComponent bind:configThresholdConfigs={dataItem} {isDisplayBgColor} />
                        </AccordionItem>
                    {/each}
                </Accordion>
            </div>
        </AccordionItem>
    </Accordion>
</div>
