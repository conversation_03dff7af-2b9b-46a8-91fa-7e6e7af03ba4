<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { type ComponentType } from 'svelte/internal';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    export let configNested: any;
    export let componentDisplay: ComponentType;
    export let extraProps: Record<string, any>;
    export let path: Array<string | number>;
</script>

{#if Array.isArray(configNested[path[0]])}
    <svelte:self
        bind:configNested={configNested[path[0]][path[1]]}
        path={path.slice(2, path.length)}
        {componentDisplay}
        {extraProps} />
{:else if configNested[path[0]] instanceof Object}
    <svelte:self
        bind:configNested={configNested[path[0]]}
        path={path.slice(1, path.length)}
        {componentDisplay}
        {extraProps} />
{:else}
    <svelte:component
        this={componentDisplay}
        {...extraProps}
        bind:value={configNested[path[0]]}
        bind:checked={configNested[path[0]]} />
{/if}
