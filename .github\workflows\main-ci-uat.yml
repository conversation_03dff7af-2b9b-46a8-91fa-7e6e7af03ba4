name: Web - Main CI - UAT
# ---- This is the CI script for UAT ----
# 1. Make sure the code in UAT branch is tagged in GIT
# 2. Update the env: mytag to the tag name in GIT

env:
    # CURRENT_DATETIME: $(date --rfc-3339=date)" >> ${GITHUB_ENV} 

#    image_prefix: ghcr.io
    org_name: hcc3
    app_name: hcc3-web
    mytag: 1.1.3a

    # PORT: 5173
    # IAM_CLIENT_ID: aoh_web
    # IAM_CLIENT_SECRET: localdev
    # PUBLIC_STATIC_BUILD_VERSION: next
    # PUBLIC_STATIC_OVERRIDE_HOME: 1
    # PUBLIC_HASURA_COOKIE_MODE: 1
    # IAM_URL: http://**************:8888/realms/ar2
    # GRAPHQL_URL: http://hasura:8080/v1/graphql
    # PUBLIC_SECURE_MODE: 0
    # PUBLIC_REPLAY_MODE: 0
    # PUBLIC_BATCHJOB_API: http://batchjob:5000/v1
    # PUBLIC_SOP_URL: http://sop:5002/v1
    # PUBLIC_AUDITLOG_URL: http://audittrail:8085/api
    # PUBLIC_INVENTORY_URL: http://inventory:1323
    # TAG_URL: http://tag:5006
    # DASH_URL: http://dash:5005
    # ORIGIN: http://**************:3000
    # PUBLIC_DOMAIN: **************

on:
    workflow_dispatch:
    push:
        branches:
            - 'UAT'
        paths:
            - '!.github/workflows/**'

concurrency:
    group: ci-hcc3-web-${{ github.ref }}
    cancel-in-progress: true

defaults:
    run:
        shell: bash

jobs:
    # test:
    #     continue-on-error: true
    #     name: 'Execute test jobs'
    #     runs-on: ubuntu-latest
    #     steps:
    #         - name: 'Execute tests'
    #           run: |
    #               echo "TEMP placeholder"
    #         - name: 'Execute code analysis'
    #           run: |
    #               echo "TEMP placeholder"

    build-and-publish-image:
        name: 'Build & Publish Container Image'
        runs-on: ubuntu-latest
        # needs:
        #     - test
        outputs:
            mytag: ${{ steps.getImageTag.outputs.mytag }}
        steps:
            - name: Generate CURRENT_DATE unique variable based on timestamp
              run: echo CURRENT_DATE=$(date +%s) >> $GITHUB_ENV
        
            - name: '☁️ Checkout code'
              uses: actions/checkout@v3
              with:
                ref: ${{ env.mytag }}
                path: ./SRC
            - name: Configure AWS Credentials for CodeArtifact
              uses: aws-actions/configure-aws-credentials@v3
              with:
                aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                aws-region: ap-southeast-1
            
            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v1


            - name: '${{ env.app_name }} - Build and upload container image'
              id: build-image
              env: 
                ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
                IMAGE_TAG: ${{ env.mytag }}
              run: |
                  cd ./SRC
                  # export PORT=3000
                  # export PUBLIC_STATIC_BUILD_VERSION=${TAG}
                  export IMAGE_TAG=${IMAGE_TAG}
                  export GITHUB_ACCESS_TOKEN=${{ secrets.WEB_PAT }}
                  # export PUBLIC_STATIC_OVERRIDE_HOME=
                  # export PUBLIC_INVENTORY_URL=http://inventory:1323
                  # export NODE_OPTIONS="--max-old-space-size=16384"

                  # docker compose -f docker-compose.yml build --tag $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
                  # docker build . --file docker/service.Dockerfile --tag $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
                  # docker compose -f docker-compose.yml build web:latest
                  # docker tag web:latest $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
                  
                  docker build . --file Dockerfile.sit --tag $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG \
                  --build-arg PORT=3000 \
                  --build-arg IAM_CLIENT_ID=aoh_web \
                  --build-arg IAM_CLIENT_SECRET=localdev \
                  --build-arg PUBLIC_STATIC_BUILD_VERSION=${{ env.CURRENT_DATE }} \
                  --build-arg PUBLIC_STATIC_OVERRIDE_HOME=1 \
                  --build-arg PUBLIC_HASURA_COOKIE_MODE=1 \
                  --build-arg IAM_URL=http://**************:8888/realms/ar2 \
                  --build-arg GRAPHQL_URL=http://hasura:8080/v1/graphql \
                  --build-arg PUBLIC_SECURE_MODE=0 \
                  --build-arg PUBLIC_REPLAY_MODE=0 \
                  --build-arg BATCHJOB_URL=http://batchjob:5000/v1 \
                  --build-arg SOP_URL=http://sop:5002/v1 \
                  --build-arg AUDITLOG_URL=http://audittrail:8085/api \
                  --build-arg INVENTORY_URL=http://inventory:1323 \
                  --build-arg TAG_URL=http://tag:5006 \
                  --build-arg DASH_URL=http://dash:5005 \
                  --build-arg COMMON_URL=http://common:5009 \
                  --build-arg DATA_AGG_URL=http://data-agg:5003 \
                  --build-arg ORIGIN=http://**************:3000 \
                  --build-arg PUBLIC_DOMAIN=************** \
                  --build-arg KEYCLOAK_AUTH_URL=http://**************:8888/realms/AOH/protocol/openid-connect/userinfo \
                  --build-arg IAMS_AAS_URL=http://iams-aas:8080 \
                  --build-arg WORKFLOW_ENGINE_URL=http://workflow-engine:8080 \
                  --build-arg INCIDENT_MONITORING_URL=http://incident:5010
                  
                  #docker build . --file Dockerfile.dev --tag $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
                  docker push $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
