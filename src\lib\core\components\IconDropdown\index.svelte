<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { fly } from 'svelte/transition';

    import Button from '$lib/core/components/Button/index.svelte';
    import type { DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { clickOutside } from '$lib/utils';

    export let setClass = 'text-sm p-1';
    export let appendClass = undefined;

    export let menuClass = '';
    export let appendMenuClass = '';

    export let itemClass = 'text-on-surface hover:bg-surface-variant';

    export let options: Array<DropdownItem> = [];

    let isOpen = false;
</script>

<div class="relative">
    <Button
        {setClass}
        {appendClass}
        on:click={() => {
            isOpen = true;
        }}>
        <slot />
    </Button>

    {#if isOpen && options}
        <div
            transition:fly|global={{ y: -10, duration: 100 }}
            use:clickOutside
            on:outclick={() => {
                isOpen = false;
            }}
            class="top-15 absolute max-h-64 w-32 overflow-y-auto {menuClass
                ? menuClass
                : 'text-on-surface bg-surface-3'} {appendMenuClass}">
            {#each options as option}
                <div
                    role="listbox"
                    tabindex="0"
                    class="flex cursor-pointer items-center justify-between gap-4 p-2 text-xs text-inherit {itemClass}"
                    on:click={() => {
                        if (option.action) option.action(option);
                        isOpen = false;
                    }}>
                    {#if option.htmlString}
                        <p class="">{@html option.htmlString}</p>
                    {:else if option.label?.length}
                        <span class="flex items-center justify-start gap-2">
                            {#if option.itemIcon}
                                <p class={option.itemIconStyle}>{@html icon(option.itemIcon).html}</p>
                            {/if}
                            <p class={option.itemStyle ? option.itemStyle : ''}>{option.label}</p>
                        </span>
                    {/if}
                </div>
            {/each}
        </div>
    {/if}
</div>
