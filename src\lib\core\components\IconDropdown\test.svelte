<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faArrowDown, faUser } from '@fortawesome/free-solid-svg-icons';

    import type { DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import IconDropdown from '$lib/core/components/IconDropdown/index.svelte';

    let selectedValue;
    let defaultwithIconItems: Array<DropdownItem> = [
        {
            label: 'John',
            value: 'John',
            itemIcon: faUser,
            itemIconStyle: 'text-xs text-primary',
            action: opt => {
                selectedValue = opt.value;
            },
        },
        {
            label: 'Bob',
            value: 'Bob',
            itemIcon: faUser,
            itemIconStyle: 'text-xs text-primary',
            action: opt => {
                selectedValue = opt.value;
            },
        },
        {
            label: 'Anna',
            value: 'Anna',
            itemIcon: faUser,
            itemIconStyle: 'text-xs text-error',
            action: opt => {
                selectedValue = opt.value;
            },
        },
    ];
    let htmlStringItems: Array<DropdownItem> = [{ label: 'label1', htmlString: '<div class="">test</div>' }];
    let isClicked = false;
</script>

<div class="text-on-surface flex w-full flex-col gap-2 p-2 text-sm">
    <p>Click the icon to select a value: <span class="">{@html icon(faArrowDown).html}</span></p>
    <div class="flex h-fit w-1/2 items-center gap-2 p-1 text-xs">
        <IconDropdown options={defaultwithIconItems}>
            <div
                tabindex="0"
                role="button"
                on:click={() => {
                    isClicked = true;
                }}
                class="text-on-tertiary-container hover:bg-surface-variant flex h-6 w-6 items-center justify-center rounded-full border-2 border-outline p-3.5 text-sm {isClicked
                    ? ''
                    : 'animate-bounce'}">
                {@html icon(faUser).html}
            </div>
        </IconDropdown>
        <span
            class="text-on-surface flex min-w-1/5 items-center justify-center rounded border border-outline p-1 px-2 text-xs"
            >{selectedValue ? selectedValue : 'None.'}</span>
    </div>
</div>
