<script lang="ts">
    import { page } from '$app/stores';

    let modules = import.meta.glob(`/src/lib/**/test.svelte`);
    let path = $page.url.pathname.split('/tests/')[1];
    let testComponentPath = `/src/lib/${path}/test.svelte`;

    let component = modules[testComponentPath];
</script>

<div class="p-6">
    <div class="my-4">
        <span class="font-bold">Test Component:</span>
        {path}
    </div>
    <div id="test-container" data-testid="test-container" class="border-2 border-red-500 shadow-e-12">
        {#if component}
            {#await component() then module}
                <svelte:component this={module['default']} />
            {/await}
        {:else}
            Loading Dynamic Component...
        {/if}
    </div>
</div>
