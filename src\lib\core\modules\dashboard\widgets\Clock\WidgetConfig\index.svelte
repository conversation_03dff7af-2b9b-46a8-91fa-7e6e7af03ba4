<script lang="ts" context="module">
    export interface ClockConfig {
        title?: string;
        titleTextSize?: number;
        titleAlign?: string;
        datetimeFormat?: string;
        datetimeTextSize?: number,
        datetimeAlign?: string;
    }
    export const configDefaults: ClockConfig = {
        title: 'Clock Widget',
        titleTextSize: 18,
        titleAlign: 'center',
        datetimeFormat: 'D MMM YYYY HH:mm:ss',
        datetimeTextSize: 18,
        datetimeAlign: 'center',
    };
</script>

<script lang="ts">
    export let config: ClockConfig;
    import WidgetConfig from '$lib/core/modules/dashboard/components/WidgetConfig/index.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetButton/index.svelte';
    const configElementWidth = 'w-[32%]';
    const datetimeFormatList: Array<String> = [
        'D MMM YYYY hh:mm:ss',
        'DD-MM-YYYY HH:mm:ss',
        'YYYY-MM-DD h:mm:ss a',
        'dddd, MMMM D YYYY hh:mm:ss A',
    ];



    const propertyFields = [
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
                customClass: 'mb-4',
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-wrap items-end gap-3 mb-4',
            },
            children: [
                {
                    id: 'titleTextSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Title Text Size',
                        type: 'number',
                        customClass: '',
                        min: 0,
                        pattern: "^[0-9]\\d*$",
                    },
                },
                {
                    extraProps: {
                        customClass: 'flex flex-wrap items-end gap-2',
                    },
                    children: [
                        {
                            id: 'titleAlign',
                            componentDisplay: WidgetButton,
                            extraProps: {
                                title: 'Title Alignment',
                                label: 'Left',
                                customClass: 'flex flex-col justify-end w-20 whitespace-nowrap',
                            },
                        },
                        {
                            id: 'titleAlign',
                            componentDisplay: WidgetButton,
                            extraProps: {
                                label: 'Center',
                                customClass: 'flex flex-col justify-end',
                            },
                        },
                        {
                            id: 'titleAlign',
                            componentDisplay: WidgetButton,
                            extraProps: {
                                label: 'Right',
                                customClass: 'flex flex-col justify-end',
                            },
                        },
                    ],
                },
            ],
        },
        {
            id: 'datetimeFormat',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Datetime Format',
                placeholder: 'D MMM YYYY hh:mm:ss',
                pattern: '^[aszhmdyASZHMDY/\\-:s., ]*$',
                customClass: 'mb-4',
                toolTip: datetimeFormatList,
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-wrap items-end gap-3 mb-4',
            },
            children: [
                {
                    id: 'datetimeTextSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Datetime Text Size',
                        type: 'number',
                        customClass: '',
                        min: 0,
                        pattern: "^[0-9]\\d*$",
                    },
                },
                {
                    extraProps: {
                        customClass: 'flex flex-wrap items-end gap-2',
                    },
                    children: [
                        {
                            id: 'datetimeAlign',
                            componentDisplay: WidgetButton,
                            extraProps: {
                                title: 'Datetime Alignment',
                                label: 'Left',
                                customClass: 'flex flex-col justify-end w-20 whitespace-nowrap',
                            },
                        },
                        {
                            id: 'datetimeAlign',
                            componentDisplay: WidgetButton,
                            extraProps: {
                                label: 'Center',
                                customClass: 'flex flex-col justify-end',
                            },
                        },
                        {
                            id: 'datetimeAlign',
                            componentDisplay: WidgetButton,
                            extraProps: {
                                label: 'Right',
                                customClass: 'flex flex-col justify-end',
                            },
                        },
                    ],
                },
            ],
        },
    ];
</script>

<WidgetConfig bind:config {propertyFields} {configElementWidth} />
