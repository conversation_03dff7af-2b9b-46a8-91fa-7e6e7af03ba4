import FilterDisplay from './FilterDisplay';

// SOC-5
export default function WaitingAndServingTimeTrendingByVisitTypOfClinics(form, fields) {
    for (let index = 0; index < fields.length; index++) {
        const element = fields[index];
        if (element?.field === 'StagesByService') {
            element.datasource = {
                ...element.datasource,
                viewFields: ['category'],
                valuesInArrayFilter: {
                    field: 'service',
                    value: ['AHS', 'SOC'],
                },
            };
        }
    }
}
