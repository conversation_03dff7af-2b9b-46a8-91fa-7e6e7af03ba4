import { locations } from '$lib/stores/Locations';
import { StatusCodes } from 'http-status-codes';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

const DataAggURL = `${get(locations).data_agg}`;

export const POST: RequestHandler = async ({ locals, request, url, params }) => {
    let message: string;
    let status: StatusCodes;
    const requestData: { Tenant_Id?: string; Updated_By?: string } = {};
    requestData.Tenant_Id = locals.user.claims.active_tenant?.tenant_id;
    requestData.Updated_By = locals.user.claims.preferred_username;
    const res = await fetch(`${DataAggURL}/sys-datasource/clone/${params.id}`, {
        method: 'POST',
        body: JSON.stringify(requestData),
        headers: {
            'content-type': 'application/json',
            'Authorization': 'Bearer ' + locals.user.access_token,
        },
    }).then(response => {
        return response.json();
    });
    status = res.status;
    message = res.statusText;
    const responseBody = {
        message,
        data: res.data,
    };
    return new Response(JSON.stringify(responseBody), {
        status,
    });
};
