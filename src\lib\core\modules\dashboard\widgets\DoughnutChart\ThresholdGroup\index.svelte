<script lang="ts" context="module">
    export interface ThresholdItemValid {
        isValid?: boolean;
        errorMessage: string;
    }

    export interface ThresholdItem {
        titleThresholdItem?: string;
        fieldThresholdItem?: string;
        comparatorThreshold?: string;
        valueOfThreshold?: number;
        isValidItem?: ThresholdItemValid;
        typeItems: any;
    }

    export interface ThresholdConfig {
        textColorThreshold?: string;
        isConditionAnd?: boolean;
        thresholdConfigItems: ThresholdItem[];
    }

    export interface ThresholdGroupConfig {
        isEnableThreshold?: boolean;
        thresholdGroupConfigItems: ThresholdConfig[];
    }
</script>

<script lang="ts">
    import { Label, Input, Select, Toggle, Button, Helper, Hr } from 'flowbite-svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
    import { TYPEOF_COMPARATOR } from '$lib/core/modules/dashboard/widgets/utils';
    import { AccordionItem, Accordion } from 'flowbite-svelte';
    import { e } from 'mathjs';

    export let buttonfaPlus = faPlus;
    export let buttonfaMinus = faMinus;
    export let isDisplayTextColor = true;
    export let isDisplayBgColor = true;

    export const FIELD_THRESHOLD = [
        { name: 'Patient', value: 'Patient' },
        { name: 'Max WT', value: 'Max WT' },
    ];

    export const configThresholdItem: ThresholdItem = {
        titleThresholdItem: 'Red',
        fieldThresholdItem: FIELD_THRESHOLD[0].value,
        comparatorThreshold: TYPEOF_COMPARATOR[0].value,
        valueOfThreshold: 10,
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
        typeItems: TYPEOF_COMPARATOR,
    };

    const THRESHOLD_CONFIG_ITEMS: Array<ThresholdItem> = [
        {
            titleThresholdItem: 'Red',
            fieldThresholdItem: FIELD_THRESHOLD[0].value,
            comparatorThreshold: TYPEOF_COMPARATOR[0].value,
            valueOfThreshold: 100,
            isValidItem: {
                isValid: true,
                errorMessage: '',
            },
            typeItems: TYPEOF_COMPARATOR,
        },
        {
            titleThresholdItem: 'Red',
            fieldThresholdItem: FIELD_THRESHOLD[1].value,
            comparatorThreshold: TYPEOF_COMPARATOR[0].value,
            valueOfThreshold: 20,
            isValidItem: {
                isValid: true,
                errorMessage: '',
            },
            typeItems: TYPEOF_COMPARATOR,
        },
    ];

    export let configThresholdConfigs: ThresholdConfig = {
        textColorThreshold: '#FFFFFF',
        isConditionAnd: false,
        thresholdConfigItems: THRESHOLD_CONFIG_ITEMS,
    };

    const THRESHOLD_GROUP_CONFIG_ITEMS: Array<ThresholdConfig> = [configThresholdConfigs];

    export let thresholdGroupConfigs: ThresholdGroupConfig = {
        isEnableThreshold: false,
        thresholdGroupConfigItems: THRESHOLD_GROUP_CONFIG_ITEMS,
    };

    let TYPEOF_COMPARATOR_THRESHOLD_ITEMS = [];
    const TYPEOF_COMPARATOR_FOR_ASC_VAL = TYPEOF_COMPARATOR.filter(element => element.name.includes('<'));
    const TYPEOF_COMPARATOR_FOR_DESC_VAL = TYPEOF_COMPARATOR.filter(element => element.name.includes('>'));

    //logic for threshold value
    const COMPARISON_THRESHOLD_OPERATORS_HASH = {
        '<': (a, b) => a <= b,
        '>': (a, b) => a >= b,
        '>=': (a, b) => a > b,
        '<=': (a, b) => a < b,
    };

    $: TYPEOF_COMPARATOR_THRESHOLD_ITEMS = Array(1).fill(TYPEOF_COMPARATOR_FOR_ASC_VAL);

    function getThresholdData() {
        return configThresholdConfigs.thresholdConfigItems;
    }

    function getThresholdGroup() {
        return thresholdGroupConfigs.thresholdGroupConfigItems;
    }

    function updateThresholdConfigData(data) {
        thresholdGroupConfigs.thresholdGroupConfigItems = data;
    }

    function addThresholdConfigItem(thresholdConfig, index) {
        let array = thresholdConfig.thresholdConfigItems;
        if (!array.some(x => x.isValidItem.isValid === false)) {
            const lastItem = array[array.length - 1];
            const arrClone = {
                titleThresholdItem: lastItem.titleThresholdItem + 'Clone',
                fieldThresholdItem: FIELD_THRESHOLD[0].value,
                comparatorThreshold: lastItem.comparatorThreshold,
                valueOfThreshold: lastItem.valueOfThreshold,
                textColor: lastItem.textColor,
                bgColor: lastItem.bgColor,
                isValidItem: {
                    isValid: true,
                    errorMessage: '',
                },
                typeItems: TYPEOF_COMPARATOR,
            };
            array = [...array, arrClone];
            thresholdGroupConfigs.thresholdGroupConfigItems[index].thresholdConfigItems.push(arrClone);
            updateThresholdConfigData(thresholdGroupConfigs.thresholdGroupConfigItems);
        }
    }

    function addThresholdConfig(thresholdGroupConfig) {
        let arrayGroupThreshold = thresholdGroupConfig.thresholdGroupConfigItems;
        //if (!arrayGroupThreshold.some(x => x.isValidItem.isValid === false)) {
        const lastItemGroup = arrayGroupThreshold[arrayGroupThreshold.length - 1];
        const lastItem = lastItemGroup.thresholdConfigItems[lastItemGroup.thresholdConfigItems.length - 1];
        const arrClone = {
            isConditionAnd: false,
            thresholdConfigItems: [
                {
                    titleThresholdItem: lastItem.titleThresholdItem + 'Clone',
                    fieldThresholdItem: FIELD_THRESHOLD[0].value,
                    comparatorThreshold: lastItem.comparatorThreshold,
                    valueOfThreshold: lastItem.valueOfThreshold,
                    textColor: lastItem.textColor,
                    bgColor: lastItem.bgColor,
                    isValidItem: {
                        isValid: true,
                        errorMessage: '',
                    },
                },
                {
                    titleThresholdItem: lastItem.titleThresholdItem + 'Clone',
                    fieldThresholdItem: FIELD_THRESHOLD[1].value,
                    comparatorThreshold: lastItem.comparatorThreshold,
                    valueOfThreshold: lastItem.valueOfThreshold,
                    textColor: lastItem.textColor,
                    bgColor: lastItem.bgColor,
                    isValidItem: {
                        isValid: true,
                        errorMessage: '',
                    },
                },
            ],
        };
        arrayGroupThreshold = [...arrayGroupThreshold, arrClone];
        updateThresholdConfigData(arrayGroupThreshold);
        //}
    }
    function removeThresholdGroupConfigItem(index) {
        let array = getThresholdGroup();
        if (array.length > 1) {
            array = array.slice(0, index).concat(array.slice(index + 1));
            updateThresholdConfigData(array);
        }
    }

    function removeThresholdConfigItem(index1, index2) {
        let arrayGroupThresholdConfig = getThresholdGroup();
        let arrayThresholdConfig = arrayGroupThresholdConfig[index1].thresholdConfigItems;
        if (arrayThresholdConfig.length > 2) {
            arrayGroupThresholdConfig[index1].thresholdConfigItems = arrayThresholdConfig
                .slice(0, index2)
                .concat(arrayThresholdConfig.slice(index2 + 1));
            updateThresholdConfigData(arrayGroupThresholdConfig);
        }
    }

    function updateDataWhenThresholdChanged(arrayConfig: ThresholdConfig[]) {
        arrayConfig.forEach((i, index) => {
            const firstPatient = i.thresholdConfigItems.findIndex(i => i.fieldThresholdItem === 'Patient');
            const firstWt = i.thresholdConfigItems.findIndex(i => i.fieldThresholdItem === 'Max WT');
            let valuePatient, valueWt;
            valuePatient = i.thresholdConfigItems[firstPatient].valueOfThreshold * 1;
            valueWt = i.thresholdConfigItems[firstWt].valueOfThreshold * 1;

            i.thresholdConfigItems.forEach((j, k) => {
                j.typeItems = TYPEOF_COMPARATOR;
                if (k !== firstPatient && k !== firstWt) {
                    j.typeItems = TYPEOF_COMPARATOR.filter(({ value }) =>
                        value.includes(
                            i.thresholdConfigItems[
                                j.fieldThresholdItem === 'Patient' ? firstPatient : firstWt
                            ].comparatorThreshold.slice(0, 1)
                        )
                    );
                    if (!j.comparatorThreshold.includes(j.typeItems[0].value.slice(0, 1))) {
                        j.comparatorThreshold = j.typeItems[0].value;
                    }
                    const compareValue = j.fieldThresholdItem === 'Patient' ? valuePatient : valueWt;
                    const isCompareGreater = j.comparatorThreshold.includes('>');
                    const currentValue = j.valueOfThreshold * 1;
                    if (isCompareGreater) {
                        j.isValidItem.isValid = currentValue < compareValue;
                    } else {
                        j.isValidItem.isValid = currentValue > compareValue;
                    }
                    if (j.fieldThresholdItem === 'Patient') valuePatient = currentValue;
                    else valueWt = currentValue;
                    j.isValidItem.errorMessage =
                        currentValue === compareValue
                            ? `Current threshold value is similar to previous threshold`
                            : `Current threshold value must be ${
                                  isCompareGreater ? 'less' : 'greater'
                              }  than previous threshold`;
                }
            });
        });
    }

    $: if (thresholdGroupConfigs.thresholdGroupConfigItems) {
        updateDataWhenThresholdChanged(thresholdGroupConfigs.thresholdGroupConfigItems);
    }
</script>

<div class="text-white dark:text-white">
    <div class="flex h-12">
        <Toggle color="blue" bind:checked={thresholdGroupConfigs.isEnableThreshold} />
        <span class="mr-2 self-center text-sm font-medium text-white dark:text-white">Show Threshold</span>
    </div>

    <div class=" {thresholdGroupConfigs.isEnableThreshold ? '' : 'hidden'}">
        <span class="mr-2 self-center text-sm font-medium text-white dark:text-white">Add Rule Group</span>
        <Button class="h-12 w-12  bg-gray-500" pill={true} on:click={() => addThresholdConfig(thresholdGroupConfigs)}
            >{@html icon(buttonfaPlus).html}</Button>
        <Hr classHr="my-4" />
    </div>

    <div class="{thresholdGroupConfigs.isEnableThreshold ? '' : 'hidden'} ">
        {#each thresholdGroupConfigs.thresholdGroupConfigItems as item1, i}
            <div class="flex justify-between">
                <div class="flex">
                    <span class="mr-2 self-center text-sm font-medium text-white dark:text-white">Add Threshold</span>
                    <Button
                        class="ml-3 h-12 w-12 bg-gray-500"
                        pill={true}
                        on:click={() => addThresholdConfigItem(item1, i)}>{@html icon(buttonfaPlus).html}</Button>

                    <Toggle color="blue" class="ml-4 " bind:checked={item1.isConditionAnd} />
                    <span class="mr-2 self-center text-sm font-medium text-white dark:text-white">AND</span>
                </div>
                <div class="flex">
                    <span class="mr-3 self-center text-sm font-medium text-white dark:text-white"
                        >Remove Rule Group</span>
                    <Button
                        class="mr-2 h-12 w-12 bg-gray-500"
                        pill={true}
                        on:click={() => removeThresholdGroupConfigItem(i)}>{@html icon(buttonfaMinus).html}</Button>
                </div>
            </div>

            <section class="flex flex-row flex-wrap items-center gap-2 py-2">
                <div class="w-[18%] self-center">
                    <Label>Threshold Title</Label>
                </div>
                <div class="w-[20%] self-center break-all">
                    <Label>Field</Label>
                </div>
                <div class="w-[15%] self-center break-all">
                    <Label>Comparator</Label>
                </div>
                <div class="w-[19%] self-center break-all">
                    <Label>Threshold Value</Label>
                </div>
                <div class="w-[10%] self-center {isDisplayBgColor ? '' : 'hidden'}">
                    <Label class="text-center">Bg Color</Label>
                </div>
                <div class="w-[12%] self-center {isDisplayTextColor ? '' : 'hidden'}">
                    <Label class="text-center">Text Color</Label>
                </div>
            </section>
            {#each item1.thresholdConfigItems || [] as item, index}
                <section class="flex flex-row flex-wrap items-center gap-2 py-2">
                    <div class="w-[18%] self-center">
                        <Input placeholder="Threshold Title" bind:value={item.titleThresholdItem} />
                    </div>
                    <div class="w-[19%] self-center">
                        <Select class="dark:bg-gray-600" items={FIELD_THRESHOLD} bind:value={item.fieldThresholdItem} />
                    </div>
                    <div class="w-[15%] self-center">
                        <Select
                            class="dark:bg-gray-600"
                            items={item.typeItems || []}
                            bind:value={item.comparatorThreshold} />
                    </div>
                    <div class="w-[20%] self-center">
                        <Input placeholder="Threshold Value" type="number" bind:value={item.valueOfThreshold} />
                    </div>

                    {#if index === 0}
                        <div class="w-[12%] self-center {isDisplayTextColor ? '' : 'hidden'}">
                            <Input
                                type="color"
                                class="mx-auto h-10 w-10 min-w-[2.5rem] p-0"
                                placeholder="Text Color"
                                bind:value={item1.textColorThreshold} />
                        </div>
                    {:else}
                        <div class="w-[12%] self-center"></div>
                    {/if}

                    <div class="self-center">
                        <Button
                            class="h-12 w-12 bg-gray-500"
                            pill={true}
                            on:click={() => removeThresholdConfigItem(i, index)}
                            >{@html icon(buttonfaMinus).html}</Button>
                    </div>
                </section>
                {#if item.isValidItem.isValid == false}
                    <Helper class="text-sm font-medium" color="red">
                        <span>{item.isValidItem.errorMessage}</span>
                    </Helper>
                {/if}
            {/each}

            <hr
                class="my-4 h-px border-0 bg-gray-200 dark:bg-gray-700 {i ==
                thresholdGroupConfigs.thresholdGroupConfigItems.length - 1
                    ? 'hidden'
                    : ''}" />
        {/each}
    </div>
</div>
