<!-- @component
    This component displays the date, day, and time based on date time that's been passed to it.
 -->
<script lang="ts" context="module">
    import { env } from '$env/dynamic/public';
    import Weather from '$lib/core/components/Weather/index.svelte';
    import { logger } from '$lib/stores/Logger';
    import dayjs from 'dayjs';
    import duration from 'dayjs/plugin/duration.js';
    import { onDestroy } from 'svelte';

    dayjs.extend(duration);

    const log = logger.child({ src: new URL(import.meta.url).pathname });
</script>

<script lang="ts">
    export let dateTime: ISO8601Date;
    export let showOutOfSyncIndication = false;
    export let isOutOfSync = false;

    const period: number = dayjs.duration({ seconds: 1 }).asMilliseconds();
    const thresholdSeconds: number = 5;

    let date: string;
    let day: string;
    let time: string;

    $: updateTime(dateTime);

    function updateTime(dateTime: ISO8601Date) {
        if (dateTime) {
            let dayjsDateTime = dayjs(dateTime);
            date = dayjsDateTime.format('DD/MM/YYYY');
            day = dayjsDateTime.format('ddd').toLocaleUpperCase();
            time = dayjsDateTime.format('HH:mm:ss');
        } else {
            date = ' - - / - - / - - - -';
            day = '- - -';
            time = ' - - : - - : - - ';
        }
    }

    let compareTimeInterval: NodeJS.Timeout;

    if (env.PUBLIC_REPLAY_MODE !== '1') {
        compareTimeInterval = setInterval(() => {
            let dayjsDateTime = dayjs(dateTime);
            let now = dayjs();

            isOutOfSync = Math.abs(dayjsDateTime.diff(now, 'seconds')) > thresholdSeconds;
        }, period);
    }

    onDestroy(async () => {
        if (compareTimeInterval) {
            clearInterval(compareTimeInterval);
        }
    });
</script>

<div class="flex flex-col items-start justify-start whitespace-nowrap text-on-background">
    <div class="flex flex-row items-start justify-start gap-2">
        <div class="font-norma text-base">{date}</div>
        <Weather setClass="font-black text-xl" />
    </div>
    <div class="flex flex-row items-start justify-start gap-2 text-sm">
        <div class="font-semibold">{day}</div>
        <div class="font-medium {showOutOfSyncIndication && isOutOfSync ? 'text-error' : ''}">{time}</div>
    </div>
</div>
