<script lang="ts" context="module">
    import { faGear, faLayerGroup } from '@fortawesome/free-solid-svg-icons';
    import { onMount } from 'svelte';
    import type { SubGridConfig } from './WidgetConfig/index.svelte';
    import { getCurrentTimezoneOffset, WIDGET_GROUP_TITLE } from '../utils';
    import { But<PERSON>, Heading } from 'flowbite-svelte';
    import Notification from '$lib/shared/components/notification/Notification.svelte';

    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { user } from '$lib/stores/Auth';
    import type { CreateCfgUserFilteringDto, UpdateCfgUserFilteringDto } from '$lib/shared/dto/cfg_user_filtering';
    import { queryStore, subscriptionStore, type OperationResultStore, type Pausable } from '@urql/svelte';
    import FormModal from '$lib/core/modules/DynamicForm/FormModal.svelte';
    import CfgParameterViewer from '$lib/shared/components/cfg-parameter/CfgParameterViewer.svelte';

    import {
        GetCfgFilteringLabelDocument,
        GetCfgParameterByIdDocument,
        GetCfgUserFilteringByCfgParamUserIdDocument,
        GetCfgUserFilteringByCfgParamUserIdSubscriptionDocument,
        type GetCfgFilteringLabelSubscription,
        type GetCfgParameterByIdQuery,
        type GetCfgUserFilteringByCfgParamUserIdQuery,
        type GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription,
    } from '$generated-types';
    import { SOC_CFG_POPUP_INFO, SOC_CFG_POPUP_KEYS } from '$lib/shared/enum/general';
    import { getToggleStatus, mappingDataByKeys, standardizedFilterLabel } from '../../utils/utils';
    import { convertDateMonthQuarterYearToDateString } from '$lib/core/modules/DynamicForm/utils';

    export const widgetIcon = faLayerGroup;
    export const widgetCategory = 'Widget Group Management';
    export const widgetLimit = 999;
    export const widgetTitle = WIDGET_GROUP_TITLE;
    export const widgetEnabled = true;
    export const isSubGrid = true;
    export const widgetMinWidth = 6;
    export const widgetMinHeight = 6;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
</script>

<script lang="ts">
    export let config: SubGridConfig;
    export let sendDataToDashboard;
    export let editable: boolean = false;

    const client = get(gqlClientStore);

    let groupHtml: HTMLElement;
    let mounted: boolean = false;
    let isModalOpen: boolean = false;
    let modalMessage: string = '';
    let openFilter = false;

    let form_fields_json;
    let userFilteringData;

    let titleFiltering;
    let formModal;
    let key_value_json;
    let dataCfgParameter: Record<string, string | boolean> = {};
    let user_id = '';
    let subUserFiltering: OperationResultStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription> & Pausable;
    let customFilteringLabel: OperationResultStore<GetCfgFilteringLabelSubscription> & Pausable;
    let cfgParam;
    let defaultUserFilteringLabel: OperationResultStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription> &
        Pausable;

    onMount(() => {
        user_id = get(user).claims.hasura_access['x-hasura-user-id'];
        getUserFiltering();
        // getUserFiltering1();
        mounted = true;
    });

    const onGearClick = () => {
        openFilter = true;
    };
    const handleSocFilterLabel = async (data, dto: any) => {
        const isSocUs = Object.values(SOC_CFG_POPUP_KEYS).some(key => data[key] !== undefined);
        if (isSocUs) {
            const filterLabel = structuredClone(data);
            for (const key of Object.keys(filterLabel)) {
                const info = SOC_CFG_POPUP_INFO.find(info => info.key.toLocaleLowerCase() === key.toLocaleLowerCase());
                if (info?.key) {
                    const toggleStatus = await getToggleStatus(info.cfgName);
                    // handle for 1 toggle buttons, if >=2 => update here
                    const isOn = toggleStatus ? Object.values(toggleStatus)?.[0] || false : false;
                    const newValue = await mappingDataByKeys(data[key], info, isOn);
                    filterLabel[key] = newValue[key]?.length ? newValue[key].join(', ') : filterLabel[key];
                }
            }
            dto.filter_label = JSON.stringify(standardizedFilterLabel(filterLabel, true));
            const keyValueJson = JSON.parse(dto.key_value_json);
            dto.key_value_json = JSON.stringify(convertDateMonthQuarterYearToDateString(keyValueJson, true));
        }
    };

    const cfgSave = async data => {
        let newData = JSON.parse(
            JSON.stringify({ ...data, TimeZone: getCurrentTimezoneOffset() }).replaceAll('T00:00:00.000Z', '')
        );
        let newFilterLabel = JSON.parse(JSON.stringify(data).replaceAll('T00:00:00.000Z', ''));
        let res;
        if (!userFilteringData?.id) {
            const dto: CreateCfgUserFilteringDto = {
                filter_label: JSON.stringify(data),
                filtering_id: config.cfg_param,
                user_id: user_id,
                key_value_json: JSON.stringify(newData),
            };
            await handleSocFilterLabel(newData, dto);
            res = await fetch(`/api/CfgUserFilteringService/upsert`, {
                method: 'POST',
                body: JSON.stringify(dto),
            });
            await getUserFiltering();
        } else {
            const dto: UpdateCfgUserFilteringDto = {
                filter_label: JSON.stringify(newFilterLabel),
                key_value_json: JSON.stringify(newData),
                filtering_id: config.cfg_param,
                user_id,
            };
            await handleSocFilterLabel(newData, dto);
            res = await fetch(`/api/CfgUserFilteringService/${userFilteringData.id}`, {
                method: 'PATCH',
                body: JSON.stringify(dto),
            });
        }

        if (res.ok) {
            modalMessage = 'Configuration is saved successfully.';
            sendDataToDashboard({ cfgParamId: dataCfgParameter?.id, cfgParamName: dataCfgParameter?.name });
            if (data['Enable'] !== undefined) {
                userFilteringData = {
                    ...userFilteringData,
                    key_value_json: newData,
                };
            }
        } else {
            modalMessage = 'Unable to save configuration successfully.';
        }
        isModalOpen = true;
        openFilter = false;
    };

    function getUserFiltering() {
        if (!config?.cfg_param) {
            return;
        }
        user_id = get(user).claims.hasura_access['x-hasura-user-id'];
        const queryCfgParams = queryStore<GetCfgParameterByIdQuery>({
            client,
            query: GetCfgParameterByIdDocument,
            variables: { id: config.cfg_param },
        });
        const unsubscriber = queryCfgParams.subscribe(res => {
            if (!res.fetching && !res.error) {
                cfgParam = res.data?.cc3_hoc_cfg_parameter[0];
                form_fields_json = cfgParam?.form_fields_json || [];
                titleFiltering = cfgParam?.title || '';
                if (cfgParam) {
                    dataCfgParameter.id = cfgParam.id;
                    dataCfgParameter.name = cfgParam.name;
                    dataCfgParameter.is_custom_handler = cfgParam.is_custom_handler;
                }
                unsubscriber();
            } else if (!res.fetching && res.error) {
                unsubscriber();
            }
        });
        const queryUserFiltering = queryStore<GetCfgUserFilteringByCfgParamUserIdQuery>({
            client,
            query: GetCfgUserFilteringByCfgParamUserIdDocument,
            variables: { id: config.cfg_param, user_id },
        });
        const unsubscriberFiltering = queryUserFiltering.subscribe(res => {
            if (!res.fetching && !res.error) {
                userFilteringData = res.data?.cc3_hoc_cfg_user_filtering[0];
                key_value_json = res.data?.cc3_hoc_cfg_user_filtering[0]?.key_value_json || {};
                unsubscriberFiltering();
            } else if (!res.fetching && res.error) {
                unsubscriberFiltering();
            }
        });
        subUserFiltering = subscriptionStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription>({
            client,
            query: GetCfgUserFilteringByCfgParamUserIdSubscriptionDocument,
            variables: { id: config.cfg_param, user_id },
        });

        defaultUserFilteringLabel = subscriptionStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription>({
            client,
            query: GetCfgUserFilteringByCfgParamUserIdSubscriptionDocument,
            variables: { id: config.cfg_param, user_id: '' },
        });

        if (config.showFilteringLabel && !config.label_use_filter_datasource && config.cfg_param_label) {
            const tenant_id = get(user).claims.active_tenant.tenant_id;
            customFilteringLabel = subscriptionStore<GetCfgFilteringLabelSubscription>({
                client,
                query: GetCfgFilteringLabelDocument,
                variables: { id: config.cfg_param_label, tenant_id },
            });
        }
    }

    $: if (mounted && config.bgColor) {
        let gridStackItemContent: HTMLElement = groupHtml.closest('.grid-stack-item');
        if (gridStackItemContent) {
            gridStackItemContent.style.backgroundColor = config.bgColor;
        }
    }

    $: if ($subUserFiltering?.data?.cc3_hoc_cfg_user_filtering) {
        key_value_json = $subUserFiltering.data?.cc3_hoc_cfg_user_filtering[0]?.key_value_json || {};
        if (userFilteringData)
            userFilteringData.filter_label = $subUserFiltering.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label;
    }

    $: getFilterLabel = () => {
        if (userFilteringData?.filter_label) {
            let defaultLabel = {};
            if (
                $defaultUserFilteringLabel?.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label &&
                $defaultUserFilteringLabel?.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label !== '{}'
            ) {
                defaultLabel = JSON.parse(
                    $defaultUserFilteringLabel.data.cc3_hoc_cfg_user_filtering[0].filter_label
                )[0];
            }
            const keyValueJson = userFilteringData.key_value_json;
            if (typeof keyValueJson['Enable'] === 'boolean' && keyValueJson['Enable'] === false) {
                for (const key of Object.keys(defaultLabel)) {
                    defaultLabel[`_${key}`] = defaultLabel[key];
                    delete defaultLabel[key];
                }
                return defaultLabel;
            }
            return userFilteringData?.filter_label;
        } else {
            let defaultLabel = {};
            if (
                $defaultUserFilteringLabel?.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label &&
                $defaultUserFilteringLabel?.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label !== '{}'
            ) {
                defaultLabel = JSON.parse(
                    $defaultUserFilteringLabel.data.cc3_hoc_cfg_user_filtering[0].filter_label
                )[0];
                for (const key of Object.keys(defaultLabel)) {
                    defaultLabel[`_${key}`] = defaultLabel[key];
                    delete defaultLabel[key];
                }
                return defaultLabel;
            }
        }
    };
</script>

<header
    bind:this={groupHtml}
    class="w-full flex-1 basis-4 text-wrap text-base
font-medium leading-tight text-on-background">
    {#if config?.title}
        <Heading class="flex items-center px-3" tag="h6" style="min-height: calc(var(--cell-height) * 1px - 2px)"
            >{config.title}</Heading>
    {:else if ['OverviewOfRadiologyNonXRayForED', 'OverviewOfRadiologyNonXRayForInpatient', 'OverviewOfRadiologyNonXRayForOutpatient'].includes(config?.cfg_param_name)}
        <!-- SOC 21 -->
        <Heading class="flex items-center px-3" tag="h6" style="min-height: 4rem" />
    {/if}

    <div class="invisible absolute top-2 z-[1] w-full pr-4 pt-2 text-right group-hover/grid:visible">
        {#if config?.showGear && !editable}
            <Button size="md" on:click={onGearClick}>
                <span>
                    {@html icon(faGear).html}
                </span>
            </Button>
        {/if}
    </div>

    {#if config.showFilteringLabel}
        <div class="mx-2 mb-2">
            <CfgParameterViewer
                showFilteringLabel={config.showFilteringLabel}
                justify_items={config?.filterLabelAlignment || ''}
                filter_label={!config.label_use_filter_datasource
                    ? $customFilteringLabel?.data?.cc3_hoc_cfg_parameter?.[0]?.key_value_json
                    : getFilterLabel()} />
        </div>
    {/if}

    {#if form_fields_json && key_value_json}
        {#if openFilter}
            <div
                id="large-modal-{userFilteringData?.id || ''}"
                tabindex="-1"
                class="fixed inset-0 z-50 flex max-h-full items-center justify-center overflow-y-auto overflow-x-hidden bg-black bg-opacity-80 p-4">
                <div class="relative max-h-full w-full max-w-4xl">
                    <!-- Modal content -->
                    <div class="relative rounded-lg bg-white shadow dark:bg-gray-700">
                        <!-- Modal header -->
                        <div
                            class="flex items-center justify-between rounded-t border-b p-4 md:p-4 dark:border-gray-600">
                            <h3 class="text-xl font-medium text-gray-900 dark:text-white">{titleFiltering}</h3>
                            <button
                                type="button"
                                class="ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
                                on:click={() => (openFilter = false)}>
                                <svg
                                    class="h-3 w-3"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 14 14">
                                    <path
                                        stroke="currentColor"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <div class="space-y-4 bg-gray-900 bg-opacity-50 pt-4 dark:bg-opacity-80">
                            <FormModal
                                onSave={cfgSave}
                                {formModal}
                                data={{
                                    ...dataCfgParameter,
                                    key_value_json,
                                    fields: form_fields_json,
                                }} />
                        </div>
                    </div>
                </div>
            </div>
        {/if}
    {/if}
    <Notification bind:isOpen={isModalOpen} modalTitle="Notification" {modalMessage} />
</header>
