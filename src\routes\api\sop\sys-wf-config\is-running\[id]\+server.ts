import { locations } from '$lib/stores/Locations';
import { logger } from '$lib/stores/Logger';
import { StatusCodes } from 'http-status-codes';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals, request, url, params }) => {
    let message: string;
    let status: StatusCodes;

    const res = await fetch(`${get(locations).sop}/sys-wf-config/is-running/${params.id}`, {
        method: 'GET',
        headers: {
            'content-type': 'application/json',
            'Authorization': 'Bearer ' + locals.user.access_token,
        },
    });

    status = res.status;
    message = res.statusText;
    let resp;
    try {
        resp = await res.json();
    } catch (_) {}

    const responseBody = {
        message,
        data: resp,
        sent_at: new Date().toISOString(),
    };

    return new Response(JSON.stringify(responseBody), {
        status,
    });
};
