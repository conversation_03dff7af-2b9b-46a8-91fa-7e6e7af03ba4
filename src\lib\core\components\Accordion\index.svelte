<script lang="ts">
    import Button from '$lib/core/components/Button/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faAngleDown } from '@fortawesome/free-solid-svg-icons';
    import { onMount } from 'svelte';
    import { quadInOut } from 'svelte/easing';
    import { slide } from 'svelte/transition';

    export let setClass = 'border border-outline';
    export let setContentClass = '';

    export let headerClass = 'bg-surface-variant min-h-[48px] justify-between';
    export let arrowIcon = faAngleDown;

    export let title = 'Title';
    export let isOpen = false;
    export let isDisabled = false;

    onMount(() => {
        if (isDisabled === true) isOpen = false;
    });
</script>

<div class="flex w-full flex-col {setClass}">
    <Button
        setDisabled={isDisabled}
        on:click={() => {
            isOpen = !isOpen;
        }}
        setClass={`${headerClass} sticky top-0 flex items-center gap-3 shrink-0`}>
        <i class:rotate-180={isOpen} class:opacity-20={isDisabled} class="transition-transform duration-75 ease-in-out"
            >{@html icon(arrowIcon).html}</i>
        <p class:opacity-50={isDisabled}>{title}</p>
    </Button>

    {#if isOpen}
        <div transition:slide|global={{ duration: 100, easing: quadInOut }} class="h-full w-full {setContentClass}">
            <slot />
        </div>
    {/if}
</div>
