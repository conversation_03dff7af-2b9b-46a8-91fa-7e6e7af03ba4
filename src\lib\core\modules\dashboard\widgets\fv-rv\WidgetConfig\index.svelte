<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export type FvRvConfig = {
        title?: string;
        resolution: number;
        showLabel: boolean;
    };

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'First Visit - Re-Visit';

    export const configDefaults: FvRvConfig = {
        title: DEFAULT_TITLE,
        resolution: 1000,
        showLabel: false,
    };
</script>

<script lang="ts">
    //Components
    import { Checkbox, Input, Label } from 'flowbite-svelte';

    export let config: FvRvConfig;
</script>

<div class="text-on-surface flex flex-col gap-3">
    <Label for="title">Title</Label>
    <Input name="title" placeholder={'Title'} bind:value={config.title} />
    <Label for="resolution">First name</Label>
    <Input name="resolution" type="number" placeholder={'Resolution'} bind:value={config.resolution} />
    <Checkbox bind:checked={config.showLabel}>Show Label</Checkbox>
</div>
