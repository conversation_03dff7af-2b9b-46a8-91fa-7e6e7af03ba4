<script lang="ts" context="module">
    import { faPlaneArrival } from '@fortawesome/free-solid-svg-icons';
    export const widgetTitle = 'Patients Arrived';
    export const widgetIcon = faPlaneArrival;
    export const widgetLimit = 999;
    export const widgetCategory = 'Resource Management';
    export const componentName = new URL(import.meta.url).pathname;
    export const widgetMinWidth = 3;
    export const widgetMinHeight = 1;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 12;
</script>

<script lang="ts">
    import type { WidgetPatientsArrivedConfig } from './WidgetConfig/index.svelte';
    export let config: WidgetPatientsArrivedConfig;
    let numberOfPatients = config.numberOfPatients;
</script>

<div class="flex h-full w-full flex-col px-2">
    <table class="h-full">
        <tr>
            <td
                ><div class="flex items-center justify-center text-on-background">
                    <span>{config.arrivedVia}</span>
                </div>
            </td>
            <td
                ><div class="flex items-center justify-center text-on-background">
                    <span>{numberOfPatients}/{config.uom.charAt(0)}</span>
                </div></td>
        </tr>
    </table>
</div>
