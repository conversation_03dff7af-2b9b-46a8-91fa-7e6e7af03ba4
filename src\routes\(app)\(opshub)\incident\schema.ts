import { z } from 'zod';

export const INCIDENT_TABLES = 'cc3_hoc.im_incident';
export const COMM_DEPARTMENT_TABLES = 'cc3_hoc.comm_department';
export const RESPONSE_TRACKING_TABLES = 'cc3_hoc.im_response_tracking';

export enum IncidentStatus {
    Draft = 'Draft',
    Open = 'Open',
    Reopen = 'Reopen',
    Resolved = 'Resolved',
    Closed = 'Closed',
    Request_Update = 'Request_Update',
}

export enum ButtonAction {
    Save = 'Save',
    Reset = 'Reset',
    Resolve_Incident_NN = 'Resolve Incident (No Notification)',
    Resolve_Incident = 'Resolve Incident (Send Notification)',
    Copy = 'Copy to New Incident',
    Download = 'Download',
    Export_To_MOH = 'Export to MOH Report',
    Raise_NN = 'Raise Incident (No Notification)',
    Raise = 'Raise Incident (Send Notification)',
    Reopen = 'Reopen',
    Request_Update = 'Request Update',
}

export const incidentSchema = z.object({
    ID: z.string().optional(),
    Name: z.string().optional(),
    Incident_Number: z.any().optional(),
    Incident_Status: z.any().default(IncidentStatus.Draft),
    Incident_Type_Id: z.string().optional(),
    Incident_At: z.any(),
    Resolved_At: z.any(),
    Incident_Location_Id: z.string().optional(),
    Is_Pending_Assessment: z.coerce.boolean().optional(),
    Next_Update_Days: z.string().nullable().optional(),
    Next_Update_Hours: z.string().nullable().optional(),
    Next_Update_Mins: z.string().nullable().optional(),
    Next_Update_Interval: z.number().optional(),
    Incident_Category_Id: z.string().optional(),
    Description: z.string().optional(),
    Is_Backdated: z.coerce.boolean().default(false),
    Incident_Impact_Update: z.any().optional(),
    Is_Notification: z.coerce.boolean().default(false),
    Is_Send_All: z.coerce.boolean().default(false),
    Is_Reminder: z.coerce.string().default('reminder'),
    Created_At: z.any(),
});

export const mohReportSchema = z.object({
    Institution_Name: z.string().nonempty("This field is required"),
    Institution_Office: z.string().nonempty("This field is required"),
    Incident_Number: z.string().nonempty("This field is required"),
    Incident_Id: z.string().nonempty("This field is required"),
    Incident_Type_Id: z.string().nonempty("This field is required"),
    Incident_At: z.any(),
    Incident_Location_Id: z.string().nonempty("This field is required"),
    Incident_Category_Id: z.string().nonempty("This field is required"),
    Risk_Assessment: z.string().nonempty("This field is required"),
    Description: z.string().nonempty("This field is required"),
    Comments: z.string(),
    Reporter_Info: z.string().nonempty("This field is required"),
    Institution_Phone: z.string().nonempty("This field is required"),
    Institution_Email: z.string().email("Invalid email address").nonempty("This field is required"),
});


export const categoryColorValues = [
    {
        value: 'red',
        name: 'RED',
    },
    {
        value: 'gray',
        name: 'GRAY',
    },
    {
        value: 'amber',
        name: 'AMBER',
    },
];

export const OpsAffectedStatus = 'Ops Affected';
export const NoOpsAffectedStatus = 'No Ops Affected';
export const PendingReplyStatus = 'Pending Reply';

export const responseIncidentSchema = z.object({
    status: z.string().default(OpsAffectedStatus),
    remark: z.string().nullable(),
});
