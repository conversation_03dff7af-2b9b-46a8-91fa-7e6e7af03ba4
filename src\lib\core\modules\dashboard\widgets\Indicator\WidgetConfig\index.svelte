<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface IndicatorConfig {
        dataSource?: string;
        title?: string;
        showThreshold?: boolean;
        lowThreshold?: number;
        mediumThreshold?: number;
        highThreshold?: number;
        lowStatusText?: string;
        mediumStatusText?: string;
        highStatusText?: string;
        lowColor?: string;
        mediumColor?: string;
        highColor?: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'aoh_charts_indicator_sample_aggregate';
    const DEFAULT_TITLE = 'Indicator';
    const DEFAULT_SHOW_THRESHOLD = true;
    const DEFAULT_LOW_STATUS_TEXT = 'Low';
    const DEFAULT_LOW_THRESHOLD = 10;
    const DEFAULT_MEDIUM_STATUS_TEXT = 'Med';
    const DEFAULT_MEDIUM_THRESHOLD = 100;
    const DEFAULT_HIGH_STATUS_TEXT = 'High';

    export const configDefaults: IndicatorConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        showThreshold: DEFAULT_SHOW_THRESHOLD,
        lowThreshold: DEFAULT_LOW_THRESHOLD,
        mediumThreshold: DEFAULT_MEDIUM_THRESHOLD,
        lowStatusText: DEFAULT_LOW_STATUS_TEXT,
        mediumStatusText: DEFAULT_MEDIUM_STATUS_TEXT,
        highStatusText: DEFAULT_HIGH_STATUS_TEXT,
        lowColor: '',
        mediumColor: '',
        highColor: '',
    };
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';
    import Toggle from '$lib/core/components/Toggle/index.svelte';

    export let config: IndicatorConfig;

    const expectedFormat = [{ name: 'number', type: 'numeric' }];

    // List of data source names (valid GraphQL query roots)
    const validQueries: Array<DropdownItem> = [];

    // Look for aggregate > sum queries with expected format
    // aggregate > sum follows Hasura's aggregate sum schema
    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => {
                if (field?.name !== 'aggregate') {
                    return false;
                }

                let aggregateFields = field?.type?.fields;

                return aggregateFields.some(aField => {
                    if (aField?.name !== 'sum') {
                        return false;
                    }

                    let sumFields = aField?.type?.fields;

                    return sumFields.some(sField => sField?.name === format.name && sField?.type?.name === format.type);
                });
            });
        });

        if (isQueryValid) {
            validQueries.push({ label: query.name, value: query.name });
        }
    });

    const textClass = 'w-full';
    const inputClass =
        textClass +
        ' w-full p-1 bg-transparent border border-outline transition-all duration-200 \
    ease-in-out rounded focus:border-2 focus:border-primary font-light';
</script>

<div class="flex flex-col gap-2">
    <div>
        <Dropdown
            title="Data Source"
            options={validQueries}
            setClass="border-b border-b-outline bg-transparent"
            bind:value={config.dataSource} />
    </div>
    <div>
        <TextField
            placeholder="Title"
            bind:value={config.title}
            setClass="{inputClass} h-10"
            setLabelClass="text-primary text-xs" />
    </div>

    <section class="flex flex-col items-center gap-2 border-y-2 border-y-outline py-2">
        <TextField
            placeholder={'Low Threshold'}
            bind:value={config.lowThreshold}
            setClass="{inputClass} h-10"
            setLabelClass="text-primary text-xs" />

        <div class="flex w-full items-center gap-2">
            <TextField
                required
                placeholder={'Low Status Text'}
                bind:value={config.lowStatusText}
                setClass="{inputClass} h-10 grow"
                setLabelClass="text-primary text-xs" />
            <input type="color" class="h-12 w-12 rounded border-outline bg-transparent" bind:value={config.lowColor} />
        </div>
    </section>

    <section class="flex flex-col items-center gap-2 border-b-2 border-b-outline py-2">
        <TextField
            placeholder={'Medium Threshold'}
            bind:value={config.mediumThreshold}
            setClass="{inputClass} h-10"
            setLabelClass="text-primary text-xs" />

        <div class="flex w-full items-center gap-2">
            <TextField
                required
                placeholder={'Medium Status Text'}
                bind:value={config.mediumStatusText}
                setClass="{inputClass} h-10 grow"
                setLabelClass="text-primary text-xs" />
            <input
                type="color"
                class="h-12 w-12 rounded border-outline bg-transparent"
                bind:value={config.mediumColor} />
        </div>
    </section>

    <section class="flex flex-col items-center gap-2 py-2">
        <TextField
            placeholder={'High Threshold'}
            bind:value={config.highThreshold}
            setClass="{inputClass} h-10"
            setLabelClass="text-primary text-xs" />

        <div class="flex w-full items-center gap-2">
            <TextField
                required
                placeholder={'High Status Text'}
                bind:value={config.highStatusText}
                setClass="{inputClass} h-10 grow"
                setLabelClass="text-primary text-xs" />
            <input type="color" class="h-12 w-12 rounded border-outline bg-transparent" bind:value={config.highColor} />
        </div>
    </section>
    <div>
        <Toggle labelText="Show Status Text" bind:checked={config.showThreshold} />
    </div>
</div>
