<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface SupplyAndDemandConfig {
        dataSource?: string;
        title?: string;
        windowMonths?: number;
        mediumRiskThreshold?: number;
        highRiskThreshold?: number;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'aoh_charts_supply_and_demand_sample';
    const DEFAULT_TITLE = 'Sample Title';
    const DEFAULT_WINDOW_MONTHS = 12;
    const DEFAULT_MEDIUM_RISK_THRESHOLD = 6;
    const DEFAULT_HIGH_RISK_THRESHOLD = 3;

    export const configDefaults: SupplyAndDemandConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        windowMonths: DEFAULT_WINDOW_MONTHS,
        mediumRiskThreshold: DEFAULT_MEDIUM_RISK_THRESHOLD,
        highRiskThreshold: DEFAULT_HIGH_RISK_THRESHOLD,
    };
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';

    export let config: SupplyAndDemandConfig;

    const expectedFormat = [
        { name: 'date_time', type: 'timestamptz' },
        { name: 'supply', type: 'numeric' },
        { name: 'demand', type: 'numeric' },
    ];

    // List of data source names (valid GraphQL query roots)
    const validQueries: Array<DropdownItem> = [];

    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => field?.name == format.name && field?.type?.ofType?.name == format.type);
        });

        if (isQueryValid) {
            validQueries.push({ label: query.name, value: query.name });
        }
    });
</script>

<div class="flex flex-col gap-2">
    <div>
        <Dropdown title="Data Source" options={validQueries} bind:value={config.dataSource} />
    </div>
    <div>
        <TextField required placeholder={'Title'} bind:value={config.title} />
    </div>
    <div>
        <TextField required placeholder={'Time Window (Months)'} bind:value={config.windowMonths} />
    </div>
    <div>
        <TextField required placeholder={'Medium Risk Threshold'} bind:value={config.mediumRiskThreshold} />
    </div>
    <div>
        <TextField required placeholder={'High Risk Threshold'} bind:value={config.highRiskThreshold} />
    </div>
</div>
