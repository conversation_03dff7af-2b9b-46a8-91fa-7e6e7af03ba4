<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import type { RegisteredSeriesOption } from 'echarts';

    export interface ChartXY {
        title?: string;
        dataSource?: string;
        formatType?: string;
        chartType?: keyof RegisteredSeriesOption;
        showXAxis: boolean;
        showYAxis: boolean;
        xKey?: string;
        keySeries?: Array<string>;
        showLegend: boolean;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'Consumable Stock Movement';
    const DEFAULT_CHART_TYPE = 'line';

    export const configDefaults: ChartXY = {
        dataSource: '',
        title: DEFAULT_TITLE,
        formatType: '',
        chartType: DEFAULT_CHART_TYPE,
        showXAxis: true,
        showYAxis: true,
        xKey: '',
        keySeries: [],
        showLegend: true,
    };
    export const MMM_DD_hh_mm: string = 'MMM-DD hh:mm';
    export const MMM_DD: string = 'MMM-DD';
    export const DD: string = 'DD';
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';
    import {
        DROPDOWN_CLASS,
        INPUT_CLASS,
        LABEL_CLASS,
        type Query,
    } from '$lib/core/modules/dashboard/widgets/utils';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCaretDown, faDatabase, faSliders, faXmark } from '@fortawesome/free-solid-svg-icons';
    import { onMount } from 'svelte';
    import { cubicInOut } from 'svelte/easing';
    import { slide } from 'svelte/transition';
    import Button from '$lib/core/components/Button/index.svelte';

    export let config: ChartXY;
    let currentDatasource: Query = undefined;

    function updateDatasources() {
        datasources = [];

        datasources = $dataSource.__schema?.types.map(query => {
            if (config?.dataSource === query.name) currentDatasource = query;

            return {
                label: query.name,
                value: query.name,
                action: () => {
                    currentDatasource = query;
                },
            };
        });

        if (!datasources.length) config.dataSource = '';
    }

    // List of data source names (valid GraphQL query roots)
    let datasources: Array<DropdownItem> = [];
    let options: DropdownItem[] = [];
    let xFormatLabel: DropdownItem[] = [
        {
            label: MMM_DD_hh_mm,
            value: MMM_DD_hh_mm,
        },
        {
            label: MMM_DD,
            value: MMM_DD,
        },
        {
            label: DD,
            value: DD,
        },
    ];

    const supportedCharts: DropdownItem[] = [
        {
            label: 'Line Chart',
            value: 'line',
        },
        {
            label: 'Bar Chart',
            value: 'bar',
        },
        {
            label: 'Area Chart',
            value: 'area',
        },
    ];

    const inputClass =
        ' w-full p-1 bg-transparent border border-outline transition-all duration-200 \
    ease-in-out rounded focus:border-2 focus:border-primary font-light';

    function updateOptions(queries) {
        if (queries) {
            options = [];

            options = queries
                .map(q => {
                    if (q?.type?.ofType?.name === 'Int') {
                        return {
                            label: q.name,
                            value: q.name,
                        };
                    }
                })
                .filter(option => {
                    //To filter undefined values
                    return Boolean(option);
                });
        }
    }

    $: updateOptions(currentDatasource?.fields);

    $: config.keySeries = config.keySeries;

    updateDatasources();

    // onMount(async () => {
    // });
</script>

<div class="text-on-surface flex flex-col gap-3">
    <TextField placeholder={'Title'} bind:value={config.title} setClass={INPUT_CLASS} setLabelClass={LABEL_CLASS} />

    <!-- Datasource column-->
    <section class="flex flex-col gap-2">
        <header class="flex w-full items-center gap-2 text-xs">
            Data Source
            {@html icon(faDatabase).html}
        </header>

        <Dropdown
            title={'Data Source'}
            bind:value={config.dataSource}
            enableSearch={true}
            options={datasources}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />
    </section>

    <!-- Properties Column-->
    <div
        class="w-full {currentDatasource
            ? ''
            : 'pointer-events-none brightness-50'} flex flex-col gap-2 transition-colors duration-200 ease-in-out">
        <p class="py-2 text-center">{@html icon(faCaretDown).html}</p>

        <header class="flex w-full items-center gap-2 border-b-2 border-outline py-1 text-xs">
            Properties
            {@html icon(faSliders).html}
        </header>

        <Dropdown
            title={'Format X axis label'}
            options={xFormatLabel}
            bind:value={config.formatType}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />

        <Dropdown
            title="Chart Type"
            options={supportedCharts}
            bind:value={config.chartType}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />

        <div transition:slide={{ easing: cubicInOut, axis: 'y' }} class="flex w-full items-center justify-between px-2">
            <p class="text-on-surface text-sm">Show X-axis</p>
            <input type="checkbox" bind:checked={config.showXAxis} />
        </div>

        <div transition:slide={{ easing: cubicInOut, axis: 'y' }} class="flex w-full items-center justify-between px-2">
            <p class="text-on-surface text-sm">Show Y-axis</p>
            <input type="checkbox" bind:checked={config.showYAxis} />
        </div>

        <div transition:slide={{ easing: cubicInOut, axis: 'y' }} class="flex w-full items-center justify-between px-2">
            <p class="text-on-surface text-sm">Show Legend</p>
            <input type="checkbox" bind:checked={config.showLegend} />
        </div>

        <!-- <TextField
            placeholder={'xKey'}
            bind:value={config.xKey}
            setClass="{inputClass} h-10"
            setLabelClass="text-primary text-xs" />

        {#each config?.keySeries as key, i}
            <span
                                    class="my-1 flex w-full items-center justify-between gap-2 px-2"
                                    transition:slide|local={{ axis: 'y' }}>
                <TextField
                    placeholder={'key series'}
                    bind:value={key}
                    setClass="{inputClass} h-10"
                    setLabelClass="text-primary text-xs" />
                <Button
                    setClass={BUTTON_CLASS}
                    on:click={() => {
                        config.keySeries.splice(i, 1);
                        config.keySeries = config.keySeries;
                    }}>{@html icon(faXmark).html}</Button>
            </span>
        {/each}
        
        <Button
            on:click={() => {
                config.keySeries = [...config.keySeries, ''];
            }}
            setClass="w-fit px-1 ml-auto text-end text-2xs {BUTTON_CLASS}">
            ...Add series.
        </Button> -->
    </div>
</div>
