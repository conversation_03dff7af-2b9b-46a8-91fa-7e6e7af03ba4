<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCopy } from '@fortawesome/free-solid-svg-icons';
    import { linear, quadOut } from 'svelte/easing';
    import { blur, fly } from 'svelte/transition';

    import { logger } from '$lib/stores/Logger';

    export let label = 'undefined';
    export let value = 'undefined';
    export let textColor = 'text-on-background';
    export let labelClass = 'undefined';
    export let valueClass = 'undefined';

    let copied = false;
    let notSupported = false;
</script>

<div
    tabindex="0"
    role="button"
    on:click={async () => {
        try {
            // Note that this feature is available only in secure contexts (HTTPS)
            if (navigator && navigator.clipboard) {
                await navigator.clipboard.writeText(value ? value : 'undefined');
                copied = true;
            } else {
                notSupported = true;
            }
        } catch (err) {
            logger.debug({ error: err }, 'Error copying to clipboard.');
        }
    }}
    class="text-md group relative cursor-pointer opacity-100 {textColor} w-full hover:opacity-80 active:opacity-60">
    <div class="flex w-full">
        <div class={labelClass}>
            {label}:&nbsp;
        </div>
        <div class={valueClass}>
            {value}
        </div>
        <div class="relative ml-1">
            {@html icon(faCopy).html}
            {#if copied}
                <div
                    class="absolute right-0 inline {textColor}"
                    in:blur|global={{ duration: 0 }}
                    on:introend={() => {
                        copied = false;
                    }}
                    out:fly|global={{ y: -30, duration: 700, easing: quadOut }}>
                    {@html icon(faCopy).html}
                </div>
            {/if}
            {#if notSupported}
                <div
                    class="absolute right-0 inline {textColor}"
                    in:blur|global={{ duration: 0 }}
                    on:introend={() => {
                        notSupported = false;
                    }}
                    out:fly|global={{ y: -80, duration: 2000, easing: linear }}>
                    Clipboard Not Supported
                </div>
            {/if}
        </div>
    </div>
</div>
