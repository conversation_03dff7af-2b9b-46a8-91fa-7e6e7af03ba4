<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import WidgetItemConfig from '$lib/core/modules/dashboard/components/WidgetItemConfig/index.svelte';
    import DashboardDetails, {
        type DashboardDetailsCreatedEvent,
        type DashboardDetailsUpdatedEvent,
    } from '$lib/core/modules/dashboard/components/DashboardDetails/index.svelte';
    import WidgetItem from '$lib/core/modules/dashboard/components/WidgetItem/index.svelte';
    import WidgetList from '$lib/core/modules/dashboard/components/WidgetList/index.svelte';
    import WidgetListCompact from '$lib/core/modules/dashboard/components/WidgetListCompact/index.svelte';
    import Tooltip from '$lib/core/components/Tooltip/index.svelte';
    import type { WidgetDetail } from '$lib/core/modules/dashboard/components/WidgetItem/index.svelte';
    import '$lib/core/modules/dashboard/components/WidgetViewer/index.scss';
    import '$lib/core/modules/dashboard/components/WidgetItem/index.scss';
    import { setSidebarDisplay } from '$lib/core/modules/menubar/Sidebar/index.svelte';
    import { setStatusBarDisplay } from '$lib/core/components/StatusBar/index.svelte';
    import { onMount, setContext, tick, onDestroy, createEventDispatcher } from 'svelte';
    import { computePosition, flip, offset, shift } from '@floating-ui/dom';
    import Modal from '$lib/core/components/Modal/index.svelte';
    import {
        type DeleteConfigurationResponseBody,
        type UpdateDashboardLayoutRequestBody,
    } from '$routes/api/dash/config/+server';
    import { DashboardStore, LoadDashboardConfiguration } from '$lib/core/modules/dashboard/module';
    import { uiState } from '$lib/core/core';
    import { StatusCodes } from 'http-status-codes';
    import {
        faEdit,
        faFileCirclePlus,
        faGear,
        faPlus,
        faEye,
        faTrash,
        faUpRightAndDownLeftFromCenter,
        faFloppyDisk,
    } from '@fortawesome/free-solid-svg-icons';
    import { locations } from '$lib/stores/Locations';
    import { browser } from '$app/environment';
    import { v4 } from 'uuid';
    import { get, type Writable } from 'svelte/store';
    import { fade } from 'svelte/transition';
    import type { GridStack, GridStackOptions, GridStackWidget } from 'gridstack';
    import { isDirty } from '$lib/core/components/PageTitle/index.svelte';
    import Dialog from '$lib/core/components/Dialog/index.svelte';
    import Spinner from '$lib/core/components/Spinner/index.svelte';
    import Status from '$lib/core/components/StatusBar/Status/index.svelte';
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import WidgetGrid, { GRID_STACK_CONTEXT } from '$lib/core/modules/dashboard/components/WidgetGrid/index.svelte';
    import DashboardSelector, {
        type DashboardSelectorSelectionEvent,
        type DashboardSelectorUpdateEvent,
    } from '$lib/core/modules/dashboard/components/DashboardSelector/index.svelte';
    import Notification from '$lib/shared/components/notification/Notification.svelte';

    export enum DisplayState {
        ViewingDashboard,
        BusySaving,
        EditingDashboard,
        EditingWidget,
    }

    export const UI_STATE_LAST_SELECTED_DASHBOARD = 'last_selected_dashboard';
    export const WIDGET_GROUP_TYPE_ID = 'd99c226b-5b1e-42b7-8021-33b4181360a4';
    const ADVANCED_TABLE_TYPE_ID = 'dd221fbe-9595-438a-a00d-6f00fcea216c';
    const src = new URL(import.meta.url).pathname;
    const [componentName] = src.split('/').slice(-2, -1);
    const log = logger.child({ src, componentName });
    const MaxColumns = 36;
    const MaxRows = 0;
</script>

<script lang="ts">
    import { writable } from 'svelte/store';
    import { page } from '$app/stores';
    import { beforeNavigate, goto } from '$app/navigation';
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import type { UUID } from 'crypto';
    import { sortNestedGrid } from '../../utils/utils';
    import { TriggerService } from '$lib/service/trigger';
    import { DATA_SOURCE, SCOPE } from '$lib/shared/enum/general';
    import { hasAccess } from '$lib/shared/util/validation';
    import Guard from '$lib/shared/components/guard/Guard.svelte';

    export let dashboards: Array<DashboardConfiguration> = [];
    export let tags: Array<Tag & StandardPatchFields> = [];
    export let isEditable = false;
    export let displayState = DisplayState.ViewingDashboard;
    export let reloadWidgetViewer;
    export let abilities = [];

    const dispatch = createEventDispatcher();
    const windowWidth = writable(typeof window !== 'undefined' ? window.innerWidth : 1200);
    const IncidentMonitoringURL = `${get(locations).incident_monitoring_web}`;
    let updateWidth;
    if (typeof window !== 'undefined') {
        updateWidth = () => windowWidth.set(window.innerWidth);
        window.addEventListener('resize', updateWidth);
    }

    let widgetGrid: WidgetGrid; // the grid encapsulating the widgets

    let grid_layout: Array<WidgetGridItem> = []; // the list of widgets to render in the widget grid
    let selected_loaded_widget_item: WidgetGridItem;
    let selected_widget_item_shared_configs: Array<SharedConfig> = [];

    let selectedLayout: DashboardConfiguration; // the selected configuration from the layout list

    let isConfigOpen = false; // Flag to control whether the individual widget's configuration panel is showing or not
    let isEditDashboardDetailsDialogOpen = false; // update dashboard details dialog open flag
    let isCreateDashboardDialogOpen = false; // create dashboard details dialog open flag
    let showScrollForWidgetView = false;
    let mounted = false;
    let originWidgets = [];
    let styleCellHeight = 0;
    let initialContainerWidth: number;

    let subOptions: GridStackOptions = {
        row: MaxRows,
        cellHeight: 'initial',
        cellHeightThrottle: 100,
        column: 'auto',
        acceptWidgets: '.grid-stack-item.not-subgrid',
        class: 'nested-grid h-full transition-transform  w-full ',
    };

    let options: GridStackOptions;
    let hasError = false;

    // Set options reactively
    $: options = {
        acceptWidgets: true,
        alwaysShowResizeHandle: false,
        animate: false,
        cellHeight: styleCellHeight,
        cellHeightUnit: 'px',
        auto: true,
        cellHeightThrottle: 100,
        column: MaxColumns,
        row: MaxRows,
        disableDrag: false,
        disableResize: false,
        float: false,
        margin: '0.25rem',
        subGridOpts: subOptions,
        handle: '.grid-stack-item',
    };

    let sysDatasource: Array<SysDatasource> = [];

    let isModalOpen: boolean = false;
    let modalMessage: string = '';

    $: switch (displayState) {
        case DisplayState.ViewingDashboard:
            isEditable = false;
            break;
        case DisplayState.EditingDashboard:
            isEditable = true;
            break;
        case DisplayState.EditingWidget:
            isEditable = true;
            break;
        case DisplayState.BusySaving:
            isEditable = true;
            break;
    }

    // Currently used in hidden instantiated components to decide if they should render on page
    // in the future, we should probably values via a separte typescript file instead of instantiating the components
    let props = {
        isPreviewMode: false,
    };

    function resizeGrid() {
        if (widgetGrid) {
            widgetGrid.resize();
        }
    }

    /**
     * Symbol used to unsubscribe when slected_loaded_widget_item changes
     */
    const widget_item_config_action_bbsymbol: unique symbol = Symbol();

    /**
     * On widget item config creation, pull out the element reference
     * so and subscribe to resize events with it so we can move the config panel
     * to the correct position everytime
     * @param node
     */
    function widget_item_config_action(node: Node) {
        let update_widget_config_position = () => {
            const element = node as HTMLElement;

            if (!selected_loaded_widget_item) {
                log.error({ element }, 'no selected widget item config to update position');
                return;
            }

            const refElement = selected_loaded_widget_item.svelte_component_ref?.getGridstackNode()?.el;

            if (!refElement) {
                log.error(
                    { element, selected_loaded_widget_item },
                    'selected widget item has no ref element, please investigate'
                );
                return;
            }

            computePosition(refElement, element, {
                placement: 'right-start',
                middleware: [offset(24), flip(), shift()],
            }).then(({ x, y }) => {
                if (x < 0) {
                    Object.assign(element.style, {
                        left: `${refElement.offsetLeft + refElement.offsetWidth + 24}px`,
                        top: `${y}px`,
                    });
                } else {
                    Object.assign(element.style, {
                        left: `${x}px`,
                        top: `${y}px`,
                    });
                }
            });
        };

        // Run validate whenever a widget change occurs
        browserBroadcaster.sub(
            Channel.AOH_WIDGET_RESIZED,
            _m => {
                update_widget_config_position();
            },
            widget_item_config_action_bbsymbol
        );

        // Run validate once on initialization
        update_widget_config_position();
    }

    function cleanup_close_widget_item_config() {
        isConfigOpen = false;
        selected_loaded_widget_item?.svelte_component_ref?.setSelected(false);
        selected_loaded_widget_item = undefined;
        browserBroadcaster.unsub(Channel.AOH_WIDGET_RESIZED, widget_item_config_action_bbsymbol);
        if (!isNeedScroll) {
            containerDiv.scrollLeft = 0;
        }
    }

    const escapeHandler = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            exitFullscreen();
        }
    };

    const exitFullscreenHandler = (event: KeyboardEvent) => {
        if (!document.fullscreenElement) {
            exitFullscreen();
        }
    };

    onMount(async () => {
        styleCellHeight = window.innerHeight / 12.5;
        mounted = true;
        if (browser) {
            document.addEventListener('keydown', escapeHandler);
            document.addEventListener('fullscreenchange', exitFullscreenHandler);
        }

        if (containerDiv) {
            initialContainerWidth = containerDiv.offsetWidth;
        }
    });

    onDestroy(() => {
        if (browser) {
            document.removeEventListener('keydown', escapeHandler);
            document.removeEventListener('fullscreenchange', exitFullscreenHandler);

            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('DATASOURCE-')) {
                    localStorage.removeItem(key);
                }
            });
        }
        if (typeof window !== 'undefined' && updateWidth) {
            window.removeEventListener('resize', updateWidth);
        }
    });

    let gridstackStore: Writable<GridStack>;

    let fullyInitialized = false;
    let cellHeight;

    // Load initial config when gridstack gets initialized
    $: {
        if (!fullyInitialized) {
            if (widgetGrid) {
                gridstackStore = widgetGrid.getGridStackStore();
                setContext(GRID_STACK_CONTEXT, gridstackStore);
            }

            if (gridstackStore && $gridstackStore) {
                // Use all the loaded layouts
                cellHeight = $gridstackStore.getCellHeight();
                let needToLoad = false;

                if (dashboards && dashboards.length > 0) {
                    needToLoad = true;
                }

                if (needToLoad && mounted) {
                    let deepLinkedDashboardId = $page.url.searchParams.get('id');

                    let shouldUpdateSearchParams = false;

                    if (deepLinkedDashboardId) {
                        selectedLayout = dashboards.find(val => {
                            return val.id === deepLinkedDashboardId;
                        });
                    }

                    // No deep linking OR deep linking couldn't find a layout
                    if (!selectedLayout || !deepLinkedDashboardId) {
                        selectedLayout = dashboards.find(val => {
                            return val.id === $uiState.dashboard[UI_STATE_LAST_SELECTED_DASHBOARD];
                        });

                        shouldUpdateSearchParams = true;
                    }

                    if (selectedLayout && shouldUpdateSearchParams) {
                        // set dashboard names searchParams to history so that people can
                        // copy paste to share the deep link to the dashboard
                        const changeLayout = new URL($page.url);
                        changeLayout.searchParams.set('id', selectedLayout.id);
                        goto(changeLayout, { invalidateAll: false });
                    }

                    LoadConfig();

                    for (const [_, w] of $DashboardStore.widget_map) {
                        grid_layout.forEach(g => {
                            if (g.id === w.id) {
                                g.shared_config_id = w.shared_config_id;

                                if (g.shared_config_id) {
                                    g.config = $DashboardStore.shared_config_map.get(w.shared_config_id).config;
                                }
                            }
                        });
                    }

                    fullyInitialized = true;
                }
            }
        }
    }

    DashboardStore.subscribe(ds => {
        dashboards = [];
        tags = [];

        for (const [_, d] of ds.dashboard_map) {
            dashboards.push(d);
        }

        for (const [_, t] of ds.tag_map) {
            tags.push(t);
        }

        for (const [_, w] of ds.widget_map) {
            grid_layout.forEach(g => {
                if (g.id === w.id) {
                    g.shared_config_id = w.shared_config_id;

                    if (g.shared_config_id) {
                        g.config = ds.shared_config_map.get(w.shared_config_id).config;
                    }

                    g.width = w.width;
                    g.height = w.height;
                    g.row = w.row;
                    g.column = w.column;
                    g.occ_lock = w.occ_lock;
                }
            });
        }

        if (selected_loaded_widget_item) {
            let widget = ds.widget_map.get(selected_loaded_widget_item.id);
            selected_loaded_widget_item.config = widget.config;
            selected_loaded_widget_item.shared_config_id = widget.shared_config_id;
            selected_loaded_widget_item = selected_loaded_widget_item;
        }

        dashboards = dashboards;
        tags = tags;
        grid_layout = grid_layout;

        sysDatasource = [];
        for (const [_, t] of ds.sys_datasource) {
            sysDatasource.push(t);
        }
    });

    $: if (selected_loaded_widget_item) {
        selected_widget_item_shared_configs = Array.from($DashboardStore.shared_config_map.values()).filter(
            scm => scm.widget_type_id === selected_loaded_widget_item.widget_type_id
        );
    }

    const dashboardSelectorUpdateHandler = (e: DashboardSelectorUpdateEvent) => {
        if (fullyInitialized) {
            dashboards = e.detail.dashboards.map(d => {
                return {
                    id: d.id,
                    widgets: d.widgets,
                    favourite: d.favourite,
                    name: d.name,
                    description: d.description,
                    tags: d.tags,
                    occ_lock: d.occ_lock,
                };
            });

            selectedLayout = dashboards.find(val => {
                return val.id === e.detail.selected_dashboard.id;
            });

            $uiState.dashboard[UI_STATE_LAST_SELECTED_DASHBOARD] = selectedLayout?.id;

            // set dashboard names searchParams to history so that people can copy
            // paste to share the deep link to the dashboard
            const changeLayout = new URL($page.url);
            changeLayout.searchParams.set('id', selectedLayout.id);

            goto(changeLayout, { invalidateAll: false });
        }
    };

    const dashboardSelectorSelectionHandler = (e: DashboardSelectorSelectionEvent) => {
        if ($isDirty) {
            confirmation_modal[ModalEnum.ChangeDashboardUnsaved] = {
                title: 'Alert',
                content: 'Are you sure you wish to switch dashboards without saving?',
                confirmText: 'Proceed',
                showCancel: true,
                cancelText: 'Cancel',
                specialContent: false,
                accept: async () => {
                    $isDirty = false;
                    displayState = DisplayState.ViewingDashboard;
                    cleanup_close_widget_item_config();
                    dashboardSelectorUpdateHandler(e);
                    await LoadConfig();
                },
                reject: async () => {
                    selectedLayout = selectedLayout;
                },
            };

            openModal(ModalEnum.ChangeDashboardUnsaved);
        } else {
            displayState = DisplayState.ViewingDashboard;
            cleanup_close_widget_item_config();
            dashboardSelectorUpdateHandler(e);
            LoadConfig();
        }
    };

    const dashboardDetailsUpdatedHandler = (e: DashboardDetailsUpdatedEvent) => {
        let dashToUpdate = dashboards.find(d => (d.id = e.detail.id));
        dashToUpdate.name = e.detail.name;
        dashToUpdate.description = e.detail.description;
        dashToUpdate.occ_lock = e.detail.occ_lock;

        selectedLayout.name = e.detail.name;
        selectedLayout.description = e.detail.description;
        selectedLayout.occ_lock = e.detail.occ_lock;

        $uiState.dashboard[UI_STATE_LAST_SELECTED_DASHBOARD] = selectedLayout?.id;

        // set dashboard names searchParams to history so that people can copy
        // paste to share the deep link to the dashboard
        const setDashboardQueryParams = new URL($page.url);
        setDashboardQueryParams.searchParams.set('id', selectedLayout.id);

        goto(setDashboardQueryParams, { invalidateAll: false });

        dashboards = dashboards;
        selectedLayout = selectedLayout;
    };

    const dashboardDetailsCreateHandler = (e: DashboardDetailsCreatedEvent) => {
        isCreateDashboardDialogOpen = false;
        selectedLayout = {
            id: e.detail.id,
            name: e.detail.name,
            description: e.detail.description,
            tags: e.detail.tags,
            occ_lock: e.detail.occ_lock,
        };

        // set dashboard names searchParams to history so that people can copy
        // paste to share the deep link to the dashboard
        const changeLayout = new URL($page.url);
        changeLayout.searchParams.set('id', selectedLayout.id);
        goto(changeLayout, { invalidateAll: false });

        LoadConfig();

        // Remove dirty flag
        $isDirty = false;

        // Close individual widget configs
        cleanup_close_widget_item_config();

        DashboardStore.update(ds => {
            ds.dashboard_map.set(selectedLayout.id, selectedLayout);
            return ds;
        });
    };

    let disableInteraction = false;

    async function openModal(modalType: ModalEnum) {
        modal_type = modalType;
        is_confirm_modal_open = true;

        // Consider also focusing on the input text box
    }

    async function delete_dashboard_layout(dashboard_id: Uuid) {
        displayState = DisplayState.ViewingDashboard;

        const response = await fetch(`${get(locations).dash_web}/config`, {
            method: 'DELETE',
            body: JSON.stringify({ id: dashboard_id }),
        });

        let result = await (response.json() as Promise<DeleteConfigurationResponseBody>);

        if (response.status !== StatusCodes.OK) {
            log.error({ errors: result.errors, status: response.status });
            throw new Error('failed to delete dashboard configuration');
        }

        dashboards = dashboards.filter(e => e.id !== dashboard_id);
        if (selectedLayout?.id === dashboard_id) {
            if (dashboards.length > 0) {
                selectedLayout = dashboards[0];
            } else {
                selectedLayout = null;
            }
        }

        DashboardStore.update(ds => {
            ds.dashboard_map.delete(dashboard_id);
            ds.widget_map.forEach(w => {
                if (w.dashboard_id === dashboard_id) {
                    ds.widget_map.delete(w.id);
                }
            });
            return ds;
        });
    }

    function widgetValidationAction(node: Element, widget_type: WidgetType) {
        let validate = () => {
            let gridstack = get(gridstackStore);

            let willItFit = gridstack.willItFit({
                w: widget_type.min_width,
                h: widget_type.min_height,
                autoPosition: true,
            });

            let count = 0;

            if (grid_layout) {
                grid_layout.forEach(loaded_widget_item => {
                    if (loaded_widget_item.widget_type.name === widget_type.name) {
                        count++;
                    }
                });
            }

            let subject: HTMLButtonElement = node.querySelector('[data-subject=button]');

            // Disable ability to add widget if not valid (can't fit or exceeds limit)
            if (subject) {
                subject.disabled = !willItFit || count >= widget_type.limit;

                let errorMessage = '';
                if (count >= widget_type.limit) errorMessage = 'Limit reached';
                if (!willItFit) errorMessage = 'Insufficient workspace';

                (subject.querySelector('#error-msg') as HTMLElement).innerText = errorMessage;
            }
        };

        // Run validate whenever a widget change occurs
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            validate();
        });

        // Run validate once on initialization
        validate();
    }

    const reStructData = (layout, grid) => {
        const new_grid = grid.map(i => ({
            ...i,
            config: { ...i.config, parent_grid_id: '' },
        }));

        layout.forEach(item => {
            const subGridChildren = item.subGridOpts?.children;
            if (subGridChildren?.length > 0) {
                const subGrid = new_grid.find(g => g.id === item.id);
                if (subGrid) {
                    subGrid.config.column = Math.max(...subGridChildren.map(n => (n.x || 0) + (n.w || 0)));
                }

                subGridChildren.forEach(subItem => {
                    const widget_grid_item = new_grid.find(g => g.id === subItem.id);
                    if (widget_grid_item) {
                        widget_grid_item.config.parent_grid_id = item.id;
                    }
                });
            }
        });

        return new_grid;
    };

    const deleteUnusedWidget = () => {
        const list = deletedItems.filter(id => originWidgets.includes(id));
        list.forEach(id => deleteWidget(id));
        deletedItems = [];
    };

    const trimAllStrings = (obj: any): any => {
        if (Array.isArray(obj)) {
            return obj.map(trimAllStrings);
        } else if (obj && typeof obj === 'object') {
            const trimmed: any = {};
            for (const key in obj) {
                if (typeof obj[key] === 'string') {
                    trimmed[key] = obj[key].replace(/\s+/g, ' ').trim();
                } else if (typeof obj[key] === 'object') {
                    trimmed[key] = trimAllStrings(obj[key]);
                } else {
                    trimmed[key] = obj[key];
                }
            }
            return trimmed;
        }
        return obj;
    };

    async function syncIncidentDepartment() {
        try {
            const response = await fetch(`${IncidentMonitoringURL}/incident-department/iams-sync`, {
                method: 'PATCH',
            });
            console.log('syncIncidentDepartment: response status', response.status);
            if (response.status !== 200) {
                const data = await response.json();
                console.log('syncIncidentDepartment: error response', data);
            }
            return true;
        } catch (err) {
            console.log('syncIncidentDepartment: exception', err);
        }
    }

    // Save Current Grid Configuration
    async function saveConfiguration() {
        isNeedScroll = (draggingRootWidgetData?.x ?? 0) + (draggingRootWidgetData?.w ?? 0) > 24;
        if (!isNeedScroll) {
            containerDiv.scrollLeft = 0;
        }
        let dashboard_widgets: Array<Widget> = [];
        const layout = $gridstackStore.save(false, false);
        const new_grid_layout = reStructData(layout, grid_layout);

        if (grid_layout.map(item => item.config.dataSource).includes(DATA_SOURCE.LE04_RESPONSE_TRACKING)) {
            syncIncidentDepartment();
        }

        // construct the configForSaving based on what's in the grid.
        for (let i = 0; i < new_grid_layout.length; i++) {
            let widget_grid_item = new_grid_layout[i];
            widget_grid_item.config = trimAllStrings(widget_grid_item.config);
            let gsNode = (widget_grid_item.svelte_component_ref as WidgetItem)?.getGridstackNode();
            dashboard_widgets.push({
                id: widget_grid_item.id,
                row: gsNode?.y,
                column: gsNode?.x,
                width: gsNode?.w,
                height: gsNode?.h,
                config: widget_grid_item.config,
                widget_type_id: widget_grid_item.widget_type_id,
                dashboard_id: selectedLayout?.id as UUID,
                shared_config_id: widget_grid_item.shared_config_id,
                occ_lock: widget_grid_item.occ_lock,
            });
        }

        let response: Response;

        const body: UpdateDashboardLayoutRequestBody = {
            dashboard_id: selectedLayout?.id,
            widgets: dashboard_widgets,
        };

        // Update the dashboard's list of widgets
        deleteUnusedWidget();
        try {
            response = await fetch(`${get(locations).dash_web}/config`, {
                method: 'PATCH',
                body: JSON.stringify(body),
            });

            fetch(`${get(locations).sys_datasource_web}/widget-sds-mapping`, {
                method: 'POST',
                body: JSON.stringify({
                    Widgets: body.widgets
                        .filter(i => i.id && i.config?.dataSource)
                        .map(i => ({
                            Widget_Id: i.id,
                            Sys_Datasource_Id: i.config.dataSource,
                        })),
                }),
            });

            if (response.status === StatusCodes.OK) {
                modalMessage = 'Configuration is saved successfully.';
                isModalOpen = true;

                DashboardStore.update(ds => {
                    for (const rw of dashboard_widgets) {
                        ds.widget_map.set(rw.id, rw);
                    }

                    return ds;
                });
            } else {
                log.error(
                    { resutlt: await response.json() },
                    'unexpected response attempting to save dashboard layout'
                );
                modalMessage = 'Unable to save configuration successfully.';
                isModalOpen = true;
            }
        } catch (e) {
            log.error({ error: e }, 'error attempting to save dashboard layout');
        } finally {
            disableInteraction = false;
        }

        // Update the dashboard store with the new layout
        DashboardStore.update(ds => {
            ds.dashboard_map.set(selectedLayout.id, selectedLayout);
            return ds;
        });

        let tableTriggers = [];
        for (let widget of dashboard_widgets) {
            const dataSourceId = (widget?.config?.dataSource || '') as string;
            if (dataSourceId) {
                const tableTrigger = sysDatasource?.find(d => d.ID === dataSourceId)?.Agg_Tables;
                if (tableTrigger && !tableTriggers.includes(tableTrigger)) tableTriggers.push(tableTrigger);
            }
        }

        if (tableTriggers.length) {
            const widgetIds: string[] = grid_layout
                .filter(item => item.widget_type_id !== WIDGET_GROUP_TYPE_ID)
                .map(item => item.id);
            TriggerService.trigger(tableTriggers.join(','), true, true, widgetIds);
        }
        // Update the dirty flag
        $isDirty = false;
    }

    // Save Current Grid Configuration

    // Disallow multiple simultaneous loadConfigs
    let loading = false;

    // Load Current Grid Configuration
    async function LoadConfig() {
        if (!selectedLayout || !mounted || loading) {
            log.debug({ selectedLayout, loading }, 'Skipping LoadConfig');
            return;
        }

        // Flag to control whether multiple loadConfigs can occur
        loading = true;

        // Reset list of loaded widgets, later add the new ones to be loaded in
        grid_layout = [];
        widgetGrid.resetGrid(options);

        // Pull unloaded widgets from dashboard (this constitutes the dashboard layout)
        // We'll use this to load into grid layout later
        let widgets_to_load_into_layout = [];
        originWidgets = [];
        $DashboardStore.widget_map.forEach(widget => {
            if (widget.dashboard_id == selectedLayout.id) {
                widgets_to_load_into_layout.push(widget);
                originWidgets.push(widget.id);
            }
        });

        // Batch addWidgets - for performance
        if (gridstackStore && $gridstackStore) {
            $gridstackStore.batchUpdate(true);
        }

        grid_layout = LoadDashboardConfiguration(widgets_to_load_into_layout);

        grid_layout = grid_layout;

        await tick();

        // Triggers batch update to occur
        if (gridstackStore && $gridstackStore) {
            $gridstackStore.batchUpdate(false);
        }

        $isDirty = false;
        loading = false;
    }

    function widgetListAddWidgetHandler(new_widget: WidgetGridItem) {
        addWidget(new_widget.widget_type, new_widget.component, {
            widget_id: new_widget.id,
            config: new_widget.config,
            new_widget: true,
        });
    }

    async function addWidgetInSubGridHandler(new_widget: WidgetGridItem) {
        const widget = grid_layout.find(n => n.id === selected_loaded_widget_item.id);
        widget.config = {
            ...selected_loaded_widget_item.config,
        };

        DashboardStore.update(ds => {
            ds.widget_map.set(selected_loaded_widget_item.id, {
                ...ds.widget_map.get(selected_loaded_widget_item.id),
                config: selected_loaded_widget_item.config,
            });
            return ds;
        });
        await tick();
        addWidget(
            new_widget.widget_type,
            new_widget.component,
            {
                widget_id: new_widget.id,
                config: new_widget.config,
                new_widget: true,
            },
            selected_loaded_widget_item.id
        );
    }

    /**
     * This function adds a widget type to the WidgetGrid (Grid Stack) via updating the grid_layout and updates
     * the dashboard store to propagate the changes. There's a lot of messy updating across multiple files. This
     * should all be refactored with Svelte5 migration.
     *
     * KIV: At the moment, the 'new_widget' option causes us to save the entire dashboard because a new widget might
     * shift other widgets' positions because it pushes them around in the widget grid. Proper diffing would solve this.
     *
     * @param widget_type
     * @param widget_component
     * @param options
     */
    async function addWidget(
        widget_type: WidgetType,
        widget_component: WidgetComponentRecord,
        options?: {
            widget_id?: string;
            config?: Record<string, unknown>;
            new_widget?: boolean;
        },
        parent_grid_id?: string
    ) {
        disableInteraction = true;

        let willItFit = get(widgetGrid.getGridStackStore()).willItFit({
            w: widget_type.min_width,
            h: widget_type.min_height,
            autoPosition: true,
        });

        if (!willItFit) {
            const message = 'not enough free space to place the widget';
            log.warn({ dashboard_id: options.widget_id, widget_type }, message);
            throw new Error(message);
        }

        if (!options.config) {
            options.config = structuredClone(widget_component?.config?.configDefaults);
        }

        if (!options.widget_id) {
            options.widget_id = v4();
        }

        const widget_to_add = {
            id: options.widget_id as UUID,
            width: widget_type.min_width,
            height: widget_type.min_height,
            row: 0,
            column: 0,
            config: { ...(options.config ?? widget_component?.config?.configDefaults), parent_grid_id },
            widget_type: widget_type,
            widget_type_id: widget_type.id as UUID,
            dashboard_id: selectedLayout.id as UUID,
            shared_config_id: null,
            occ_lock: 0,
            component: widget_component,
        };

        draggingWidgetData = {
            id: widget_to_add.id,
            w: widget_to_add.width,
            h: widget_to_add.height,
            x: 0,
            y: 0,
        };

        draggingRootWidgetData = {
            id: widget_to_add.id,
            w: widget_to_add.width,
            h: widget_to_add.height,
            x: 0,
            y: 0,
        };

        grid_layout.push({
            ...widget_to_add,
        });

        DashboardStore.update(ds => {
            ds.widget_map.set(widget_to_add.id, widget_to_add);
            return ds;
        });

        await tick(); // Update grid layout with dashboar store

        grid_layout = grid_layout;

        await tick(); // Update DOM with grid layout

        // if (options.new_widget) {
        //     await saveConfiguration(); // save requires gridstack to be populated (DOM must be updated)
        // }

        disableInteraction = false;
        $isDirty = true;
    }

    let deletedItems = [];

    const handleDeleteWidgetById = widgetId => {
        // Unselect if deleted widget is the same as the currently selected one
        if (selected_loaded_widget_item) {
            let previousSelectedElement: undefined | WidgetGridItem;

            previousSelectedElement = grid_layout.find(element => {
                return widgetId === element.id && element.id === selected_loaded_widget_item.id;
            });

            if (previousSelectedElement) {
                let widgetRef = previousSelectedElement.svelte_component_ref as WidgetItem;
                widgetRef.setSelected(false);
            }

            // Unset selected widget item config and close the panel
            cleanup_close_widget_item_config();
        }

        grid_layout = grid_layout.filter(widgetItemConfig => {
            return widgetId !== widgetItemConfig.id;
        });

        DashboardStore.update(ds => {
            ds.widget_map.delete(widgetId);

            return ds;
        });

        deletedItems.push(widgetId);
        $isDirty = true;
    };

    const deleteWidget = widgetId => {
        fetch(`${$locations.dash_web}/widget/${selectedLayout.id}`, {
            method: 'DELETE',
            body: widgetId,
        })
            .then(resp => {
                resp.json()
                    .then(result => {
                        log.debug({ result, selectedLayout }, 'result from deleting the widget');
                    })
                    .catch(err => {
                        log.error({ err, selectedLayout }, 'error from deleting the widget');
                    });
            })
            .catch(err => {
                log.error({ err, selectedLayout }, 'error from deleting the widget');
            });

        fetch(`${get(locations).sys_datasource_web}/widget-sds-mapping/${widgetId}`, {
            method: 'DELETE',
        });
    };

    // Delete a widget from the grid
    function handleDeleteWidget(event: CustomEvent<WidgetDetail>) {
        handleDeleteWidgetById(event.detail.widgetId);
    }
    let openWarningPopup = false;
    let deletedSubGridId;
    const handleDeleteSubWidget = (event: CustomEvent<WidgetDetail>) => {
        cleanup_close_widget_item_config();
        const children = getSubGridChildren(event.detail.widgetId);
        if (children.length > 0) {
            deletedSubGridId = event.detail.widgetId;
            openWarningPopup = true;
        } else {
            deleteSubGridById(event.detail.widgetId);
        }
    };
    const deleteSubGridById = widgetId => {
        getSubGridChildren(widgetId);
        const deletedSubWidgets = getSubGridChildren(widgetId);
        if (deletedSubWidgets.length > 0) {
            deletedSubWidgets.forEach(item => {
                handleDeleteWidgetById(item.id);
            });
        }
        handleDeleteWidgetById(widgetId);
    };

    const getSubGridChildren = widgetId => {
        const layout = $gridstackStore.save(false, false) as GridStackWidget[];
        const children = layout.find(item => item.id === widgetId)?.subGridOpts?.children;
        return children;
    };

    function deselect() {
        let previousSelectedElement: undefined | WidgetGridItem;

        // Unselect any already selected widget
        if (selected_loaded_widget_item) {
            previousSelectedElement = grid_layout.find(element => {
                return element.id === selected_loaded_widget_item.id;
            });

            if (previousSelectedElement) {
                let widgetRef = previousSelectedElement.svelte_component_ref as WidgetItem;
                widgetRef.setSelected(false);
            }

            // Unset selected widget item config and close the panel
            cleanup_close_widget_item_config();
        }

        return previousSelectedElement;
    }

    // Go into edit mode for a specific widget
    async function handleEdit(event: CustomEvent<WidgetDetail>, openConfig = true) {
        let previousSelectedElement: undefined | WidgetGridItem = deselect();

        await tick();

        let newSelectedElement = grid_layout.find(element => {
            return element.id === event.detail.widgetId;
        });

        // If the new selection event is on a different widget (not a de-selection), then set it as 'selected'
        if (!previousSelectedElement || newSelectedElement.id !== previousSelectedElement.id || !openConfig) {
            let newWidgetRef = newSelectedElement.svelte_component_ref as WidgetItem;
            newWidgetRef.setSelected(true);

            selected_loaded_widget_item = newSelectedElement;
            isConfigOpen = openConfig;
        } else {
            // Deselection, close panel
            cleanup_close_widget_item_config();
        }
    }

    function openAddList(event: CustomEvent<WidgetDetail>) {
        handleEdit(event, false);
        isWidgetListCompactOpen = true;
    }

    function handleSelect(event: CustomEvent<WidgetDetail>) {
        // Not in use, using handleEdit instead...
    }

    function handleTriggerTables(event: any) {
        const cfgParamId = event?.detail?.cfgParamId; // Id for Simple Chart
        const cfgParamName = event?.detail?.cfgParamName; // Name for Advanced Table,

        const tableTriggers = [];
        for (let widget of grid_layout) {
            const widgetCfgParam = widget?.config?.cfg_param; // Id for SimChart, Name for AdvTab
            if (widgetCfgParam && [cfgParamId, cfgParamName].includes(widgetCfgParam)) {
                const dataSourceId = widget?.config?.dataSource;
                const tableTrigger = sysDatasource?.find(d => d.ID === dataSourceId)?.Agg_Tables;
                if (tableTrigger && !tableTriggers.includes(tableTrigger)) tableTriggers.push(tableTrigger);
            }
        }

        if (tableTriggers.length) {
            const widgetIds: string[] = grid_layout
                .filter(item => item.widget_type_id !== WIDGET_GROUP_TYPE_ID)
                .map(item => item.id);
            TriggerService.trigger(tableTriggers.join(','), true, false, widgetIds);
        }
    }

    function handleToggleFullscreen() {
        if (browser && document) {
            if (document.fullscreenElement == document.documentElement) {
                exitFullscreen();
            } else {
                document.documentElement.requestFullscreen();
                setSidebarDisplay('none');
                setStatusBarDisplay('none');
            }
        }
    }

    function exitFullscreen() {
        if (browser && document) {
            if (document.fullscreenElement == document.documentElement) {
                document.exitFullscreen();
            }
            setSidebarDisplay('flex');
            setStatusBarDisplay('flex');
        }
    }

    enum ModalEnum {
        Delete,
        Reset,
        Save,
        LeavePageUnsaved,
        ChangeDashboardUnsaved,
        DeleteSuccess,
        NewPage,
        DuplicatePage,
    }

    type ModalInfo = {
        title: string;
        content: string;
        confirmText: string;
        showCancel: boolean;
        cancelText: string;
        accept: () => Promise<unknown>;
        specialContent?: boolean;
        specialContentName?: string;
        reject?: () => Promise<unknown>;
    };

    let confirmation_modal: Partial<Record<ModalEnum, ModalInfo>> = new Object();

    // Initialize Modals
    confirmation_modal[ModalEnum.Delete] = {
        title: 'Delete Page',
        content: 'Are you sure you wish to delete this layout?',
        confirmText: 'Confirm',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            // Upon confirmation - the user wants to delete the page
            // They accept that they don't care about lost changes, so reset the flag
            $isDirty = false;

            // Open waiting overlay
            disableInteraction = true;
            await tick();
            await delete_dashboard_layout(selectedLayout?.id);
            await tick();
            LoadConfig();

            // Close waiting overlay
            disableInteraction = false;
        },
    };

    confirmation_modal[ModalEnum.Reset] = {
        title: 'Reset Page',
        content: 'Are you sure you wish to undo all changes made?',
        confirmText: 'Confirm',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            await LoadConfig();
        },
    };

    confirmation_modal[ModalEnum.LeavePageUnsaved] = {
        title: 'Alert',
        content: 'Are you sure you wish to leave without saving?',
        confirmText: 'Proceed',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            await LoadConfig();
            $isDirty = false;

            if (routingUrl == undefined) {
                log.warn('route is undefined, preventing navigation');
                return;
            }

            goto(routingUrl);
        },
    };

    confirmation_modal[ModalEnum.DeleteSuccess] = {
        title: 'Layout Deleted Successfully',
        content: 'Your layout has been successfully deleted.',
        confirmText: 'Ok',
        showCancel: false,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            // Do nothing
        },
    };

    confirmation_modal[ModalEnum.DuplicatePage] = {
        title: 'Duplicate Page',
        content: '',
        confirmText: 'Confirm',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: true,
        specialContentName: 'Duplicate Page',
        accept: async () => {
            if (selected_loaded_widget_item && selected_loaded_widget_item.svelte_component_ref) {
                selected_loaded_widget_item.svelte_component_ref.setSelected(false);
                selected_loaded_widget_item = undefined;
            }

            // Open waiting overlay
            disableInteraction = true;
            await tick();

            await saveConfiguration();

            // Close waiting overlay
            disableInteraction = false;

            // Remove dirty flag
            $isDirty = false;

            // Close individual widget configs
            cleanup_close_widget_item_config();
        },
    };

    // Just default to any one of them
    let modal_type = ModalEnum.Delete;
    let is_confirm_modal_open = false;

    let routingUrl;

    /* --------------------------- handle widget list --------------------------- */
    let isWidgetListOpen = false;
    let isWidgetListCompactOpen = false;
    let draggingWidgetData;
    let draggingRootWidgetData;
    let isNeedScroll = false;
    let reloadComponent = false;
    let containerDiv: HTMLDivElement;

    $: if (!isNeedScroll) {
        isNeedScroll = (draggingRootWidgetData?.x ?? 0) + (draggingRootWidgetData?.w ?? 0) > 24;
    }

    $: {
        showScrollForWidgetView =
            (draggingRootWidgetData?.x ?? 0) + (draggingRootWidgetData?.w ?? 0) > 24 ||
            isNeedScroll ||
            grid_layout.some(item => (item.width ?? 0) + (item.column ?? 0) > 24) ||
            isConfigOpen;
    }

    beforeNavigate(async ({ to, from, cancel }) => {
        if ($isDirty) {
            cancel();
            openModal(ModalEnum.LeavePageUnsaved);
            routingUrl = to.url;
        }
    });

    const updateWidgetConfig = async (id, config) => {
        const widget = grid_layout.find(n => n.id === id);
        if (!widget) return;

        if (config.useParentFilter !== widget.config.useParentFilter) {
            widget.config = {
                ...widget.config,
                ...config,
            };
            await tick();
            grid_layout = grid_layout;
        }
    };

    const getWidgetConfig = id => {
        return grid_layout.find(n => n.id === id)?.config;
    };

    const updateWidgetFiltering = async (parentId: string, widgetId: string) => {
        if (!parentId && widgetId) {
            return;
        }

        const gridConfig = getWidgetConfig(parentId);

        const hasParentFilter = gridConfig?.cfg_param;
        const widget = grid_layout.find(n => n.id === widgetId);
        if (!widget) return;
        if (hasParentFilter) {
            widget.config = {
                ...widget.config,
                cfg_param:
                    widget.widget_type_id === ADVANCED_TABLE_TYPE_ID
                        ? gridConfig?.cfg_param_name
                        : gridConfig?.cfg_param,
                useParentFilter: true,
            };

            await tick();
            grid_layout = grid_layout;
        }
    };

    const updateChildrenConfig = async (parentId: string, config: Record<string, string>) => {
        const layout = $gridstackStore.save(false, false);
        const children = Array.isArray(layout)
            ? layout.find(item => item.id === parentId)?.subGridOpts?.children
            : undefined;

        if (children?.length > 0) {
            children.forEach(item => {
                const widget = grid_layout.find(n => n.id === item.id);
                if (!widget) return;
                widget.config = {
                    ...widget.config,
                    cfg_param:
                        widget.widget_type_id === ADVANCED_TABLE_TYPE_ID ? config?.cfg_param_name : config?.cfg_param,
                    useParentFilter: true,
                };
            });
        }
        await tick();
        grid_layout = grid_layout;
    };

    const PREFIX_SCOPE = 'Dashboard-UI-';
    let exists;
    function checkTagAllowed(tagText, abilities) {
        const tagLower = tagText?.toLowerCase() || '';
        for (const ability of abilities) {
            if (!ability.scopeName) continue;
            const scopeName = ability.scopeName || '';
            if (scopeName.toLowerCase().startsWith(PREFIX_SCOPE.toLowerCase())) {
                const suffix = scopeName.substring(PREFIX_SCOPE.length).toLowerCase();
                if (suffix === tagLower) {
                    return ability.allow === true;
                }
            }
        }
        return false;
    }
    const iamsSpecificScopes = abilities
        .filter(
            ability => ability?.scopeName && ability?.scopeName.toLowerCase().startsWith(PREFIX_SCOPE.toLowerCase())
        )
        .map(ability => ability.scopeName.substring(PREFIX_SCOPE.length).toLowerCase());
    $: dashboards = dashboards
        .map(dashboard => {
            if (!dashboard.tags || dashboard.tags.length === 0) {
                return dashboard;
            }
            const hasTagMatchingScope = dashboard.tags.some(tag => {
                const tagLower = tag.text?.toLowerCase() || '';
                return iamsSpecificScopes.includes(tagLower);
            });
            if (!hasTagMatchingScope) {
                return { ...dashboard };
            }
            const hasAllowedTag = dashboard.tags.some(tag => {
                return checkTagAllowed(tag.text, abilities);
            });
            if (hasAllowedTag) {
                return { ...dashboard };
            }
            return null;
        })
        .filter(Boolean);
    $: setTimeout(() => {
        let deepLinkedDashboardId = $page.url.searchParams.get('id');
        if (!deepLinkedDashboardId) {
            deepLinkedDashboardId = $uiState.dashboard[UI_STATE_LAST_SELECTED_DASHBOARD];
        }
        selectedLayout = dashboards.find(val => {
            return val.id === deepLinkedDashboardId;
        });
        exists = dashboards.some(item => item.id === deepLinkedDashboardId);
    }, 0);
</script>

{#if disableInteraction}
    <div
        transition:fade={{ duration: 150 }}
        class="fixed z-[60] flex w-full flex-col items-center justify-center overflow-y-auto overflow-x-hidden rounded
        bg-background text-on-primary opacity-50 transition md:inset-0 md:h-full">
        <Spinner appendClass="text-9xl" />
    </div>
{/if}

<Guard {abilities} scopeId={SCOPE.DASHBOARD_UI} specificPermission={exists}>
    <!-- We hide everything (but still allow the Svelte to process the DOM) until loading is finished -->
    <div
        bind:this={containerDiv}
        class="h-full overflow-x-hidden bg-background p-2 {$windowWidth < 1200 ? 'min-w-fit' : ''}"
        class:!overflow-x-scroll={showScrollForWidgetView && $windowWidth > 1200}
        style="min-width: {showScrollForWidgetView
            ? ''
            : (initialContainerWidth < 1500 ? 1500 : initialContainerWidth) +
              'px'}; --style-cell-height: {styleCellHeight};">
        <div class:grid-display={isEditable} class="h-full w-fit" class:visible={!loading}>
            <!-- Main Content -->
            <!-- Dashboard Selector and other page controls at the top of the page -->
            <Status>
                <!-- Injects the content to where the Status Bar is in the page -->
                <header class="flex h-full items-center px-2 text-lg">
                    <div class="flex h-full items-center justify-start gap-6">
                        {#if exists}
                            <DashboardSelector
                                on:update={dashboardSelectorUpdateHandler}
                                on:selection={dashboardSelectorSelectionHandler}
                                dashboard_list_items={dashboards}
                                selected_dashboard={selectedLayout} />
                            <div>
                                {#if hasAccess(abilities, SCOPE.CREATE)}
                                    <div>
                                        <Button
                                            on:click={async () => {
                                                isCreateDashboardDialogOpen = !isCreateDashboardDialogOpen;
                                            }}
                                            variant={ButtonVariant.EMPTY}
                                            iconDefinition={faFileCirclePlus}
                                            setColorClasses="text-mono-1" />
                                        <Tooltip><div class="w-28">Create a new dashboard.</div></Tooltip>
                                    </div>
                                {/if}
                                <Dialog title="Create New Dashboard" bind:isOpen={isCreateDashboardDialogOpen}>
                                    <DashboardDetails
                                        mode="insert"
                                        details={{
                                            id: '',
                                            name: undefined,
                                            description: undefined,
                                            tags: [],
                                        }}
                                        chips={tags}
                                        on:create={dashboardDetailsCreateHandler} />
                                </Dialog>
                            </div>
                        {/if}
                        {#if $isDirty}
                            <Button
                                data-subject="button"
                                on:click={async () => {
                                    if (hasError) return;
                                    if (
                                        selected_loaded_widget_item &&
                                        selected_loaded_widget_item.svelte_component_ref
                                    ) {
                                        selected_loaded_widget_item.svelte_component_ref.setSelected(false);
                                        selected_loaded_widget_item = undefined;
                                    }
                                    // Open waiting overlay
                                    disableInteraction = true;
                                    await tick();
                                    await saveConfiguration();
                                    // Close waiting overlay
                                    disableInteraction = false;

                                    // Remove dirty flag
                                    $isDirty = false;

                                    // Close individual widget configs
                                    cleanup_close_widget_item_config();
                                }}
                                setColorClasses="bg-primary text-on-background"
                                iconDefinition={faFloppyDisk}
                                setDisabled={hasError}>
                                {#if $windowWidth > 1200}
                                    SAVE LAYOUT CHANGES
                                {/if}
                            </Button>
                        {/if}
                        <!-- Controls Shown Only in View Mode - Toggle Edit and Fullscreen -->
                        {#if displayState === DisplayState.ViewingDashboard}
                            <!-- Don't show edit and fullscreen button when there's no selected layout -->
                            {#if selectedLayout}
                                {#if hasAccess(abilities, SCOPE.UPDATE)}
                                    <div>
                                        <Button
                                            on:click={() => {
                                                displayState = DisplayState.EditingDashboard;
                                            }}
                                            variant={ButtonVariant.EMPTY}
                                            iconDefinition={faEdit} />
                                        <Tooltip><div class="w-36">Edit the current layout.</div></Tooltip>
                                    </div>
                                {/if}
                                <div>
                                    <Button
                                        on:click={handleToggleFullscreen}
                                        variant={ButtonVariant.EMPTY}
                                        iconDefinition={faUpRightAndDownLeftFromCenter} />
                                    <Tooltip><div class="w-36">Enter fullscreen mode.</div></Tooltip>
                                </div>
                            {/if}
                        {:else}
                            <!-- Controls Show only in Edit Mode - Toggle edit mode, add widget, change details, 
                            create new dashboard, and delete dashboard-->
                            <div class="flex h-full items-center justify-start gap-2">
                                <Button
                                    data-subject="button"
                                    on:click={async () => {
                                        displayState = DisplayState.ViewingDashboard;

                                        // Close individual widget configs
                                        cleanup_close_widget_item_config();
                                    }}
                                    iconDefinition={faEye}
                                    variant={ButtonVariant.OUTLINE}>
                                    {#if $windowWidth > 1200}
                                        VIEW MODE
                                    {/if}
                                </Button>
                                <Button
                                    on:click={() => {
                                        deselect();
                                        isWidgetListOpen = true;
                                    }}
                                    iconDefinition={faPlus}>
                                    {#if $windowWidth > 1200}
                                        ADD WIDGET
                                    {/if}
                                </Button>

                                <WidgetList
                                    onAdd={widgetListAddWidgetHandler}
                                    {widgetValidationAction}
                                    widgets={Array.from($DashboardStore.widget_type_map.values())}
                                    bind:isOpen={isWidgetListOpen} />

                                <div>
                                    <div>
                                        <Button
                                            data-subject="button"
                                            on:click={async () => {
                                                isEditDashboardDetailsDialogOpen = !isEditDashboardDetailsDialogOpen;
                                            }}
                                            iconDefinition={faGear} />
                                        <Tooltip><div class="w-28">Edit dashboard details.</div></Tooltip>
                                    </div>
                                    <Dialog
                                        title="Edit Dashboard Details"
                                        bind:isOpen={isEditDashboardDetailsDialogOpen}>
                                        <DashboardDetails
                                            mode="update"
                                            details={{
                                                id: selectedLayout?.id,
                                                name: selectedLayout?.name,
                                                description: selectedLayout?.description,
                                                tags: selectedLayout?.tags,
                                                occ_lock: selectedLayout?.occ_lock,
                                            }}
                                            chips={tags}
                                            on:update={dashboardDetailsUpdatedHandler} />
                                    </Dialog>
                                </div>
                                {#if selectedLayout && selectedLayout.name}
                                    {#if hasAccess(abilities, SCOPE.DELETE)}
                                        <div>
                                            <Button
                                                on:click={async () => {
                                                    confirmation_modal[
                                                        ModalEnum.Delete
                                                    ].content = `Are you sure you wish to delete "${selectedLayout.name}"?`;
                                                    await openModal(ModalEnum.Delete);
                                                }}
                                                setColorClasses="text-mono-1"
                                                iconDefinition={faTrash} />
                                            <Tooltip><div class="w-28">Delete this layout.</div></Tooltip>
                                        </div>
                                    {/if}
                                {/if}
                            </div>
                        {/if}
                    </div>
                </header>
            </Status>
            <!-- Dashboard - Widget Grid Display -->
            <div class="border-box relative flex-grow bg-transparent" style="--cell-height: {cellHeight};">
                <WidgetGrid
                    {cleanup_close_widget_item_config}
                    {updateWidgetConfig}
                    bind:this={widgetGrid}
                    {options}
                    showGrid={isEditable}
                    editable={isEditable}
                    bind:draggingRootWidgetData
                    bind:draggingWidgetData>
                    {#each sortNestedGrid(grid_layout) as item (item.id)}
                        {@const isWidgetGroup = item.widget_type_id === WIDGET_GROUP_TYPE_ID}
                        <!-- Per-widget Configuration Panel -->
                        <WidgetItem
                            {cleanup_close_widget_item_config}
                            {updateWidgetFiltering}
                            dataSourceId={item.config?.dataSource ?? ''}
                            bind:draggingWidgetData
                            bind:draggingRootWidgetData
                            isSubGrid={isWidgetGroup}
                            column={item.config.column}
                            bind:this={item.svelte_component_ref}
                            id={item.id}
                            title={item.widget_type.name}
                            options={{
                                w: item.width,
                                h: item.height,
                                x: item.column,
                                y: item.row,
                                minW: item.widget_type.min_width,
                                minH: item.widget_type.min_height,
                                maxW: item.widget_type.max_width,
                                maxH: item.widget_type.max_height,
                                noMove: !isEditable,
                                noResize: !isEditable,
                                id: item.id,
                                ...(isWidgetGroup
                                    ? {
                                          subGridOpts: subOptions,
                                      }
                                    : {}),
                            }}
                            editable={isEditable}
                            on:delete={handleDeleteWidget}
                            on:edit={handleEdit}
                            on:select={handleSelect}
                            on:triggerTables={handleTriggerTables}
                            on:openAddList={openAddList}
                            on:deleteSubGrid={handleDeleteSubWidget}
                            on:iamsSync={syncIncidentDepartment}
                            {fullyInitialized}
                            parent_grid_id={item.config?.parent_grid_id}
                            let:sysDatasource>
                            {#if selected_loaded_widget_item && selected_loaded_widget_item.id === item.id}
                                <!-- What's rendered when widget is selected -->
                                <svelte:component
                                    this={item.component.main.default}
                                    config={selected_loaded_widget_item.config}
                                    {...isWidgetGroup ? { editable: isEditable } : {}}
                                    {...sysDatasource} />
                            {:else if item.config}
                                <!-- What's rendered when there's a config -->
                                <svelte:component
                                    this={item.component.main.default}
                                    config={item.config}
                                    {...isWidgetGroup ? { editable: isEditable } : {}}
                                    {...sysDatasource} />
                            {:else}
                                <!-- What's rendered when there's no config-->
                                <svelte:component
                                    this={item.component.main.default}
                                    config={item.component.config.configDefaults}
                                    {...isWidgetGroup ? { editable: isEditable } : {}}
                                    {...sysDatasource} />
                            {/if}
                        </WidgetItem>
                    {/each}
                </WidgetGrid>
                <WidgetListCompact
                    onAdd={addWidgetInSubGridHandler}
                    {widgetValidationAction}
                    widgets={Array.from($DashboardStore.widget_type_map.values())}
                    bind:isOpen={isWidgetListCompactOpen} />
                {#if isConfigOpen && selected_loaded_widget_item}
                    <WidgetItemConfig
                        bind:isOpen={isConfigOpen}
                        init={widget_item_config_action}
                        animationEndCallback={resizeGrid}
                        {selected_loaded_widget_item}
                        shared_configs={selected_widget_item_shared_configs}
                        onClose={cleanup_close_widget_item_config}>
                        <svelte:component
                            this={selected_loaded_widget_item.component.config.default}
                            {...selected_loaded_widget_item.widget_type_id === WIDGET_GROUP_TYPE_ID
                                ? {
                                      updateChildrenConfig,
                                  }
                                : {}}
                            id={selected_loaded_widget_item.id}
                            bind:config={selected_loaded_widget_item.config}
                            datasources={sysDatasource
                                .filter(i => i.Type === selected_loaded_widget_item?.widget_type_id)
                                .filter(i => i.Is_Active && !i.Is_Deleted)
                                .map(i => ({
                                    label: i.Name,
                                    value: i.ID,
                                }))}
                            bind:hasError />
                    </WidgetItemConfig>
                {/if}
            </div>
        </div>
    </div>
</Guard>

<Modal title={confirmation_modal[modal_type].title} isOpen={is_confirm_modal_open}>
    <div class="flex h-full flex-col items-center justify-center">
        <div class="flex grow items-center justify-center p-16 text-center text-sm">
            {confirmation_modal[modal_type].content}
        </div>
        <div class="flex h-fit w-full justify-end gap-1 border-t border-t-outline p-2">
            <Button
                setClass="rounded border-outline border p-1 px-2 text-sm
        hover:brightness-150 transition-color duration-200 ease-in-out {confirmation_modal[modal_type].showCancel
                    ? ''
                    : 'hidden'}"
                on:click={async () => {
                    // Close modal
                    is_confirm_modal_open = false;
                    await confirmation_modal[modal_type].reject();
                    await tick();
                }}>
                <span>{confirmation_modal[modal_type].cancelText}</span>
            </Button>
            <Button
                on:click={async () => {
                    // Close modal whilst executing callback
                    is_confirm_modal_open = false;
                    await confirmation_modal[modal_type].accept();
                    await tick();
                }}
                setClass="rounded border-outline border p-1 px-3 text-sm bg-error
        text-on-error hover:brightness-150 transition-color duration-200 ease-in-out">
                <span>{confirmation_modal[modal_type].confirmText}</span>
            </Button>
        </div>
    </div>
</Modal>

<Notification
    bind:isOpen={openWarningPopup}
    modalTitle="Confirmation"
    modalMessage="Widget Group contains other widgets. Are you sure you want to delete it?"
    confirmAction={async () => {
        deleteSubGridById(deletedSubGridId);
        openWarningPopup = false;
    }}
    cancelAction={() => {
        openWarningPopup = false;
    }} />

<Notification
    cancelAction={() => {
        reloadWidgetViewer = !reloadWidgetViewer;
    }}
    bind:isOpen={isModalOpen}
    modalTitle="Notification"
    onlyOkBtn
    {modalMessage} />
