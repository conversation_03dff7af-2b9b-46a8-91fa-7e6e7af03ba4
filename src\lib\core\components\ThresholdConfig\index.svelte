<script lang="ts" context="module">
    export interface ThresholdItemValid {
        isValid?: boolean;
        errorMessage: string;
    }

    export interface ThresholdItem {
        titleThresholdItem?: string;
        comparatorThreshold?: string;
        valueOfThreshold?: number;
        textColor?: string;
        bgColor?: string;
        isValidItem?: ThresholdItemValid;
    }

    export interface ThresholdConfig {
        name?: string;
        isEnableThreshold?: boolean;
        thresholdConfigItems: ThresholdItem[];
    }
</script>

<script lang="ts">
    import { Label, Input, Select, Toggle, Button, Helper } from 'flowbite-svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
    import { TYPEOF_COMPARATOR } from '$lib/core/modules/dashboard/widgets/utils';

    export let buttonfaPlus = faPlus;
    export let buttonfaMinus = faMinus;
    export let isDisplayTextColor = true;
    export let isDisplayBgColor = true;
    export let isDisplayEnableButton = true;

    export const configThresholdItem: ThresholdItem = {
        titleThresholdItem: 'Critical',
        comparatorThreshold: TYPEOF_COMPARATOR[0].value,
        valueOfThreshold: 10,
        textColor: '#FFFFFF',
        bgColor: '#000000',
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
    };

    const THRESHOLD_CONFIG_ITEMS: Array<ThresholdItem> = [configThresholdItem];

    export let configThresholdConfigs: ThresholdConfig = {
        isEnableThreshold: false,
        thresholdConfigItems: THRESHOLD_CONFIG_ITEMS,
    };

    let TYPEOF_COMPARATOR_THRESHOLD_ITEMS = [];
    const TYPEOF_COMPARATOR_FOR_ASC_VAL = TYPEOF_COMPARATOR.filter(element => element.name.includes('<'));
    const TYPEOF_COMPARATOR_FOR_DESC_VAL = TYPEOF_COMPARATOR.filter(element => element.name.includes('>'));

    //logic for threshold value
    const COMPARISON_THRESHOLD_OPERATORS_HASH = {
        '<': (a, b) => a <= b,
        '>': (a, b) => a >= b,
        '>=': (a, b) => a > b,
        '<=': (a, b) => a < b,
    };

    $: TYPEOF_COMPARATOR_THRESHOLD_ITEMS = Array(1).fill(TYPEOF_COMPARATOR_FOR_ASC_VAL);

    function getThresholdData() {
        return configThresholdConfigs.thresholdConfigItems;
    }

    function updateThresholdConfigData(data) {
        for (let i = 0; i < data.length; i++) {
            data[i].valueOfThreshold = Number(data[i].valueOfThreshold);
        }
        configThresholdConfigs.thresholdConfigItems = data;
    }

    function addThresholdConfigItem(thresholdConfig) {
        let array = thresholdConfig.thresholdConfigItems;
        if (!array.some(x => x.isValidItem.isValid === false)) {
            const lastItem = array[array.length - 1];
            const arrClone = {
                titleThresholdItem: lastItem.titleThresholdItem + 'Clone',
                comparatorThreshold: lastItem.comparatorThreshold,
                valueOfThreshold: Number.parseInt(lastItem.valueOfThreshold),
                textColor: lastItem.textColor,
                bgColor: lastItem.bgColor,
                isValidItem: {
                    isValid: true,
                    errorMessage: '',
                },
            };
            array = [...array, arrClone];
            updateThresholdConfigData(array);
        }
    }

    function removeThresholdConfigItem(index) {
        let array = getThresholdData();
        if (array.length > 1) {
            array = array.slice(0, index).concat(array.slice(index + 1));
            updateThresholdConfigData(array);
        }
    }

    function checkValueOfThreshold(index: number) {
        const array = getThresholdData();
        if (index > 0 || array.length > 1) {
            let itemCurrent = array[index];
            let itempPrevious = array[index - 1];
            if (!itemCurrent || !itempPrevious) {
                return;
            }
            let itemCurrentThresValue = Number.parseInt(itemCurrent.valueOfThreshold.toString());
            let itempPreviousThresValue = Number.parseInt(itempPrevious.valueOfThreshold.toString());
            itemCurrent.isValidItem.isValid = true;
            itemCurrent.isValidItem.errorMessage = '';

            if (
                !COMPARISON_THRESHOLD_OPERATORS_HASH[itempPrevious.comparatorThreshold](
                    itempPreviousThresValue,
                    itemCurrentThresValue
                )
            ) {
                itemCurrent.isValidItem.isValid = false;
                itemCurrent.isValidItem.errorMessage = `Current threshold value must be ${
                    itempPrevious.comparatorThreshold === '<=' || itempPrevious.comparatorThreshold === '<'
                        ? 'greater'
                        : 'less'
                }  than previous threshold`;
            }

            if (
                itemCurrentThresValue === itempPreviousThresValue &&
                itemCurrent.comparatorThreshold === itempPrevious.comparatorThreshold
            ) {
                itemCurrent.isValidItem.isValid = false;
                itemCurrent.isValidItem.errorMessage = `Current threshold value is similar to previous threshold`;
            }
        }
    }

    function updateAllComparator(comparisonValue) {
        function checkComparatorIsDesc(comparator) {
            return comparator === TYPEOF_COMPARATOR[0].value || comparator === TYPEOF_COMPARATOR[1].value;
        }

        let array = getThresholdData();

        if (array.length >= 2) {
            const lastItemComparator = array[array.length - 1].comparatorThreshold;
            const firstItemComparatorIsDesc = checkComparatorIsDesc(comparisonValue);
            const lastItemComparatorIsDesc = checkComparatorIsDesc(lastItemComparator);

            TYPEOF_COMPARATOR_THRESHOLD_ITEMS[0] = firstItemComparatorIsDesc
                ? TYPEOF_COMPARATOR_FOR_DESC_VAL
                : TYPEOF_COMPARATOR_FOR_ASC_VAL;
            if (firstItemComparatorIsDesc !== lastItemComparatorIsDesc) {
                for (let i = 1; i < array.length; i++) {
                    array[i].comparatorThreshold = comparisonValue;
                }
            }
        }
        updateThresholdConfigData(array);
    }

    function updateDataWhenThresholdChanged() {
        let array = getThresholdData();
        updateAllComparator(array[0].comparatorThreshold);
        for (let i = 1; i < array.length; i++) {
            checkValueOfThreshold(i);
        }
        array[0].isValidItem.isValid = true;
        array[0].isValidItem.errorMessage = '';
    }

    $: if (configThresholdConfigs.thresholdConfigItems) {
        updateDataWhenThresholdChanged();
    }
</script>

<div class="text-white dark:text-white">
    <div class="flex h-12">
        {#if isDisplayEnableButton}
            <Toggle color="blue" bind:checked={configThresholdConfigs.isEnableThreshold} />
            <span class="mr-2 self-center text-sm font-medium text-white dark:text-white">Show Threshold</span>
            <Button
                pill={true}
                class={configThresholdConfigs.isEnableThreshold ? 'w-12 bg-gray-500' : 'hidden'}
                on:click={() => addThresholdConfigItem(configThresholdConfigs)}>{@html icon(buttonfaPlus).html}</Button>
        {:else}
            <Button
                pill={true}
                class={configThresholdConfigs.isEnableThreshold ? 'w-12 bg-gray-500' : 'hidden'}
                on:click={() => addThresholdConfigItem(configThresholdConfigs)}>{@html icon(buttonfaPlus).html}</Button>
            <span class="ml-2 self-center text-sm font-medium text-white dark:text-white">Add Threshold</span>
        {/if}
    </div>

    <div class="{configThresholdConfigs.isEnableThreshold ? '' : 'hidden'} ">
        <section class="flex flex-row flex-wrap items-center gap-2 py-2">
            <div class="w-[30%] self-center">
                <Label>Threshold Title</Label>
            </div>
            <div class="w-[15%] self-center break-all">
                <Label>Comparator</Label>
            </div>
            <div class="w-[18%] self-center break-all">
                <Label>Threshold Value</Label>
            </div>
            <div class="w-[10%] self-center {isDisplayBgColor ? '' : 'hidden'}">
                <Label class="text-center">Bg Color</Label>
            </div>
            <div class="w-[12%] self-center {isDisplayTextColor ? '' : 'hidden'}">
                <Label class="text-center">Text Color</Label>
            </div>
        </section>
        {#each configThresholdConfigs.thresholdConfigItems as item, index}
            <section class="flex flex-row flex-wrap items-center gap-2 py-2">
                <div class="w-[30%] self-center">
                    <Input placeholder="Threshold Title" bind:value={item.titleThresholdItem} />
                </div>
                <div class="w-[15%] self-center">
                    {#if index == 0}
                        <Select
                            class="dark:bg-gray-600"
                            items={TYPEOF_COMPARATOR}
                            bind:value={item.comparatorThreshold}
                            on:change={() => updateAllComparator(item.comparatorThreshold)} />
                    {:else}
                        <Select
                            class="dark:bg-gray-600"
                            items={TYPEOF_COMPARATOR_THRESHOLD_ITEMS[0]}
                            bind:value={item.comparatorThreshold} />
                    {/if}
                </div>
                <div class="w-[18%] self-center">
                    <Input
                        placeholder="Threshold Value"
                        type="number"
                        bind:value={item.valueOfThreshold}
                        on:change={() => checkValueOfThreshold(index)} />
                </div>
                <div class="w-[10%] self-center {isDisplayBgColor ? '' : 'hidden'}">
                    <Input
                        type="color"
                        class="mx-auto h-10 w-10 min-w-[2.5rem] p-0"
                        placeholder="Background Color"
                        bind:value={item.bgColor} />
                </div>
                <div class="w-[12%] self-center {isDisplayTextColor ? '' : 'hidden'}">
                    <Input
                        type="color"
                        class="mx-auto h-10 w-10 min-w-[2.5rem] p-0"
                        placeholder="Text Color"
                        bind:value={item.textColor} />
                </div>
                <div class="self-center">
                    <Button class="h-12 w-12 bg-gray-500" pill={true} on:click={() => removeThresholdConfigItem(index)}
                        >{@html icon(buttonfaMinus).html}</Button>
                </div>
            </section>
            {#if item.isValidItem.isValid == false}
                <Helper class="text-sm font-medium" color="red">
                    <span>{item.isValidItem.errorMessage}</span>
                </Helper>
            {/if}
        {/each}
    </div>
</div>
