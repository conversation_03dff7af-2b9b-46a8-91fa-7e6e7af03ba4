<script lang="ts" context="module">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { logger } from '$lib/stores/Logger';
    import {
        GetCheckCurrentStockBalanceJobsDocument,
        type ClearCheckCurrentStockBalanceJobsMutation,
        type GetCheckCurrentStockBalanceJobsSubscription,
        ClearCheckCurrentStockBalanceJobsDocument,
        type CreateCheckCurrentStockBalanceJobMutation,
        CreateCheckCurrentStockBalanceJobDocument,
    } from '$generated-types';

    export const widgetIcon = faPumpMedical;
    export const widgetCategory = 'Inventory';
    export const widgetLimit = Number.POSITIVE_INFINITY;
    export const widgetTitle = 'Inventory - Check Current Stock Balance';
    export const widgetEnabled = true;
    export const widgetMinWidth = 12;
    export const widgetMinHeight = 10;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;
    export let max_height: number = 96;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { onMount } from 'svelte';
    import {
        Button,
        Checkbox,
        Heading,
        Radio,
        Table,
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
    } from 'flowbite-svelte';
    import { mutationStore, subscriptionStore } from '@urql/svelte';
    import { faPumpMedical } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';

    enum JobType {
        ConsumableItem = 'consumable_item',
        Blood = 'blood',
        CriticalDrug = 'critical_drug',
        Equipment = 'equipment',
    }

    const CheckStockBalanceType = {
        [JobType.ConsumableItem]: 'Consumable Item',
        [JobType.Blood]: 'Blood',
        [JobType.CriticalDrug]: 'Critical Drug',
        [JobType.Equipment]: 'Equipment',
    };

    const client = get(gqlClientStore);

    let stockQuery;
    let viewLevel: string = 'group_view';
    let isWithThreshold: boolean = true;
    let isWithoutThreshold: boolean = true;

    const createQuery = () => {
        return subscriptionStore<GetCheckCurrentStockBalanceJobsSubscription>({
            client,
            query: GetCheckCurrentStockBalanceJobsDocument,
        });
    };

    const createJob = (jobType: JobType) => {
        return mutationStore<CreateCheckCurrentStockBalanceJobMutation>({
            client,
            query: CreateCheckCurrentStockBalanceJobDocument,
            variables: {
                objects: [
                    {
                        check_stock_balance: CheckStockBalanceType[jobType],
                        view_level: viewLevel,
                        is_with_threshold: isWithThreshold,
                        is_without_threshold: isWithoutThreshold,
                    },
                ],
            },
        });
    };

    const formatTimestamp = (timestamp: string): string => {
        return timestamp ? dayjs(timestamp).format('YYYY-MM-DD hh:mm:ss') : '';
    };

    const clearJobs = () => {
        mutationStore<ClearCheckCurrentStockBalanceJobsMutation>({
            client,
            query: ClearCheckCurrentStockBalanceJobsDocument,
        });
    };

    onMount(async () => {
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, () => {});
        stockQuery = createQuery();
    });
</script>

<div class="flex justify-center rounded-lg bg-gray-900 p-3 text-start text-on-surface-1">
    <Heading tag="h5">Inventory - Check Current Stock Balance</Heading>
</div>
<div class="flex flex-col gap-5 p-3 text-on-surface-1">
    <div class="grid grid-cols-12 gap-3">
        <div class="col-span-12 p-2">
            <div class="mb-5 grid grid-cols-12 gap-4 gap-y-4">
                <div class="col-span-2">
                    <Button class="w-full" color="light" on:click={() => createJob(JobType.ConsumableItem)}
                        >Check Consumable Balance</Button>
                </div>
                <div class="col-span-2">
                    <Button class="w-full" color="light" on:click={() => createJob(JobType.CriticalDrug)}
                        >Check Critical Drug Balance</Button>
                </div>
                <div class="col-span-2">
                    <Button class="w-full" color="light" on:click={() => createJob(JobType.Equipment)}
                        >Check Available Equipment</Button>
                </div>
                <div class="col-span-2">
                    <Button class="w-full" color="light" on:click={() => createJob(JobType.Blood)}
                        >Check Blood Balance</Button>
                </div>
                <div class="col-span-4 grid grid-cols-4 pl-10">
                    <div class="col-span-2">
                        <Radio class="pb-2" name="view-level" bind:group={viewLevel} value="group_view"
                            >Group Level</Radio>
                        <Radio name="view-level" bind:group={viewLevel} value="detail_view">Detail Level</Radio>
                    </div>
                    <div class="col-span-2 gap-y-4">
                        <Checkbox class="pb-2" bind:checked={isWithThreshold}>With Threshold</Checkbox>
                        <Checkbox bind:checked={isWithoutThreshold}>Without Threshold</Checkbox>
                    </div>
                </div>
                <div class="col-span-2">
                    <Button class="w-full" color="light" on:click={() => {}}>Export Run History</Button>
                </div>
                <div class="col-span-2">
                    <Button class="w-full" color="light" on:click={clearJobs}>Clear Run History</Button>
                </div>
            </div>
            <div class="col-span-10 max-h-[400px] flex-1 overflow-auto rounded-lg">
                <Table striped={true} class="table-fixed" divClass={`max-h-${max_height === 96 ? "96" : "[calc(100vh-220px)]"} overflow-auto rounded-lg`}>
                    <TableHead defaultRow={false} theadClass="sticky top-0 uppercase bg-gray-50 dark:bg-gray-900 z-10">
                        <TableHeadCell class="w-1/6 border border-gray-600 p-3">Stock Take Job ID</TableHeadCell>
                        <TableHeadCell class="border border-gray-600 p-3">Check Stock Balance</TableHeadCell>
                        <TableHeadCell class="border border-gray-600 p-3">Start Date/Time</TableHeadCell>
                        <TableHeadCell class="border border-gray-600 p-3">End Date/Time</TableHeadCell>
                        <TableHeadCell class="border border-gray-600 p-3">Action</TableHeadCell>
                        <TableHeadCell class="w-1/6 border border-gray-600 p-3">Status</TableHeadCell>
                    </TableHead>
                    <TableBody>
                        {#if $stockQuery?.data?.cc3_inventory_check_current_stock_balance_jobs?.length}
                            {#each $stockQuery.data.cc3_inventory_check_current_stock_balance_jobs as job}
                                <TableBodyRow>
                                    <TableBodyCell class="text-wrap border-x border-gray-600 p-3"
                                        >{job.stock_take_job_id}</TableBodyCell>
                                    <TableBodyCell class="text-wrap border-x border-gray-600 p-3"
                                        >{job.check_stock_balance}</TableBodyCell>
                                    <TableBodyCell class="text-wrap border-x border-gray-600 p-3">
                                        {formatTimestamp(job.start_date_time)}
                                    </TableBodyCell>
                                    <TableBodyCell class="text-wrap border-x border-gray-600 p-3">
                                        {(job.end_date_time && formatTimestamp(job.end_date_time)) || ''}
                                    </TableBodyCell>
                                    <TableBodyCell class="text-wrap border-x border-gray-600 p-3">
                                        {job.action || ''}
                                    </TableBodyCell>
                                    <TableBodyCell class="text-wrap border-x border-gray-600 p-3">
                                        {job.status}
                                    </TableBodyCell>
                                </TableBodyRow>
                            {/each}
                        {/if}
                    </TableBody>
                </Table>
            </div>
        </div>
    </div>
</div>
