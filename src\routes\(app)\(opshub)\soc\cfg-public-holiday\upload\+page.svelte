<script lang="ts">
    import * as XLSX from 'xlsx/xlsx.mjs';

    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faUpload, faRefresh } from '@fortawesome/free-solid-svg-icons';
    import { Button, Heading, Dropzone } from 'flowbite-svelte';

    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import { INV_STYLES } from '$lib/shared/constants/general';
    import { get } from 'svelte/store';
    import { locations } from '$lib/stores/Locations';
    import { isLoading } from '$lib/stores/Loading';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';
    import { hasAccess } from '$lib/shared/util/validation';
    export let data;
    const CommonURL = `${get(locations).common_web}`;

    const EXCEL_TYPES = ['text/csv'];

    let fileList: File[] = [];
    let isModalOpen: boolean = false;
    let modalMessage: string = '';

    const dropHandle = async (event: DragEvent) => {
        fileList = [];
        event.preventDefault();
        const items = event?.dataTransfer?.items;
        if (items?.length > 1) {
            isModalOpen = true;
            modalMessage = 'Please upload only one file.';
            return;
        }
        if (!EXCEL_TYPES.includes(items?.[0]?.type)) {
            isModalOpen = true;
            modalMessage = 'Only Csv files (.csv) are supported.';
            return;
        }
        if (items[0].kind === 'file') {
            const file = items[0].getAsFile();
            fileList = [file];
        }
        if (fileList.length) {
            await writeToDB(fileList[0]);
        }
    };

    const writeToDB = async (file: File) => {
        isLoading.set(true);
        try {
            const formData = new FormData();
            formData.append('file', file);
            const response = await fetch(`${CommonURL}/calendar/public-holiday`, {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error('File upload failed');
            }

            const result = await response.json();
            if (response.ok) {
                isModalOpen = true;
                modalMessage = 'File uploaded successfully.';
            } else {
                isModalOpen = true;
                modalMessage = result.message || 'File upload failed.';
            }
        } catch (error) {
            console.error('Error processing file:', error);
            isModalOpen = true;
            modalMessage = 'An error occurred while processing the file.';
        } finally {
            isLoading.set(false);
        }
    };

    const handleChange = (event: Event) => {
        const files = (event.target as HTMLInputElement).files;
        if (!files || !EXCEL_TYPES.includes(files[0].type)) {
            isModalOpen = true;
            modalMessage = 'Only Excel files (.xls or .xlsx) are supported.';
            return;
        }
        fileList = [files[0]];
    };

    const showFiles = (fileName: string) => {
        if (fileName.length > 40) return fileName.slice(0, 40) + '...';
        return fileName;
    };

    const onUpload = async () => {
        if (!fileList.length) {
            isModalOpen = true;
            modalMessage = 'Please select a file to upload.';
            return;
        }
        await writeToDB(fileList[0]);
        fileList = [];
        (document.getElementById('dropzone') as HTMLInputElement).value = '';
    };

    const onReset = () => {
        fileList = [];
        (document.getElementById('dropzone') as HTMLInputElement).value = '';
    };
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <div class="flex flex-col gap-20 p-3 text-on-surface-1">
        <div class="relative flex h-96 min-w-fit max-w-96 flex-col rounded-lg border border-gray-700 bg-gray-900">
            <div class="{INV_STYLES.header} mb-8">
                <Heading tag="h5">Public Holiday File Upload</Heading>
            </div>

            <Dropzone
                id="dropzone"
                class="mx-8 mb-8 w-max px-16"
                on:drop={dropHandle}
                accept={EXCEL_TYPES}
                on:dragover={event => {
                    event.preventDefault();
                }}
                on:change={handleChange}>
                <div class="mb-4 flex justify-center text-4xl">
                    {@html icon(faUpload).html}
                </div>
                <p class="mb-2 text-lg text-gray-500 dark:text-gray-400">
                    Click or drag and drop to upload an Csv file (.csv)
                </p>
                {#if fileList.length > 0}
                    {#each fileList as file}
                        <p>{showFiles(file?.name)}</p>
                    {/each}
                {/if}
            </Dropzone>
            <div class="rounded-b-lg bg-gray-800">
                <div class="m-4 flex justify-center gap-8">
                    {#if hasAccess(data.abilities, SCOPE.CREATE)}
                        <Button color="light" on:click={onUpload} class="flex w-32 justify-center gap-2 md:w-32">
                            <span>{@html icon(faUpload).html}</span>
                            <span>Upload</span>
                        </Button>
                    {/if}
                    <Button color="light" on:click={onReset} class="flex w-32 justify-center gap-2 md:w-32">
                        <span>{@html icon(faRefresh).html}</span>
                        <span>Reset</span>
                    </Button>
                </div>
            </div>
        </div>
    </div>
</Guard>
<Notification bind:isOpen={isModalOpen} {modalMessage} />
