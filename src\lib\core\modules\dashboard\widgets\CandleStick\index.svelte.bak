<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { faCheck } from '@fortawesome/free-solid-svg-icons';
    import { type CandleStickConfig, Candle_Stick_Label_Enum } from './WidgetConfig/index.svelte';
    import dayjs from 'dayjs';
    import type { EChartOption, EChartsType } from 'echarts';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import gql from 'graphql-tag';

    // Plugin to allow usage of durations
    dayjs.extend(dayjs.duration);

    export const widgetTitle = 'Candle Stick';
<<<<<<< HEAD
    export const widgetIcon = faCheck;
    export const widgetHeight = 5;
    export const widgetWidth = 5;
=======
    export const widgetIcon = faCandleHolder;
>>>>>>> aoh-web/develop
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetEnabled = true;
    export const widgetMinWidth = 5;
    export const widgetMinHeight = 5;
    export const widgetMaxWidth = 5;
    export const widgetMaxHeight = 5;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { gqlClientStore } from '$lib/stores/Clients';
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';

    export let config: CandleStickConfig;

    let echarts;
    let chart: EChartsType;
    let barChartEl: HTMLElement;
    let option: EChartOption;

    let subscription: Subscription;

    let xAxis: string[] = [];
    let primaryBar: number[] = [];
    let yBar2: number[] = [];
    let threshold: number[] = [];

    const loadData = result => {
        if (result?.data) {
            xAxis = [];
            primaryBar = [];
            yBar2 = [];
            result?.data[config.dataSource].forEach(element => {
                xAxis.push(element.name);
                primaryBar.push(element.quantity_1);

                let q2: number = element.quantity_2 > 0 ? -element.quantity_2 : element.quantity_2;
                yBar2.push(q2);
                threshold.push(element.threshold ? element.threshold : 0);
            });

            option.xAxis['data'] = xAxis;

            option.color = [
                config.colors[Candle_Stick_Label_Enum.Primary],
                config.colors[Candle_Stick_Label_Enum.Secondary],
                config.colors[Candle_Stick_Label_Enum.Threshold],
            ];

            option.series = [
                {
                    data: primaryBar,
                    type: 'bar',
                    barMinWidth: 50,
                    stack: 'x',
                    barWidth: '50%',
                    itemStyle: {
                        borderRadius: [5, 5, 0, 0],
                    },
                    label: {
                        show: true,
                        padding: 10,
                        borderRadius: 5,
                        color: '#FFFFFF',
                        position: 'top',
                    },
                },
                {
                    data: yBar2,
                    type: 'bar',
                    barMinWidth: 50,
                    stack: 'x',
                    barWidth: '50%',
                    itemStyle: {
                        borderRadius: [0, 0, 5, 5],
                    },
                    label: {
                        show: true,
                        padding: 5,
                        color: '#FFFFFF',
                        position: 'bottom',
                        formatter: function (params) {
                            return (-params.value).toString(); //rng for now because value isnt available yet.
                        },
                    },
                },
                {
                    name: 'Threshold',
                    type: 'line',
                    data: threshold,
                    label: {
                        show: true,
                        padding: 3,
                        borderRadius: 5,
                        color: '#FFFFFF',
                        backgroundColor: config.colors[Candle_Stick_Label_Enum.Threshold],
                    },
                },
            ];
        }
    };

    const FetchCandleStickDataDocument = gql`
        query {
            ${config?.dataSource}{
                name
                quantity_1
                quantity_2
                threshold
            }
        }
    `;

    const ObserveCandleStickDataDocument = gql`
        subscription {
            ${config?.dataSource}{
                name
                quantity_1
                quantity_2
                threshold
            }
        }
    `;

    const client = get(gqlClientStore);
    client.query(FetchCandleStickDataDocument, {}).toPromise().then(loadData);

    onMount(async () => {
        if (config?.dataSource?.length) {
            subscription = wPipe(client.subscription(ObserveCandleStickDataDocument, {}), wSubscribe(loadData));
        }

        //Initialize echarts and channel
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            if (chart) chart.resize();
        });

        echarts = await import('echarts');
        chart = echarts.init(barChartEl);

        window.addEventListener('resize', () => {
            chart.resize();
        });

        option = {
            grid: {
                top: '10%',
                width: '75%',
                height: '75%',
                containLabel: true,
            },
            xAxis: {
                type: 'category',
                position: 'bottom',
                axisLine: {
                    show: true,
                    lineStyle: {
                        width: 4,
                    },
                },
                axisTick: {
                    show: false,
                },
                data: xAxis,
                axisLabel: {
                    padding: 15,
                    interval: 0,
                    rotate: 0, //If the label names are too long you can manage this by rotating the label.
                },
            },
            yAxis: [
                {
                    show: false,
                },
            ],
            series: [],
        };
    });

    onDestroy(() => {
        subscription?.unsubscribe();
    });

    $: if (Boolean(option) && Boolean(barChartEl) && Boolean(config)) {
        chart?.setOption(option);
    }
</script>

<div class="flex h-full w-full flex-col">
    <header class="flex h-16 items-center gap-4 px-4 py-2 text-on-background">
        <p class="bg-transparent">{config.title}</p>
        <div class="flex items-center gap-2 text-xs">
            <span
                class="h-[12px] w-[25px] rounded"
                style="background-color:{config.colors[Candle_Stick_Label_Enum.Primary]}" />
            <span class="">{config.labels[Candle_Stick_Label_Enum.Primary]}</span>
        </div>

        <div class="flex items-center gap-2 text-xs">
            <span
                class="h-[12px] w-[25px] rounded"
                style="background-color:{config.colors[Candle_Stick_Label_Enum.Secondary]}" />
            <span class="">{config.labels[Candle_Stick_Label_Enum.Secondary]}</span>
        </div>

        <div class="flex items-center gap-2 text-xs">
            <span class="w-[25px] border-b-4" style="border-color:{config.colors[Candle_Stick_Label_Enum.Threshold]}" />
            <span class="">{config.labels[Candle_Stick_Label_Enum.Threshold]}</span>
        </div>
    </header>

    <section class="w-full grow overflow-y-auto" bind:this={barChartEl} />
</div>
