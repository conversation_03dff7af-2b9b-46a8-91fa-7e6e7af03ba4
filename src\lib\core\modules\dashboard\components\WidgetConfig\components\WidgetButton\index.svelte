<script lang="ts">
    import { Label, Button } from 'flowbite-svelte';
    export let title: string;
    export let value;
    export let customClass = 'mb-4';
    export let label: string;
    let isActive = false;

    const onClick = () => {
        if (typeof value == 'boolean') {
            value = !value;
        } else {
            value = label.toLowerCase();
        }
    };

    $: {
        switch (typeof value) {
            case 'boolean':
                isActive = value;
                break;
            default:
                if (label && value && value == label.toLowerCase()) {
                    isActive = true;
                } else {
                    isActive = false;
                }
        }
    }
</script>

<div class={customClass}>
    {#if title}<Label class="block mb-2">{title}</Label>{/if}
    <div class="flex gap-2">
        <Button color={isActive ? 'blue' : 'light'} class="h-10 w-20" on:click={onClick}>{label}</Button>
    </div>
</div>
