<script lang="ts">
    import Dropdown from './index.svelte';

    let myValue: any = 'No value';
    let items = [
        {
            label: 'Label 1',
            value: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididu',
        },
        { label: 'Label 2', value: 3 },
        { label: 'Label 3', value: true },
    ];
</script>

<div class="grid w-1/4 gap-2 p-2 text-on-background">
    <Dropdown title={'Dropdown'} options={items} bind:value={myValue} />

    <span class="text-on-surface flex justify-center rounded border border-outline p-2 text-sm font-light"
        >{myValue}</span>
</div>
