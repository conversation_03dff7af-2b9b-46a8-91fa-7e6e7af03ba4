<script lang="ts" context="module">
    import { faBullseye } from '@fortawesome/free-solid-svg-icons';
    import { JUSTIFY_iTEMS_OBJ } from '../utils';
    import dayjs from 'dayjs';
    export const widgetTitle = 'Inpatient Target';
    export const widgetIcon = faBullseye;
    export const widgetLimit = 999;
    export const widgetMinWidth = 3;
    export const widgetMinHeight = 2;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 12;
    export const widgetCategory = 'Resource Management';
    export const componentName = new URL(import.meta.url).pathname;
</script>

<script lang="ts">
    import type { WidgetInpatientTargetConfig } from './WidgetConfig/index.svelte';
    export let config: WidgetInpatientTargetConfig;

    let titleElement: HTMLElement;
    let indicatorElement: HTMLElement;
    let indicatorTitleElement: HTMLElement;
    let indicatorValueElement: HTMLElement;
    let currentTime = dayjs().format('HH:mm');
    let inpatientTarget = 0;
    let displayLabel = '-';

    const convertHtmlCode = {
        '>=': '&#8805;',
        '>': '&#x3e;',
        '<=': '&#8804;',
        '<': '&#x3c;',
    };

    $: configTitleFrontSize = config.titleFontSize;
    $: if (titleElement && configTitleFrontSize) {
        titleElement.style.fontSize = `${configTitleFrontSize}px`;
    }

    $: indicatorTitleStyle = config.indicatorStyleConfig.indicatorTitleStyle;
    $: comparatorHTMLCODE = indicatorTitleStyle.comparator;
    $: if (indicatorTitleElement && indicatorTitleStyle) {
        indicatorTitleElement.style.fontSize = `${indicatorTitleStyle.fontSize}px`;
        indicatorTitleElement.style.justifyContent = JUSTIFY_iTEMS_OBJ[indicatorTitleStyle.align]
            ? JUSTIFY_iTEMS_OBJ[indicatorTitleStyle.align]
            : '';
        comparatorHTMLCODE = convertHtmlCode[indicatorTitleStyle.comparator];
    }

    $: indicatorValueStyle = config.indicatorStyleConfig.indicatorValueStyle;
    $: if (indicatorValueElement && indicatorValueStyle) {
        indicatorValueElement.style.fontSize = `${indicatorValueStyle.fontSize}px`;
        indicatorValueElement.style.backgroundColor = `${indicatorValueStyle.bgColor}`;
        indicatorValueElement.style.color = `${indicatorValueStyle.textColor}`;
    }

    function getDataIndicator() {
        inpatientTarget = 0;
        displayLabel = '-';
        for (let i = 0; i < config.thresholdConfig.thresholdConfigItems.length; i++) {
            let item = config.thresholdConfig.thresholdConfigItems[i];
            if (currentTime <= item.targetUntilTime) {
                inpatientTarget = item.inpatientTarget;
                displayLabel = item.displayLabel;
                break;
            }
        }
    }
    setInterval(() => {
        currentTime = dayjs().format('HH:mm');
    }, 1000);

    $: configThresholdConfig = config.thresholdConfig;
    $: if (configThresholdConfig || currentTime) {
        getDataIndicator();
    }
</script>

<div class="flex h-full w-full flex-col p-2 text-on-background">
    <div bind:this={titleElement} class="text-wrap p-1 text-{config.titleAlign}">
        {config.title}
    </div>

    <div bind:this={indicatorElement} class="flex h-full w-full items-center">
        <div class="flex w-2/3 items-center pr-2" bind:this={indicatorTitleElement}>
            <span>{@html comparatorHTMLCODE} {inpatientTarget}% DB {displayLabel}</span>
        </div>
        <div class="flex h-1/2 w-1/3 items-center justify-center rounded" bind:this={indicatorValueElement}>
            {config.indicatorValue}
        </div>
    </div>
</div>
