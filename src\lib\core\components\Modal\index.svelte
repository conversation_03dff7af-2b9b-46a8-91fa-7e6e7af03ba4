<script lang="ts">
    import Button from '$lib/core/components/Button/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faXmark } from '@fortawesome/free-solid-svg-icons';
    import { quintOut } from 'svelte/easing';
    import { fade } from 'svelte/transition';

    export let title = '';
    export let isOpen = true;
    export let heightClass = 'h-fit';
    export let widthClass = '';
    export let bgClass = 'bg-surface-2 rounded-t-sm';
    export let isParentRoot = true;
    export let paddingAndMarginClass = '';
    export let onClose: (event: Event) => void = undefined;

    function attachToRoot(node: Node) {
        if (isParentRoot) {
            document.documentElement.appendChild(node);
        }

        return {
            destroy() {},
        };
    }
</script>

<!--Overlay-->
{#if isOpen}
    <div
        use:attachToRoot
        transition:fade|global={{ delay: 0, duration: 300, easing: quintOut }}
        class="fixed inset-0 z-50 flex h-full w-full items-center justify-center bg-black bg-opacity-50 text-on-primary">
        <!-- Container -->
        <!-- svelte-ignore a11y-no-static-element-interactions -->
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <div
            class="flex flex-col {heightClass} {widthClass} {bgClass} {paddingAndMarginClass} rounded-lg"
            on:click|stopPropagation={() => {}}>
            <!--Background-->
            <!--Title-->
            <header class="flex items-center justify-between rounded-t-lg bg-surface-4 px-3 py-2 text-on-background">
                <p class="text-xs leading-[115%] xl:text-sm">
                    {title}
                </p>
                <Button
                    on:click={e => {
                        if (onClose) {
                            onClose(e);
                        }

                        isOpen = false;
                    }}
                    setClass="bg-inherit hover:text-selected-stroke text-inherit h-full grid items-center justify-center"
                    >{@html icon(faXmark).html}</Button>
            </header>
            <!--Content-->
            <slot />
        </div>
    </div>
{/if}
