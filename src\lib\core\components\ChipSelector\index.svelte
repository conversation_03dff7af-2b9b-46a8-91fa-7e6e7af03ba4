<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faCaretDown,
        faMagnifyingGlass,
        faPlus,
        faSquare,
        faSquareCheck,
        faSquareMinus,
    } from '@fortawesome/free-solid-svg-icons';
    import Chip from '$lib/core/components/Chip/index.svelte';
    import { fade } from 'svelte/transition';
    import { createEventDispatcher } from 'svelte';
    import { capitalCase } from 'change-case-all';
    import type { FormEventHandler } from 'svelte/elements';

    const src = new URL(import.meta.url).pathname;
    const [componentName] = src.split('/').slice(-2, -1);
    const log = logger.child({ src, componentName });

    const CHIPS_MAX_LENGTH = 20;

    type ComponentEvents = {
        update: ChipUpdateDetail;
        create: ChipCreateDetail;
    };

    export type ChipForDisplay = {
        id: string;
        text: string;
        checked?: boolean;
        filterHit?: boolean;
        occ_lock?: number;
    };

    export type ChipUpdateDetail = {
        chips: Array<ChipForDisplay>;
        checkedCount: number;
        filteredCount: number;
        isChanged: boolean;
    };

    export type ChipCreateDetail = {
        text: string;
    };

    export type ChipUpdateEvent = CustomEvent<ChipUpdateDetail>;
    export type ChipCreateEvent = CustomEvent<ChipCreateDetail>;
</script>

<script lang="ts">
    export let label = 'chip';

    const dispatch = createEventDispatcher<ComponentEvents>();

    let isChipSelectorOpen = false;

    export let chips: Array<ChipForDisplay> = [];

    export let createChipEnabled = false;
    export let openedListWidth: string = 'auto';
    export let openedListAlign: 'left' | 'right' = 'left';
    export let isCreatingChip = false;

    let chipFilterValue: string;
    let isCanCreateNewChip = false;
    let filteredChipCount = 0;
    let checkedChipCount = 0;

    const chipFilterInputHandler: FormEventHandler<HTMLInputElement> = (e: Event) => {
        const input = e.target as HTMLInputElement;

        if (input.validity) {
            filterChips(chipFilterValue, chips);
            dispatch('update', {
                checkedCount: checkedChipCount,
                filteredCount: filteredChipCount,
                chips: chips,
                isChanged: false,
            });
        }
    };

    function filterChips(filterValue: string, data: typeof chips): void {
        const filterTerm = filterValue?.toLowerCase()?.trim();

        let chipExactMatch = false;
        isCanCreateNewChip = false;
        filteredChipCount = 0;
        checkedChipCount = 0;

        if (filterValue) {
            // Set results based on filter of full list using search term
            data.forEach(label => {
                const labelFormatted = label.text.toLowerCase();

                if (labelFormatted === filterTerm) {
                    chipExactMatch = true;
                    label.filterHit = true;
                    filteredChipCount = filteredChipCount + 1;
                } else if (labelFormatted.toLowerCase().includes(filterTerm)) {
                    label.filterHit = true;
                    filteredChipCount = filteredChipCount + 1;
                } else {
                    label.filterHit = false;
                }

                if (label.checked) {
                    checkedChipCount++;
                }
            });

            // Allow to create new label baed on these conditions
            isCanCreateNewChip = createChipEnabled && !chipExactMatch && filterValue.length > 1;
        } else {
            data.forEach(label => {
                label.filterHit = true;
                filteredChipCount = filteredChipCount + 1;

                if (label.checked) {
                    checkedChipCount++;
                }
            });
        }

        // Beware possible infinite loop if external component modifies chips on update - consider redesign
        chips = chips;
    }

    $: if (chips) {
        filterChips(chipFilterValue, chips);
    }
</script>

<!-- Chip Selector Container -->
<div
    tabindex="0"
    role="button"
    class="w-full"
    on:click={() => {
        isChipSelectorOpen = !isChipSelectorOpen;
    }}>
    <!-- Selected Chips -->
    <div
        class="box-border min-h-14 border-b border-mono-3 px-4 py-2 text-base text-body
hover:border-primary hover:text-primary">
        <div class="text-xs tracking-wide">{capitalCase(label)}(s)</div>
        <div class="flex min-h-8 justify-between">
            <div class="flex flex-wrap items-center justify-start gap-2 px-0 py-1">
                {#if chips && chips.length > 0 && checkedChipCount > 0}
                    {#each chips as chip}
                        {#if chip.checked}
                            <Chip>{chip.text.toUpperCase()}</Chip>
                        {/if}
                    {/each}
                {:else}
                    -
                {/if}
            </div>
            <div class="flex items-start justify-center gap-2">
                <span>{@html icon(faCaretDown).html}</span>
            </div>
        </div>
    </div>
    <!-- Chip Selector List-->
    {#if isChipSelectorOpen}
        <div transition:fade={{ duration: 100 }} class="relative w-full">
            <div
                style="width:{openedListWidth}"
                class="absolute min-w-32 bg-surface-3 shadow-light"
                class:right-0={openedListAlign === 'right'}
                class:left-0={openedListAlign === 'left'}>
                <!-- Chip Search Input -->
                <div class="rounded-sm border-b-2 border-outline p-2 text-base text-subheader">
                    <!-- svelte-ignore a11y-no-static-element-interactions -->
                    <div
                        class="flex"
                        on:click={e => {
                            e.stopPropagation();
                        }}>
                        <span class="pointer-events-none absolute left-4">{@html icon(faMagnifyingGlass).html}</span>
                        <input
                            bind:value={chipFilterValue}
                            on:input={chipFilterInputHandler}
                            class="grow bg-transparent pl-8 focus:outline-none"
                            placeholder="Search"
                            type="text"
                            maxlength={CHIPS_MAX_LENGTH}
                            pattern="^[A-Za-z](?!.*--)(?!.*-$)[A-Za-z\-]+"
                            tabindex="0" />
                    </div>
                </div>
                <!-- Select All Chip -->
                {#if filteredChipCount !== 0}
                    <div
                        tabindex="0"
                        role="button"
                        class="flex flex-col border-b-2 border-outline p-2 px-3 py-2 text-base hover:cursor-pointer
            hover:bg-surface-5 active:brightness-125"
                        on:click={event => {
                            event.stopPropagation();

                            if (checkedChipCount == chips.length) {
                                chips.forEach(label => {
                                    label.checked = false;
                                });

                                checkedChipCount = 0;
                            } else {
                                chips.forEach(label => {
                                    label.checked = true;
                                });

                                checkedChipCount = chips.length;
                            }

                            chips = chips;

                            dispatch('update', {
                                checkedCount: checkedChipCount,
                                filteredCount: filteredChipCount,
                                chips: chips,
                                isChanged: true,
                            });
                        }}>
                        <div class="flex items-center justify-start gap-6 text-base">
                            <!-- Checkbox with White Background -->
                            <div class="relative">
                                {#if checkedChipCount == chips.length}
                                    <div class="absolute left-[2px] top-[6px] z-10 h-3 w-3 bg-on-primary" />
                                    <div class="absolute z-10 text-primary">
                                        {@html icon(faSquareCheck).html}
                                    </div>
                                {:else if checkedChipCount > 0}
                                    <div class="absolute left-[2px] top-[6px] z-10 h-3 w-3 bg-on-primary" />
                                    <div class="absolute z-10 text-primary">
                                        {@html icon(faSquareMinus).html}
                                    </div>
                                {:else}
                                    <div class="z-2 absolute text-mono-2">
                                        {@html icon(faSquare).html}
                                    </div>
                                {/if}
                                &nbsp;
                            </div>
                            <div class="overflow-hidden overflow-ellipsis text-nowrap text-header">
                                {#if checkedChipCount == chips.length}
                                    Clear All
                                {:else}
                                    Select All
                                {/if}
                            </div>
                        </div>
                    </div>
                    <!-- Chip Results -->
                    <div class="max-h-80 overflow-auto">
                        {#each chips as label}
                            {#if label.filterHit}
                                <div
                                    tabindex="0"
                                    role="button"
                                    class="flex flex-col px-3 py-2 text-base
                hover:cursor-pointer hover:bg-surface-5 active:brightness-125"
                                    on:click={event => {
                                        event.stopPropagation();

                                        label.checked = !label.checked;

                                        if (label.checked) {
                                            checkedChipCount = checkedChipCount + 1;
                                        } else {
                                            checkedChipCount = checkedChipCount - 1;
                                        }

                                        chips = chips;

                                        dispatch('update', {
                                            checkedCount: checkedChipCount,
                                            filteredCount: filteredChipCount,
                                            chips: chips,
                                            isChanged: true,
                                        });
                                    }}>
                                    <div class="flex items-center justify-start gap-6 text-base">
                                        <div class="relative">
                                            <!-- Checkbox with White Background -->
                                            {#if label.checked}
                                                <div class="absolute left-[2px] top-[6px] z-10 h-3 w-3 bg-on-primary" />
                                                <div class="absolute z-10 text-primary">
                                                    {@html icon(faSquareCheck).html}
                                                </div>
                                            {:else}
                                                <div class="absolute z-10 text-mono-2">
                                                    {@html icon(faSquare).html}
                                                </div>
                                            {/if}
                                            &nbsp;
                                        </div>
                                        <div class="overflow-hidden overflow-ellipsis text-nowrap text-subheader">
                                            {label.text.toUpperCase()}
                                        </div>
                                    </div>
                                </div>
                            {/if}
                        {/each}
                    </div>
                {:else if !isCanCreateNewChip}
                    <div
                        class="flex w-full items-center justify-center overflow-hidden overflow-ellipsis
            text-nowrap px-3 py-4 text-sm italic leading-[120%] text-header">
                        {chips.length > 0 ? 'No search results' : 'No ' + label.toLowerCase() + 's found'}
                    </div>
                {/if}
                <!-- Create New Chip Button -->
                {#if isCanCreateNewChip}
                    <div
                        tabindex="0"
                        role="button"
                        class:border-t-2={filteredChipCount != 0}
                        class:bg-mono-2={isCreatingChip}
                        class="flex flex-col border-outline p-2 px-3 py-2 text-base hover:cursor-pointer
                hover:bg-surface-5 active:brightness-125"
                        on:click={event => {
                            // Create the new chip
                            event.stopPropagation();

                            dispatch('create', {
                                text: chipFilterValue?.trim().toLowerCase(),
                            });
                        }}>
                        <div class="flex items-center justify-start gap-6 text-base">
                            <div class="relative">
                                <!-- Checkbox with White Background -->
                                <div class="absolute z-10 text-mono-2">
                                    {@html icon(faPlus).html}
                                </div>
                                &nbsp;
                            </div>
                            <div class="overflow-hidden overflow-ellipsis text-nowrap text-header">
                                Create new {label.toLowerCase()}: "{chipFilterValue?.trim()}"
                            </div>
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    {/if}
</div>
