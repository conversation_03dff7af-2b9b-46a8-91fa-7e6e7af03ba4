<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCheck, faInfo, faTriangleExclamation, faXmark } from '@fortawesome/free-solid-svg-icons';
    import { createEventDispatcher } from 'svelte';
    import { fade } from 'svelte/transition';

    import Button from '$lib/core/components/Button/index.svelte';
    import { NotificationType } from '$lib/stores/Notifications';

    const dispatch = createEventDispatcher();

    export let type: NotificationType = NotificationType.ERROR;
    export let dismissible = true;
</script>

<article
    class="mx-auto my-1 flex w-80 items-center rounded py-3 pl-4 pr-2 text-on-primary {type === NotificationType.ERROR
        ? 'bg-error/90'
        : type === NotificationType.SUCCESS
        ? 'bg-warning/90'
        : 'bg-primary/90'}"
    role="alert"
    transition:fade|global>
    {#if type === NotificationType.SUCCESS}
        <div class="w-1/12 text-xl">{@html icon(faCheck).html}</div>
    {:else if type === NotificationType.ERROR}
        <div class="w-1/12 text-xl">{@html icon(faTriangleExclamation).html}</div>
    {:else}
        <div class="w-1/12 text-xl">{@html icon(faInfo).html}</div>
    {/if}

    <div class="text w-10/12">
        <slot />
    </div>

    {#if dismissible}
        <div class="w-1/12 text-xl">
            <Button setClass="font-bold float-right" on:click={() => dispatch('dismiss')}>
                {@html icon(faXmark).html}
            </Button>
        </div>
    {/if}
</article>
