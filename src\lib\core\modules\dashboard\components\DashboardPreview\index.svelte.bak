<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import WidgetGrid, {
        type WidgetGridinitializedEvent,
    } from '$lib/core/modules/dashboard/components/WidgetGrid/index.svelte';
    import WidgetItem from '$lib/core/modules/dashboard/components/WidgetItem/index.svelte';
    import { LoadDashboardConfiguration } from '$lib/core/modules/dashboard/module';
    import type { GridStack, GridStackOptions } from 'gridstack';
    import { tick } from 'svelte';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    /**
     * The number of columns the preview dashboard will have.
     *
     * @defaultValue `16`
     */
    export let maxColumns = 16;

    /**
     * The number of rows the preview dashboard will have (0 for unlimited);
     *
     * @defaultValue `0`
     */
    export let maxRows = 0;

    /**
     * Set to true to display the grid in the background.
     *
     * @defaultValue `false`
     */
    export let showGrid = false;

    /***
     * Set to true to display the default background, false to keep it transparent.
     *
     * @defaultValue `true`
     */
    export let showBackground = true;

    /**
     * A string in the format of a stringified dashboard config.
     */
    export let dashboard_config: Array<Widget>;

    let loaded_dashboard_config: Array<WidgetGridItem>;
    let gridstack: GridStack; // redesign all this - gridstack should only exist in widget grid

    const options: GridStackOptions = {
        acceptWidgets: false,
        alwaysShowResizeHandle: false,
        animate: false,
        auto: true,
        cellHeightThrottle: 100,
        column: maxColumns,
        row: maxRows,
        disableDrag: true,
        disableResize: true,
        float: false,
        margin: '0.25rem',
    };

    $: if (dashboard_config) {
        log.warn(dashboard_config);
        LoadDashboard();
    }

    $: if (gridstack && dashboard_config) {
        ReloadDashboard();
    }

    // Wrap with anonymous func to prevent infinite reactive loop
    async function LoadDashboard() {
        loaded_dashboard_config = LoadDashboardConfiguration(dashboard_config);
    }

    // Wrap with anonymous func to prevent infinite reactive loop
    async function ReloadDashboard() {
        gridstack.batchUpdate(true);
        gridstack.removeAll();
        await tick();
        loaded_dashboard_config = LoadDashboardConfiguration(dashboard_config);
        await tick();
        gridstack.batchUpdate(false);
    }

    const widgetGridInitHandler = (e: WidgetGridinitializedEvent) => {
        gridstack = e.detail.gridstack;
    };
</script>

<div class="flex h-full w-full flex-col justify-between gap-2 p-4">
    {#if !loaded_dashboard_config}
        <div class="flex flex-grow items-center justify-center italic">Choose a dashboard to preview.</div>
    {:else}
<<<<<<< HEAD
        <WidgetGrid
            on:init={widgetGridInitHandler}
            {testid}
            {options}
            {showGrid}
            {showBackground}
            gridChangeBroadcast={false}>
            {#if gridstack}
=======
        <WidgetGrid on:init={widgetGridInitHandler} {options} {showGrid} {showBackground} gridChangeBroadcast={false}>
>>>>>>> aoh-web/develop
            <!-- !: widgetId must be unique -->
            {#each loaded_dashboard_config as item (item.id)}
                <WidgetItem
                    id={item.id}
                    title={item.widget_type.name}
                    options={{
                        x: item.column,
                        y: item.row,
                        h: item.height,
                        w: item.width,
                        minW: item.widget_type.min_width,
                        minH: item.widget_type.min_height,
                        maxW: item.widget_type.max_width,
                        maxH: item.widget_type.max_height,
                        noMove: true,
                        noResize: true,
                    }}
                    editable={false}>
                    {#if item.config}
                        <!-- Render with given config -->
                        <svelte:component this={item.component.main.default} config={item.config} />
                    {:else}
                        <!-- Render widget with default config as there's none provided -->
                        <svelte:component this={item.component.main.default} config={item.component.config.default} />
                    {/if}
                </WidgetItem>
            {/each}
            {/if}
        </WidgetGrid>
    {/if}
</div>
