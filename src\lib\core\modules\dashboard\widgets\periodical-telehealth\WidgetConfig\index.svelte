<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export type PeriodicalTelehealthConfig = {
        title?: string;
        showLabel: boolean;
    };

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'Periodical Telehealth Situation';

    export const configDefaults: PeriodicalTelehealthConfig = {
        title: DEFAULT_TITLE,
        showLabel: false,
    };
</script>

<script lang="ts">
    //Components
    import { Checkbox, Input, Label } from 'flowbite-svelte';

    export let config: PeriodicalTelehealthConfig;
</script>

<div class="text-on-surface flex flex-col gap-3">
    <Label for="title">Title</Label>
    <Input placeholder={'Title'} bind:value={config.title} />
    <Checkbox bind:checked={config.showLabel}>Show label</Checkbox>
</div>
