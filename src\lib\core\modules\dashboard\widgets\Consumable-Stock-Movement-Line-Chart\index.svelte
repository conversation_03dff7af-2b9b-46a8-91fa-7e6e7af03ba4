<script lang="ts" context="module">
    //Utils
    import { logger } from '$lib/stores/Logger';
    import { faGear, faLineChart } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';

    //svelte
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';

    //Graphql
    import { gqlClientStore } from '$lib/stores/Clients';
    import gql from 'graphql-tag';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';

    import { MMM_DD_hh_mm, MMM_DD, DD, type ChartXY } from './WidgetConfig/index.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);

    export const widgetTitle = 'Consumable Stock Movement';
    export const widgetIcon = faLineChart;
    export const widgetCategory = 'Inventory';
    export const widgetLimit = 999;
    export const widgetMinWidth = 3;
    export const widgetMinHeight = 6;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { SeriesModel, type EChartsOption, type EChartsType, type SeriesOption } from 'echarts';
    import { Button, Modal, P, Tooltip } from 'flowbite-svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { subscriptionStore, type OperationResultStore, type Pausable } from '@urql/svelte';
    import { GetFilteringByUserIdStreamDocument, type GetFilteringByUserIdStreamSubscription } from '$generated-types';
    import ConsumableLineChartConfig from '$lib/core-inv/modules/ConsumableLineChartConfig.svelte';
    import { user } from '$lib/stores/Auth';

    /* ---------------------------------- data ---------------------------------- */
    let subscriptions: Subscription[] = [];

    /* --------------------------------- echart --------------------------------- */
    let echarts;
    let chart: EChartsType;
    let lineChartEl: HTMLElement;
    let option: EChartsOption;
    let mockData = [
        [
            ['2024-01-08', 10],
            ['2024-01-09', 12],
            ['2024-01-10', 40],
            ['2024-01-11', 10],
            ['2024-01-12', 10],
            ['2024-01-13', 20],
            ['2024-01-14', 10],
        ],
        [
            ['2024-01-08', 20],
            ['2024-01-09', 50],
            ['2024-01-10', 2],
            ['2024-01-11', 50],
            ['2024-01-12', 10],
            ['2024-01-13', 5],
            ['2024-01-14', 20],
        ],
    ];
    //const months: Array<string> = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    const mons: Array<string> = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    export let config: ChartXY;

    function loadChart(data: any[]) {
        // for (let i = 0; i < config?.keySeries.length; i++) {
        //     option.series[i].data = data?.map(d => {
        //                                                 return [d[config?.xKey], d[config?.keySeries[i]]];
        //                                             });
        // }
        for (let i = 0; i < config?.keySeries.length; i++) {
            option.series[i].data = mockData[i];
        }
        option = option;
    }

    function addSeries() {
        if (option.series?.length > 0) return;
        let arr: object[] = [];
        for (let i = 0; i < config?.keySeries.length; i++) {
            arr.push({
                name: `series ${i}`,
                smooth: 0,
                symbol: 'number',
                type: config?.chartType ? config.chartType.toString() : 'line',
                lineStyle: {
                    //color: config.inflowLineColor,
                    width: 5,
                },
                data: [],
                label: {
                    formatter: param => {
                        return param.data[1] === 0 ? '' : param.data[1];
                    },
                    show: true,
                    padding: 3,
                    borderRadius: 5,
                    color: '#FFFFFF',
                },
                triggerLineEvent: true,
            });
        }
        option.series = arr;
    }

    async function render() {
        echarts = await import('echarts');
        chart = echarts.init(lineChartEl);

        option = fnOption(true);

        addSeries();

        window.addEventListener('resize', () => {
            chart.resize();
        });
    }

    const fnOption = function (isInit: boolean) {
        if (isInit) {
            option = {
                grid: {
                    left: 30,
                    top: 40,
                    right: 30,
                    bottom: 20,
                },
                xAxis: {
                    show: config?.showXAxis,
                    axisLabel: {
                        formatter: value => `${formatDisplay(value, config?.formatType)}`,
                        align: 'center',
                    },
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            width: 1,
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                },
                yAxis: {
                    show: config?.showYAxis,
                    type: 'value',
                    position: 'right',
                    splitLine: {
                        show: false,
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            width: 3,
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                },
                legend: {
                    orient: 'horizontal', // Try 'horizontal'
                    //right: 10,
                    top: 0,
                    data: [],
                },
            };
            let arr: string[] = [];
            for (let i = 0; i < config?.keySeries.length; i++) {
                arr.push(`series ${i}`);
            }
            option.legend = { ...option.legend, data: arr };
        }
        option.xAxis = { ...option.xAxis, show: config?.showXAxis, boundaryGap: config?.chartType == 'bar' };
        option.yAxis = { ...option.yAxis, show: config?.showYAxis };
        let chartType = config?.chartType ? config.chartType.toString() : 'line';
        for (let i = 0; i < option.series?.length; i++) {
            if (chartType == 'area') {
                option.series[i].areaStyle = {};
                option.series[i].type = 'line';
            } else {
                option.series[i].areaStyle = null;
                option.series[i].type = chartType;
            }
        }
        option.legend = { ...option.legend, show: config?.showLegend };
        return option;
    };

    const formatDisplay = function (dtValue: string, strFormat: string) {
        let dt = new Date(dtValue);
        let result: string = dtValue;
        if (!Date.parse(dtValue)) return result;
        let _date = (dt.getDate() < 10 ? '0' : '') + dt.getDate();
        let _hour = (dt.getHours() < 10 ? '0' : '') + dt.getHours();
        let _minute = (dt.getMinutes() < 10 ? '0' : '') + dt.getMinutes();
        switch (strFormat) {
            case MMM_DD_hh_mm:
                result = mons[dt.getMonth()] + '-' + _date + ' ' + _hour + ':' + _minute;
                break;
            case MMM_DD:
                result = mons[dt.getMonth()] + '-' + _date;
                break;
            case DD:
                result = _date;
                break;
            default:
                result = dtValue;
                break;
        }
        return result;
    };

    let user_id = '';
    let filteringOpen = false;

    const client = get(gqlClientStore);
    let filtering: OperationResultStore<GetFilteringByUserIdStreamSubscription> & Pausable;

    $: if (user_id !== '') {
        filtering = subscriptionStore<GetFilteringByUserIdStreamSubscription>({
            client,
            query: GetFilteringByUserIdStreamDocument,
            variables: { user_id },
        });
    }

    onMount(async () => {
        //Initialize echarts and channel
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            if (chart) chart.resize();
        });
        config.xKey = 'label';
        config.keySeries = ['incidents_open', 'incidents_closed'];
        // if (config?.dataSource?.length) {
        //     //Get today's
        //     let FetchInflowDocument = gql`
        //         subscription  {
        //             ${config?.dataSource} {
        //                 ${config?.xKey}
        //                 ${config?.keySeries}
        //             }
        //         }
        //     `;

        //     const client = get(gqlClientStore);
        //     subscriptions.push(
        //         wPipe(
        //             client.subscription(FetchInflowDocument, {}),
        //             wSubscribe(result => {
        //                 if (result?.data) loadChart(result.data[config.dataSource]);
        //             })
        //         )
        //     );
        user_id = get(user).claims.hasura_access['x-hasura-user-id'];
        await render();
        loadChart(null);
        //}
    });

    onDestroy(() => {
        subscriptions.forEach(s => {
            s?.unsubscribe();
        });
    });

    $: if (Boolean(option) && Boolean(lineChartEl) && Boolean(config)) {
        fnOption(false);
        chart?.setOption(option);
    }

    const onGearClick = () => {
        filteringOpen = true;
    };
</script>

<div class="group flex h-full w-full flex-col p-4">
    <header class="flex items-center justify-between gap-4 text-on-background">
        <p class="bg-transparent text-base">{config.title}</p>
        <Button size="md" on:click={onGearClick} class="opacity-0 transition-all duration-300 group-hover:opacity-100">
            <span>
                {@html icon(faGear).html}
            </span>
            <Tooltip class="z-10">Filter configuration</Tooltip>
        </Button>
    </header>
    <div class="grid flex-1 grid-cols-12 gap-2">
        <div class="col-span-10">
            <section class="h-full w-full" bind:this={lineChartEl} />
        </div>
        {#if $filtering && $filtering.data}
            <div class="col-span-2 flex flex-col gap-2">
                <P class="rounded-lg border border-gray-700 bg-gray-900 p-3">
                    View by {$filtering.data.cc3_soc_filtering[0].view_chart_by}
                </P>
                <P class="rounded-lg border border-gray-700 bg-gray-900 p-3">
                    {dayjs($filtering.data.cc3_soc_filtering[0].view_date_from).format('D/M/YYYY')} to {dayjs(
                        $filtering.data.cc3_soc_filtering[0].view_date_to
                    ).format('D/M/YYYY')}
                </P>
                <P class="rounded-lg border border-gray-700 bg-gray-900 p-3">View by Item</P>
            </div>
        {/if}
    </div>
</div>

<Modal title="Consumable Line Chart Configuration" bind:open={filteringOpen}>
    <ConsumableLineChartConfig width="full" showTitle={false} {user_id} />
</Modal>
