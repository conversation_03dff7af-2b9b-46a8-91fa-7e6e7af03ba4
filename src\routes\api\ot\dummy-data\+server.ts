import { locations } from '$lib/stores/Locations';
import { json } from '@sveltejs/kit';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

const otRoomURL = `${get(locations).soc}/ot/get-dummy-data`;

export const GET: RequestHandler = async ({ locals, url }) => {
    const token = locals.user.access_token;
    const funcName = url.searchParams.get('func-name');
    const res = await fetch(`${otRoomURL}?func-name=${funcName}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
    });

    const data = await res?.json();

    return json({ data });
};
