<script lang="ts" context="module">
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { locations } from '$lib/stores/Locations';
    import { faXmark, faLinkSlash, faCheck } from '@fortawesome/free-solid-svg-icons';
    import { logger } from '$lib/stores/Logger';
    import type { Action } from 'svelte/action';
    import { get } from 'svelte/store';
    import Modal from '$lib/core/components/Modal/index.svelte';
    import TextFieldv2 from '$lib/core/components/TextField/v2/index.svelte';
    import Spinner from '$lib/core/components/Spinner/index.svelte';
    import { onMount, tick } from 'svelte';
    import { fade } from 'svelte/transition';
    import { DashboardStore } from '$lib/core/modules/dashboard/module';
    import { StatusCodes } from 'http-status-codes';
    import { WIDGET_GROUP_TITLE } from '../../widgets/utils';

    export enum DisplayState {
        Initializing,
        EmbeddedBaseConfiguration,
        SavingNewSharedConfiguration,
        LoadingSharedConfiguration,
        SharedConfigurationSafe,
        SharedConfigurationEdit,
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
    const CONFIGURATION_NAME_MAX_LENGTH = 50;
</script>

<script lang="ts">
    export let isOpen = false;
    export let onClose = () => {};
    export let init: Action = () => {};
    export let animationEndCallback = () => {};
    export let shared_configs: Array<SharedConfig> = [];
    export let selected_loaded_widget_item: WidgetGridItem;

    let displayState: DisplayState = DisplayState.Initializing;
    let previousDisplayState: DisplayState;
    let disableInteraction = false;

    let original_configuration: WidgetConfig = {};
    let shared_config_options: Array<DropdownItem> = [];
    let previous_selected_shared_config: SharedConfig;
    let selected_shared_config: SharedConfig;
    let new_configuration_name = '';
    let name_valid = false;
    let clear_selected_dropdown_option: () => void;

    const inputClass = `scroll text-on-surface p-1 bg-transparent border border-outline transition-all duration-200 ease-in-out
        rounded focus:border-2 focus:border-primary font-light`;

    $: switch (displayState) {
        case DisplayState.Initializing:
            break;
        case DisplayState.EmbeddedBaseConfiguration:
            break;
        case DisplayState.SavingNewSharedConfiguration:
            break;
        case DisplayState.LoadingSharedConfiguration:
            break;
        case DisplayState.SharedConfigurationSafe:
            break;
        case DisplayState.SharedConfigurationEdit:
            break;
    }

    $: if (shared_configs) {
        shared_config_options = [];

        for (const sc of shared_configs) {
            shared_config_options.push({
                label: sc.name,
                value: sc,
            });
        }

        shared_config_options = shared_config_options;
    }

    function handle_cancel_change_widget_shared_config() {
        if (selected_shared_config) {
            displayState = DisplayState.SharedConfigurationSafe;
        } else {
            displayState = DisplayState.EmbeddedBaseConfiguration;
        }

        if (previous_selected_shared_config) {
            selected_shared_config = previous_selected_shared_config;
        }
    }

    function handle_cancel_save_new_configuration() {
        if (previousDisplayState === undefined) {
            throw new Error('no previous display state when cancelling save new config');
        }

        displayState = previousDisplayState;
    }

    function handle_shared_config_selected() {
        if (selected_shared_config == previous_selected_shared_config) {
            return;
        }

        displayState = DisplayState.LoadingSharedConfiguration;
    }

    async function handle_save_new_shared_configuration() {
        disableInteraction = true;
        await tick();

        const new_shared_config: SharedConfig = {
            id: undefined,
            name: new_configuration_name,
            widget_type_id: selected_loaded_widget_item.widget_type_id,
            config: selected_loaded_widget_item.config,
        };

        const response = await fetch(`${get(locations).dash_web}/shared_config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(new_shared_config),
        });

        if (response.status != StatusCodes.OK) {
            log.error({ errors: (await response.json()).errors, status: response.status });
            throw new Error('failed to save new shared configuration');
        }

        const result = await response.json();

        if (!result.data.id || result.data.occ_lock == null) {
            throw new Error('id and occ_lock are expected after creating new shared configuration');
        }

        DashboardStore.update(ds => {
            ds.widget_map.set(selected_loaded_widget_item.id, {
                ...selected_loaded_widget_item,
            });

            ds.shared_config_map.set(result.data.id, result.data);

            return ds;
        });

        selected_shared_config = result.data;
        previous_selected_shared_config = selected_shared_config;

        await tick();
        disableInteraction = false;
        displayState = DisplayState.SharedConfigurationSafe;
    }

    async function handle_updated_shared_configuration() {
        disableInteraction = true;
        await tick();

        const updated_shared_config: SharedConfig = {
            id: selected_shared_config.id,
            name: selected_shared_config.name,
            widget_type_id: selected_shared_config.widget_type_id,
            config: selected_loaded_widget_item.config,
            occ_lock: selected_shared_config.occ_lock,
        };

        const response = await fetch(`${get(locations).dash_web}/shared_config`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updated_shared_config),
        });

        if (response.status != StatusCodes.OK) {
            log.error({ errors: (await response.json()).errors, status: response.status });
            throw new Error('failed to save new shared configuration');
        }

        const result = await response.json();

        if (!result.data.id || result.data.occ_lock == null) {
            throw new Error('id and occ_lock are expected after creating new shared configuration');
        }

        DashboardStore.update(ds => {
            ds.widget_map.set(selected_loaded_widget_item.id, {
                ...selected_loaded_widget_item,
            });

            ds.shared_config_map.set(result.data.id, result.data);

            return ds;
        });

        selected_shared_config = result.data;
        previous_selected_shared_config = selected_shared_config;

        await tick();
        disableInteraction = false;
        displayState = DisplayState.SharedConfigurationSafe;
    }

    async function handle_change_widget_shared_config() {
        let widget = $DashboardStore.widget_map.get(selected_loaded_widget_item.id);

        selected_loaded_widget_item.shared_config_id = selected_shared_config.id;
        selected_loaded_widget_item.config = selected_shared_config.config;
        widget.shared_config_id = selected_shared_config.id;
        widget.config = selected_shared_config.config;

        disableInteraction = true;
        await tick();

        const response = await fetch(`${get(locations).dash_web}/widget/${selected_loaded_widget_item.dashboard_id}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: widget.id,
                occ_lock: widget.occ_lock,
                dashboard_id: widget.dashboard_id,
                widget_type_id: widget.widget_type_id,
                row: widget.row,
                column: widget.column,
                width: widget.width,
                height: widget.height,
                shared_config_id: selected_shared_config.id,
                config: selected_shared_config.config,
            }),
        });

        if (response.status != StatusCodes.OK) {
            log.error({ errors: (await response.json()).errors, status: response.status });
            throw new Error('failed to patch widget configuration');
        }

        let updated_widget = await response.json();

        widget.occ_lock = updated_widget.occ_lock;

        DashboardStore.update(ds => {
            ds.widget_map.set(selected_loaded_widget_item.id, widget);
            return ds;
        });

        previous_selected_shared_config = selected_shared_config;

        await tick();
        disableInteraction = false;
        displayState = DisplayState.SharedConfigurationSafe;
    }

    async function handle_delink_widget_config() {
        let widget = $DashboardStore.widget_map.get(selected_loaded_widget_item.id);
        let delinked_config = structuredClone(selected_shared_config.config);

        selected_loaded_widget_item.shared_config_id = null;
        selected_loaded_widget_item.config = delinked_config;
        widget.shared_config_id = null;
        widget.config = delinked_config;

        disableInteraction = true;
        await tick();

        const response = await fetch(`${get(locations).dash_web}/widget/${selected_loaded_widget_item.dashboard_id}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: widget.id,
                occ_lock: widget.occ_lock,
                dashboard_id: widget.dashboard_id,
                widget_type_id: widget.widget_type_id,
                row: widget.row,
                column: widget.column,
                width: widget.width,
                height: widget.height,
                shared_config_id: null,
                config: selected_shared_config.config,
            }),
        });

        if (response.status != StatusCodes.OK) {
            log.error({ errors: (await response.json()).errors, status: response.status });
            throw new Error('failed to patch widget');
        }

        let updated_widget = await response.json();

        widget.occ_lock = updated_widget.occ_lock;

        DashboardStore.update(ds => {
            ds.widget_map.set(selected_loaded_widget_item.id, widget);
            return ds;
        });

        previous_selected_shared_config = selected_shared_config;
        clear_selected_dropdown_option();

        await tick();
        disableInteraction = false;
        displayState = DisplayState.EmbeddedBaseConfiguration;
    }

    function shared_configuration_name_change_handler(e: InputEvent) {
        const input = e.target as HTMLInputElement;

        for (const [_, sc] of $DashboardStore.shared_config_map) {
            if (sc.name === input.value) {
                input.setCustomValidity('Name must be unique!');
                input.reportValidity();
                break;
            }
        }

        name_valid = input.validity.valid;
    }

    onMount(() => {
        selected_shared_config = $DashboardStore.shared_config_map.get(selected_loaded_widget_item.shared_config_id);
        previous_selected_shared_config = selected_shared_config;

        if (selected_shared_config) {
            displayState = DisplayState.SharedConfigurationSafe;
        } else {
            displayState = DisplayState.EmbeddedBaseConfiguration;
        }
    });
</script>

<div
    on:introend={animationEndCallback}
    on:outroend={animationEndCallback}
    use:init
    id="widget-config"
    class:hidden={!isOpen}
    class:block={isOpen}
    class="absolute z-50 !h-[90vh] min-h-fit w-[30%] flex-grow space-y-5 overflow-y-hidden rounded-lg border
border-selected-stroke bg-surface-2 text-on-background shadow-glow shadow-selected-stroke">
    <div class="sticky top-0">
        <div class="flex justify-between px-4 py-2 text-center text-xl">
            <span>Configure Widget</span>
            <Button variant={ButtonVariant.EMPTY} iconDefinition={faXmark} on:click={onClose} />
        </div>
        <div class="flex flex-col">
            <div class="relative p-4">
                <div
                    class={`${
                        $DashboardStore.widget_type_map.get(selected_loaded_widget_item.widget_type_id)?.name ===
                        WIDGET_GROUP_TITLE
                            ? ''
                            : 'max-h-[70vh] overflow-y-auto'
                    }`}
                    class:pointer-events-none={displayState === DisplayState.SharedConfigurationSafe}>
                    <slot />
                </div>
                {#if displayState === DisplayState.SharedConfigurationSafe}
                    <div class="absolute inset-0 bg-black opacity-30" />
                {/if}
            </div>
        </div>
        <div class="flex flex-col gap-2 border-t-2 border-mono-3 p-4">
            <Dropdown
                title={'Stored Configuration'}
                setDisabled={shared_config_options.length <= 0}
                bind:previousValue={previous_selected_shared_config}
                bind:value={selected_shared_config}
                on:update={handle_shared_config_selected}
                options={shared_config_options}
                bind:clearOption={clear_selected_dropdown_option}
                setClass="{inputClass} h-10 text-xs w-full"
                placeholder="No configuration selected"
                label="No configuration selected"
                setLabelClass="text-primary"
                enableSearch={true} />
            {#if displayState === DisplayState.EmbeddedBaseConfiguration || displayState === DisplayState.SavingNewSharedConfiguration || (displayState === DisplayState.LoadingSharedConfiguration && !selected_shared_config)}
                <Button
                    on:click={async () => {
                        previousDisplayState = displayState;
                        displayState = DisplayState.SavingNewSharedConfiguration;
                    }}>SAVE AS NEW</Button>
            {:else if displayState === DisplayState.SharedConfigurationSafe || (displayState === DisplayState.LoadingSharedConfiguration && selected_shared_config)}
                <div class="flex w-full gap-2">
                    <Button
                        appendClass="w-full"
                        on:click={async () => {
                            displayState = DisplayState.SharedConfigurationEdit;
                            original_configuration = structuredClone(selected_loaded_widget_item.config);
                        }}>EDIT SHARED CONFIG</Button>
                    <Button
                        on:click={handle_delink_widget_config}
                        iconDefinition={faLinkSlash}
                        variant={ButtonVariant.DEFAULT} />
                </div>
            {:else if displayState === DisplayState.SharedConfigurationEdit}
                <Button on:click={handle_updated_shared_configuration}>UPDATE</Button>
                <Button
                    on:click={async () => {
                        previousDisplayState = displayState;
                        displayState = DisplayState.SavingNewSharedConfiguration;
                    }}>SAVE AS NEW</Button>
                <Button
                    on:click={async () => {
                        previousDisplayState = displayState;
                        displayState = DisplayState.SharedConfigurationSafe;

                        DashboardStore.update(ds => {
                            let widget = ds.widget_map.get(selected_loaded_widget_item.id);
                            widget.config = original_configuration;
                            ds.widget_map.set(selected_loaded_widget_item.id, widget);
                            return ds;
                        });
                    }}>DISCARD CHANGES</Button>
            {/if}
        </div>
    </div>
</div>

<!-- Modal for confirming changing of embedded config to shared config - selected from list, or save as new -->
<Modal
    title="Confirm"
    isOpen={displayState === DisplayState.LoadingSharedConfiguration}
    onClose={handle_cancel_change_widget_shared_config}>
    <div class="flex flex-col">
        <div class="flex flex-col gap-2 p-6 pb-4 text-sm">
            <div>The existing configuration will be lost.</div>
            <div>Are you sure you want to link this configuration to this widget?</div>
        </div>
        <div class="flex w-full gap-2 border-t-2 border-t-mono-3 px-6 py-4">
            <Button on:click={handle_cancel_change_widget_shared_config} appendClass="w-full">CANCEL</Button>
            <Button on:click={handle_change_widget_shared_config} appendClass="w-full" iconDefinition={faCheck}
                >CONFIRM</Button>
        </div>
    </div>
</Modal>

<!-- Modal for choosing new shared configuration name -->
<Modal title="Save New Configuration" isOpen={displayState === DisplayState.SavingNewSharedConfiguration}>
    <div class="flex flex-col gap-4 px-6 pb-4 pt-6">
        <div class="">
            <TextFieldv2
                title="Please name this new stored configuration"
                maxlength={CONFIGURATION_NAME_MAX_LENGTH}
                bind:value={new_configuration_name}
                on:change={shared_configuration_name_change_handler}
                required={true} />
        </div>
        <div class="flex w-full gap-2">
            <Button on:click={handle_cancel_save_new_configuration} appendClass="w-full">CANCEL</Button>
            <Button
                setDisabled={!name_valid}
                on:click={handle_save_new_shared_configuration}
                appendClass="w-full"
                iconDefinition={faCheck}>CONFIRM</Button>
        </div>
    </div>
</Modal>

{#if disableInteraction}
    <div
        transition:fade={{ duration: 150 }}
        class="fixed z-[60] flex w-full flex-col items-center justify-center overflow-y-auto overflow-x-hidden rounded
        bg-background text-on-primary opacity-50 transition md:inset-0 md:h-full">
        <Spinner appendClass="text-9xl" />
    </div>
{/if}
