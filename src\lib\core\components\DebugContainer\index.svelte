<script lang="ts" context="module">
    import { dev } from '$app/environment';

    export type DebugValues = {
        label: string;
        value: string;
    };
</script>

<script lang="ts">
    import Clippable from '$lib/core/components/Clippable/index.svelte';

    export let debugVals: Array<DebugValues> = [];
    export let appendClass = '';
    export let isDevOnly = true;
</script>

{#if !isDevOnly || dev}
    <div class="fixed bottom-0 right-0 z-50 p-3 text-right {appendClass} hover:bg-white/80">
        {#each debugVals as dv}
            <Clippable label={dv.label} value={dv.value} />
        {/each}
    </div>
{/if}
