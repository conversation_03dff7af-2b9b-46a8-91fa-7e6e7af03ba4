<script lang="ts">
    import { onMount, onD<PERSON>roy, createEventDispatcher } from 'svelte';
    import debounce from 'just-debounce-it';
    import { Label } from 'flowbite-svelte';

    // Properties
    export let editor: unknown;
    export let title: string;
    export let value = '';
    export let config = {
        fontSize: {
            options: [9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36],
        },
        toolbar: {
            items: ['fontSize', '|', 'fontFamily', 'bold', 'italic', 'underline', '|', 'alignment', 'fontColor'],
        },
    };
    export let customClass = 'mb-6';
    export let disabled = false;
    let id = 'ckeditor-id-' + Math.random().toString(36).substr(2, 9); // Generate a unique ID

    // Instance variables
    let instance = null;
    let lastEditorData = '';
    let editorElement;

    const INPUT_EVENT_DEBOUNCE_WAIT = 300;
    const dispatch = createEventDispatcher();

    $: watchValue(value);

    function watchValue(x) {
        if (instance && x !== lastEditorData) {
            instance.setData(x);
        }
    }

    onMount(async () => {
        if (!instance) {
            const { default: DecoupledEditor } = await import(
                '@ckeditor/ckeditor5-build-decoupled-document/build/ckeditor'
            );

            // If value is passed then add it to config
            if (value) {
                Object.assign(config, {
                    initialData: value,
                });
            }
            // Get dom element to mount initialised editor instance
            editorElement = document.getElementById(id);
            DecoupledEditor.create(editorElement, config)
                .then(editor => {
                    // Save the reference to the instance for future use.
                    instance = editor;
                    editor.ui
                        .getEditableElement()
                        .parentElement.insertBefore(editor.ui.view.toolbar.element, editor.ui.getEditableElement());
                    // Set initial disabled state.
                    if (disabled) editor.enableReadOnlyMode(id);
                    // Let the world know the editor is ready.
                    dispatch('ready', editor);
                    setUpEditorEvents();
                })
                .catch(error => {
                    console.error(error);
                });
        }
    });

    onDestroy(() => {
        if (instance) {
            // Remove the toolbar and editable elements from the DOM
            instance.ui.view.toolbar.element.remove();
            instance.ui.view.editable.element.remove();

            instance.destroy();
            instance = null;
        }

        // Note: By the time the editor is destroyed (promise resolved, editor#destroy fired)
        // the Vue component will not be able to emit any longer.
        // So emitting #destroy a bit earlier.
        dispatch('destroy', instance);
    });

    function setUpEditorEvents() {
        const emitInputEvent = evt => {
            // Cache the last editor data. This kind of data is a result of typing,
            // editor command execution, collaborative changes to the document, etc.
            // This data is compared when the component value changes in a 2-way binding.
            const data = (value = lastEditorData = instance.getData());
            dispatch('input', { data, evt, instance });
        };

        // Debounce emitting the #input event. When data is huge, instance#getData()
        // takes a lot of time to execute on every single key press and ruins the UX.
        instance.model.document.on('change:data', debounce(emitInputEvent, INPUT_EVENT_DEBOUNCE_WAIT));

        instance.editing.view.document.on('focus', evt => {
            dispatch('focus', { evt, instance });
        });

        instance.editing.view.document.on('blur', evt => {
            dispatch('blur', { evt, instance });
        });
    }
</script>

<div class={customClass}>
    {#if title}<Label class="mb-2 block">{title}</Label>{/if}
    <div
        {id}
        class="focus:border-primary-500 focus:ring-primary-500 dark:focus:border-primary-500 dark:focus:ring-primary-500 border-gray-300 bg-gray-50 text-sm text-gray-900 dark:border-gray-500 dark:bg-gray-600 dark:text-white dark:placeholder-gray-400" />
</div>
