<script lang="ts" context="module">
    export interface WidgetPatientsArrivedConfig {
        numberOfPatients?: number;
        arrivedVia?: string;
        uom: string;
    }
    export const configDefaults: WidgetPatientsArrivedConfig = {
        numberOfPatients: 10,
        arrivedVia: 'Amb (SCDF + PTE)',
        uom: 'hour',
    };
</script>

<script lang="ts">
    export let config: WidgetPatientsArrivedConfig;
    import WidgetConfig from '$lib/core/modules/dashboard/components/WidgetConfig/index.svelte';
    import WidgetConfigSelect from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigSelect/index.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';
    
    const arrivedOptions = [
        { name: 'Ambulance to ED', value: 'Amb (SCDF + PTE)' },
        { name: 'Walk-in to ED', value: 'ED Walkers' },
    ];
    const LIST_UOMS: string[] = ['Hour', 'Minute', 'Day'];

    const configElementWidth = 'w-1/5';
    const propertyFields = [
        {
            id: 'arrivedVia',
            componentDisplay: WidgetConfigSelect,
            extraProps: {
                title: 'Patients arrived via',
                items: arrivedOptions,
            },
        },
        {
            id: 'uom',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'UOM',
                labels: LIST_UOMS,
            },
        },
    ];
</script>

<WidgetConfig bind:config {propertyFields} {configElementWidth} />
