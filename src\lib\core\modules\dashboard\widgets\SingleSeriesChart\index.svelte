<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { faPieChart } from '@fortawesome/free-solid-svg-icons';

    export const widgetTitle = 'Single Series Chart';
    export const widgetIcon = faPieChart;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetEnabled = true;
    export const widgetMinWidth = 5;
    export const widgetMinHeight = 5;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { gqlClientStore } from '$lib/stores/Clients';
    import type { EChartsOption, EChartsType } from 'echarts';
    import gql from 'graphql-tag';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import type { SingleSeriesChartConfig } from './WidgetConfig/index.svelte';

    export let config: SingleSeriesChartConfig;

    const FetchGenericChartDocument = gql`
        query FetchIndicatorWidget {
            ${config.dataSource} {
                name
                value
            }
        }
    `;

    const ObserveGenericChartDocument = gql`
        subscription SubscriptionIndicatorWidget {
            ${config.dataSource} {
                name
                value
            }
        }
    `;

    let chartContainer: HTMLElement;
    let chart: EChartsType;
    let option: EChartsOption;

    let subscription: Subscription;
    let chartData: Array<{ name: string; value: number }> = [];
    let colorPalette = ['#00b04f', '#ffbf00', 'ff0000'];
    const updateFromResult = result => {
        chartData = result?.data[config.dataSource];

        let series: EChartsOption['series'];
        let yAxis: EChartsOption['yAxis'];
        let xAxis: EChartsOption['xAxis'];

        switch (config.chartType) {
            case 'bar': {
                xAxis = {
                    type: 'category',
                    show: config?.showXAxis === undefined ? true : config?.showXAxis,
                    data: chartData.map(dataElement => dataElement.name),
                    axisLabel: {
                        inside: false,
                        color: '#fff',
                        interval: 0,
                        rotate: 0,
                        fontSize: 11,
                        width: '10px',
                    },

                    axisLine: {
                        show: false,
                    },
                };

                yAxis = {
                    type: 'value',
                    show: config?.showYAxis === undefined ? true : config?.showYAxis,
                    axisLine: {
                        lineStyle: {
                            opacity: 0.4,
                        },
                    },
                    axisLabel: {
                        opacity: 0.4,
                        color: '#fff',
                    },
                };

                series = [
                    {
                        data: chartData,
                        type: 'bar',
                        color: '#58A8FF',
                        barMinWidth: 50,
                        stack: 'x',
                        barWidth: '50%',
                        itemStyle: {
                            borderRadius: [5, 5, 0, 0],
                        },
                        label: {
                            show: true,
                            padding: 10,
                            borderRadius: 5,
                            color: '#FFFFFF',
                            position: 'top',
                        },
                    },
                ];
                break;
            }
            case 'pie':
            default:
                series = [
                    {
                        type: 'pie',
                        data: chartData,
                        label: {
                            show: false,
                        },
                        bottom: '20%',
                    },
                ];
                break;
        }

        option = {
            legend: {
                bottom: 20,
                left: 'center',
                textStyle: {
                    color: '#fff',
                },
            },
            tooltip: {
                trigger: 'item',
            },
            series,
            xAxis,
            yAxis,
        };
    };

    const client = get(gqlClientStore);
    client.query(FetchGenericChartDocument, {}).toPromise().then(updateFromResult);

    onMount(async () => {
        subscription = wPipe(client.subscription(ObserveGenericChartDocument, {}), wSubscribe(updateFromResult));

        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            if (chart) chart.resize();
        });

        const echarts = await import('echarts');

        if (chartContainer) chart = echarts.init(chartContainer);

        window.addEventListener('resize', () => {
            chart.resize();
        });
    });

    $: if (Boolean(option) && Boolean(chart)) {
        chart?.setOption(option);
    }

    onDestroy(() => {
        subscription?.unsubscribe();
    });
</script>

<div class="flex h-full w-full flex-col">
    <header class="flex h-12 items-center gap-4 px-3 py-1">
        <p class="bg-transparent text-on-background">{config.title}</p>
    </header>
    <div tabindex="0" role="button" on:click class="h-full w-full" bind:this={chartContainer} />
</div>
