<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faCalendarDay,
        faChevronRight,
        faCircleNodes,
        faGears,
        faJar,
        faSyringe,
        faToolbox,
        faWarehouse,
    } from '@fortawesome/free-solid-svg-icons';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    /* ---------------------------------- utils --------------------------------- */
    import { removeSlashPrefix } from '$lib/utils';
    import { onMount } from 'svelte';
    /* --------------------------------- svelte --------------------------------- */
    import { goto } from '$app/navigation';

    import { page } from '$app/stores';
    import { Dropdown, DropdownItem } from 'flowbite-svelte';

    export let isPreviewMode: boolean = false;
    export let isExpanded: boolean = false;
    export let isSelected: boolean = false;
    export let onclick: Function;

    async function navigate(route: string) {
        if (onclick) onclick();

        route = removeSlashPrefix(route);

        await goto(`${$page.url.origin}/${route}`);
    }

    onMount(() => {});
</script>

<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
<li
    id="li-Sys"
    on:click={() => {
        // navigate('/ancillary');
    }}
    class:bg-highlight={isSelected}
    class:text-navbar-selected={isSelected}
    class:text-on-navbar={!isSelected}
    class="group flex w-full cursor-pointer gap-2 self-start overflow-hidden whitespace-nowrap p-6
      duration-200 ease-in-out hover:text-on-background">
    <span class="text-secondary">
        {@html icon(faJar)?.html}
    </span>

    <p
        class="transition-opacity {isExpanded
            ? 'opacity-100 '
            : 'opacity-0'} text-secondary flex w-full items-center text-sm">
        Sys
    </p>
</li>

<Dropdown trigger="hover" placement="right" class="bg-[#434343]">
    <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
        <div class="flex w-full min-w-64 flex-col">
            <span class="text-left text-sm font-semibold text-white">Sys</span>
            <span class="text-xs text-white opacity-75">Sys Configurations</span>
            <div class="mt-1 h-px w-full bg-gray-500 px-3" />
        </div>
    </DropdownItem>

    <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
        <a href={`${$page.url.origin}/batchjob/comm-batch-job/list`}>
            <div class="group flex w-full min-w-64 items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-left text-sm font-semibold text-white">Batch Job</span>
                    <span
                        class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                        class:opacity-100={$page.url.pathname.includes('batchjob/comm-batch-job/list')}
                        >Batch Job
                    </span>
                </div>
            </div>
        </a>
    </DropdownItem>
    <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
        <a href={`${$page.url.origin}/inventory/equipment/rtls-asset-subtype-upload`}>
            <div class="group flex w-full min-w-64 items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-left text-sm font-semibold text-white">RTLS Codeset Upload</span>
                    <span
                        class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                        class:opacity-100={$page.url.pathname.includes(
                            '/inventory/equipment/rtls-asset-subtype-upload'
                        )}>RTLS Codeset Upload</span>
                </div>
            </div>
        </a>
    </DropdownItem>
    <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
        <a href={`${$page.url.origin}/inventory/equipment/rtls-location-mapping-upload`}>
            <div class="group flex w-full min-w-64 items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-left text-sm font-semibold text-white"
                        >RTLS Location Mapping Upload - Inventory</span>
                    <span
                        class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                        class:opacity-100={$page.url.pathname.includes(
                            'inventory/equipment/rtls-location-mapping-upload'
                        )}>RTLS Location Mapping Upload - Inventory</span>
                </div>
            </div>
        </a>
    </DropdownItem>
    <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
        <a href={`${$page.url.origin}/inventory/rft-inv-equipment-location-utilization/list`}>
            <div class="group flex w-full min-w-64 items-center justify-between">
                <div class="flex flex-col">
                    <span class="text-left text-sm font-semibold text-white"
                        >RTLS Location Mapping Configuration - Inventory</span>
                    <span
                        class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                        class:opacity-100={$page.url.pathname.includes(
                            'inventory/rft-inv-equipment-location-utilization/list'
                        )}>RTLS Location Mapping Configuration - Inventory</span>
                </div>
            </div>
        </a>
    </DropdownItem>
    <!-- <Dropdown placement="right-start" trigger="hover" class="bg-[#434343]">
      <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
          <div class="flex w-full min-w-64 flex-col">
              <span class="text-left text-sm font-semibold text-white">Inventory</span>
              <span class="text-xs text-white opacity-75">Inventory Configurations</span>
              <div class="mt-1 h-px w-full bg-gray-500 px-3" />
          </div>
      </DropdownItem>
      <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
          <a href={`${$page.url.origin}/SOP/inventory/blood-stock-replenishment-configuration`}>
              <button
                  on:click={() => {
                      navigate('/SOP/inventory/blood-stock-replenishment-configuration');
                  }}
                  class="group flex w-full min-w-80 items-center justify-between gap-3">
                  <div class="flex flex-col">
                      <span class="text-left text-sm font-semibold text-white"
                          >Blood Stock Replenishment Configuration</span>
                      <span
                          class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                          class:opacity-100={$page.url.pathname.includes(
                              '/SOP/inventory/blood-stock-replenishment-configuration'
                          )}>Blood Stock Replenishment Configuration</span>
                  </div>
                  <div
                      class="text-white opacity-75 duration-300 group-hover:opacity-100"
                      class:opacity-100={$page.url.pathname.includes(
                          '/SOP/inventory/blood-stock-replenishment-configuration'
                      )}>
                      {@html icon(faSyringe)?.html}
                  </div>
              </button>
          </a>
      </DropdownItem>

      <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
          <a href={`${$page.url.origin}/SOP/inventory/consumable-replenishment-configuration`}>
              <button
                  on:click={() => {
                      navigate('/SOP/inventory/consumable-replenishment-configuration');
                  }}
                  class="group flex w-full min-w-80 items-center justify-between gap-3">
                  <div class="flex flex-col">
                      <span class="text-left text-sm font-semibold text-white"
                          >Consumable Replenishment Configuration</span>
                      <span
                          class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                          class:opacity-100={$page.url.pathname.includes(
                              '/SOP/inventory/consumable-replenishment-configuration'
                          )}>Consumable Replenishment Configuration</span>
                  </div>
                  <div
                      class="text-white opacity-75 duration-300 group-hover:opacity-100"
                      class:opacity-100={$page.url.pathname.includes(
                          '/SOP/inventory/consumable-replenishment-configuration'
                      )}>
                      {@html icon(faSyringe)?.html}
                  </div>
              </button>
          </a>
      </DropdownItem>

      <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
          <a href={`${$page.url.origin}/SOP/inventory/drug-replenishment-configuration`}>
              <button
                  on:click={() => {
                      navigate('/SOP/inventory/drug-replenishment-configuration');
                  }}
                  class="group flex w-full min-w-80 items-center justify-between gap-3">
                  <div class="flex flex-col">
                      <span class="text-left text-sm font-semibold text-white">Drug Replenishment Configuration</span>
                      <span
                          class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                          class:opacity-100={$page.url.pathname.includes(
                              '/SOP/inventory/drug-replenishment-configuration'
                          )}>Drug Replenishment Configuration</span>
                  </div>
                  <div
                      class="text-white opacity-75 duration-300 group-hover:opacity-100"
                      class:opacity-100={$page.url.pathname.includes('/SOP/inventory/drug-replenishment-configuration')}>
                      {@html icon(faSyringe)?.html}
                  </div>
              </button>
          </a>
      </DropdownItem>

      <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
          <div class="group flex w-full min-w-64 items-center justify-between">
              <div class="flex flex-col">
                  <span class="text-left text-sm font-semibold text-white">Outbreak SOP Report</span>
                  <span
                      class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                      class:opacity-100={$page.url.pathname.includes('SOP/inventory/outbreak-sop-report')}
                      >Outbreak SOP Report Configurations</span>
              </div>
              <div
                  class="text-white opacity-75 duration-300 group-hover:opacity-100"
                  class:opacity-100={$page.url.pathname.includes('SOP/inventory/outbreak-sop-report')}>
                  {@html icon(faChevronRight)?.html}
              </div>
          </div>
      </DropdownItem>
      <Dropdown placement="right-start" trigger="hover" class="bg-[#434343]">
          <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
              <div class="flex w-full min-w-64 flex-col">
                  <span class="text-left text-sm font-semibold text-white">Outbreak SOP Report</span>
                  <span class="text-xs text-white opacity-75">Outbreak SOP Report Configurations</span>
                  <div class="mt-1 h-px w-full bg-gray-500 px-3" />
              </div>
          </DropdownItem>

          <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
              <a href={`${$page.url.origin}/SOP/inventory/outbreak-sop-report/outbreak-sop-notification-email`}>
                  <button
                      on:click={() => {
                          navigate('/SOP/inventory/outbreak-sop-report/outbreak-sop-notification-email');
                      }}
                      class="group flex w-full min-w-80 items-center justify-between gap-3">
                      <div class="flex flex-col">
                          <span class="text-left text-sm font-semibold text-white">Outbreak SOP Notification Email</span>
                          <span
                              class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                              class:opacity-100={$page.url.pathname.includes(
                                  '/SOP/inventory/outbreak-sop-report/outbreak-sop-notification-email'
                              )}>Outbreak SOP Notification Email</span>
                      </div>
                      <div
                          class="text-white opacity-75 duration-300 group-hover:opacity-100"
                          class:opacity-100={$page.url.pathname.includes(
                              '/SOP/inventory/outbreak-sop-report/outbreak-sop-notification-email'
                          )}>
                          {@html icon(faSyringe)?.html}
                      </div>
                  </button>
              </a>
          </DropdownItem>

          <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
              <a href={`${$page.url.origin}/SOP/inventory/outbreak-sop-report/outbreak-sop-report-configuration`}>
                  <button
                      on:click={() => {
                          navigate('/SOP/inventory/outbreak-sop-report/outbreak-sop-report-configuration');
                      }}
                      class="group flex w-full min-w-80 items-center justify-between gap-3">
                      <div class="flex flex-col">
                          <span class="text-left text-sm font-semibold text-white"
                              >Outbreak SOP Report Configuration</span>
                          <span
                              class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                              class:opacity-100={$page.url.pathname.includes(
                                  '/SOP/inventory/outbreak-sop-report/outbreak-sop-report-configuration'
                              )}>Outbreak SOP Report Configuration</span>
                      </div>
                      <div
                          class="text-white opacity-75 duration-300 group-hover:opacity-100"
                          class:opacity-100={$page.url.pathname.includes(
                              '/SOP/inventory/outbreak-sop-report/outbreak-sop-report-configuration'
                          )}>
                          {@html icon(faSyringe)?.html}
                      </div>
                  </button>
              </a>
          </DropdownItem>
      </Dropdown>

      <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
          <div class="group flex w-full min-w-64 items-center justify-between">
              <div class="flex flex-col">
                  <span class="text-left text-sm font-semibold text-white">Haze SOP Report</span>
                  <span
                      class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                      class:opacity-100={$page.url.pathname.includes('SOP/inventory/haze-sop-report')}
                      >Haze SOP Report Configurations</span>
              </div>
              <div
                  class="text-white opacity-75 duration-300 group-hover:opacity-100"
                  class:opacity-100={$page.url.pathname.includes('SOP/inventory/haze-sop-report')}>
                  {@html icon(faChevronRight)?.html}
              </div>
          </div>
      </DropdownItem>
      <Dropdown placement="right-start" trigger="hover" class="bg-[#434343]">
          <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
              <div class="flex w-full min-w-64 flex-col">
                  <span class="text-left text-sm font-semibold text-white">Haze SOP Report</span>
                  <span class="text-xs text-white opacity-75">Haze SOP Report Configurations</span>
                  <div class="mt-1 h-px w-full bg-gray-500 px-3" />
              </div>
          </DropdownItem>

          <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
              <a href={`${$page.url.origin}/SOP/inventory/haze-sop-report/haze-sop-notification-email`}>
                  <button
                      on:click={() => {
                          navigate('/SOP/inventory/haze-sop-report/haze-sop-notification-email');
                      }}
                      class="group flex w-full min-w-80 items-center justify-between gap-3">
                      <div class="flex flex-col">
                          <span class="text-left text-sm font-semibold text-white">Haze SOP Notification Email</span>
                          <span
                              class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                              class:opacity-100={$page.url.pathname.includes(
                                  '/SOP/inventory/haze-sop-report/haze-sop-notification-email'
                              )}>Haze SOP Notification Email</span>
                      </div>
                      <div
                          class="text-white opacity-75 duration-300 group-hover:opacity-100"
                          class:opacity-100={$page.url.pathname.includes(
                              '/SOP/inventory/haze-sop-report/haze-sop-notification-email'
                          )}>
                          {@html icon(faSyringe)?.html}
                      </div>
                  </button>
              </a>
          </DropdownItem>

          <DropdownItem defaultClass="font-medium py-2 px-4 text-sm hover:bg-[#434343] dark:hover:bg-[#434343]">
              <a href={`${$page.url.origin}/SOP/inventory/haze-sop-report/haze-sop-report-configuration`}>
                  <button
                      on:click={() => {
                          navigate('/SOP/inventory/haze-sop-report/haze-sop-report-configuration');
                      }}
                      class="group flex w-full min-w-80 items-center justify-between gap-3">
                      <div class="flex flex-col">
                          <span class="text-left text-sm font-semibold text-white">Haze SOP Report Configuration</span>
                          <span
                              class="text-left text-xs text-white opacity-75 duration-300 group-hover:opacity-100"
                              class:opacity-100={$page.url.pathname.includes(
                                  '/SOP/inventory/haze-sop-report/haze-sop-report-configuration'
                              )}>Haze SOP Report Configuration</span>
                      </div>
                      <div
                          class="text-white opacity-75 duration-300 group-hover:opacity-100"
                          class:opacity-100={$page.url.pathname.includes(
                              '/SOP/inventory/haze-sop-report/haze-sop-report-configuration'
                          )}>
                          {@html icon(faSyringe)?.html}
                      </div>
                  </button>
              </a>
          </DropdownItem>
      </Dropdown>
  </Dropdown> -->
</Dropdown>
