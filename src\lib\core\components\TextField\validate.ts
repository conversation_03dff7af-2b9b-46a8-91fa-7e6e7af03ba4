function buildValidator(validators) {
    return function validate(value, dirty: boolean) {
        if (!validators || validators.length === 0) {
            return { dirty, valid: true, message: null };
        }

        const failing = validators.find(v => v(value) !== true);

        return {
            dirty,
            valid: !failing,
            message: failing && failing(value),
        };
    };
}

export { buildValidator };
