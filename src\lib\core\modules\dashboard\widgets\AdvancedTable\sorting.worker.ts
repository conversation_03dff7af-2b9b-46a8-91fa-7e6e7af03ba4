// Web Worker for Advanced Table Sorting
import dayjs from 'dayjs';
import { random } from 'mathjs';
import type { AdvancedTableConfig } from './WidgetConfig/index.svelte';

// ISO_REGEX pattern - extracted from utils
const ISO_REGEX = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?([+-]\d{2}:\d{2}|Z)$/;

interface SortMessage {
    type: 'sort';
    data: {
        finalData: { column: string; value: string; style: string }[][];
        sortKey: string;
        sortDirection: number;
        config: AdvancedTableConfig;
    };
}

interface SortResult {
    type: 'sortComplete';
    data: { column: string; value: string; style: string }[][];
}

const validateDate = (date: string, format: string): boolean => {
    return dayjs(date, format).format(format) === date || ISO_REGEX.test(date);
};

// Helper function to extract and normalize cell value for comparison
const getCellValue = (row: any[], column: string): any => {
    const cell = row.find((e: any) => e.column === column);
    if (!cell) {
        console.log('no cell:', row, column);
        return ''; // Return empty string if cell is not found
    }

    if (!cell.value) {
        return '';
    }

    let value = cell.value;

    try {
        // Check for date formats first
        if (
            validateDate(cell.value, 'MM/DD/YYYY HH:mm:ss') ||
            validateDate(cell.value, 'YYYY-MM-DD HH:mm:ss') ||
            ISO_REGEX.test(cell.value)
        ) {
            value = dayjs(cell.value).valueOf();
        }
        // Only convert to number if it's a pure numeric string (including '0', '1', etc.)
        else if (typeof cell.value === 'string' && /^\d+(\.\d+)?$/.test(cell.value.trim())) {
            value = parseFloat(cell.value);
        }
        // Keep as string for mixed alphanumeric or non-numeric strings
    } catch (error) {
        // Fallback to original value
        value = cell.value;
    }

    return value;
};

// Comparison function for quicksort
const compareRows = (rowA: any[], rowB: any[], sortKey: string, sortDirection: number): number => {
    try {
        let leftValue = getCellValue(rowA, sortKey);
        let rightValue = getCellValue(rowB, sortKey);

        // Handle null/undefined/empty values
        if (leftValue === '' && rightValue === '') {
            return 0;
        }
        if (leftValue === '') {
            return -1 * sortDirection; // Empty values go to one end
        }
        if (rightValue === '') {
            return 1 * sortDirection;
        }

        // Both values are numbers
        if (typeof leftValue === 'number' && typeof rightValue === 'number') {
            return (leftValue - rightValue) * sortDirection;
        }

        // Both values are strings or mixed types - convert to strings for consistent comparison
        const leftStr = String(leftValue);
        const rightStr = String(rightValue);

        return leftStr.localeCompare(rightStr) * sortDirection;
    } catch (error) {
        console.log('Error in compareRows:', error);
        return 0;
    }
};

// Quicksort implementation
const quickSort = (arr: any[][], low: number, high: number, sortKey: string, sortDirection: number): void => {
    try {
        if (low < high) {
            const randomIndex = Math.floor(Math.random() * (high - low + 1)) + low;
            [arr[randomIndex], arr[high]] = [arr[high], arr[randomIndex]];

            const pivotIndex = partition(arr, low, high, sortKey, sortDirection);
            quickSort(arr, low, pivotIndex - 1, sortKey, sortDirection);
            quickSort(arr, pivotIndex + 1, high, sortKey, sortDirection);
        }
    } catch (error) {
        // console.log('Error in quickSort:', error);
    }
};

// Partition function for quicksort
const partition = (arr: any[][], low: number, high: number, sortKey: string, sortDirection: number): number => {
    try {
        const pivot = arr[high];
        let i = low - 1;

        for (let j = low; j < high; j++) {
            if (compareRows(arr[j], pivot, sortKey, sortDirection) <= 0) {
                i++;
                [arr[i], arr[j]] = [arr[j], arr[i]];
            }
        }

        [arr[i + 1], arr[high]] = [arr[high], arr[i + 1]];
        return i + 1;
    } catch (error) {
        console.log('Error in partition:', error);
        return random(low, high);
    }
};

const performSort = (message: SortMessage): SortResult => {
    const { finalData, sortKey, sortDirection, config } = message.data;

    // Create a deep copy to avoid mutating the original data
    const sortedData = JSON.parse(JSON.stringify(finalData));

    if (sortKey !== '') {
        const startTime = new Date().getTime();
        // Column-based sorting using quicksort
        quickSort(sortedData, 0, sortedData.length - 1, sortKey, sortDirection);
        const endTime = new Date().getTime();
        console.log(`Sorting took ${endTime - startTime} ms`);
    } else {
        // Priority-based sorting
        const YELLOW = 'rgba(245, 218, 42, 0.7)';
        const RED = 'rgba(255, 0, 0, 0.7)';

        // Default priority from the USDM
        const priorityColumn1 = 'days_left';
        const priorityColumn2 = 'current_stock_balance';
        const priorityColumn3 = 'netflow';

        // This is Threshold Sorting priority in the UI
        const listSorting = config?.listSorting?.split(',')?.filter((e: string) => e) || [];
        const columnNames = config?.columns?.map((c: any) => c.name);

        // If users do not set any priorities, follow USDM
        if (!listSorting.length) {
            if (columnNames?.includes(priorityColumn1)) listSorting.push(priorityColumn1);
            if (columnNames?.includes(priorityColumn2)) listSorting.push(priorityColumn2);
            if (columnNames?.includes(priorityColumn3)) listSorting.push(priorityColumn3);
        }

        const columnStatus: Record<string, { hasRed: boolean; hasYellow: boolean }> = {};
        // Initialize column statuses
        for (const row of sortedData) {
            for (const cell of row) {
                const column = cell.column;
                if (!columnStatus[column]) columnStatus[column] = { hasRed: false, hasYellow: false };
                if (cell.style.includes(RED)) columnStatus[column].hasRed = true;
                if (cell.style.includes(YELLOW)) columnStatus[column].hasYellow = true;
            }
        }

        // Sort columns based on priority, color > settings
        const sortedColumns = Object.keys(columnStatus).sort((colA, colB) => {
            const a = columnStatus[colA];
            const b = columnStatus[colB];
            const newList = [...listSorting].reverse();

            let priorityA = 0;
            if (colA === priorityColumn1 && (a.hasRed || a.hasYellow)) priorityA = 3;
            if (colA === priorityColumn2 && (a.hasRed || a.hasYellow)) priorityA = 2;
            if (colA === priorityColumn3 && (a.hasRed || a.hasYellow)) priorityA = 1;
            let priorityB = 0;
            if (colB === priorityColumn1 && (b.hasRed || b.hasYellow)) priorityB = 3;
            if (colB === priorityColumn2 && (b.hasRed || b.hasYellow)) priorityB = 2;
            if (colB === priorityColumn3 && (b.hasRed || b.hasYellow)) priorityB = 1;
            if (priorityA !== 0 || priorityB !== 0) return priorityB - priorityA;

            if (a.hasRed && b.hasRed) {
                const idxA = newList.findIndex((l: string) => l === colA);
                const idxB = newList.findIndex((l: string) => l === colB);
                return idxB - idxA;
            }
            if (a.hasRed && !b.hasRed) return -1;
            if (!a.hasRed && b.hasRed) return 1;

            if (a.hasYellow && b.hasYellow) {
                const idxA = newList.findIndex((l: string) => l === colA);
                const idxB = newList.findIndex((l: string) => l === colB);
                return idxB - idxA;
            }
            if (a.hasYellow && !b.hasYellow) return -1;
            if (!a.hasYellow && b.hasYellow) return 1;

            if (!a.hasRed && !b.hasRed && !a.hasYellow && !b.hasYellow) {
                const idxA = newList.findIndex((l: string) => l === colA);
                const idxB = newList.findIndex((l: string) => l === colB);
                return idxB - idxA;
            }

            return 0;
        });

        const prioritySort = (rowA: any[], rowB: any[], priorityColumn: string) => {
            const aItem = rowA.find((item: any) => item.column === priorityColumn);
            const bItem = rowB.find((item: any) => item.column === priorityColumn);

            const aValue = parseFloat(aItem?.value) || 0;
            const bValue = parseFloat(bItem?.value) || 0;

            const aIsRed = aItem?.style?.includes(RED);
            const bIsRed = bItem?.style.includes(RED);

            if (aIsRed && !bIsRed) return -1;
            if (!aIsRed && bIsRed) return 1;
            if (aIsRed && bIsRed) {
                if (aValue < bValue) return -1;
                if (aValue > bValue) return 1;
            }

            const aIsYellow = aItem?.style?.includes(YELLOW);
            const bIsYellow = bItem?.style?.includes(YELLOW);

            if (aIsYellow && !bIsYellow) return -1;
            if (!aIsYellow && bIsYellow) return 1;
            if (aIsYellow && bIsYellow) {
                if (aValue < bValue) return -1;
                if (aValue > bValue) return 1;
            }

            return undefined;
        };

        sortedData?.sort((rowA: any[], rowB: any[]) => {
            let res;
            if (sortedColumns?.includes(priorityColumn1)) {
                res = prioritySort(rowA, rowB, priorityColumn1);
                if (res !== undefined) return res;
            }
            if (sortedColumns?.includes(priorityColumn2)) {
                res = prioritySort(rowA, rowB, priorityColumn2);
                if (res !== undefined) return res;
            }
            if (sortedColumns?.includes(priorityColumn3)) {
                res = prioritySort(rowA, rowB, priorityColumn3);
                if (res !== undefined) return res;
            }

            for (let column of sortedColumns) {
                const aItem = rowA.find((item: any) => item.column === column);
                const bItem = rowB.find((item: any) => item.column === column);

                const aValue = parseFloat(aItem?.value) || 0;
                const bValue = parseFloat(bItem?.value) || 0;

                const aIsRed = aItem?.style?.includes(RED);
                const bIsRed = bItem?.style.includes(RED);

                if (aIsRed && !bIsRed) return -1;
                if (!aIsRed && bIsRed) return 1;
                if (aIsRed && bIsRed) {
                    if (aValue < bValue) return -1;
                    if (aValue > bValue) return 1;
                }
            }

            for (let column of sortedColumns) {
                const aItem = rowA.find((item: any) => item.column === column);
                const bItem = rowB.find((item: any) => item.column === column);

                const aValue = parseFloat(aItem?.value) || 0;
                const bValue = parseFloat(bItem?.value) || 0;

                const aIsYellow = aItem?.style?.includes(YELLOW);
                const bIsYellow = bItem?.style?.includes(YELLOW);

                if (aIsYellow && !bIsYellow) return -1;
                if (!aIsYellow && bIsYellow) return 1;
                if (aIsYellow && bIsYellow) {
                    if (aValue < bValue) return -1;
                    if (aValue > bValue) return 1;
                }
            }

            for (let column of sortedColumns) {
                const aItem = rowA.find((item: any) => item.column === column);
                const bItem = rowB.find((item: any) => item.column === column);

                const aValue = parseFloat(aItem?.value) || 0;
                const bValue = parseFloat(bItem?.value) || 0;

                if (aValue < bValue) return -1;
                if (aValue > bValue) return 1;
            }

            return 0;
        });
    }

    return {
        type: 'sortComplete',
        data: sortedData,
    };
};

// Handle messages from the main thread
self.addEventListener('message', (event: MessageEvent<SortMessage>) => {
    if (event.data.type === 'sort') {
        const result = performSort(event.data);
        self.postMessage(result);
    }
});

export {};
