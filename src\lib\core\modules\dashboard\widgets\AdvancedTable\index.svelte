<script lang="ts" context="module">
    //Components
    //Utils
    import { logger } from '$lib/stores/Logger';
    import {
        faTable,
        faCircleChevronLeft,
        faCircleChevronRight,
        faGear,
        faSortUp,
        faSortDown,
        faSearch,
        faFilter,
        faRemove,
        faSpinner,
    } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';

    //svelte
    import { get, writable, type Unsubscriber } from 'svelte/store';

    //Graphql
    import { gqlClientStore } from '$lib/stores/Clients';

    import { icon } from '@fortawesome/fontawesome-svg-core';

    import { type AdvancedTableConfig } from './WidgetConfig/index.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);

    export let id;
    export const widgetIcon = faTable;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetTitle = 'Advanced Table';
    export const widgetEnabled = true;
    export const widgetMinWidth = 5;
    export const widgetMinHeight = 5;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import {
        Heading,
        Table,
        TableBody,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Pagination as PaginationComponent,
        TableBodyCell,
        Button,
        Tooltip,
        Modal,
        Input,
        ButtonGroup,
        InputAddon,
        Spinner,
    } from 'flowbite-svelte';
    import { subscriptionStore, type AnyVariables, type OperationResultStore, type Pausable } from '@urql/svelte';
    import TableHeaderContextMenu from '$lib/core/components/ContextMenu/TableHeaderContextMenu.svelte';
    import CKEditor from 'ckeditor5-svelte';
    import { onMount } from 'svelte';
    import {
        type GetCfgUserFilteringStreamingSubscriptionSubscription,
        type GetCfgUserFilteringUserNullStreamingSubscription,
        type GetCfgParameterCommonSubscription,
        GetCfgParameterCommonDocument,
        GetCfgUserFilteringStreamingSubscriptionDocument,
    } from '$generated-types';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import { user } from '$lib/stores/Auth';
    import CfgParameterModal from '$lib/shared/components/cfg-parameter/CfgParameterModal.svelte';
    import CfgParameterViewer from '$lib/shared/components/cfg-parameter/CfgParameterViewer.svelte';
    import type { CreateCfgUserFilteringDto, UpdateCfgUserFilteringDto } from '$lib/shared/dto/cfg_user_filtering';
    import type { CfgUserFiltering } from '$lib/shared/types/cfgUserFiltering';
    import type { Pagination } from '$lib/types/pagination';
    import { CFG_PARAMETER_NAMES, SOC_CFG_POPUP_INFO, SYS_DATA_SOURCE_MAIN_CATEGORY } from '$lib/shared/enum/general';
    import { type SysDataSource } from '$lib/shared/types/sys_datasource';
    import { searchBool, searchDate, searchNumber, searchString } from '$lib/shared/util/advancedTable';
    import {
        AdvancedSearchLogic,
        BooleanComparisonOperator,
        DateComparisonOperator,
        NumberComparisonOperator,
        StringComparisonOperator,
    } from '$lib/shared/enum/search';
    import {
        DROPDOWN_CLASS,
        formatDateToClientTimezone,
        getCurrentTimezoneOffset,
        ISO_REGEX,
        LABEL_CLASS,
        NORMAL_DATE_REGEX,
    } from '../utils';
    import Dropdown from '$lib/shared/components/dropdown/Dropdown.svelte';
    import type { DropdownOption } from '$lib/shared/types/base';
    import { exportCsv } from '$lib/shared/util/csv';
    import { isNumberString } from '$lib/utils';
    import { locations } from '$lib/stores/Locations';
    import { standardizedFilterLabel, getToggleStatus, mappingDataByKeys } from '../../utils/utils';
    import { convertDateMonthQuarterYearToDateString } from '$lib/core/modules/DynamicForm/utils';

    export let config: AdvancedTableConfig;
    export let id;
    export let sendDataToDashboard;

    let editorConfig = {
        fontSize: {
            options: [9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36],
        },
        fontColor: { options: ['white'] },
        toolbar: {
            items: ['fontSize', '|', 'fontFamily', 'bold', 'italic', 'underline', '|', 'alignment', 'fontColor'],
        },
    };

    let compareStringOptions = [
        { value: StringComparisonOperator.Is, name: 'Is', label: 'Is' },
        { value: StringComparisonOperator.Begins, name: 'Begins', label: 'Begins' },
        { value: StringComparisonOperator.Contains, name: 'Contains', label: 'Contains' },
        { value: StringComparisonOperator.Ends, name: 'Ends', label: 'Ends' },
    ];

    let sortTime = 0;

    let compareNumberOptions = [
        {
            value: NumberComparisonOperator.Equals,
            name: NumberComparisonOperator.Equals,
            label: NumberComparisonOperator.Equals,
        },
        {
            value: NumberComparisonOperator.Greater,
            name: NumberComparisonOperator.Greater,
            label: NumberComparisonOperator.Greater,
        },
        {
            value: NumberComparisonOperator.Less,
            name: NumberComparisonOperator.Less,
            label: NumberComparisonOperator.Less,
        },
        {
            value: NumberComparisonOperator.GTE,
            name: NumberComparisonOperator.GTE,
            label: NumberComparisonOperator.GTE,
        },
        {
            value: NumberComparisonOperator.LTE,
            name: NumberComparisonOperator.LTE,
            label: NumberComparisonOperator.LTE,
        },
    ];

    let compareDateOptions = [
        { value: DateComparisonOperator.Is, name: 'Is', label: 'Is' },
        { value: DateComparisonOperator.Before, name: 'Before', label: 'Before' },
        { value: DateComparisonOperator.Since, name: 'Since', label: 'Since' },
        { value: DateComparisonOperator.Between, name: 'Between', label: 'Between' },
    ];

    let compareBoolOptions = [
        {
            value: BooleanComparisonOperator.Is,
            name: BooleanComparisonOperator.Is,
            label: BooleanComparisonOperator.Is,
        },
        {
            value: BooleanComparisonOperator.IsNot,
            name: BooleanComparisonOperator.IsNot,
            label: BooleanComparisonOperator.IsNot,
        },
    ];

    let filterTags = [];
    let editor: unknown;

    const ASC: number = 1;

    let page = 0;
    let limit = parseInt(config.numberOfRows, 10);
    let helper = { start: 1, end: 10, total: 100 };
    let borderStyle = '';

    const sortKey = writable('');
    const sortDirection = writable(ASC);
    const sortItems = writable([]);
    const sumItems = writable({});
    let resizingIndex = -1;
    let stylingIndex = -1;
    let contextMenuID = '';
    let expanding = false;
    let start = 0;
    let headerCurrentWidth = 0;
    let x = 0;
    let direction = '';
    let openEditor = false;
    let user_id = '';
    let tenant_id = '';
    let openFilter = false;
    let formModal;
    let data: Record<string, string> = {};
    export let json_values;

    const client = get(gqlClientStore);
    let finalData: { column: string; value: string; style: string }[][] = [];
    let header: { column: string; value: string; style: string; compareOption?: string; compareText?: string }[] = [];
    let originData;

    const buildWidthHeader = () => {
        setTimeout(() => {
            let toShowColumns = header.map(e => e.column);
            try {
                toShowColumns.forEach((e, index) => {
                    const headCell = document.getElementById(`hd-${index}`);
                    if (maxHeight < headCell.scrollHeight) {
                        maxHeight = headCell.scrollHeight + 10;
                    }
                });
            } catch (error) {}
        }, 300);
    };
    $: if (json_values) {
        if (!json_values.length) {
            json_values = [[]];
        }
        finalData = JSON.parse(JSON.stringify(json_values));
        header = finalData.shift();
        const dataColumns = finalData?.[0]?.map(row => row.column);
        header = dataColumns?.length ? header?.filter(h => dataColumns?.includes(h.column)) : header;
        originData = JSON.parse(JSON.stringify(finalData));
        buildWidthHeader();

        json_values = null;
    }

    $: if (finalData && finalData.length > 0) {
        helper.total = finalData.length;
        helper.start = page * limit + 1;
        helper.end = page * limit + limit;
        if (helper.end > finalData.length) {
            helper.end = finalData.length;
        }
    }

    const onReady = ({ detail: editor }) => {
        // Insert the toolbar before the editable area.
        editorInstance = editor;
        editor.ui
            .getEditableElement()
            .parentElement.insertBefore(editor.ui.view.toolbar.element, editor.ui.getEditableElement());
    };

    const previous = () => {
        if (page - 1 > -1) {
            page--;
        }
        // refresh();
    };

    const next = () => {
        if (page + 1 < Math.ceil(helper.total / limit)) {
            page++;
        } else {
            if (config.autoPagination) page = 0;
        }
        // refresh();
    };

    const sortTable = key => {
        if ($sortKey === key) {
            sortDirection.update(val => -val);
        } else {
            sortKey.set(key);
            sortDirection.set(ASC);
        }

        if (sortingWorker) {
            isSorting = true;
            sortingWorker.postMessage({
                type: 'sort',
                data: {
                    finalData,
                    sortKey: key,
                    sortDirection: $sortDirection,
                    config,
                },
            });
        } else {
            // Do nothing if no worker is available
            // performInlineSorting();
        }
    };

    const validateDate = (date, format) => {
        return dayjs(date, format).format(format) === date || ISO_REGEX.test(date);
    };

    $: if (config?.defaultSortingColumn) {
        if (sortTime === 0 && finalData.length > 0) {
            sortKey.set(config?.defaultSortingColumn);
            sortTable(config?.defaultSortingColumn);
            sortTime++;
        }
    }
    $: if (config?.defaultOrder) {
        sortDirection.set(config?.defaultOrder);
    }

    // Web Worker for sorting
    let sortingWorker: Worker | null = null;
    let isSorting = false;

    // Reactive statement to trigger sorting via worker
    const performInlineSorting = () => {
        const key = $sortKey;
        const direction = $sortDirection;
        if (key !== '') {
            for (let i = 0; i < finalData.length - 1; i++) {
                for (let j = i + 1; j < finalData.length; j++) {
                    const leftCell = finalData[i].find(e => e.column === key);
                    const rightCell = finalData[j].find(e => e.column === key);
                    if (!leftCell || !rightCell) continue;

                    let leftValue =
                        typeof leftCell.value === 'string' && !isNaN(Number(leftCell.value))
                            ? parseFloat(leftCell.value)
                            : leftCell.value;
                    let rightValue =
                        typeof rightCell.value === 'string' && !isNaN(Number(rightCell.value))
                            ? parseFloat(rightCell.value)
                            : rightCell.value;
                    try {
                        if (
                            validateDate(leftCell.value, 'MM/DD/YYYY HH:mm:ss') ||
                            validateDate(leftCell.value, 'YYYY-MM-DD HH:mm:ss') ||
                            ISO_REGEX.test(leftCell.value)
                        ) {
                            leftValue = dayjs(leftCell.value).valueOf();
                            rightValue = dayjs(rightCell.value).valueOf();
                        }
                    } catch (error) {
                        leftValue =
                            typeof leftCell.value === 'string' && !isNaN(Number(leftCell.value))
                                ? parseFloat(leftCell.value)
                                : leftCell.value;
                        rightValue =
                            typeof rightCell.value === 'string' && !isNaN(Number(rightCell.value))
                                ? parseFloat(rightCell.value)
                                : rightCell.value;
                    }
                    const shouldSwap =
                        (typeof leftValue === 'string' &&
                            typeof rightValue === 'string' &&
                            leftValue.localeCompare(rightValue) * direction > 0) ||
                        (typeof leftValue === 'number' &&
                            typeof rightValue === 'number' &&
                            (leftValue - rightValue) * direction > 0);
                    if (shouldSwap) {
                        [finalData[i], finalData[j]] = [finalData[j], finalData[i]];
                    }
                }
            }
        } else {
            const YELLOW = 'rgba(245, 218, 42, 0.7)';
            const RED = 'rgba(255, 0, 0, 0.7)';

            // Default priority from the USDM
            const priorityColumn1 = 'days_left';
            const priorityColumn2 = 'current_stock_balance';
            const priorityColumn3 = 'netflow';

            // This is Threshold Sorting priority in the UI
            const listSorting = config?.listSorting?.split(',')?.filter(e => e) || [];
            const columnNames = config?.columns?.map(c => c.name);

            // If users do not set any priorities, follow USDM
            if (!listSorting.length) {
                if (columnNames?.includes(priorityColumn1)) listSorting.push(priorityColumn1);
                if (columnNames?.includes(priorityColumn2)) listSorting.push(priorityColumn2);
                if (columnNames?.includes(priorityColumn3)) listSorting.push(priorityColumn3);
            }

            const columnStatus = {};
            // Initialize column statuses
            for (const row of finalData) {
                for (const cell of row) {
                    const column = cell.column;
                    if (!columnStatus[column]) columnStatus[column] = { hasRed: false, hasYellow: false };
                    if (cell.style.includes(RED)) columnStatus[column].hasRed = true;
                    if (cell.style.includes(YELLOW)) columnStatus[column].hasYellow = true;
                }
            }

            // Sort columns based on priority, color > settings
            const sortedColumns = Object.keys(columnStatus).sort((colA, colB) => {
                const a = columnStatus[colA];
                const b = columnStatus[colB];
                const newList = [...listSorting].reverse();

                let priorityA = 0;
                if (colA === priorityColumn1 && (a.hasRed || a.hasYellow)) priorityA = 3;
                if (colA === priorityColumn2 && (a.hasRed || a.hasYellow)) priorityA = 2;
                if (colA === priorityColumn3 && (a.hasRed || a.hasYellow)) priorityA = 1;
                let priorityB = 0;
                if (colB === priorityColumn1 && (b.hasRed || b.hasYellow)) priorityB = 3;
                if (colB === priorityColumn2 && (b.hasRed || b.hasYellow)) priorityB = 2;
                if (colB === priorityColumn3 && (b.hasRed || b.hasYellow)) priorityB = 1;
                if (priorityA !== 0 || priorityB !== 0) return priorityB - priorityA;

                if (a.hasRed && b.hasRed) {
                    const idxA = newList.findIndex(l => l === colA);
                    const idxB = newList.findIndex(l => l === colB);
                    return idxB - idxA;
                }
                if (a.hasRed && !b.hasRed) return -1;
                if (!a.hasRed && b.hasRed) return 1;

                if (a.hasYellow && b.hasYellow) {
                    const idxA = newList.findIndex(l => l === colA);
                    const idxB = newList.findIndex(l => l === colB);
                    return idxB - idxA;
                }
                if (a.hasYellow && !b.hasYellow) return -1;
                if (!a.hasYellow && b.hasYellow) return 1;

                if (!a.hasRed && !b.hasRed && !a.hasYellow && !b.hasYellow) {
                    const idxA = newList.findIndex(l => l === colA);
                    const idxB = newList.findIndex(l => l === colB);
                    return idxB - idxA;
                }

                return 0;
            });

            const prioritySort = (rowA, rowB, priorityColumn) => {
                const aItem = rowA.find(item => item.column === priorityColumn);
                const bItem = rowB.find(item => item.column === priorityColumn);

                const aValue = parseFloat(aItem?.value) || 0;
                const bValue = parseFloat(bItem?.value) || 0;

                const aIsRed = aItem?.style?.includes(RED);
                const bIsRed = bItem?.style.includes(RED);

                if (aIsRed && !bIsRed) return -1;
                if (!aIsRed && bIsRed) return 1;
                if (aIsRed && bIsRed) {
                    if (aValue < bValue) return -1;
                    if (aValue > bValue) return 1;
                }

                const aIsYellow = aItem?.style?.includes(YELLOW);
                const bIsYellow = bItem?.style?.includes(YELLOW);

                if (aIsYellow && !bIsYellow) return -1;
                if (!aIsYellow && bIsYellow) return 1;
                if (aIsYellow && bIsYellow) {
                    if (aValue < bValue) return -1;
                    if (aValue > bValue) return 1;
                }

                return;
            };

            finalData?.sort((rowA, rowB) => {
                let res;
                if (sortedColumns?.includes(priorityColumn1)) {
                    res = prioritySort(rowA, rowB, priorityColumn1);
                    if (res !== undefined) return res;
                }
                if (sortedColumns?.includes(priorityColumn2)) {
                    res = prioritySort(rowA, rowB, priorityColumn2);
                    if (res !== undefined) return res;
                }
                if (sortedColumns?.includes(priorityColumn3)) {
                    res = prioritySort(rowA, rowB, priorityColumn3);
                    if (res !== undefined) return res;
                }

                for (let column of sortedColumns) {
                    const aItem = rowA.find(item => item.column === column);
                    const bItem = rowB.find(item => item.column === column);

                    const aValue = parseFloat(aItem?.value) || 0;
                    const bValue = parseFloat(bItem?.value) || 0;

                    const aIsRed = aItem?.style?.includes(RED);
                    const bIsRed = bItem?.style.includes(RED);

                    if (aIsRed && !bIsRed) return -1;
                    if (!aIsRed && bIsRed) return 1;
                    if (aIsRed && bIsRed) {
                        if (aValue < bValue) return -1;
                        if (aValue > bValue) return 1;
                    }
                }

                for (let column of sortedColumns) {
                    const aItem = rowA.find(item => item.column === column);
                    const bItem = rowB.find(item => item.column === column);

                    const aValue = parseFloat(aItem?.value) || 0;
                    const bValue = parseFloat(bItem?.value) || 0;

                    const aIsYellow = aItem?.style?.includes(YELLOW);
                    const bIsYellow = bItem?.style?.includes(YELLOW);

                    if (aIsYellow && !bIsYellow) return -1;
                    if (!aIsYellow && bIsYellow) return 1;
                    if (aIsYellow && bIsYellow) {
                        if (aValue < bValue) return -1;
                        if (aValue > bValue) return 1;
                    }
                }

                for (let column of sortedColumns) {
                    const aItem = rowA.find(item => item.column === column);
                    const bItem = rowB.find(item => item.column === column);

                    const aValue = parseFloat(aItem?.value) || 0;
                    const bValue = parseFloat(bItem?.value) || 0;

                    if (aValue < bValue) return -1;
                    if (aValue > bValue) return 1;
                }

                return 0;
            });
        }
    };

    $: if (config.gridVisible) {
        const thickness = config.borderThickness ? config.borderThickness : 1;
        if (thickness === 1) borderStyle = `border border-gray-600`;
        if (thickness === 2) borderStyle = `border-2 border-gray-600`;
        if (thickness === 3) borderStyle = `border-4 border-gray-600`;
        if (thickness === 4) borderStyle = `border-8 border-gray-600`;
    } else {
        borderStyle = '';
    }

    let interval;
    let autoPaginationRunning = false;
    $: if (config.autoPagination && finalData.length && !autoPaginationRunning) {
        interval = setInterval(next, config.paginationPeriod * 1000 || 3000);
        autoPaginationRunning = true;
    } else if (!config.autoPagination && autoPaginationRunning) {
        if (interval) {
            clearInterval(interval);
            autoPaginationRunning = false;
        }
    }

    const onGearClick = () => {
        openFilter = true;
    };

    const swap = (indexA: number, indexB: number) => {
        const temp = config?.columns[indexA];
        (config as any).columns[indexA] = config?.columns[indexB];
        (config as any).columns[indexB] = temp;
    };

    const processHeaderAction = (actionName: string, headerIndex: number) => {
        if (actionName === 'resize' && headerIndex !== resizingIndex) {
            resizingIndex = headerIndex;
        } else {
            resizingIndex = -1;
        }

        if (actionName === 'move-left') {
            swap(headerIndex, headerIndex - 1);
        } else if (actionName === 'move-right') {
            swap(headerIndex, headerIndex + 1);
        } else if (actionName === 'edit-style') {
            stylingIndex = headerIndex;
            openEditor = true;
        }
    };

    const startExpand = (
        e: MouseEvent & {
            currentTarget: EventTarget & HTMLButtonElement;
        },
        directionInput: string
    ) => {
        expanding = true;
        start = e.clientX;
        const parent = document.getElementById('table-main');
        parent.addEventListener('mouseleave', e => e.preventDefault());
        direction = directionInput;
        if (directionInput === 'left') {
            headerCurrentWidth = document.getElementById(`hd-${resizingIndex - 1}`).clientWidth;
            parent.addEventListener('mousemove', expandLeft);
        } else {
            headerCurrentWidth = document.getElementById(`hd-${resizingIndex}`).clientWidth;
            parent.addEventListener('mousemove', expandRight);
            document.getElementById(`hd-${resizingIndex}-right`).removeEventListener('mouseleave', e => console.log(e));
        }
        x = 0;
    };

    const stopExpand = () => {
        expanding = false;
        x = 0;
        start = 0;
    };

    const expandLeft = (
        e: MouseEvent & {
            currentTarget: EventTarget & HTMLButtonElement;
        }
    ) => {
        if (!expanding) {
            return;
        }
        const delta = e.pageX - start;
        document.getElementById(`hd-${resizingIndex - 1}`).style.width = `${(headerCurrentWidth + delta).toString()}px`;
        x = -delta;
        return;
    };

    const expandRight = (
        e: MouseEvent & {
            currentTarget: EventTarget & HTMLButtonElement;
        }
    ) => {
        if (!expanding) {
            return;
        }
        const delta = e.clientX - start;
        document.getElementById(`hd-${resizingIndex}`).style.width = `${(headerCurrentWidth + delta).toString()}px`;
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
        return;
    };

    let cfg_params: OperationResultStore<GetCfgParameterCommonSubscription, AnyVariables> & Pausable;
    let cfg_user_filtering: OperationResultStore<GetCfgUserFilteringStreamingSubscriptionSubscription, AnyVariables> &
        Pausable;
    let isUserFilteringInitialized: boolean = false;
    let maxHeight = 0;

    let isModalOpen: boolean = false;
    let modalMessage: string = '';
    let filteringLabel: {
        View_By: string;
        Compared_Period: string;
        Compared_Incident: string;
    } = {
        View_By: '',
        Compared_Period: '-',
        Compared_Incident: '-',
    };

    let ODSColumns: { name?: string; type: string }[] = [];

    const getListAggDatasource = async () => {
        const result = await fetch(
            `/api/SysDataSourceService?main_category=${SYS_DATA_SOURCE_MAIN_CATEGORY.AGGREGATED_DATA_SOURCE}`
        );

        let sysData: SysDataSource[] = [];
        const { res } = await result.json();
        sysData = res?.data ?? [];
        const ADSFound = sysData.find(e => e.ID === config.dataSource);
        if (!ADSFound) return;
        const jsonValue = JSON.parse(ADSFound.Json_Values);
        if (jsonValue[0] && jsonValue[0].dataSource) {
            const ODS = sysData.find(e => e.ID === jsonValue[0].dataSource);
            if (ODS) {
                const jsonValueODS = JSON.parse(ODS.Json_Values);
                ODSColumns = jsonValueODS.columns;
            }
        }
    };

    onMount(async (): Promise<any> => {
        if (typeof Worker !== 'undefined') {
            sortingWorker = new Worker(new URL('./sorting.worker.ts', import.meta.url), {
                type: 'module',
            });

            sortingWorker.addEventListener('message', event => {
                if (event.data.type === 'sortComplete') {
                    finalData = event.data.data;
                    isSorting = false;
                }
            });
        }

        editor = await import('@ckeditor/ckeditor5-build-decoupled-document/build/ckeditor');
        user_id = get(user).claims.hasura_access['x-hasura-user-id'];
        tenant_id = get(user).claims.active_tenant.tenant_id;
        if (config.cfg_param !== '' || config.cfg_param_label !== '') {
            const names = [CFG_PARAMETER_NAMES.CRITICAL_CONSUMABLE as string];
            if (config.cfg_param) names.push(config.cfg_param);
            if (config.cfg_param_label) names.push(config.cfg_param_label);
            cfg_params = subscriptionStore<GetCfgParameterCommonSubscription>({
                client,
                query: GetCfgParameterCommonDocument,
                variables: { names, tenant_id },
            });
        }

        // get ODS
        getListAggDatasource();

        return () => {
            if (sortingWorker) {
                sortingWorker.terminate();
                sortingWorker = null;
            }
        };
    });

    const getUserFiltering = async () => {
        let cfg_parameter_id = $cfg_params.data.cc3_hoc_cfg_parameter.find(
            i => i.name === config.cfg_param && i.category == 'filtering_config'
        )?.id;
        if (config.cfg_param_label === CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT_LABEL) {
            cfg_parameter_id = $cfg_params.data.cc3_hoc_cfg_parameter.find(
                i => i.name === CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT
            ).id;
        }
        isUserFilteringInitialized = true;
        const cfgUserFilteringResult = await fetch(
            `/api/CfgUserFilteringService?filtering_id=${cfg_parameter_id}&user_id=${user_id}`
        );
        const cfgUserFilterings: Pagination<CfgUserFiltering> = (await cfgUserFilteringResult.json()).res;
        if (cfgUserFilterings?.data?.length > 0) {
            cfg_user_filtering = subscriptionStore<GetCfgUserFilteringUserNullStreamingSubscription>({
                client,
                query: GetCfgUserFilteringStreamingSubscriptionDocument,
                variables: { filtering_id: cfg_parameter_id, user_id },
            });
        }
    };

    $: if ($cfg_params && $cfg_params.data) {
        if (
            config.cfg_param_label === CFG_PARAMETER_NAMES.CRITICAL_CONSUMABLE_LABEL &&
            $cfg_params?.data?.cc3_hoc_cfg_parameter.filter(e => e.category === 'InventoryBackend')[0]?.key_value_json
                .IsEnabled
        ) {
        }
        if (config.cfg_param_label === CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT_LABEL) {
            const cfgConfig = $cfg_params.data?.cc3_hoc_cfg_parameter.find(
                i => i.name === CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT
            )?.key_value_json;
            if (!filteringLabel.View_By) {
                const viewBy = cfgConfig.View_By.split('_')[0];
                filteringLabel.View_By = viewBy[0]?.toUpperCase() + viewBy?.slice(1);
            }
            (filteringLabel.Compared_Period = cfgConfig.Compared_Period.replace(
                'current',
                dayjs().format('YYYY-MM-DD')
            )),
                (filteringLabel.Compared_Incident = cfgConfig.Compared_Incident);
        }
        if (!isUserFilteringInitialized) {
            getUserFiltering();
        }
    }
    $: if ($cfg_user_filtering && $cfg_user_filtering.data) {
        parseFilteringLabels();
    }
    $: {
        if ($cfg_params?.data?.cc3_hoc_cfg_parameter) {
            let needUpdate = $cfg_params.data.cc3_hoc_cfg_parameter.find(
                i =>
                    (i.name === CFG_PARAMETER_NAMES.CRITICAL_CONSUMABLE_LABEL ||
                        i.name === CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT) &&
                    i.category === 'filtering_label'
            )?.key_value_json;

            if (needUpdate) {
                needUpdate['Compared_Period'] = needUpdate.Compared_Period.replace(
                    'current',
                    dayjs().format('YYYY-MM-DD')
                );
            }
        }
    }

    const parseFilteringLabels = () => {
        const cfgConfig = $cfg_params.data?.cc3_hoc_cfg_parameter.find(
            i => i.name === CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT
        )?.key_value_json;
        if (!cfgConfig) {
            return;
        }
        const filteringConfig = $cfg_user_filtering.data?.cc3_hoc_cfg_user_filtering[0]?.key_value_json;
        const viewBy = filteringConfig['widget_list_display'].split('_')[0] === 'consumable' ? 'consumable' : 'drug';

        filteringLabel.View_By = viewBy[0]?.toUpperCase() + viewBy?.slice(1);
    };

    const cfgSave = async data => {
        let newData = JSON.parse(
            JSON.stringify({ ...data, TimeZone: getCurrentTimezoneOffset() }).replaceAll('T00:00:00.000Z', '')
        );
        let newFilterLabel = JSON.parse(JSON.stringify(data).replaceAll('T00:00:00.000Z', ''));

        // Specific for SOC User Story, perhaps there is a better way to handle if it's SOC US later
        let isSocUs = false;
        const socFilterLabel = structuredClone(newFilterLabel);
        for (const key of Object.keys(socFilterLabel)) {
            const info = SOC_CFG_POPUP_INFO.find(info => info.key.toLowerCase() === key.toLowerCase());
            if (info?.key) {
                isSocUs = true;
                const toggleStatus = await getToggleStatus(info.cfgName);
                // handle for 1 toggle buttons, if >=2 => update here
                const isOn = toggleStatus ? Object.values(toggleStatus)?.[0] || false : false;
                const newValue = await mappingDataByKeys(socFilterLabel[key], info, isOn);
                socFilterLabel[key] = newValue[key]?.length ? newValue[key].join(', ') : socFilterLabel[key];
            }
        }
        // End specific

        const cfgParam = $cfg_params.data.cc3_hoc_cfg_parameter.find(
            i => i.name === config.cfg_param && i.category == 'filtering_config'
        );

        const cfg_parameter_id = cfgParam?.id;
        const cfgUserFilteringResult = await fetch(
            `/api/CfgUserFilteringService?filtering_id=${cfg_parameter_id}&user_id=${user_id}`
        );

        const cfgUserFilterings: Pagination<CfgUserFiltering> = (await cfgUserFilteringResult.json()).res;
        const dto: CreateCfgUserFilteringDto = {
            filter_label: JSON.stringify(data),
            filtering_id: cfg_parameter_id,
            user_id: user_id,
            key_value_json: JSON.stringify(newData),
        };
        let res;
        if (cfgUserFilterings.data.length === 0) {
            if (isSocUs) {
                dto.filter_label = JSON.stringify(standardizedFilterLabel(socFilterLabel, true));
                const keyValueJson = JSON.parse(dto.key_value_json);
                dto.key_value_json = JSON.stringify(convertDateMonthQuarterYearToDateString(keyValueJson, true));
            }
            res = await fetch(`/api/CfgUserFilteringService/upsert`, {
                method: 'POST',
                body: JSON.stringify(dto),
            });
            if (res.ok) {
                cfg_user_filtering = subscriptionStore<GetCfgUserFilteringUserNullStreamingSubscription>({
                    client,
                    query: GetCfgUserFilteringStreamingSubscriptionDocument,
                    variables: { filtering_id: cfg_parameter_id, user_id },
                });
            }
        } else {
            const dto: UpdateCfgUserFilteringDto = {
                filter_label: JSON.stringify(newFilterLabel),
                key_value_json: JSON.stringify(newData),
                filtering_id: cfg_parameter_id,
                user_id,
            };
            if (isSocUs) {
                dto.filter_label = JSON.stringify(standardizedFilterLabel(socFilterLabel, true));
                const keyValueJson = JSON.parse(dto.key_value_json);
                dto.key_value_json = JSON.stringify(convertDateMonthQuarterYearToDateString(keyValueJson, true));
            }
            res = await fetch(`/api/CfgUserFilteringService/${cfgUserFilterings.data[0].id}`, {
                method: 'PATCH',
                body: JSON.stringify(dto),
            });
        }

        if (res.ok) {
            modalMessage = 'Configuration is saved successfully.';
            isModalOpen = true;
            openFilter = false;

            sendDataToDashboard({ cfgParamId: cfgParam?.id, cfgParamName: cfgParam?.name });
        } else {
            modalMessage = 'Unable to save configuration successfully.';
            isModalOpen = true;
        }
    };

    let popoverOpen = false;
    let advancedSearchLogic: AdvancedSearchLogic = AdvancedSearchLogic.All;
    const AdvancedSearchLogics: DropdownOption[] = [
        { label: 'AND', value: AdvancedSearchLogic.All },
        { label: 'OR', value: AdvancedSearchLogic.Any },
    ];

    const onCompareSelectOptionChange = (e: Event, i: number) => {
        const value = (e.target as HTMLSelectElement).value;
        header[i].compareOption = value;
    };

    const onCompareSelectInputChange = (e: Event, i: number) => {
        let value = (e.target as HTMLInputElement).value;
        if (typeof value === 'string') {
            value = value.trim();
        }
        header[i].compareText = value;
    };

    const resetSearchParams = () => {
        header = header.map(e => {
            e.compareOption = '';
            e.compareText = '';
            return e;
        });

        finalData = JSON.parse(JSON.stringify(originData));
    };
    const onApplySearch = () => {
        finalData = JSON.parse(JSON.stringify(originData));
        const searchedFinalData = [];
        for (const h of header) {
            if ((h.compareOption && !h.compareText) || (!h.compareOption && h.compareText)) {
                isModalOpen = true;
                modalMessage = 'Please input the data matched with selected operator(s) to search.';
                return;
            }
            if (h.compareOption && h.compareText) {
                if (
                    [
                        NumberComparisonOperator.Equals,
                        NumberComparisonOperator.GTE,
                        NumberComparisonOperator.LTE,
                        NumberComparisonOperator.Greater,
                        NumberComparisonOperator.Less,
                    ].includes(h.compareOption as NumberComparisonOperator) &&
                    !isNumberString(h.compareText)
                ) {
                    isModalOpen = true;
                    modalMessage = 'Please input the data matched with selected operator(s) to search.';
                    return;
                }
                let tagValue = `${h.value} ${h.compareOption} ${h.compareText}`;
                let tagId = h.column;
                if (!filterTags.find(item => item.id === tagId))
                    filterTags = [...filterTags, { id: tagId, value: tagValue }];
            }
        }
        finalData.forEach(row => {
            let inserted = false;
            let andColumns = row.filter(cell => {
                const head = header.find(h => h.column === cell.column);
                if (head.compareOption && head.compareText) {
                    return true;
                }
            }).length;
            let numberOfAndColumnAccepted = 0;
            row.forEach(cell => {
                if (inserted) return;

                const head = header.find(h => h.column === cell.column);
                let column = ODSColumns.find(c => c.name === cell.column);
                if (!head.compareOption || !head.compareText) {
                    return;
                }
                if (!column) {
                    column = { type: 'number' };
                }
                let result = false;

                switch (column.type) {
                    case 'string':
                        result = searchString(cell.value, head.compareText, head.compareOption);
                        break;
                    case 'number':
                        result = searchNumber(Number(cell.value), Number(head.compareText), head.compareOption);
                        break;
                    case 'date':
                        result = searchDate(new Date(cell.value), new Date(head.compareText), head.compareOption);
                        break;
                    case 'boolean':
                        result = searchBool(Boolean(cell.value), Boolean(head.compareText), head.compareOption);
                        break;
                }

                if (result) {
                    if (advancedSearchLogic === AdvancedSearchLogic.Any) {
                        searchedFinalData.push(row);
                        inserted = true;
                    } else {
                        numberOfAndColumnAccepted += 1;
                    }
                }
            });

            if (advancedSearchLogic === AdvancedSearchLogic.All && numberOfAndColumnAccepted === andColumns) {
                searchedFinalData.push(row);
            }
        });

        finalData = [...searchedFinalData];
        popoverOpen = false;
    };

    const toggleSearch = () => {
        popoverOpen = !popoverOpen;
    };

    const getCompareOptions = (column: string) => {
        let foundODSColumn = ODSColumns.find(c => c.name === column);
        if (!foundODSColumn) {
            foundODSColumn = { type: 'number' };
        }
        switch (foundODSColumn.type) {
            case 'string':
                return compareStringOptions;
            case 'number':
                return compareNumberOptions;
            case 'date':
                return compareDateOptions;
            case 'boolean':
                return compareBoolOptions;
            default:
                return compareStringOptions;
        }
    };

    const onFullColSearch = (e: Event) => {
        const searchText = (e.target as HTMLInputElement).value;
        finalData = JSON.parse(JSON.stringify(originData));

        if (searchText === '') {
            return;
        }

        const searchedFinalData = [];
        finalData.forEach(row => {
            let inserted = false;
            row.forEach(cell => {
                if (inserted) return;

                if (searchText !== '') {
                    if (cell.value.toLowerCase().includes(searchText.toLowerCase())) {
                        searchedFinalData.push(row);
                        inserted = true;
                        return;
                    }
                }
            });
        });

        finalData = [...searchedFinalData];
    };

    const downloadCsv = async () => {
        if (config.customDownload) {
            try {
                const res = await fetch('/api/advanced-table', {
                    method: 'POST',
                    body: JSON.stringify({
                        endpoint: config.downloadEndpoint,
                        method: 'POST',
                        body: {},
                    }),
                });

                if (res.ok) {
                    const blob = await res.blob();
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = config.title;
                    link.click();
                }
            } catch (error) {}
        } else {
            await exportCsv(header, originData, '', config.title, config.title);
        }
    };

    const handleRemoveTag = tagId => {
        filterTags = filterTags.filter(t => t.id !== tagId);
        header = header.map(h => {
            if (h.column === tagId) {
                h.compareOption = '';
                h.compareText = '';
            }
            return h;
        });
        onApplySearch();
    };
    $: getCellStyle = (currentStyle: string, header: string, value: string) => {
        if (!config.comparisonThresholds) {
            return currentStyle;
        }
        let style = currentStyle;
        config.comparisonThresholds.forEach(threshold => {
            if (threshold.column === header) {
                let c = ODSColumns.find(c => c.name === header);
                if (threshold.value !== '' && c) {
                    let result = false;
                    if (!c) {
                        c = {
                            type: 'number',
                        };
                    }

                    switch (c.type) {
                        case 'string':
                            result = searchString(value, threshold.value, threshold.comparator);
                            break;
                        case 'number':
                            result = searchNumber(Number(value), Number(threshold.value), threshold.comparator);
                            break;
                        case 'date':
                            result = searchDate(new Date(value), new Date(threshold.value), threshold.comparator);
                            break;
                        case 'boolean':
                            result = searchBool(Boolean(value), Boolean(threshold.value), threshold.comparator);
                            break;
                    }

                    if (result) {
                        style = `background-color: ${threshold.color}`;
                    }
                }
            }
        });
        return style;
    };

    const getODSColumnType = (type?: string) => {
        if (!type) {
            return 'number';
        }
        if (type === 'date') {
            return 'date';
        } else if (type === 'string') {
            return 'text';
        } else if (type === 'number') {
            return 'number';
        }
    };
</script>

<svelte:window on:mouseup={stopExpand} />

<div class="flex flex-col gap-2 p-3">
    <div class="group group flex justify-between">
        <div class="flex gap-2">
            <Heading tag="h6">{config?.title}</Heading>
            {#if isSorting}
                <Spinner color="gray" />
            {/if}
        </div>
        {#if config.showFiltering && !config.useParentFilter}
            <Button size="md" on:click={onGearClick} class="opacity-0 duration-300 group-hover:opacity-100">
                <span>
                    {@html icon(faGear).html}
                </span>
                <Tooltip placement="left" class="z-50">
                    {`Filter configuration`}</Tooltip>
            </Button>
        {/if}
    </div>
    <div
        class={`flex flex-wrap ${
            config.searchEnable || config.downloadEnable
                ? 'items-end justify-between gap-2'
                : `justify-${config.filterLabelAlignment}`
        }`}>
        <div class="relative flex flex-nowrap gap-2">
            {#if config.searchEnable}
                <ButtonGroup class="w-full">
                    <Input
                        size="md"
                        id="input-addon"
                        type="text"
                        placeholder="Search"
                        on:input={onFullColSearch}
                        class="!rounded-e-none rounded-l-lg rounded-r-none !border-r-transparent">
                        <div slot="left">
                            {@html icon(faSearch).html}
                        </div>
                    </Input>
                    <InputAddon size="md" id="searchFilter" class="border-[0.5px] !border-gray-600 !bg-gray-800">
                        <button on:click={toggleSearch} class="px-2 text-white">
                            {@html icon(faFilter).html}
                        </button>
                    </InputAddon>
                </ButtonGroup>
                {#if popoverOpen}
                    <div
                        class="tooltip !absolute top-[100%] z-[13] mt-3 h-auto w-[700px] overflow-auto rounded-lg border-gray-100 bg-white p-4 text-gray-700 shadow-md dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200">
                        <div class="flex flex-col gap-5">
                            <div class="flex justify-between">
                                <Heading tag="h5" class="text-left">Advanced Search</Heading>
                                <Dropdown
                                    bind:value={advancedSearchLogic}
                                    enableSearch={false}
                                    options={AdvancedSearchLogics}
                                    setClass={`${DROPDOWN_CLASS} w-[120px]`}
                                    setLabelClass={LABEL_CLASS} />
                            </div>
                            <div class="grid grid-cols-12 gap-3">
                                {#each header as headItem, i}
                                    <div class="col-span-12 my-auto text-right capitalize !text-white md:col-span-3">
                                        {headItem.value}
                                    </div>
                                    <div class="col-span-12 md:col-span-3">
                                        <div>
                                            <Dropdown
                                                bind:value={header[i].compareOption}
                                                enableSearch={false}
                                                options={getCompareOptions(headItem.column)}
                                                setClass={DROPDOWN_CLASS}
                                                setLabelClass={LABEL_CLASS} />
                                            <!-- <Select
                                                placeholder=""
                                                items={getCompareOptions(headItem.column)}
                                                value={headItem.compareOption ?? ''}
                                                on:change={e => onCompareSelectOptionChange(e, i)} /> -->
                                        </div>
                                    </div>
                                    <div class="col-span-12 flex md:col-span-6">
                                        <Input
                                            size="md"
                                            type={getODSColumnType(
                                                ODSColumns.find(c => c.name === headItem.column)?.type
                                            )}
                                            placeholder=""
                                            value={headItem.compareText}
                                            on:change={e => onCompareSelectInputChange(e, i)} />
                                    </div>
                                {/each}
                                <div class="col-span-12">
                                    <div class="flex justify-between">
                                        <Button
                                            color="light"
                                            class="me-2"
                                            on:click={() => {
                                                popoverOpen = false;
                                            }}>Close</Button>
                                        <div class="flex justify-end">
                                            <Button
                                                type="button"
                                                color="light"
                                                class="m-0 me-2"
                                                on:click={resetSearchParams}>
                                                <span>Reset</span>
                                            </Button>
                                            <Button
                                                type="button"
                                                color="light"
                                                class="m-0 me-2"
                                                on:click={onApplySearch}>Search</Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}
            {/if}
            {#if config.downloadEnable}
                <div>
                    <Button size="md" color="light" on:click={downloadCsv}>
                        <span>Download</span>
                    </Button>
                </div>
            {/if}
        </div>
        {#if !config.useParentFilter}
            <div class={`${config.searchEnable ? '' : 'w-auto'}`}>
                {#if config.cfg_param_label !== CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT_LABEL}
                    <CfgParameterViewer
                        showFilteringLabel={config.showFilteringLabel}
                        justify_items={config.filterLabelAlignment}
                        filter_label={config.use_filter_datasource
                            ? $cfg_user_filtering?.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label
                            : $cfg_params?.data?.cc3_hoc_cfg_parameter.filter(e =>
                                  e.category?.includes('filtering_label')
                              )?.[0]?.key_value_json} />
                {:else if config.cfg_param_label === CFG_PARAMETER_NAMES.VIEW_SITUATION_VS_INCIDENT_LABEL}
                    <CfgParameterViewer
                        showFilteringLabel={config.showFilteringLabel}
                        justify_items={config.filterLabelAlignment}
                        filter_label={JSON.stringify(filteringLabel)} />
                {/if}
            </div>
        {/if}
    </div>
    <div class="flex flex-shrink-0 gap-1">
        {#each filterTags as tag, index}
            <div class="00 space-x-2 rounded-md bg-slate-700 p-2 text-white">
                <span>{tag.value}</span>
                <span class="cursor-pointer" style={`color: red`} on:click={() => handleRemoveTag(tag.id)}>
                    {@html icon(faRemove).html}</span>
            </div>
        {/each}
    </div>
    <div class="overflow-auto">
        <Table id="table-main" hoverable={true}>
            <TableHead
                class="sticky -top-[0.1px] top-0 z-10 bg-gray-50 capitalize dark:bg-slate-700"
                theadClass="sticky -top-[0.1px] top-0 capitalize bg-gray-50 dark:bg-slate-700 z-10">
                {#each header as column, index}
                    <TableHeadCell
                        id={`hd-${index}`}
                        class={`relative p-0 text-white`}
                        style={`min-height: ${maxHeight}px`}>
                        <TableHeaderContextMenu
                            height={maxHeight}
                            showLeft={index === 0 ? false : true}
                            showRight={index === config?.columns.length - 1 ? false : true}
                            activeID={contextMenuID}
                            classes={`${borderStyle} text-${config.textHeaderAlignment} w-full h-full px-6 py-3`}
                            on:show={result => (contextMenuID = result.detail)}
                            on:action={result => {
                                processHeaderAction(result.detail, index);
                            }}>
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <button
                                        class={`flex w-full justify-${config.textHeaderAlignment}`}
                                        on:click={() => sortTable(column.column)}>
                                        {@html column.value}
                                    </button>
                                </div>
                                <div>
                                    {#if column.column === $sortKey && $sortDirection === ASC}
                                        <span>{@html icon(faSortUp).html}</span>
                                    {/if}
                                    {#if column.column === $sortKey && $sortDirection === -ASC}
                                        <span>{@html icon(faSortDown).html}</span>
                                    {/if}
                                </div>
                            </div>
                        </TableHeaderContextMenu>
                        {#if resizingIndex === index}
                            <button
                                id={`hd-${index}-right`}
                                class="absolute right-0 top-0 z-20 h-full w-2 bg-blue-300 hover:cursor-col-resize {index ===
                                    config?.columns.length - 1 && 'hidden'}"
                                on:mousedown={e => startExpand(e, 'right')} />
                        {/if}
                    </TableHeadCell>
                {/each}
            </TableHead>
            <TableBody>
                {#key popoverOpen}
                    {#if finalData && finalData.length > 0}
                        {#each finalData.slice(page * limit, (page + 1) * limit) as row, index}
                            <TableBodyRow
                                class={`${
                                    config?.stripedRows &&
                                    'odd:bg-white even:bg-gray-50 odd:dark:bg-gray-800 even:dark:bg-gray-700'
                                }`}>
                                {#each row as column}
                                    <TableBodyCell
                                        rowSpan={config.mergeRowsOnSortedColumn &&
                                        column.column === $sortKey &&
                                        index + 1 < $sortItems.length &&
                                        $sortItems[index + 1][column.column] === row[column.column]
                                            ? $sortItems[$sortItems.length - 1][column.column] === row[column.column]
                                                ? $sortItems.length
                                                : $sortItems
                                                      .slice(index)
                                                      .findIndex(i => i[column.column] !== row[column.column])
                                            : 1}
                                        style={getCellStyle(column.style, column.column, column.value)}
                                        class={`text-wrap break-normal ${borderStyle} ${
                                            config.mergeRowsOnSortedColumn &&
                                            column.column === $sortKey &&
                                            index &&
                                            $sortItems[index - 1][column.column] === row[column.column] &&
                                            'hidden'
                                        } text-${config.textContentAlignment}`}
                                        >{column.value === '<nil>' || column.value === ''
                                            ? '-'
                                            : ISO_REGEX.test(column.value)
                                            ? formatDateToClientTimezone(column.value)
                                            : NORMAL_DATE_REGEX.test(column.value)
                                            ? column.value.includes('0001-01-01')
                                                ? '-'
                                                : column.value
                                            : column.value}</TableBodyCell>
                                {/each}
                            </TableBodyRow>
                        {/each}
                        {#if config?.sumHeaderVisible}
                            <Heading tag="h6" class="mt-4">Total</Heading>
                        {/if}
                        {#if config?.sumVisible}
                            <TableBodyRow class={`text-wrap text-lg`}>
                                {#if !config.showAlternate}
                                    {#each config.columns as column}
                                        <TableBodyCell
                                            class={`text-wrap ${borderStyle} text-${config.textContentAlignment}`}
                                            >{$sumItems[column.name] || 'N/A'}</TableBodyCell>
                                    {/each}
                                {:else}
                                    {#each config.alternativeColumns as column}
                                        <TableBodyCell
                                            class={`text-wrap ${borderStyle} text-${config.textContentAlignment}`}
                                            >{$sumItems[column.name] || 'N/A'}</TableBodyCell>
                                    {/each}
                                {/if}
                            </TableBodyRow>
                        {/if}
                    {:else}
                        <div>No data available</div>
                    {/if}
                {/key}
            </TableBody>
        </Table>
    </div>
    {#if config?.paginationVisible && finalData && finalData.length > 0}
        <div class="flex flex-col items-center justify-center gap-2">
            <div class="text-sm text-gray-700 dark:text-gray-400">
                Showing <span class="font-semibold text-gray-900 dark:text-white">{helper.start}</span>
                to
                <span class="font-semibold text-gray-900 dark:text-white">{helper.end}</span>
                of
                <span class="font-semibold text-gray-900 dark:text-white">{helper.total}</span>
                Rows
            </div>

            <PaginationComponent table on:next={next} on:previous={previous}>
                <div
                    slot="prev"
                    class="flex items-center gap-2 bg-gray-800 text-white {page === 0 && 'cursor-not-allowed'}">
                    {@html icon(faCircleChevronLeft).html}
                    Prev
                </div>
                <div
                    slot="next"
                    class="flex items-center gap-2 bg-gray-800 text-white {page ===
                        Math.ceil(helper.total / limit) - 1 && 'cursor-not-allowed'}">
                    Next
                    {@html icon(faCircleChevronRight).html}
                </div>
            </PaginationComponent>
        </div>
    {/if}
</div>

<Modal title="Edit Style" bind:open={openEditor}>
    <CKEditor
        bind:editor
        on:ready={onReady}
        bind:config={editorConfig}
        bind:value={config.columns[stylingIndex].customName} />
    <div class="flex-end flex">
        <Button color="light" on:click={() => (openEditor = false)}>Save</Button>
    </div>
</Modal>
{#if $cfg_params && $cfg_params.data}
    <CfgParameterModal
        bind:openFilter
        {cfgSave}
        {formModal}
        data={$cfg_params?.data.cc3_hoc_cfg_parameter.filter(e => e.category === 'filtering_config')[0] || []}
        title={$cfg_params?.data.cc3_hoc_cfg_parameter.filter(e => e.category === 'filtering_config')[0]?.title ?? ''}
        key_value_json={$cfg_params?.data.cc3_hoc_cfg_parameter.filter(e => e.category === 'filtering_config')[0]
            ?.key_value_json ?? '{}'}
        key_value_json_user={$cfg_user_filtering &&
        $cfg_user_filtering.data &&
        $cfg_user_filtering.data.cc3_hoc_cfg_user_filtering.length > 0
            ? $cfg_user_filtering.data.cc3_hoc_cfg_user_filtering[0]?.key_value_json
            : '{}'}
        form_fields_json={$cfg_params?.data.cc3_hoc_cfg_parameter.filter(e => e.category === 'filtering_config')[0]
            ?.form_fields_json ?? '{}'} />
{/if}

<Notification bind:isOpen={isModalOpen} modalTitle="Notification" {modalMessage} />
