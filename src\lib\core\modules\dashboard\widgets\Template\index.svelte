<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    // import { faSquare1 } from '@fortawesome/pro-duotone-svg-icons';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCircle, faSquare } from '@fortawesome/free-solid-svg-icons';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import { type WidgetConfiguration } from './WidgetConfig/index.svelte';
    import dayjs from 'dayjs';
    import gql from 'graphql-tag';
    import { expectedFormat } from './WidgetConfig/index.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(dayjs.duration);

    export const widgetTitle = 'Template Widget Title';
    export const widgetIcon = faSquare;
    export const widgetCategory = 'Template Widgets';
    export const widgetLimit = 999;
    export const widgetEnabled = false;
    export const widgetMinWidth = 4;
    export const widgetMinHeight = 4;
    export const widgetMaxWidth = 8;
    export const widgetMaxHeight = 8;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { gqlClientStore } from '$lib/stores/Clients';

    export let config: WidgetConfiguration;

    const FetchWidgetDataDocument = gql`
        query FetchIndicatorWidget {
            ${config.dataSource} {
                aggregate {
                    sum {
                        ${expectedFormat[0].name}
                    }
                }
            }
        }
    `;

    const ObserveWidgetDataDocument = gql`
        subscription FetchIndicatorWidget {
            ${config.dataSource} {
                aggregate {
                    sum {
                        ${expectedFormat[0].name}
                    }
                }
            }
        }
    `;

    let subscription: Subscription;
    let titleElement: HTMLElement;
    let displayNumber: number;
    const bbsymbol: unique symbol = Symbol();

    const updateFromResult = result => {
        displayNumber = result?.data[config?.dataSource]?.aggregate?.sum[expectedFormat[0].name];
    };

    const client = get(gqlClientStore);
    client.query(FetchWidgetDataDocument, {}).toPromise().then(updateFromResult);

    onMount(async () => {
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {}, bbsymbol);

        subscription = wPipe(client.subscription(ObserveWidgetDataDocument, {}), wSubscribe(updateFromResult));
    });

    onDestroy(async () => {
        subscription?.unsubscribe();
        browserBroadcaster.unsub(Channel.AOH_WIDGET_RESIZED, bbsymbol);
    });
</script>

<div class="flex h-full w-full flex-col items-center justify-start p-4 text-on-background">
    <header
        bind:this={titleElement}
        class="flex w-full basis-4 items-center justify-start text-wrap text-base font-medium
        leading-tight">
        {config.title}
    </header>
    <div class="flex grow items-center justify-center text-3xl">{displayNumber}</div>
</div>
