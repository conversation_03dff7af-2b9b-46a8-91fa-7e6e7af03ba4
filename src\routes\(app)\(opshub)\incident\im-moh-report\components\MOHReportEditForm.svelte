<script lang="ts">
    import { DateTimePicker, SelectField, TextField, TextareaField } from '$lib/core/modules/DynamicForm/components';
    import { INV_STYLES } from '$lib/shared/constants/general';
    import { REPORT_STATUS } from '$lib/shared/enum/general';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faPencilAlt } from '@fortawesome/free-solid-svg-icons';
    import { But<PERSON>, Heading } from 'flowbite-svelte';
    import { superForm } from 'sveltekit-superforms';
    import { zod } from 'sveltekit-superforms/adapters';
    import { IncidentStatus, mohReportSchema } from '../../schema';

    export let initData;
    export let submitForm;
    export let onSaveAsDraft;
    export let onSendForApproval;
    export let onSendForDirectApproval;
    export let onSendForCOOApproval;
    export let abilities = [];
    export let actionType: 'new' | 'edit' = 'new';
    export let incidentTypeList;
    export let incidentLocationList;
    export let incidentCategoryList;

    const inputStyle = 'w-full';

    const validators = zod(mohReportSchema);

    const transformInitData = data => {
        if (!data) return;
        return {
            ID: undefined,
            Institution_Name: data.Institution_Name,
            Institution_Office: data.Institution_Office,
            Incident_Type_Id: data.Incident_Type_Id,
            Incident_At: new Date(data.Incident_At),
            Incident_Location_Id: data.Incident_Location_Id,
            Incident_Category_Id: data.Incident_Category_Id,
            Description: data.Description,
            Reporter_Info: data.Reporter_Info,
            Risk_Assessment: data.Risk_Assessment,
            Institution_Phone: data.Institution_Phone,
            Institution_Email: data.Institution_Email,
            Comments: data.Comments,
            Incident_Number: data.Incident_Number,
            Incident_Id: data.Incident_Id,
            Report_Status: data.Report_Status,
            Last_Rejected_Level: data.Last_Rejected_Level,
        };
    };

    const superform = superForm(transformInitData(initData), {
        dataType: 'json',
        SPA: true,
        validators,
        resetForm: false,
        onUpdate({ form }) {
            if (form.valid) {
                onSaveAsDraft(form.data);
            }
        },
    });
    const { form, enhance, reset, submit } = superform;
    $: submitForm = $form;

    const btnClass = 'dark:bg-gray-700';
</script>

<form method="POST" class="" use:enhance {...$$restProps}>
    <div class={INV_STYLES.smallTable}>
        <div class="{INV_STYLES.header} mb-8">
            <Heading tag="h5">
                <a href="/incident/im-moh-report/list" class="underline underline-offset-4">MOH Incident Report</a>
                > {submitForm.ID ? 'Edit' : 'New'}</Heading>
        </div>
        <div class="px-8 pb-6">
            <table class="w-full border border-white/80">
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="p-4 text-left text-white">
                            <label class="text-white" for="Institution_Info"
                                >Name of Institution:
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>:
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <TextField class="" type="text" {superform} field="Institution_Name" />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="p-4 text-left text-white">
                            <label class="text-white" for="Institution_Info"
                                >Office of Institution:
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>:
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <TextField class="" type="text" {superform} field="Institution_Office" />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Incident_Type_Id"
                                >Type of Incident
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]:</span>
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <SelectField
                                items={(incidentTypeList || []).map(item => ({ value: item.id, name: item.name }))}
                                class=""
                                type="text"
                                {superform}
                                field="Incident_Type_Id" />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Incident_Location_Id"
                                >Location of Incident
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]:</span>
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <SelectField
                                type="text"
                                {superform}
                                canSearch={true}
                                field="Incident_Location_Id"
                                items={(incidentLocationList || []).map(item => ({
                                    value: item.id,
                                    name: item.description,
                                }))} />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Incident_At"
                                >Date and Time of Incident
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]:</span>
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <DateTimePicker
                                {superform}
                                field="Incident_At"
                                baseForm={form}
                                format="YYYY-MM-DD HH:mm:ss"
                                disabled={submitForm.Incident_Status === IncidentStatus.Closed} />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Description"
                                >Brief Description of Incident
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]:</span>
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <TextareaField type="text" {superform} field="Description" />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Risk_Assessment"
                                >Risk Assessment
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]:</span>
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <TextareaField type="text" {superform} field="Risk_Assessment" />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Incident_Category_Id"
                                >MOH Category Level
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]:</span>
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <SelectField
                                type="text"
                                {superform}
                                field="Incident_Category_Id"
                                items={(incidentCategoryList || []).map(item => ({
                                    value: item.id,
                                    name: item.name,
                                }))} />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Reporter_Info"
                                >Name and Appointment of Reporter
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]:</span>
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <TextareaField type="text" {superform} field="Reporter_Info" />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Institution_Contact"
                                >Telephone Contact:
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>:
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <TextField type="text" {superform} field="Institution_Phone" />
                        </div>
                    </td>
                </tr>
                <tr class="border border-white/45">
                    <td class="border border-white/45">
                        <div class="ml-4 text-left text-white">
                            <label class="text-white" for="Institution_Email"
                                >Email Contact:
                                <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>:
                            </label>
                            <span class="text-sm text-red-500">*</span>
                        </div>
                    </td>
                    <td>
                        <div class="{inputStyle} mb-4 p-4">
                            <TextField type="text" {superform} field="Institution_Email" />
                        </div>
                    </td>
                </tr>
            </table>
            <div class="mt-4 flex w-full justify-center gap-2 rounded-b-2xl p-3">
                {#if submitForm.Report_Status === REPORT_STATUS.Draft}
                    <Button color="light" on:click={() => submitForm()} class={btnClass}>
                        <span>Send for Approval</span>
                    </Button>
                    {#if +submitForm.Last_Rejected_Level === 2}
                        <Button color="light" on:click={() => {}} class={btnClass}>
                            <span>Send for Direct Approval</span>
                        </Button>
                    {/if}

                    {#if +submitForm.Last_Rejected_Level === 3}
                        <Button color="light" on:click={() => {}} class={btnClass}>
                            <span>Send for COO Approval</span>
                        </Button>
                    {/if}
                {/if}
            </div>
            <div class="mb-4 flex gap-4 p-4">
                <label class="text-white" for="Comments">Comments:</label>
                <div class="w-full">
                    <TextareaField type="text" class="flex-1" {superform} field="Comments" />
                </div>
            </div>
            <Button color="light" on:click={() => {}} class={btnClass}>
                <span>Save as Draft</span>
            </Button>
        </div>
    </div>
</form>
