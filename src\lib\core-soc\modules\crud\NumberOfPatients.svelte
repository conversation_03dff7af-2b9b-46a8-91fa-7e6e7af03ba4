<script lang="ts">
    import { ColumnDataType, DataInputType, type ColumnName } from '$lib/shared/constants/general';
    import { validateCharacterLength } from '$lib/shared/util/validation';
    import SocCRUD from '../../components/SocCRUD.svelte';
    import { POPUP_MESSAGES } from '$lib/shared/util/popupMessage';

    const baseURL = '/api/soc/cfg-phar-reg-with-med-ords';
    const figureName = 'Number of Patients with Medication Order who Registered at the Pharmacy Configuration ';
    const triggerTableNames = 'cc3_hoc.cfg_phar_reg_with_med_ords';

    export let subData;
    export let abilities;

    const columnNames: ColumnName[] = [
        {
            key: 'specialty_code',
            value: 'Specialty Code',
            editable: false,
            type: ColumnDataType.String,
            inputType: DataInputType.DropDown,
            populateColumn: 'specialty_desc',
        },
        {
            key: 'specialty_desc',
            value: 'Specialty',
            editable: false,
            inputType: DataInputType.DropDown,
            type: ColumnDataType.String,
            populateColumn: 'specialty_code',
        },
        { key: 'mon', value: 'Mon (%)', editable: true, type: ColumnDataType.Number },
        { key: 'tue', value: 'Tue (%)', editable: true, type: ColumnDataType.Number },
        { key: 'wed', value: 'Web (%)', editable: true, type: ColumnDataType.Number },
        { key: 'thu', value: 'Thu (%)', editable: true, type: ColumnDataType.Number },
        { key: 'fri', value: 'Fri (%)', editable: true, type: ColumnDataType.Number },
    ];

    const validateNewData = (updateData): string | null => {
        if (!validateCharacterLength(updateData, columnNames)) {
            return POPUP_MESSAGES.lengthLimit;
        }
        for (const row of updateData) {
            if (row.mon !== '' && Number(row.mon) < 0) {
                return POPUP_MESSAGES.notNegative.replace('{field}', 'Mon');
            }
            if (row.tue !== '' && Number(row.tue) < 0) {
                return POPUP_MESSAGES.notNegative.replace('{field}', 'Tue');
            }
            if (row.wed !== '' && Number(row.wed) < 0) {
                return POPUP_MESSAGES.notNegative.replace('{field}', 'Wed');
            }
            if (row.thu !== '' && Number(row.thu) < 0) {
                return POPUP_MESSAGES.notNegative.replace('{field}', 'Thu');
            }
            if (row.fri !== '' && Number(row.fri) < 0) {
                return POPUP_MESSAGES.notNegative.replace('{field}', 'Fri');
            }
        }
        return null;
    };
</script>

<SocCRUD
    on:saveSuccess
    {baseURL}
    {figureName}
    {columnNames}
    {validateNewData}
    {subData}
    {triggerTableNames}
    {abilities}
    defaultSortKey="specialty_desc" />
