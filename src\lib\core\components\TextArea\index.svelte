<script lang="ts">
    import { onMount } from 'svelte';
    import { v4 as uuidv4 } from 'uuid';
    export let label = '';

    // Optional
    export let id = undefined;
    export let value = undefined;
    export let rows: number = 3;

    export let maxlength: number = undefined;

    export let setClass =
        'w-full border border-outline rounded bg-inherit text-on-surface invalid:border-error focus:border-primary';
    export let appendClass = '';
    export let labelClass = 'bg-inherit text-sm text-on-surface';
    export let appendLabelClass = '';

    export let enableAnimation: boolean = true;

    if (id === undefined) {
        id = uuidv4();
    }
    const ENTER_KEY = 13;

    let el: HTMLTextAreaElement;

    onMount(() => {
        el.oninput = ev => {
            el.style.height = '5px';
            el.style.height = el.scrollHeight + 'px';
        };
    });
</script>

<div data-testid={$$restProps['data-testid']} class="relative flex" tabIndex={-1}>
    <textarea
        bind:this={el}
        bind:value
        on:focusout
        {id}
        {rows}
        {maxlength}
        placeholder="Enter text"
        on:change
        on:keydown={async ev => {
            if (ev.keyCode === ENTER_KEY && !ev.shiftKey) {
                ev.preventDefault();
            }
        }}
        on:focus
        class="peer {enableAnimation
            ? 'placeholder-transparent'
            : 'placeholder-on-surface'} outline-none {setClass} {appendClass} scroll" />

    {#if enableAnimation}
        <label
            for={id}
            class="absolute left-1 top-0 z-10 origin-[0] -translate-y-2
            scale-75 transform px-2 transition-all duration-300 ease-in-out peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-2 peer-focus:scale-75 peer-focus:px-2 peer-focus:font-semibold {labelClass} {appendLabelClass}"
            >{label}</label>
    {:else}
        <label
            for={id}
            class="absolute -top-2 px-2 text-2xs transition-all duration-300 ease-in-out peer-focus:font-bold"
            >{label}</label>
    {/if}
</div>
