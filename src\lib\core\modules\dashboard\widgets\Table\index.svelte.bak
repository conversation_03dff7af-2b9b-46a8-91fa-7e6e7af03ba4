<script lang="ts" context="module">
    import Button from '$lib/core/components/Button/index.svelte';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { logger } from '$lib/stores/Logger';
<<<<<<< HEAD
    import {
        faChevronLeft,
        faChevronRight,
        faCircleChevronLeft,
        faCircleChevronRight,
        faLineChart,
        faTable,
    } from '@fortawesome/free-solid-svg-icons';
=======
    import { faChevronLeft, faChevronRight, faTable } from '@fortawesome/free-solid-svg-icons';
    import { faChevronsLeft, faChevronsRight } from '@fortawesome/pro-solid-svg-icons';
>>>>>>> aoh-web/release/v2.2.0
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import { onDestroy, onMount, SvelteComponent } from 'svelte';
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import gql from 'graphql-tag';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import type { ResourceStatusByLocationConfig } from '$lib/core/modules/dashboard/widgets/Table/WidgetConfig/index.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);

    export const widgetIcon = faTable;
    export const widgetHeight = 6;
    export const widgetWidth = 12;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = Number.POSITIVE_INFINITY;
    export const widgetTitle = 'Table';
    export const widgetEnabled = true;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    export let config: ResourceStatusByLocationConfig;
    let subscription: Subscription;

    let rows = [];
    let isLoadSuccess: boolean = false;

    let dropdownElem: SvelteComponent;
    let category: string = '';
    let options: Array<DropdownItem>;

    let PAGE_NO = 0;
    let TOTAL_PAGES = 0;
    let ROW_COUNT = 0;

    const BUTTON_CLASS =
        'disabled:brightness-50 disabled:pointer-events-none hover:brightness-150 duration-200 ease-in-out';

    function load(data: Array<any>) {
        if (!data) return;
        rows = data;
    }

    function getCenterIndex(length: number): number {
        if (length % 2 === 0) return length / 2;

        return Math.round((length - 1) / 2);
    }

    function getAttribute(groups: ResourceStatusByLocationConfig['groups']): string {
        let query = ``;
        let set: Set<string> = new Set();
        groups?.forEach(g => {
            g.attributes.forEach(a => {
                set.add(a.key);
            });
        });

        set.forEach(s => {
            query += `${s}\t`;
        });

        return query;
    }

    function isLastPage(): boolean {
        //If there is only one page
        if (TOTAL_PAGES === 1) return PAGE_NO === 0;

        //If there are multiple pages,
        return PAGE_NO === TOTAL_PAGES - 1;
    }

    /* -------------------------------------------------------------------------- */
    /*                                 LIFECYCLES                                 */
    /* -------------------------------------------------------------------------- */

    function hasParameter(str: string): string {
        return config?.category ? str : '';
    }

    const client = get(gqlClientStore);

    async function loadData(page: number = 0, category: string) {
        if (!config?.dataSource.length) return;

        //subscribe
        subscription?.unsubscribe();

        let offset = page * config?.limit;

        const FetchCountDocument = gql`
            query {
                ${config?.dataSource}_aggregate ${hasParameter(`(where: {${config.category}: {_eq: ${category}} })`)} {
                    aggregate {
                        count
                    }
                }
            }
        `;

        const FetchDataDocument = gql`
            query {
                    ${config?.dataSource} (limit: ${config.limit}, offset: ${offset} ${hasParameter(
            `,where: {${config.category}: {_eq: ${category}} }`
        )}) {
                    ${config.label}
                    ${config.category ? config.category : ''}
                    ${getAttribute(config.groups)}
                }
            }
        `;

        const ObserveDataDocument = gql`
            subscription {
                    ${config?.dataSource} (limit: ${config.limit}, offset: ${offset} ${hasParameter(
            `,where: {${config.category}: {_eq: ${category}} }`
        )}) {
                    ${config.label}
                    ${config.category ? config.category : ''}
                    ${getAttribute(config.groups)}
                }
            }
        `;

        const loadData = result => {
            if (result?.data) load(result?.data[config.dataSource]);
        };

        await client.query(FetchDataDocument, {}).then(loadData);

        subscription = wPipe(client.subscription(ObserveDataDocument, {}), wSubscribe(loadData));

        const response = await client.query(FetchCountDocument, {}).toPromise();

        ROW_COUNT = response?.data[`${config?.dataSource}_aggregate`].aggregate.count;
        TOTAL_PAGES = Math.ceil(ROW_COUNT / config?.limit);

        isLoadSuccess = true;
    }

    async function loadCategory() {
        if (!config?.category) return;

        const FetchCategoryDocument = gql`
            query {
                ${config?.dataSource}(distinct_on:[${config.category}], order_by:{${config.category}: asc}) {
                    ${config.category}
                }
            }
        `;

        const response = await client.query(FetchCategoryDocument, {}).toPromise();
        if (response?.data) {
            options = [];
            response?.data[config?.dataSource].forEach(d => {
                options.push({ label: d[config.category], value: d[config.category] });
            });
            category = options[0].value;
            options = options;
        }
    }

    onMount(async () => {
        loadCategory();
        loadData(0, category);
    });

    onDestroy(() => {
        subscription?.unsubscribe();
    });

    $: if (category) PAGE_NO = 0;

    $: if (PAGE_NO || PAGE_NO === 0) {
        loadData(PAGE_NO, category);
    }
</script>

<div class="flex h-full w-full flex-col overflow-hidden p-4 text-sm text-on-background">
    <header class="flex h-12 items-start justify-between gap-3 text-sm">
        <p class="bg-transparent text-base">{config?.title}</p>
        {#if config?.category}
            <Dropdown
                bind:this={dropdownElem}
                {options}
                appendMenuClass="w-full"
                bind:value={category}
                setClass="h-8 w-36 border-outline border-b" />
        {/if}
    </header>

    {#if isLoadSuccess}
        <div class="grow overflow-x-auto">
            <table class="w-full text-xs">
                <!-- Header Group-->
                <!-- Filter so that we only create the header section when there is at least one header with some text -->
                {#if config.groups.length > 0 && config.groups.some(hdr => Boolean(hdr.header))}
                    <thead>
                        <tr class="h-full">
                            <td />
                            {#each config.groups as headerGroup}
                                <td class="w-full bg-surface-2">
                                    <p class="flex items-center p-1">
                                        <span class="border-outline px-2 text-xs">{headerGroup.header}</span>
                                    </p>
                                </td>
                            {/each}
                        </tr>
                    </thead>
                {/if}

                <tr class="h-full border-r-0">
                    <td class="h-full"
                        ><table class="h-full table-fixed">
                            <thead>
                                <tr class=""
                                    ><td class="sticky top-0 h-8 bg-surface-1 px-2 capitalize">{config.label}</td></tr>
                            </thead>
                            <tbody>
                                {#each rows as d}
                                    <tr class="h-8 w-[150px]"> <td class="truncate px-2">{d[config.label]}</td></tr>
                                {/each}
                            </tbody>
                        </table></td>

                    {#each config.groups as headerGroup, y}
                        <td class="w-full">
                            <table class="h-full w-full table-fixed">
                                <thead>
                                    <tr>
                                        {#each headerGroup.attributes as attribute}
                                            <td class="sticky top-0 h-8 w-[150px] bg-surface-1 px-2 text-center"
                                                >{attribute.label}</td>
                                        {/each}</tr>
                                </thead>

                                <tbody>
                                    {#each rows as d}
                                        <tr>
                                            {#each headerGroup.attributes as attribute}
                                                <td class="h-8 min-w-[150px] truncate px-2 text-center"
                                                    >{d[attribute.key] ? d[attribute.key] : ' - '}</td>
                                            {/each}
                                        </tr>
                                    {/each}
                                </tbody>
                            </table>
                        </td>
                    {/each}
                </tr>
            </table>
        </div>
    {/if}

    <footer class="flex h-10 w-full items-center justify-end gap-10 border-t border-t-outline bg-inherit px-4 py-2">
        <span class="font-light brightness-75">
            {PAGE_NO * config.limit + 1} - {isLastPage()
                ? ROW_COUNT
                : TOTAL_PAGES === 1
                ? ROW_COUNT
                : PAGE_NO + 1 * config?.limit} of {ROW_COUNT}
        </span>

        <Button
            setClass={BUTTON_CLASS}
            setDisabled={PAGE_NO === 0}
            on:click={() => {
                PAGE_NO = 0;
            }}>{@html icon(faCircleChevronLeft).html}</Button>

        <Button
            setClass={BUTTON_CLASS}
            setDisabled={PAGE_NO === 0}
            on:click={() => {
                PAGE_NO = PAGE_NO - 1;
            }}>{@html icon(faChevronLeft).html}</Button>

        <Button
            setDisabled={PAGE_NO === TOTAL_PAGES - 1}
            on:click={() => {
                PAGE_NO = PAGE_NO + 1;
            }}
            setClass={BUTTON_CLASS}>{@html icon(faChevronRight).html}</Button>
        <Button
            setClass={BUTTON_CLASS}
            setDisabled={PAGE_NO === TOTAL_PAGES - 1}
            on:click={() => {
                PAGE_NO = TOTAL_PAGES - 1;
            }}>{@html icon(faCircleChevronRight).html}</Button>
    </footer>
</div>
