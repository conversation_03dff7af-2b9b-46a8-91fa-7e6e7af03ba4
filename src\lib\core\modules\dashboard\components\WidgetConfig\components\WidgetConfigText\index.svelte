<script lang="ts">
    import { Label, Tooltip, Input, type InputType } from 'flowbite-svelte';
    export let title: string;
    export let value;
    export let required = false;
    export let type: InputType = 'text';
    export let customClass = 'mb-4';
    export let inputClass = '';
    export let placeholder = '';
    export let toolTip;
    export let pattern;
    export let min;
    export let disabled = false;
    export let excludeZeroFirst = false;

    const onKeyDown = event => {
        if (pattern) {
            const newPattern = new RegExp(pattern);
            if (event.ctrlKey || event.altKey || typeof event.key !== 'string' || event.key.length !== 1) {
                return;
            }
            if (!newPattern.test(event.key)) {
                event.preventDefault();
            }
        }
    };
    const onInput = event => {
        if (excludeZeroFirst) {
            const valueInput = event.target.value;
            if (/^0+/.test(valueInput)) {
                value = valueInput.replace(/^0+/, '');
            } else {
                value = valueInput;
            }
        }
    };
</script>

<div class={customClass}>
    {#if title}<Label class="mb-2 block">{title}</Label>{/if}
    <div class="flex gap-2">
        <Input
            bind:value
            {type}
            {required}
            {min}
            {placeholder}
            {disabled}
            class={inputClass + ' min-w-[2.5rem]'}
            on:keydown={onKeyDown}
            on:input={onInput} />
        {#if toolTip}
            <Tooltip type="auto" arrow={false} placement="bottom" class="font-light">
                {#each toolTip as type}
                    {type}<br />
                {/each}
            </Tooltip>
        {/if}
    </div>
</div>
