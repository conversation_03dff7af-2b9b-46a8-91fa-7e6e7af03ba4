<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { faSquare } from '@fortawesome/free-solid-svg-icons';
    import { get } from 'svelte/store';
    import { user } from '$lib/stores/Auth';
    import dayjs from 'dayjs';

    export const widgetTitle = 'Label';
    export const widgetIcon = faSquare;
    export const widgetMinWidth = 2;
    export const widgetMinHeight = 1;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 12;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = Number.POSITIVE_INFINITY;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { onDestroy, onMount } from 'svelte';
    import { type Subscription } from 'wonka';
    import type { IndicatorConfig } from './WidgetConfig/index.svelte';
    import CfgParameterViewer from '$lib/shared/components/cfg-parameter/CfgParameterViewer.svelte';
    import { JUSTIFY_iTEMS_OBJ } from '../utils';
    import { subscriptionStore, type OperationResultStore, type Pausable } from '@urql/svelte';
    import {
        GetCfgUserFilteringLabelByCfgParamUserIdSubscriptionDocument,
        type GetCfgUserFilteringLabelByCfgParamUserIdSubscriptionSubscription,
    } from '$generated-types';
    import { gqlClientStore } from '$lib/stores/Clients';

    export let config: IndicatorConfig;
    let customFilterLabel: string;

    let subUserFilteringLabel: OperationResultStore<GetCfgUserFilteringLabelByCfgParamUserIdSubscriptionSubscription> &
        Pausable;

    let date: string;
    let subscription: Subscription;
    let Element: HTMLElement;
    let DateElement: HTMLElement;
    let DateIcon: HTMLElement;
    const client = get(gqlClientStore);

    function displayIcon(config) {
        switch (config) {
            case 'inflow':
                return 'icon-inflow';
            case 'outflow':
                return 'icon-outflow';
            case 'stock':
                return 'icon-stock';
            case 'date':
                return 'icon-date';
            case 'none':
                return 'empty';
            default:
                return 'empty';
        }
    }

    $: if (
        config ||
        !DateIcon ||
        config.isBorderTop ||
        config.isBorderBottom ||
        config.isBorderLeft ||
        config.isBorderRight
    ) {
        updateBorder();
    }
    $: date = dayjs(new Date()).format(config?.dateFormat == '' ? 'YYYY/MM' : config?.dateFormat);
    function setBorderByConfig(element: HTMLElement, isBorder: boolean, side: string) {
        if (isBorder && element) {
            element.style[
                `border${side.charAt(0).toUpperCase() + side.slice(1)}`
            ] = `${config.borderWidth.toString()}px solid ${config.borderColor}`;
        } else {
            element.style[`border${side.charAt(0).toUpperCase() + side.slice(1)}`] = 'none';
        }
    }

    function updateBorder() {
        if (!Element) {
            return;
        }
        const editorElement = Element.parentElement.parentElement;
        setBorderByConfig(editorElement, config.isBorderTop, 'top');
        setBorderByConfig(editorElement, config.isBorderBottom, 'bottom');
        setBorderByConfig(editorElement, config.isBorderLeft, 'left');
        setBorderByConfig(editorElement, config.isBorderRight, 'right');
    }

    onMount(async () => {
        if (config.dataSourceType === 'cfg_parameter') {
            getUserFiltering();
        }
    });

    onDestroy(async () => {
        subscription?.unsubscribe();
    });

    const configText = text => {
        if (!text) return '';
        return text.replace(/\{\{([^}]+)\}\}/g, (match, content) => content);
    };
    let cfgUserFiltering;
    const getUserFiltering = async () => {
        const user_id = get(user).claims.hasura_access['x-hasura-user-id'];
        const cfgUserFilteringResult = await fetch(
            `/api/CfgUserFilteringService?filtering_id=${config.dataSource}&user_id=${user_id}`
        );
        let dataRes = await cfgUserFilteringResult.json();
        if (dataRes) {
            cfgUserFiltering = dataRes?.res?.data[0];
        }
        subUserFilteringLabel = subscriptionStore<GetCfgUserFilteringLabelByCfgParamUserIdSubscriptionSubscription>({
            client,
            query: GetCfgUserFilteringLabelByCfgParamUserIdSubscriptionDocument,
            variables: { id: config.dataSource, user_id },
        });
    };
    $: if ($subUserFilteringLabel?.data?.cc3_hoc_cfg_user_filtering?.[0]?.filter_label) {
        customFilterLabel = $subUserFilteringLabel.data.cc3_hoc_cfg_user_filtering[0].filter_label;
    }
</script>

<div
    bind:this={Element}
    class="flex h-full w-full flex-col px-4"
    id="label-cont"
    style="background-color: {config?.bgColor};">
    {#if cfgUserFiltering && config?.showGear}
        <div class="flex h-full w-full flex-col p-4">
            <CfgParameterViewer
                showFilteringLabel={true}
                justify_items={JUSTIFY_iTEMS_OBJ[config?.filterLabelAlignment] || ''}
                filter_label={customFilterLabel ? customFilterLabel : cfgUserFiltering?.filter_label} />
        </div>
    {:else}
        <table class="h-full">
            <tr
                class="grid h-full items-center"
                style="grid-template-columns: {(() => {
                    switch (true) {
                        case config?.left === 'none' && config?.right === 'none':
                            return '1fr';
                        case config?.left !== 'none' && config?.right === 'none':
                            return '3fr 7fr';
                        case config?.left === 'none' && config?.right !== 'none':
                            return '7fr 3fr';
                        default:
                            return '3fr 4fr 3fr';
                    }
                })()};">
                {#if config?.left !== 'none'}
                    <td class="flex self-center justify-self-start">
                        <div
                            class="right-0 flex items-center justify-center text-on-background {config?.left == 'empty'
                                ? 'hidden'
                                : ''}">
                            <div bind:this={DateIcon} class="{displayIcon(config?.left)} mr-2" />
                            <span
                                bind:this={DateElement}
                                style="opacity:0.7;font-size:{config.dateFontSize}px;color:{config.dateFontColor}"
                                class={config?.left != 'date' ? 'hidden' : ''}
                                >{date}
                            </span>
                        </div>
                    </td>
                {/if}
                <td class="text-white">{@html configText(config.title)}</td>
                {#if config?.right !== 'none'}
                    <td class="flex self-center justify-self-end"
                        ><div
                            class=" right-0 flex items-center justify-center text-on-background {config?.right ==
                            'empty'
                                ? 'hidden'
                                : ''}">
                            <div bind:this={DateIcon} class="mr-2 {displayIcon(config?.right)}" />
                            <span
                                bind:this={DateElement}
                                style="opacity:0.7;font-size:{config.dateFontSize}px;color:{config.dateFontColor}"
                                class={config?.right != 'date' ? 'hidden' : ''}
                                >{date}
                            </span>
                        </div></td>
                {/if}
            </tr>
        </table>
    {/if}
</div>

<style>
    .icon-date {
        background: transparent url('$lib/icons/icon-date.svg') no-repeat;
        background-size: auto;
        width: 20px;
        height: 20px;
    }
    .icon-inflow {
        background: transparent url('$lib/icons/icon-inflow.svg') no-repeat;
        background-size: auto;
        width: 20px;
        height: 10px;
    }
    .icon-outflow {
        background: transparent url('$lib/icons/icon-outflow.svg') no-repeat;
        background-size: auto;
        width: 20px;
        height: 10px;
    }
    .icon-stock {
        background: transparent url('$lib/icons/icon-stock.svg') no-repeat;
        background-size: auto;
        width: 20px;
        height: 10px;
    }
</style>
