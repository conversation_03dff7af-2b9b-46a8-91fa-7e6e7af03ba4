import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
import { checkPermissions } from '$lib/shared/util/acl';
import { locations } from '$lib/stores/Locations';
import { logger } from '$lib/stores/Logger';
import { error } from '@sveltejs/kit';
import { get } from 'svelte/store';
import type { PageServerLoad } from './$types';

const log = logger.child({ src: new URL(import.meta.url).pathname });

export const load: PageServerLoad = async ({ parent, params, fetch, locals }) => {
  const response = await fetch(`${get(locations).soc_web}/cfg-ngemr-doctor`);
    if (response.status !== 200) {
        log.error({ status: response.status });
        return error(404);
    }
    const doctorData = await response.json();
    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const access_token = locals.user.access_token;
    let permissionNeedToCheck = [
        {
            scopeId: SCOPE.READ,
            resourceId: RESOURCE.SOC_DOCTOR,
        },
        {
            scopeId: SCOPE.CREATE,
            resourceId: RESOURCE.SOC_DOCTOR,
        },
        {
            scopeId: SCOPE.UPDATE,
            resourceId: RESOURCE.SOC_DOCTOR,
        },
        {
            scopeId: SCOPE.DELETE,
            resourceId: RESOURCE.SOC_DOCTOR,
        },
        {
            scopeId: SCOPE.TOGGLE,
            resourceId: RESOURCE.SOC_DOCTOR,
        },
    ];
    const abilities = await checkPermissions({
        token: access_token,
        tenantId,
        userId,
        permissions: permissionNeedToCheck,
    });
    return {
        doctor: doctorData.data,
        abilities,
    };
};
