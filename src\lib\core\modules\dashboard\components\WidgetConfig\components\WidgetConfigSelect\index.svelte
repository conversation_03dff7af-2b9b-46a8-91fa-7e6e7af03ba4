<script lang="ts">
    import { Label, Select } from 'flowbite-svelte';
    export let title: string;
    export let value;
    export let required = false;
    export let items: any[] = [];
    export let customClass = 'mb-4';
    export let inputClass = '';
</script>

<div class={customClass}>
    <Label class="block mb-2">{title}</Label>
    <Select bind:value {required} {items} class={inputClass} />
</div>
