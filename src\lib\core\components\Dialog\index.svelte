<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    import Button from '$lib/core/components/Button/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faXmark } from '@fortawesome/free-solid-svg-icons';
    import { quintOut } from 'svelte/easing';
    import { fade } from 'svelte/transition';
    import { computePosition, flip, offset as offs, shift } from '@floating-ui/dom';

    export let title = '';
    export let isOpen = false;
    export let offset = 6;
    export let heightClass = 'h-fit';
    export let widthClass = '';
    export let positionClass = '-left-full -top-full';
    export let bgClass = 'bg-surface-1';
    export let parentElement: HTMLElement = undefined;

    export let onClose: (event: Event) => void = undefined;

    function dialogAction(node: Node) {
        const updatePosition = () => {
            if (!parentElement && node.parentElement) {
                parentElement = node.parentElement;
            }

            const element = node as HTMLElement;

            computePosition(parentElement, element, {
                placement: 'bottom',
                middleware: [offs(offset), flip(), shift()],
            }).then(({ x, y }) => {
                Object.assign(element.style, {
                    left: `${x}px`,
                    top: `${y}px`,
                });
            });
        };

        updatePosition();
    }
</script>

<!-- Container -->
<div
    role="button"
    tabindex="0"
    use:dialogAction
    class="absolute {heightClass} {widthClass} {positionClass} z-50 transition-opacity"
    class:opacity-0={!isOpen}
    class:opacity-100={isOpen}
    class:pointer-events-none={!isOpen}
    on:click|stopPropagation={() => {}}>
    <!--Background-->
    <div
        data-testid="modal-window"
        class="flex h-full flex-col rounded-lg shadow-glow shadow-selected-stroke {bgClass} opacit">
        <!--Title-->
        <header class="flex items-center justify-between rounded-t-lg bg-navbar px-3 py-2 text-on-background">
            <p class="text-xs font-medium leading-[115%] xl:text-sm">
                {title}
            </p>
            <Button
                on:click={e => {
                    if (onClose) {
                        onClose(e);
                    }

                    isOpen = false;
                }}
                setClass="bg-inherit hover:text-selected-stroke text-inherit h-full grid items-center justify-center"
                >{@html icon(faXmark).html}</Button>
        </header>
        <div class="px-6 py-8">
            <!--Content-->
            <slot />
        </div>
    </div>
</div>
