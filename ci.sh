# docker system prune -a -f
docker build -f ./Dockerfile -t hocsvn/hcc3-web \
--build-arg PUBLIC_API_URL=https://inventory-api.dratini.tech \
--build-arg PUBLIC_INVENTORY_URL=https://inventory-api.dratini.tech \
--build-arg INVENTORY_URL=https://inventory-api.dratini.tech \
--build-arg DATA_AGG_URL=https://data-agg-api.dratini.tech \
--build-arg SOC_URL=https://soc-api.dratini.tech \
--build-arg COMMON_URL=https://common-api.dratini.tech \
--build-arg INCIDENT_MONITORING_URL=https://incident-api.dratini.tech \
--build-arg TAG_URL=https://tag-api.dratini.tech \
--build-arg DASH_URL=https://dash-api.dratini.tech \
--build-arg BATCHJOB_URL=https://batchjob-api.dratini.tech/v1 \
--build-arg SOP_URL=https://sop-api.dratini.tech/v1 \
--build-arg AUDITLOG_URL=https://batchjob-api.dratini.tech/v1 \
--build-arg PUBLIC_BASE_URL=https://cc3-hoc.dratini.tech \
--build-arg PUBLIC_DOMAIN=dratini.tech \
--build-arg PORT=3000 \
--build-arg BODY_SIZE_LIMIT=3000000 \
--build-arg IAM_CLIENT_ID=aoh_iams \
--build-arg IAM_CLIENT_SECRET=xqOmn0UIHpO4SgteBfma8bImac5t9Szn \
--build-arg IAM_URL=https://cc3-hoc-keycloak.dratini.tech/realms/AOH/ \
--build-arg IAMS_AAS_URL=https://cc3-hoc-iams.dratini.tech \
--build-arg GRAPHQL_URL=https://cc3-hoc-hasura-bearer.dratini.tech/v1/graphql \
--build-arg PUBLIC_GRAPHQL_COOKIE_URL=https://cc3-hoc-hasura.dratini.tech/v1/graphql \
--build-arg HASURA_ADMIN_SECRET=admin@123 \
--build-arg HASURA_USER_ID=2778a905-e257-42be-be04-a78c648f65a5 \
--build-arg MINIO_ACCESS_KEY=F16eKjk5e4jWqh5lXxbL \
--build-arg MINIO_SECRET_KEY=66whjQ26igkv7HVkQqQy1K9ocmywklUD6r8S45UJ \
--build-arg MAPBOX_SECRET_ACCESS_TOKEN=******************************************************************************************** \
--build-arg PUBLIC_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiZnhhbmhraG9hIiwiYSI6ImNsb3FtZnFxNjBoMHoybHA2cGNyejF1N3gifQ.ewBVeNwme45DwHogPrheMA \
--build-arg PUBLIC_MAPBOX_STYLE=fxanhkhoa/clpcb8tyw009i01o0gzdsap13 \
--build-arg PUBLIC_MAPBOX_STYLE_SATELLITE=fxanhkhoa/clpcb8tyw009i01o0gzdsap13 \
--build-arg PUBLIC_SECURE_MODE=1 \
--build-arg PUBLIC_REPLAY_MODE=0 \
--build-arg PUBLIC_STATIC_BUILD_VERSION=next \
--build-arg PUBLIC_HASURA_COOKIE_MODE=1 \
--build-arg PUBLIC_STATIC_OVERRIDE_HOME= .
docker logout
docker login -u <EMAIL> -p cfgparameter@123
docker push hocsvn/hcc3-web
# ssh into server and run below command
#microk8s kubectl rollout restart deployment soc