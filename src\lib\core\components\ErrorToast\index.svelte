<script lang="ts" context="module">
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import toast_, { type Toast } from 'svelte-french-toast';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faX } from '@fortawesome/free-solid-svg-icons';
    import { logger } from '$lib/stores/Logger';

    const log = logger.child({ src: new URL(import.meta.url).pathname });

    type AohError = {
        message: string;
        code: number;
    };

    export const DEFAULT_ERROR_MESSAGE = `Something went wrong`;
</script>

<script lang="ts">
    export let toast: Toast & { props: AohError };
</script>

<div
    class="flex h-full w-full flex-row items-center justify-center rounded-md border
    border-outline bg-surface-1 text-on-surface-1">
    <div class="p-4 text-3xl text-error">
        {@html icon(faX).html}
    </div>
    <div class="flex flex-row divide-x divide-outline">
        <div class="flex flex-col py-4 pr-2">
            <div class="text-sm font-semibold text-on-surface-1">
                {toast.props.message ? toast.props.message : DEFAULT_ERROR_MESSAGE}
            </div>
            <div class="max-w-md overflow-hidden overflow-ellipsis text-xs text-on-surface-2">
                Error Code: {toast.props.code}
            </div>
        </div>
        <div class="flex flex-col">
            <Button
                on:click={() => toast_.dismiss(toast.id)}
                variant={ButtonVariant.EMPTY}
                appendClass="w-full h-full text-sm hover:bg-error p-4">Dismiss</Button>
        </div>
    </div>
</div>

<style>
    :global(.message) {
        margin: 0px !important;
    }
</style>
