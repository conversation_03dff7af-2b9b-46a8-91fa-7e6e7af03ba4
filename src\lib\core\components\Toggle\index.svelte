<!--
    @component
    A toggle is a styled checkbox.

    Props:
    - `init`: A Svelte action on the outer-most element (label) - Pass a callback function here if you want to execute
        code on initialization
    - `labelText`: A string to be rendered next to the toggle checkbox
    - `checked`: Whether or not the input element is checked
    - `labelElement`: The exported label element if you wish you access it via code.
    - `inputElement`: The exported input element if you wish you access it via code.
    - `divElement`: The exported div element if you wish you access it via code.
    - `spanElement`: The exported span element if you wish you access it via code.

    - Usage:
    ```tsx
      <script lang="ts">
        import Toggle from 'path/to/this/component/Toggle/index.svelte';
      </script>
      <Toggle labelText="Self Destruct"/>
    ```
-->
<script lang="ts">
    export let init: (node: Element) => void = _node => {};
    export let labelText: string = undefined;
    export let checked: boolean = false;

    export let labelElement: HTMLLabelElement = undefined;
    export let inputElement: HTMLInputElement = undefined;
    export let divElement: HTMLDivElement = undefined;
    export let spanElement: HTMLSpanElement = undefined;

    export let appendClass = '';
</script>

<label bind:this={labelElement} use:init class="relative inline-flex cursor-pointer items-center {appendClass}">
    <div>
        <input bind:this={inputElement} type="checkbox" on:change class="peer sr-only" bind:checked />
        <div
            bind:this={divElement}
            class="peer h-6 w-11 rounded-full bg-surface-1 
            after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full
             after:border after:border-surface-1 after:bg-white after:transition-all after:content-['']
              peer-checked:bg-primary peer-checked:after:translate-x-full peer-checked:after:border-on-surface-1 
              peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-highlight 
             " />
    </div>
    <span bind:this={spanElement} class="ml-3 text-sm font-medium text-on-background">{labelText}</span>
</label>
