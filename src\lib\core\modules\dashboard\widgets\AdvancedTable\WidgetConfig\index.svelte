<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface AdvancedTableConfig {
        title?: string;
        name?: string;
        dataSource?: string;
        dateFormat: string;
        numberOfRows: string;
        columns: {
            customName: string;
            name: string;
        }[];
        alternativeColumns: {
            customName: string;
            name: string;
        }[];
        labelColumns: {
            customName: string;
            name: string;
        }[];
        defaultSortingColumn: string;
        defaultOrder: number;
        textHeaderAlignment: string;
        textContentAlignment: string;
        gridVisible: boolean;
        paginationVisible: boolean;
        stripedRows: boolean;
        sumVisible: boolean;
        sumHeaderVisible: boolean;
        mergeRowsOnSortedColumn: boolean;
        autoPagination: boolean;
        paginationPeriod: number;
        borderThickness: number;
        showFilteringLabel: boolean;
        showFiltering: boolean;
        cfg_param: string;
        cfg_param_label: string;
        use_filter_datasource: boolean;
        filterLabelAlignment: string;
        thresholdMappingTable: string;
        mappingColumn: string;
        thresholdMappingColumn: string;
        thresholdMappingList: {
            amber: string;
            red: string;
            column: string;
            groupKey: string;
            groupQuery: string;
        }[];
        thresholdMappingIDs: {
            mappingTableID: string;
            mappingDatasourceID: string;
            mappingGroupTableID: string;
            mappingGroupMstTableID: string;
        }[];
        listSorting: string;
        groupMappingTable: string;
        searchEnable: boolean;
        downloadEnable: boolean;
        downloadSelectEnable: boolean;
        customDownload: boolean;
        isDynamicColumns: boolean;
        downloadEndpoint: string;
        comparisonThresholds: { column: string; comparator: string; value: string; color: string }[];
        useParentFilter: boolean;
        dynamicColumnsMapping: { columnKey: string; valueKey: string; seriesKey: string };
        isFixedTable?: boolean;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'Advanced Table';

    export const configDefaults: AdvancedTableConfig = {
        dataSource: '',
        title: DEFAULT_TITLE,
        name: DEFAULT_TITLE,
        dateFormat: 'DD/MM/YYYY',
        numberOfRows: '10',
        columns: [],
        defaultSortingColumn: '',
        defaultOrder: 1,
        textHeaderAlignment: 'start',
        textContentAlignment: 'start',
        gridVisible: false,
        paginationVisible: false,
        stripedRows: false,
        sumVisible: false,
        sumHeaderVisible: false,
        mergeRowsOnSortedColumn: false,
        autoPagination: false,
        paginationPeriod: 3,
        borderThickness: 1,
        showFilteringLabel: false,
        alternativeColumns: [],
        showFiltering: false,
        labelColumns: [],
        cfg_param: '',
        cfg_param_label: '',
        use_filter_datasource: false,
        filterLabelAlignment: 'end',
        thresholdMappingTable: '',
        mappingColumn: '',
        thresholdMappingColumn: '',
        thresholdMappingList: [],
        listSorting: '',
        thresholdMappingIDs: [],
        groupMappingTable: '',
        searchEnable: false,
        downloadEnable: false,
        downloadSelectEnable: false,
        comparisonThresholds: [],
        customDownload: false,
        downloadEndpoint: '',
        useParentFilter: false,
        isDynamicColumns: false,
        dynamicColumnsMapping: { columnKey: '', valueKey: '', seriesKey: '' },
        isFixedTable: false,
    };
</script>

<script lang="ts">
    //Components
    import { onMount } from 'svelte';
    import {
        Input,
        Label,
        Select,
        MultiSelect,
        Listgroup,
        ListgroupItem,
        Toggle,
        Range,
        Tooltip,
        AccordionItem,
        Accordion,
        Button,
    } from 'flowbite-svelte';
    import type { CfgParameter } from '$lib/shared/types/cfgParameter';
    import { CFG_PARAMETER_NAMES, SYS_DATA_SOURCE_MAIN_CATEGORY } from '$lib/shared/enum/general';
    import type { SysDataSource } from '$lib/shared/types/sys_datasource';
    import { parseJSON } from '$lib/shared/util/dataConvert';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faTrash } from '@fortawesome/free-solid-svg-icons';
    import {
        BooleanComparisonOperator,
        DateComparisonOperator,
        NumberComparisonOperator,
        StringComparisonOperator,
    } from '$lib/shared/enum/search';

    import { faCaretUp, faCaretDown } from '@fortawesome/free-solid-svg-icons';

    export let config: AdvancedTableConfig = configDefaults;
    let listDataSource: {
        value: string;
        name: string;
        fields: string[];
        json_values: any;
    }[] = [];

    let listMappingDataSource: {
        value: string;
        name: string;
        fields: string[];
        json_values: any;
    }[] = [];

    let originalSysData: SysDataSource[] = [];

    let listColumn = [];
    let listColumnAlternate = [];
    let sortItems = [];
    let orderItems = [
        { name: 'ASC', value: 1 },
        { name: 'DESC', value: -1 },
    ];

    let multiSelectValues = [];
    let multiSelectValuesAlternate = [];

    let cfgParameters: CfgParameter[] = [];
    let backendParameters: CfgParameter[] = [];
    let listSchemaTables: { value: string; name: string }[] = [];
    let ODSColumns: { name: string; type: string }[] = [];

    const alignItems = [
        { value: 'start', name: 'left' },
        { value: 'center', name: 'center' },
        { value: 'end', name: 'right' },
    ];

    const listSQLQuery = [
        { value: 'SUM', name: 'SUM' },
        { value: 'AVG', name: 'AVG' },
        { value: 'MAX', name: 'MAX' },
        { value: 'MIN', name: 'MIN' },
    ];

    let compareStringOptions = [
        { value: StringComparisonOperator.Is, name: 'Is', label: 'Is' },
        { value: StringComparisonOperator.Begins, name: 'Begins', label: 'Begins' },
        { value: StringComparisonOperator.Contains, name: 'Contains', label: 'Contains' },
        { value: StringComparisonOperator.Ends, name: 'Ends', label: 'Ends' },
    ];

    let compareNumberOptions = [
        {
            value: NumberComparisonOperator.Equals,
            name: NumberComparisonOperator.Equals,
            label: NumberComparisonOperator.Equals,
        },
        {
            value: NumberComparisonOperator.Greater,
            name: NumberComparisonOperator.Greater,
            label: NumberComparisonOperator.Greater,
        },
        {
            value: NumberComparisonOperator.Less,
            name: NumberComparisonOperator.Less,
            label: NumberComparisonOperator.Less,
        },
        {
            value: NumberComparisonOperator.GTE,
            name: NumberComparisonOperator.GTE,
            label: NumberComparisonOperator.GTE,
        },
        {
            value: NumberComparisonOperator.LTE,
            name: NumberComparisonOperator.LTE,
            label: NumberComparisonOperator.LTE,
        },
    ];

    let compareDateOptions = [
        { value: DateComparisonOperator.Is, name: 'Is', label: 'Is' },
        { value: DateComparisonOperator.Before, name: 'Before', label: 'Before' },
        { value: DateComparisonOperator.Since, name: 'Since', label: 'Since' },
        { value: DateComparisonOperator.Between, name: 'Between', label: 'Between' },
    ];

    let compareBoolOptions = [
        {
            value: BooleanComparisonOperator.Is,
            name: BooleanComparisonOperator.Is,
            label: BooleanComparisonOperator.Is,
        },
        {
            value: BooleanComparisonOperator.IsNot,
            name: BooleanComparisonOperator.IsNot,
            label: BooleanComparisonOperator.IsNot,
        },
    ];

    $: sortItems = config?.columns?.map(c => ({ value: c.name, name: c.customName })) || [];
    const editColumnLabel = (event: Event, currentValue: string) => {
        config.columns = config.columns.map(e => {
            if (e.name === currentValue) {
                e.customName = (event as any).currentTarget.value;
            }
            return e;
        });
    };

    const editColumnLabelAlternate = (event: Event, currentValue: string) => {
        config.alternativeColumns = config.alternativeColumns.map(e => {
            if (e.name === currentValue) {
                e.customName = (event as any).currentTarget.value;
            }
            return e;
        });
    };

    const onMultiSelectChange = () => {
        if (!multiSelectValues.length) {
            config = { ...config, columns: [] };
            return;
        }

        const selectedColumns = multiSelectValues.map(value => {
            const name = listColumn.find(column => column.value === value)?.value;
            const foundColumn = config.columns.find(column => column.name === value);
            return {
                name,
                customName: foundColumn ? foundColumn.customName : name,
            };
        });

        config = { ...config, columns: selectedColumns };
    };

    const onMultiSelectChangeAlternate = () => {
        if (!multiSelectValuesAlternate.length) {
            config = { ...config, alternativeColumns: [] };
            return;
        }

        const selectedColumns = multiSelectValuesAlternate.map(value => {
            const name = listColumnAlternate.find(column => column.value === value)?.value;
            const foundColumn = config.alternativeColumns.find(column => column.name === value);
            return {
                name,
                customName: foundColumn ? foundColumn.customName : name,
            };
        });

        config = { ...config, alternativeColumns: selectedColumns };
    };

    const getOriginalDatasource = (foundDatasource: {
        value: string;
        name: string;
        fields: string[];
        json_values: any;
    }) => {
        const value = parseJSON(foundDatasource.json_values);
        return originalSysData.find(e => e.ID === value[0].dataSource);
    };

    const initListColumn = (dataSource: string) => {
        listColumn = [];
        if (!listDataSource) {
            return;
        }
        setTimeout(async () => {
            const fields = await getFieldsGeneral(dataSource);
            if (fields) {
                listColumn = [
                    ...fields.map(f => ({
                        value: f,
                        name: f,
                    })),
                ];
                fieldsGeneral = [...listColumn];
                multiSelectValues = multiSelectValues?.filter(v => fields.includes(v));
            }
        }, 300);
    };

    const initListColumnAlternate = (dataSource: string) => {
        listColumnAlternate = [];
        if (!listDataSource) {
            return;
        }
        setTimeout(async () => {
            const fields = await getFieldsGeneral(dataSource);
            if (fields) {
                listColumnAlternate = [
                    ...fields.map(f => ({
                        value: f,
                        name: f,
                    })),
                ];
                multiSelectValuesAlternate = multiSelectValuesAlternate?.filter(v => fields.includes(v));
            }
        }, 300);
    };

    const getFields = (json: string) => {
        const value = parseJSON(json);
        return value[0].property;
    };

    const getFieldsODS = (json: string) => {
        const value = parseJSON(json);
        return parseJSON(value.columns).map(e => e.name);
    };

    const getFieldsGeneral = async (dataSource: string) => {
        if (!listDataSource || listDataSource.length === 0) {
            return [];
        }
        let fields = [];
        const foundDatasource = listDataSource.find(e => e.value === dataSource);
        if (foundDatasource.fields && foundDatasource.fields.length > 0) {
            fields = foundDatasource.fields;
        } else {
            fields = getFieldsODS(getOriginalDatasource(foundDatasource).Json_Values);
        }

        const foundPickedDatasource = originalSysData.find(e => e.ID === dataSource);
        if (foundPickedDatasource) {
            const aggTableNames = foundPickedDatasource.Agg_Tables?.replaceAll('cc3_hoc.', '')
                ?.replaceAll('cc3_hoc_', '')
                .split(',');

            try {
                const res = await fetch(`/api/CfgParameterService/${CFG_PARAMETER_NAMES.DYNAMIC_COLUMNS}`);
                const json = await res.json();

                if (json?.cfgParam && aggTableNames?.length) {
                    const newFields = [];
                    const dynamicColumns = json?.cfgParam?.key_value_json;
                    // In the BackEnd, the format of key_value_json is [{agg_name: <table_name>, columns: [<col1>, <col2>]}]
                    aggTableNames.forEach(name => {
                        const result = dynamicColumns?.find(d => d.agg_name === name);
                        if (result) newFields.push(...result?.columns);
                    });

                    fields = [...fields, ...newFields];
                }
            } catch (_) {}
        }
        return fields;
    };

    const getColumns = (json: string) => {
        const value = parseJSON(json);
        const columnsParsed = parseJSON(value.columns);
        return columnsParsed.map(e => e.name);
    };

    let fieldsGeneral: { name: string; value: string }[] = [];
    let downloadADS: SysDataSource[] = [];

    const getListAggDatasource = async () => {
        const resWidgetType = await fetch(`/api/dash/widget/type?size=999`);

        const { data } = await resWidgetType.json();
        const widgetTypes = data.data;

        const advanceTableWidgetType = widgetTypes.find(e => e.name === 'Advanced Table');

        const result = await fetch(
            `/api/SysDataSourceService?main_category=${SYS_DATA_SOURCE_MAIN_CATEGORY.AGGREGATED_DATA_SOURCE}`
        );

        let sysData: SysDataSource[] = [];
        const { res } = await result.json();
        originalSysData = res.data;
        sysData = res.data;
        sysData = sysData.filter(
            e =>
                e.Main_Category === SYS_DATA_SOURCE_MAIN_CATEGORY.AGGREGATED_DATA_SOURCE &&
                e.Type === advanceTableWidgetType?.id &&
                e.End_Point === ''
        );

        downloadADS = originalSysData.filter(e => e.Is_Active && e.End_Point !== '');

        listDataSource = sysData.map(e => ({
            value: e.ID,
            name: e.Name,
            fields: getFields(e.Json_Values),
            json_values: e.Json_Values,
        }));

        listMappingDataSource = res.data
            .filter(e => e.Main_Category === SYS_DATA_SOURCE_MAIN_CATEGORY.ORIGINAL_DATA_SOURCE)
            .map(e => {
                return {
                    value: e.ID,
                    name: e.Name,
                    fields: getColumns(e.Json_Values),
                };
            });

        const ADSFound = sysData.find(e => e.ID === config.dataSource);
        if (!ADSFound) return;
        const jsonValue = JSON.parse(ADSFound.Json_Values);
        if (jsonValue[0] && jsonValue[0].dataSource) {
            const ODS = res.data.find(e => e.ID === jsonValue[0].dataSource);
            if (ODS) {
                const jsonValueODS = JSON.parse(ODS.Json_Values);
                ODSColumns = jsonValueODS.columns;
            }
        }
    };

    onMount(async () => {
        await getListAggDatasource();
        if (!config.dataSource) {
            config = {
                ...config,
                columns: [],
                alternativeColumns: [],
                thresholdMappingList: [],
                thresholdMappingIDs: [],
                dataSource: listDataSource[0].value,
            };
        }
        multiSelectValues = [...config.columns.map(e => e.name)];
        multiSelectValuesAlternate = [...config.alternativeColumns.map(e => e.name)];
        initListColumn(config.dataSource);
        initListColumnAlternate(config.dataSource);
        getCfgParameters();

        const widgetConfig = document.getElementById('widget-config');
        if (widgetConfig) {
            widgetConfig.style.width = '800px';
        }

        if (!config.comparisonThresholds) {
            config.comparisonThresholds = [];
        }
    });

    const getCfgParameters = async () => {
        try {
            const resCfg = await fetch(
                `/api/CfgParameterService?category=filtering_label&category=filtering_config&category=BackendParameter`
            );
            const { res } = await resCfg.json();
            const data: CfgParameter[] = res.data;
            cfgParameters = data;
            backendParameters = data.filter(e => e.category === 'BackendParameter');
        } catch (error) {
            console.log(error);
        }
    };

    const getSchemaTable = async () => {
        try {
            const res = await fetch(`/api/common/db-metadata?schemas=cc3_hoc`);
            const { data } = await res.json();
            listSchemaTables = data.map(e => ({
                name: e,
                value: e,
            }));
        } catch (error) {
            console.log(error);
        }
    };

    const onDataSourceChange = (event: Event) => {
        initListColumn((event as any).currentTarget.value);
        initListColumnAlternate((event as any).currentTarget.value);
        config = {
            ...config,
            dataSource: (event as any).currentTarget.value,
            columns: [],
            alternativeColumns: [],
        };
        multiSelectValues = [];
        multiSelectValuesAlternate = [];
    };

    const onNumberOfRowChanged = (event: Event) => {
        if ((event as any).currentTarget.value === '') {
            (event as any).currentTarget.value = '1';
        }
        let value = parseInt((event as any).currentTarget.value, 10);
        if (value < 1) {
            value = 1;
            (event as any).currentTarget.value = '1';
        }
        config.numberOfRows = value.toFixed();
    };

    const getTableHeaderAlignment = (event: any) => {
        config.textHeaderAlignment = event.currentTarget.value;
    };

    const getTableContentAlignment = (event: any) => {
        config.textContentAlignment = event.currentTarget.value;
    };

    const getFilterLabelAlignment = (event: any) => {
        config.filterLabelAlignment = event.currentTarget.value;
    };

    $: if (!config.showFiltering) {
        config.use_filter_datasource = false;
    }

    const addThresholdMapping = () => {
        if (!config.thresholdMappingList) {
            config.thresholdMappingList = [];
        }
        config.thresholdMappingList = [
            ...config.thresholdMappingList,
            { red: '', amber: '', column: '', groupKey: '', groupQuery: '' },
        ];
    };

    const addMappingID = () => {
        if (!config.thresholdMappingIDs) {
            config.thresholdMappingIDs = [];
        }
        config.thresholdMappingIDs = [
            ...config.thresholdMappingIDs,
            { mappingDatasourceID: '', mappingTableID: '', mappingGroupTableID: '', mappingGroupMstTableID: '' },
        ];
    };

    const editMappingID = (e: Event, key: string, index: number) => {
        const value = (e.currentTarget as any).value;
        config.thresholdMappingIDs[index][key] = value;
    };

    const editMapping = (e: Event, key: string, index: number) => {
        const value = (e.currentTarget as any).value;
        config.thresholdMappingList[index][key] = value;
    };

    const removeMappingID = (index: number) => {
        const newList = config.thresholdMappingIDs.filter((_, i) => i !== index);
        config.thresholdMappingIDs = null;
        config.thresholdMappingIDs = JSON.parse(JSON.stringify(newList));
    };

    const addThresholdComparison = () => {
        config.comparisonThresholds = [
            ...config.comparisonThresholds,
            {
                column: '',
                comparator: '',
                value: '',
                color: '',
            },
        ];
    };

    const editThresholdComparison = (index: number, key: string, e: Event) => {
        const value = (e.currentTarget as any).value;
        config.comparisonThresholds[index][key] = value;
    };
    const editDynamicColumnsMapping = (e: Event, key: string) => {
        const value = (e.currentTarget as any).value;
        if (!config.dynamicColumnsMapping) {
            config.dynamicColumnsMapping = {
                columnKey: '',
                valueKey: '',
                seriesKey: '',
            };
        }
        config.dynamicColumnsMapping[key] = value;
    };

    const deleteThresholdComparison = (index: number) => {
        const newArr = config.comparisonThresholds.filter((_, i) => i !== index);
        config.comparisonThresholds = [...newArr];
    };

    const getCompareOptions = (column: string) => {
        const foundODSColumn = ODSColumns.find(c => c.name === column);
        if (!foundODSColumn) {
            return [];
        }
        switch (foundODSColumn.type) {
            case 'string':
                return compareStringOptions;
            case 'number':
                return compareNumberOptions;
            case 'date':
                return compareDateOptions;
            case 'boolean':
                return compareBoolOptions;
            default:
                return compareStringOptions;
        }
    };

    const moveColumnItem = (index: number, direction: number, targetArray: string[], isAlternativeColumns: boolean) => {
        const newIndex = index + direction;
        if (newIndex < 0 || newIndex >= targetArray.length) return;

        const updatedValues = [...targetArray];
        [updatedValues[index], updatedValues[newIndex]] = [updatedValues[newIndex], updatedValues[index]];

        if (isAlternativeColumns) {
            multiSelectValuesAlternate = updatedValues;
        } else {
            multiSelectValues = updatedValues;
        }

        // Update the config to reflect the new order
        const configKey = isAlternativeColumns ? 'alternativeColumns' : 'columns';
        const reorderedColumns = updatedValues.map(value => {
            const column = config[configKey].find(col => col.name === value);
            return column || { name: value, customName: value };
        });
        config = { ...config, [configKey]: reorderedColumns };
    };
</script>

<div class="flex h-[50%] flex-col gap-2 overflow-auto">
    <div>
        <Label for="title" class="mb-2">Title</Label>
        <Input type="text" id="title" placeholder="Input Title" bind:value={config.title} />
    </div>
    <div>
        <Label for="name" class="mb-2">Name</Label>
        <Input type="text" id="name" placeholder="Input Name" bind:value={config.name} />
    </div>

    <Label>
        Data Source
        <Select
            placeholder="select Data source"
            class="mt-2"
            items={listDataSource}
            value={config.dataSource}
            on:change={onDataSourceChange} />
    </Label>
    <div class="ml-1">
        <Toggle color="blue" bind:checked={config.isDynamicColumns}>Dynamic Columns</Toggle>
    </div>
    <Accordion>
        {#if !config.isDynamicColumns}
            <AccordionItem>
                <span slot="header">Default Main Table</span>
                <div>
                    <Label class="mb-2">Columns</Label>
                    {#if listColumn.length > 0}
                        <MultiSelect
                            items={listColumn}
                            bind:value={multiSelectValues}
                            on:change={onMultiSelectChange}
                            defaultClass="z-50" />
                    {/if}
                </div>

                <Listgroup class="mt-2">
                    <span class="text-wrap p-2">Column Customisation</span>
                    {#each multiSelectValues as v, index}
                        <ListgroupItem>
                            <div class="flex items-center gap-2">
                                <div class="flex flex-col justify-between gap-2">
                                    <Label>Current Label</Label>
                                    <Input
                                        class="mt-auto"
                                        readonly
                                        value={config.columns.find(e => e.name === v)?.name} />
                                </div>
                                <div class="flex flex-col justify-between gap-2">
                                    <Label>Customised Label</Label>
                                    <Input
                                        class="mt-auto"
                                        type="text"
                                        value={config.columns.find(e => e.name === v)
                                            ? config.columns.find(e => e.name === v).customName
                                            : v}
                                        on:change={event => editColumnLabel(event, v)} />
                                </div>
                                <div class="flex flex-col gap-1">
                                    <Button
                                        size="xs"
                                        color="light"
                                        on:click={() => moveColumnItem(index, -1, multiSelectValues, false)}
                                        disabled={index === 0}>
                                        {@html icon(faCaretUp).html}
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="light"
                                        on:click={() => moveColumnItem(index, 1, multiSelectValues, false)}
                                        disabled={index === multiSelectValues.length - 1}>
                                        {@html icon(faCaretDown).html}
                                    </Button>
                                </div>
                            </div>
                        </ListgroupItem>
                    {/each}
                </Listgroup>
            </AccordionItem>
            <AccordionItem>
                <span slot="header">Alternate Main Table</span>
                <div>
                    <Label class="mb-2">Columns</Label>
                    {#if listColumnAlternate.length > 0}
                        <MultiSelect
                            items={listColumnAlternate}
                            bind:value={multiSelectValuesAlternate}
                            on:change={onMultiSelectChangeAlternate}
                            defaultClass="z-50" />
                    {/if}
                </div>

                <Listgroup class="mt-2">
                    <span class="items-center text-wrap p-2">Column Customisation</span>
                    {#each multiSelectValuesAlternate as v, index}
                        <ListgroupItem>
                            <div class="flex gap-2">
                                <div class="flex flex-col justify-between gap-2">
                                    <Label>Current Label</Label>
                                    <Input
                                        class="mt-auto"
                                        readonly
                                        value={config.alternativeColumns.find(e => e.name === v)?.name} />
                                </div>
                                <div class="flex flex-col justify-between gap-2">
                                    <Label>Customised Label</Label>
                                    <Input
                                        class="mt-auto"
                                        type="text"
                                        value={config.alternativeColumns.find(e => e.name === v)
                                            ? config.alternativeColumns.find(e => e.name === v).customName
                                            : v}
                                        on:change={event => editColumnLabelAlternate(event, v)} />
                                </div>
                                <div class="flex flex-col gap-1">
                                    <Button
                                        size="xs"
                                        color="light"
                                        on:click={() => moveColumnItem(index, -1, multiSelectValuesAlternate, true)}
                                        disabled={index === 0}>
                                        {@html icon(faCaretUp).html}
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="light"
                                        on:click={() => moveColumnItem(index, 1, multiSelectValuesAlternate, true)}
                                        disabled={index === multiSelectValues.length - 1}>
                                        {@html icon(faCaretDown).html}
                                    </Button>
                                </div>
                            </div>
                        </ListgroupItem>
                    {/each}
                </Listgroup>
            </AccordionItem>
            <AccordionItem>
                <span slot="header">Threshold Comparison</span>
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12 h-full md:col-span-2">
                        <div class="flex h-full flex-col justify-between">
                            <span>Remove</span>
                        </div>
                    </div>
                    <div class="col-span-12 h-full md:col-span-3">
                        <div class="flex h-full flex-col justify-between">
                            <span>Column</span>
                        </div>
                    </div>
                    <div class="col-span-12 h-full md:col-span-3">
                        <div class="flex h-full flex-col justify-between">
                            <span>Comparator</span>
                        </div>
                    </div>
                    <div class="col-span-12 h-full md:col-span-3">
                        <div class="flex h-full flex-col justify-between">
                            <span>Compare value</span>
                        </div>
                    </div>
                    <div class="col-span-12 h-full md:col-span-1">
                        <div class="flex h-full flex-col justify-between">
                            <span>Color</span>
                        </div>
                    </div>
                </div>
                {#each config?.comparisonThresholds ?? [] as threshold, i}
                    <div class="grid grid-cols-12 gap-5">
                        <div class="col-span-12 h-full md:col-span-2">
                            <div class="flex h-full flex-col justify-end">
                                <Button color="light" on:click={() => deleteThresholdComparison(i)}
                                    >{@html icon(faTrash).html}</Button>
                            </div>
                        </div>
                        <div class="col-span-12 h-full md:col-span-3">
                            <div class="flex h-full flex-col justify-end">
                                <Select
                                    size="sm"
                                    class="mt-2"
                                    value={threshold.column}
                                    on:change={e => editThresholdComparison(i, 'column', e)}
                                    items={listColumn.map(e => ({ name: e.value, value: e.value }))} />
                            </div>
                        </div>
                        <div class="col-span-12 h-full md:col-span-3">
                            <div class="flex h-full flex-col justify-end">
                                <Select
                                    size="sm"
                                    class="mt-2"
                                    items={getCompareOptions(threshold.column)}
                                    value={threshold.comparator}
                                    on:change={e => editThresholdComparison(i, 'comparator', e)} />
                            </div>
                        </div>
                        <div class="col-span-12 h-full md:col-span-3">
                            <div class="flex h-full flex-col justify-end">
                                <Input
                                    type="text"
                                    placeholder="Input Compare value"
                                    value={threshold.value}
                                    on:change={e => editThresholdComparison(i, 'value', e)} />
                            </div>
                        </div>
                        <div class="col-span-12 h-full md:col-span-1">
                            <div class="flex h-full flex-col justify-end">
                                <Input
                                    class="aspect-square h-auto min-w-5"
                                    type="color"
                                    value={threshold.color}
                                    on:change={e => editThresholdComparison(i, 'color', e)} />
                            </div>
                        </div>
                    </div>
                {/each}
                <div class="mt-2 flex">
                    <Button class="w-full" color="dark" on:click={addThresholdComparison}>+ Add new threshold</Button>
                </div>
            </AccordionItem>
            <AccordionItem>
                <span slot="header">Thresholds Mapping</span>
                <div>
                    <Label for="listSorting" class="mb-2">Threshold Sorting priority</Label>
                    <Input
                        type="text"
                        id="listSorting"
                        name="listSorting"
                        placeholder="separate with comma and the highest is on the left"
                        bind:value={config.listSorting} />
                </div>
                <div class="flex flex-col gap-2">
                    <Label>
                        Select Mapping Config
                        <Select
                            class="mt-2"
                            items={listMappingDataSource.map(e => ({
                                value: e.name,
                                name: e.name,
                            }))}
                            bind:value={config.thresholdMappingTable} />
                    </Label>
                    <Label>
                        Select Mapping Group Table
                        <Select
                            class="mt-2"
                            items={listMappingDataSource.map(e => ({
                                value: e.name,
                                name: e.name,
                            }))}
                            bind:value={config.groupMappingTable} />
                    </Label>
                    <div>
                        <div class="flex gap-2">
                            <h5>Select Mapping ID</h5>
                            <Button color="dark" on:click={addMappingID}>+ Add new mapping ID</Button>
                        </div>
                        {#if config.thresholdMappingIDs && config.thresholdMappingIDs.length > 0}
                            {#each config?.thresholdMappingIDs as mapping, i}
                                <div class="mt-2 flex gap-2">
                                    <div class="flex h-full flex-col justify-between">
                                        <span>Remove</span>
                                        <Button class="mt-7 py-3" color="light" on:click={() => removeMappingID(i)}
                                            >{@html icon(faTrash).html}</Button>
                                    </div>
                                    <Label>
                                        Agg Table Mapping Column
                                        <Select
                                            class="mt-2"
                                            items={fieldsGeneral}
                                            on:change={e => editMappingID(e, 'mappingDatasourceID', i)}
                                            value={mapping.mappingDatasourceID} />
                                    </Label>
                                    <Label>
                                        Mst Table mapping Column
                                        <Select
                                            class="mt-2"
                                            items={listMappingDataSource
                                                ?.find(e => e.name === config.thresholdMappingTable)
                                                ?.fields.map(f => ({
                                                    name: f,
                                                    value: f,
                                                })) ?? []}
                                            on:change={e => editMappingID(e, 'mappingTableID', i)}
                                            value={mapping.mappingTableID} />
                                    </Label>
                                    <Label>
                                        Group Table mapping Column with Agg Table
                                        <Select
                                            class="mt-2"
                                            items={listMappingDataSource
                                                ?.find(e => e.name === config.groupMappingTable)
                                                ?.fields.map(f => ({
                                                    name: f,
                                                    value: f,
                                                })) ?? []}
                                            on:change={e => editMappingID(e, 'mappingGroupTableID', i)}
                                            value={mapping.mappingGroupTableID} />
                                    </Label>
                                    <Label>
                                        Group Table mapping Column with Mst Table
                                        <Select
                                            class="mt-2"
                                            items={listMappingDataSource
                                                ?.find(e => e.name === config.groupMappingTable)
                                                ?.fields.map(f => ({
                                                    name: f,
                                                    value: f,
                                                })) ?? []}
                                            on:change={e => editMappingID(e, 'mappingGroupMstTableID', i)}
                                            value={mapping.mappingGroupMstTableID} />
                                    </Label>
                                </div>
                            {/each}
                        {/if}
                    </div>
                    <Button color="dark" on:click={addThresholdMapping}>+ Add new mapping</Button>
                </div>
                <div class="flex flex-col gap-2">
                    <div class="grid grid-cols-12 items-center gap-5">
                        <div class="col-span-6">
                            <div class="grid grid-cols-12 gap-3">
                                <div class="col-span-12">Detail View</div>
                                <div class="col-span-4 border-r p-2"><Label>Amber Column</Label></div>
                                <div class="col-span-4 border-r p-2"><Label>Red Column</Label></div>
                                <div class="col-span-4 p-2"><Label>Datasource Column</Label></div>
                            </div>
                        </div>
                        <div class="col-span-6">
                            <div class="grid grid-cols-12 gap-3">
                                <div class="col-span-12">Group View</div>
                                <div class="col-span-6 border-r p-2"><Label>Mapping Key</Label></div>
                                <div class="col-span-6 border-r p-2"><Label>SQL Query</Label></div>
                            </div>
                        </div>
                    </div>
                    {#if config.thresholdMappingList.length > 0}
                        {#each config?.thresholdMappingList as mapping, i}
                            <div class="grid grid-cols-12 items-center gap-5">
                                <div class="col-span-12 md:col-span-6">
                                    <div class="grid grid-cols-12 gap-3">
                                        <div class="col-span-4 border-r p-2">
                                            {#if listMappingDataSource}
                                                <Select
                                                    class="mt-2"
                                                    items={listMappingDataSource
                                                        ?.find(e => e.name === config.thresholdMappingTable)
                                                        ?.fields.map(f => ({
                                                            name: f,
                                                            value: f,
                                                        })) ?? []}
                                                    value={mapping.amber}
                                                    on:change={e => editMapping(e, 'amber', i)} />
                                            {/if}
                                        </div>
                                        <div class="col-span-4 border-r p-2">
                                            {#if listMappingDataSource}
                                                <Select
                                                    class="mt-2"
                                                    items={listMappingDataSource
                                                        ?.find(e => e.name === config.thresholdMappingTable)
                                                        ?.fields.map(f => ({
                                                            name: f,
                                                            value: f,
                                                        })) ?? []}
                                                    value={mapping.red}
                                                    on:change={e => editMapping(e, 'red', i)} />
                                            {/if}
                                        </div>
                                        <div class="col-span-4 p-2">
                                            {#if listDataSource}
                                                <Select
                                                    class="mt-2"
                                                    items={fieldsGeneral}
                                                    value={mapping.column}
                                                    on:change={e => editMapping(e, 'column', i)} />
                                            {/if}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-span-12 md:col-span-6">
                                    <div class="grid grid-cols-12 gap-3">
                                        <div class="col-span-6">
                                            {#if listMappingDataSource}
                                                <Select
                                                    class="mt-2"
                                                    items={listMappingDataSource
                                                        ?.find(e => e.name === config.thresholdMappingTable)
                                                        ?.fields.map(f => ({
                                                            name: f,
                                                            value: f,
                                                        })) ?? []}
                                                    value={mapping.groupKey}
                                                    on:change={e => editMapping(e, 'groupKey', i)} />
                                            {/if}
                                        </div>
                                        <div class="col-span-6">
                                            <Select
                                                class="mt-2"
                                                items={listSQLQuery}
                                                value={mapping.groupQuery}
                                                on:change={e => editMapping(e, 'groupQuery', i)} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/each}
                    {/if}
                </div>
            </AccordionItem>
        {:else}
            <AccordionItem>
                <span slot="header">Dynamic Columns</span>
                <Label>
                    <div class="flex items-center gap-2">
                        <div class="flex flex-col justify-between gap-2">
                            <Label>Column key</Label>
                            <Input
                                class="mt-auto"
                                value={config.dynamicColumnsMapping?.columnKey || ''}
                                on:change={e => editDynamicColumnsMapping(e, 'columnKey')} />
                        </div>
                        <div class="flex flex-col justify-between gap-2">
                            <Label>Value key</Label>
                            <Input
                                class="mt-auto"
                                type="text"
                                value={config.dynamicColumnsMapping?.valueKey || ''}
                                on:change={e => editDynamicColumnsMapping(e, 'valueKey')} />
                        </div>
                        <div class="flex flex-col justify-between gap-2">
                            <Label>Series key</Label>
                            <Input
                                class="mt-auto"
                                type="text"
                                value={config.dynamicColumnsMapping?.seriesKey || ''}
                                on:change={e => editDynamicColumnsMapping(e, 'seriesKey')} />
                        </div>
                    </div>
                </Label>
            </AccordionItem>
        {/if}

        <AccordionItem>
            <span slot="header">Filtering</span>
            <div class="my-3">
                <Toggle color="blue" bind:checked={config.showFiltering}>Show Filtering</Toggle>
            </div>
            <Label>
                Filter Data Source
                <Select
                    class="mt-2 capitalize"
                    items={cfgParameters
                        .filter(e => e.category === 'filtering_config')
                        .map(f => ({
                            name: f.name,
                            value: f.name,
                        }))}
                    bind:value={config.cfg_param} />
            </Label>
            <div class="my-3">
                <Toggle color="blue" bind:checked={config.showFilteringLabel}>Show Filtering Label</Toggle>
            </div>
            {#if config.showFilteringLabel}
                <div class="my-3">
                    <Toggle
                        disabled={!config.showFiltering}
                        class={`${!config.showFiltering && '!text-gray-600'}`}
                        color="blue"
                        bind:checked={config.use_filter_datasource}>Use the same as Filter Data Source</Toggle>
                </div>
                {#if !config.use_filter_datasource}
                    <Label>
                        Label Data Source
                        <Select
                            class="mt-2 capitalize"
                            items={cfgParameters
                                .filter(e => e.category?.includes('filtering_label'))
                                .map(f => ({
                                    name: f.name,
                                    value: f.name,
                                }))}
                            bind:value={config.cfg_param_label} />
                    </Label>
                {/if}
                <div>
                    <Label for="tableContentAlignment" class="my-2">Filtering Label Alignment</Label>
                    <div class="flex items-center">
                        {#each alignItems as item}
                            <Button
                                class="mr-2 h-10 w-20 capitalize dark:focus-within:ring-gray-900 {item.value !==
                                    config.filterLabelAlignment && 'hover:bg-blue-700 dark:bg-gray-700'}"
                                color={item.value === config.filterLabelAlignment ? 'blue' : 'none'}
                                value={item.value}
                                on:click={e => getFilterLabelAlignment(e)}
                                >{item.name}
                            </Button>
                        {/each}
                    </div>
                </div>
            {/if}
        </AccordionItem>
    </Accordion>

    <Label>
        Default Sorting Column
        <Select class="mt-2" items={sortItems} bind:value={config.defaultSortingColumn} />
    </Label>
    <Label>
        Default Order
        <Select class="mt-2" items={orderItems} bind:value={config.defaultOrder} />
    </Label>

    <div>
        <Label for="numberOfRows" class="mb-2">Number of Rows</Label>
        <Input
            type="number"
            id="numberOfRows"
            placeholder="Input Number of Rows"
            value={config.numberOfRows}
            on:change={onNumberOfRowChanged} />
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.searchEnable}>Show Search</Toggle>
    </div>

    <div class="flex flex-col gap-3">
        <Toggle color="blue" bind:checked={config.downloadEnable}>Enable download</Toggle>
        {#if config.downloadEnable}
            <div class="rounded-lg bg-gray-800 p-3">
                <!-- <Toggle color="blue" bind:checked={config.downloadSelectEnable}>Enable download with select</Toggle> -->
                <Toggle color="blue" bind:checked={config.customDownload}>Custom Download</Toggle>
                {#if config.customDownload}
                    <div class="my-2 flex">
                        <Select
                            items={downloadADS ? downloadADS.map(e => ({ name: e.Name, value: e.End_Point })) : []}
                            bind:value={config.downloadEndpoint} />
                    </div>
                {/if}
            </div>
        {/if}
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.paginationVisible}>Show Pagination</Toggle>
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.autoPagination}>Pagination Period (Seconds)</Toggle>
        {#if config.autoPagination}
            <div class="mb-2 mt-1 flex">
                <div>
                    <Input type="number" id="title" placeholder="Input Title" bind:value={config.paginationPeriod} />
                </div>
            </div>
        {/if}
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.gridVisible}>Show Grid</Toggle>
        {#if config.gridVisible}
            <Tooltip triggeredBy="#gridThickness" placement="top">Grid thickness</Tooltip>
            <div class="mb-2 mt-1 flex">
                <span class="mr-2">
                    <Range id="gridThickness" size="sm" min="1" max="4" bind:value={config.borderThickness} step="1" />
                </span>

                <span>{config.borderThickness || 'N/A'}</span>
            </div>
        {/if}
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.stripedRows}>Striped Rows</Toggle>
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.sumHeaderVisible}>Show Total Header</Toggle>
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.sumVisible}>Show Total Table</Toggle>
    </div>
    <!-- <div>
        <Toggle color="blue" bind:checked={config.mergeRowsOnSortedColumn}>Merge Rows On Sorted Column</Toggle>
    </div> -->
    <div>
        <Toggle color="blue" bind:checked={config.isFixedTable}>Is Fixed Table</Toggle>
    </div>

    <div>
        <Label for="tableHeaderAlignment" class="mb-1">Table Header Alignment</Label>
        <div class="flex items-center">
            {#each alignItems as item}
                <Button
                    class="ml-2 h-10 w-20 capitalize dark:focus-within:ring-gray-900 {item.value !==
                        config.textHeaderAlignment && 'hover:bg-blue-800 dark:bg-gray-700'}"
                    color={item.value === config.textHeaderAlignment ? 'blue' : 'none'}
                    value={item.value}
                    on:click={e => getTableHeaderAlignment(e)}
                    >{item.name}
                </Button>
            {/each}
        </div>
    </div>

    <div>
        <Label for="tableContentAlignment" class="mb-1">Table Content Alignment</Label>
        <div class="flex items-center">
            {#each alignItems as item}
                <Button
                    class="ml-2 h-10 w-20 capitalize dark:focus-within:ring-gray-900 {item.value !==
                        config.textContentAlignment && 'hover:bg-blue-700 dark:bg-gray-700'}"
                    color={item.value === config.textContentAlignment ? 'blue' : 'none'}
                    value={item.value}
                    on:click={e => getTableContentAlignment(e)}
                    >{item.name}
                </Button>
            {/each}
        </div>
    </div>
</div>
