import { INVENTORY_URL } from '$env/static/private';
import { MstSapmmMaterialService } from '$lib/service/mst_sapmm_material';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url, cookies }) => {
    const materialType = url.searchParams.get('material_type');
    const token = cookies.get('access_token') ?? '';
    const res = await MstSapmmMaterialService.FindAllItems(token, INVENTORY_URL, materialType);

    return json({ data: res });
};

export const PATCH: RequestHandler = async ({ request, cookies }) => {
    const { data } = await request.json();
    const token = cookies.get('access_token') ?? '';
    const res = await MstSapmmMaterialService.UpdateItems(token, INVENTORY_URL, data);

    return json({ res }, { status: 200 });
};
