<script lang="ts">
    import SocCrud from '$lib/core-soc/components/SocCRUD.svelte';
    import { ColumnDataType, DataInputType, type ColumnName } from '$lib/shared/constants/general';
    import { POPUP_MESSAGES } from '$lib/shared/util/popupMessage';
    import { validateCharacterLength } from '$lib/shared/util/validation';
    import { isNullOrEmpty } from '$routes/(app)/(opshub)/incident/utils/input_utils';

    export let subData = null;
    export let abilities;

    const baseURL = '/api/ot/cfg-ngemr-ot-room';
    const figureName = 'OT Room Configuration';
    const triggerTableNames = 'cc3_hoc.cfg_ngemr_ot_room';

    const columnNames: ColumnName[] = [
        {
            key: 'room',
            value: 'Room',
            editable: true,
            type: ColumnDataType.String,
            inputType: DataInputType.DropDown,
            required: true,
        },
        {
            key: 'case_priority',
            value: 'Case Priority',
            editable: true,
            type: ColumnDataType.String,
            inputType: DataInputType.DropDown,
        },
        {
            key: 'location',
            value: 'Location',
            editable: true,
            type: ColumnDataType.String,
        },
        {
            key: 'surgical_service',
            value: 'Surgical Service',
            editable: true,
            type: ColumnDataType.String,
            inputType: DataInputType.DropDown,
        },
    ];
    const validateAllData = (allData): string | null => {
        const roomSet = new Set();
        for (const row of allData) {
            if (isNullOrEmpty(row.room)) {
                return POPUP_MESSAGES.emptyValue.replace('{field}', 'Room');
            }

            const roomText = row.room?.replaceAll(' ', '');
            if (roomSet.has(roomText)) {
                return POPUP_MESSAGES.duplicateRow.replace('{value}', row.room);
            }
            roomSet.add(roomText);
        }

        return null;
    };

    const validateNewData = (newData): string | null => {
        if (!validateCharacterLength(newData, columnNames, true)) {
            return POPUP_MESSAGES.lengthLimit;
        }

        return null;
    };
</script>

<SocCrud
    {baseURL}
    {subData}
    {figureName}
    {columnNames}
    {validateAllData}
    {validateNewData}
    {triggerTableNames}
    {abilities}
    defaultSortKey="room" />
