import { locations } from '$lib/stores/Locations';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

import { apiGetRequest, apiUpdateRequest } from '$lib/shared/util/socApiRequest';

const roomsURL = `${get(locations).soc}/soc/mst-ngemr-rooms`;

export const GET: RequestHandler = async ({ locals, url }) => {
    const page = url.searchParams.get('page') ?? '1';
    const limit = url.searchParams.get('limit') ?? '9999';
    const token = locals.user.access_token;
    return apiGetRequest({ url: roomsURL, token, query: { page, limit } });
};
