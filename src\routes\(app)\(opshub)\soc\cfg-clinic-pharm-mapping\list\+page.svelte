<script lang="ts">
    import { get } from 'svelte/store';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import { ColumnDataType, type ColumnNameCustom } from '$lib/shared/constants/general';
    import { v4 as uuidv4 } from 'uuid';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import StandardTable from '../../components/StandardTable.svelte';
    import { isLoading } from '$lib/stores/Loading';
    import { TriggerService } from '$lib/service/trigger';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';
    export let data;

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    const SocURL = `${get(locations).soc_web}`;

    let popupModal = false;
    let modalTitle = '';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;
    let openUpdateResult = false;
    let newRecords = new Set();
    let searchText = '';
    let selectedRow = null;
    let updateList = [];
    let updatePosition: UpdatePosition = { id: '', columnName: '', isEditing: false };
    let newData;
    let standardTable;
    let extraData: any = {};
    const triggerTableNames = 'cc3_hoc.cfg_clinic_pharm_mapping';

    const FieldNames = {
        LevelOfCare: 'level_of_care',
    };
    extraData = data?.extraData || {};

    const subData = {
        level_of_care: data?.extraData.classOfPatientData.map(item => ({ value: item.code, name: item.code })),

        clinic_code: data?.extraData.clinicData.map(item => ({ value: item.code, name: item.code })),
        clinic_desc: data?.extraData.clinicData.map(item => ({ key: item.code, value: item.desc })),

        specialty_code: data?.extraData.specialtyData.map(item => ({ value: item.code, name: item.code })),
        specialty_desc: data?.extraData.specialtyData.map(item => ({ key: item.code, value: item.desc })),

        pharm_loc_code: data?.extraData.pharmacyData.map(item => ({ value: item.code, name: item.code })),
        pharm_loc: data?.extraData.pharmacyData.map(item => ({ key: item.code, value: item.desc })),
    };

    let tableData = Array.isArray(data.clinicPharmMappings)
        ? data.clinicPharmMappings.map(item => {
              const levelOfCare = data?.extraData?.classOfPatientData?.find(st => st.code === item.level_of_care);
              const levelOfCareDisplay = levelOfCare ? levelOfCare?.code : item.level_of_care;

              return {
                  id: item.id,
                  clinic_code: item.clinic_code,
                  clinic_desc: item.clinic_desc,
                  specialty_code: item.specialty_code,
                  specialty_desc: item.specialty_desc,
                  level_of_care: item.level_of_care,
                  level_of_care_display: levelOfCareDisplay,
                  pharm_loc_code: item.pharm_loc_code,
                  pharm_loc: item.pharm_loc,
              };
          })
        : [];

    const columnNames: ColumnNameCustom[] = [
        {
            label: 'Clinic Code',
            name: 'clinic_code',
            editable: true,
            type: ColumnDataType.SingleStringSelect,
            isSearchable: true,
            isRequired: true,
            dependedByColumns: ['clinic_desc'],
        },
        { label: 'Clinic Description', name: 'clinic_desc', editable: false, type: ColumnDataType.String },
        {
            label: 'Specialty Code',
            name: 'specialty_code',
            editable: true,
            type: ColumnDataType.SingleStringSelect,
            isRequired: true,
            isSearchable: true,
            dependedByColumns: ['specialty_desc'],
        },
        { label: 'Specialty Description', name: 'specialty_desc', editable: false, type: ColumnDataType.String },
        {
            label: 'Class Of Patient',
            name: 'level_of_care',
            editable: true,
            type: ColumnDataType.SingleStringSelect,
            isUseDataDisplay: true,
            isSearchable: true,
        },
        {
            label: 'Pharmacy Location Code',
            name: 'pharm_loc_code',
            editable: true,
            type: ColumnDataType.SingleStringSelect,
            isSearchable: true,
            isRequired: true,
            dependedByColumns: ['pharm_loc'],
        },
        { label: 'Pharmacy Location', name: 'pharm_loc', editable: false, type: ColumnDataType.String },
    ];

    const showSuccessMessage = msg => {
        popupModal = true;
        modalTitle = 'Notification';
        modalMessage = msg;
        onlyOkBtn = true;
        if (openUpdateResult) {
            confirmAction = () => {
                handleRefresh();
                standardTable.resetAllTableValue();
            };
        }
    };

    const handleCreateData = async (userClaims, newItems) => {
        await fetch(`${SocURL}/cfg-clinic-pharm-mapping`, {
            method: 'POST',
            body: JSON.stringify({
                data: newItems.map(item => ({
                    id: item.id,
                    clinic_code: item.clinic_code,
                    clinic_desc: item.clinic_desc,
                    specialty_code: item.specialty_code,
                    specialty_desc: item.specialty_desc,
                    level_of_care: item.level_of_care,
                    pharm_loc_code: item.pharm_loc_code,
                    pharm_loc: item.pharm_loc,
                })),
            }),
        });
    };

    const handleUpdateData = async (userClaims, updateItems) => {
        await fetch(`${SocURL}/cfg-clinic-pharm-mapping`, {
            method: 'PATCH',
            body: JSON.stringify({
                data: updateItems.map(item => ({
                    id: item.id,
                    clinic_code: item.clinic_code,
                    clinic_desc: item.clinic_desc,
                    specialty_code: item.specialty_code,
                    specialty_desc: item.specialty_desc,
                    level_of_care: item.level_of_care,
                    pharm_loc_code: item.pharm_loc_code,
                    pharm_loc: item.pharm_loc,
                })),
            }),
        });
    };

    const handleSaveData = async () => {
        const userClaims = get(user).claims;
        let newItems = [],
            updateItems = [];
        updateList.forEach(row => (newRecords.has(row.id) ? newItems.push(row) : updateItems.push(row)));

        if (newItems.length) await handleCreateData(userClaims, newItems);
        if (updateItems.length) await handleUpdateData(userClaims, updateItems);
        openUpdateResult = true;
        showSuccessMessage('Configuration is saved successfully.');
        TriggerService.trigger(triggerTableNames);
    };

    const handleDeleteData = async typeId => {
        await fetch(`${SocURL}/cfg-clinic-pharm-mapping`, {
            method: 'DELETE',
            body: JSON.stringify({ data: [{ id: typeId }] }),
        });
    };

    const onDelete = async () => {
        if (selectedRow && !newRecords.has(selectedRow)) {
            await handleDeleteData(selectedRow);
        }
        tableData = tableData.filter(item => item.id !== selectedRow);
        updateList = updateList.filter(item => item.id !== selectedRow);
        selectedRow = null;
        showSuccessMessage('Successfully deleted configuration!');
    };

    const handleAppendNewRow = () => {
        const newId = uuidv4();
        newData = {
            id: newId,
            clinic_code: '',
            clinic_desc: '',
            specialty_code: '',
            specialty_desc: '',
            level_of_care: '',
            pharm_loc_code: '',
            pharm_loc: '',
            isNew: true,
        };

        tableData = [...tableData, newData];
        newRecords.add(newId);
    };

    const handleRefresh = async () => {
        isLoading.set(true);
        try {
            const res = await fetch(`${SocURL}/cfg-clinic-pharm-mapping?page=1&limit=9999`, {
                method: 'GET',
            });
            const response = await res.json();
            tableData = Array.isArray(response.data)
                ? response.data.map(item => {
                      const levelOfCare = data?.extraData?.classOfPatientData?.find(
                          st => st.code === item.level_of_care
                      );
                      const levelOfCareDisplay = levelOfCare ? levelOfCare?.code : item.level_of_care;
                      return {
                          id: item.id,
                          clinic_code: item.clinic_code,
                          clinic_desc: item.clinic_desc,
                          specialty_code: item.specialty_code,
                          specialty_desc: item.specialty_desc,
                          level_of_care: item.level_of_care,
                          level_of_care_display: levelOfCareDisplay,
                          pharm_loc_code: item.pharm_loc_code,
                          pharm_loc: item.pharm_loc,
                      };
                  })
                : [];
            updateList = [];
            selectedRow = null;
            newRecords = new Set();
        } catch (e) {
            console.error('Failed to refresh data:', e);
        } finally {
            isLoading.set(false);
        }
    };
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <StandardTable
        bind:this={standardTable}
        abilities={data.abilities}
        tableName="Clinic-Pharmacy Mapping"
        {newRecords}
        uniqueKey={['clinic_code', 'specialty_code', 'pharm_loc_code']}
        isDependentField={true}
        data={tableData}
        initData={tableData}
        {subData}
        {extraData}
        {columnNames}
        {newData}
        {FieldNames}
        defaultSortKey="clinic_code"
        bind:updateList
        bind:updatePosition
        bind:searchText
        bind:selectedRow
        allowSelected={true}
        on:add={() => handleAppendNewRow()}
        on:save={() => handleSaveData()}
        on:delete={() => onDelete()}
        on:refresh={() => handleRefresh()} />

    <Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
</Guard>
