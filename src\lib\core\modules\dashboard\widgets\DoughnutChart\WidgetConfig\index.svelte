<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { onMount, onDestroy } from 'svelte';
    import type { SeriesOption } from 'echarts';
    import ThresholdGroupConfigComponent, {
        type ThresholdItem,
        type ThresholdConfig,
        type ThresholdGroupConfig,
    } from '$lib/core/modules/dashboard/widgets/DoughnutChart/ThresholdGroup/index.svelte';
    import { TYPEOF_COMPARATOR } from '$lib/core/modules/dashboard/widgets/utils';

    export interface CharSegment {
        value?: number;
        valid: {
            isValid?: boolean;
            errorMessage?: string;
        };
        colorSegment: string;
    }
    export interface SingleSeriesChartConfig {
        dataSource?: string;
        title?: string;
        showLegend?: boolean;
        positionLegend: 'topLegend' | 'rightLegend';
        showTotal?: boolean;
        fontSizeTotal?: string;
        textColorTotal?: string;
        dataset: SeriesOption | SeriesOption[];
        colorsSeries: string[];
        chartSegment: CharSegment[];
        thresholdGroupConfigs: ThresholdGroupConfig;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'aoh_charts_generic_sample';
    const DEFAULT_TITLE = 'Donut Chart';
    const DEFAULT_FONT_SIZE_TOTAL = '37';
    const DEFAULT_TEXT_COLOR_TOTAL = '#FFFFFF';

    export const FIELD_THRESHOLD = [
        { name: 'Patient', value: 'Patient' },
        { name: 'Max WT', value: 'Max WT' },
    ];

    export let CHART_SEGMENT_DEFAULT = [
        {
            value: 4,
            valid: {
                isValid: true,
                errorMessage: '',
            },
            colorSegment: '#E63244',
        },
        {
            value: 7,
            valid: {
                isValid: true,
                errorMessage: '',
            },
            colorSegment: '#2271B3',
        },
        {
            value: 11,
            valid: {
                isValid: true,
                errorMessage: '',
            },
            colorSegment: '#8A9597',
        },
        {
            value: 14,
            valid: {
                isValid: true,
                errorMessage: '',
            },
            colorSegment: '#955F20',
        },
        {
            value: 24,
            valid: {
                isValid: true,
                errorMessage: '',
            },
            colorSegment: '#CB2821',
        },
        {
            value: 30,
            valid: {
                isValid: true,
                errorMessage: '',
            },
            colorSegment: '#C7B446',
        },
    ];

    export let COLORS_SERIES_DEFAULT = ['#E63244', '#2271B3', '#8A9597', '#955F20', '#CB2821', '#C7B446'];

    export const configIndicatorThresholdItem: ThresholdItem = {
        titleThresholdItem: 'Critical',
        fieldThresholdItem: FIELD_THRESHOLD[0].value,
        comparatorThreshold: TYPEOF_COMPARATOR[0].value,
        valueOfThreshold: 10,
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
        typeItems: TYPEOF_COMPARATOR,
    };

    const INDICATOR_THRESHOLD_CONFIG_ITEMS_RED: Array<ThresholdItem> = [
        {
            titleThresholdItem: 'Red',
            fieldThresholdItem: FIELD_THRESHOLD[0].value,
            comparatorThreshold: TYPEOF_COMPARATOR[0].value,
            valueOfThreshold: 100,
            isValidItem: {
                isValid: true,
                errorMessage: '',
            },
            typeItems: TYPEOF_COMPARATOR,
        },
        {
            titleThresholdItem: 'Red',
            fieldThresholdItem: FIELD_THRESHOLD[1].value,
            comparatorThreshold: TYPEOF_COMPARATOR[0].value,
            valueOfThreshold: 20,
            isValidItem: {
                isValid: true,
                errorMessage: '',
            },
            typeItems: TYPEOF_COMPARATOR,
        },
    ];

    const INDICATOR_THRESHOLD_CONFIG_ITEMS_AMBER: Array<ThresholdItem> = [
        {
            titleThresholdItem: 'Amber',
            fieldThresholdItem: FIELD_THRESHOLD[0].value,
            comparatorThreshold: TYPEOF_COMPARATOR[0].value,
            valueOfThreshold: 70,
            isValidItem: {
                isValid: true,
                errorMessage: '',
            },
            typeItems: TYPEOF_COMPARATOR,
        },
        {
            titleThresholdItem: 'Amber',
            fieldThresholdItem: FIELD_THRESHOLD[1].value,
            comparatorThreshold: TYPEOF_COMPARATOR[0].value,
            valueOfThreshold: 15,
            isValidItem: {
                isValid: true,
                errorMessage: '',
            },
            typeItems: TYPEOF_COMPARATOR,
        },
    ];

    export const configThresholdConfigs: ThresholdConfig = {
        textColorThreshold: '#FF0000',
        isConditionAnd: false,
        thresholdConfigItems: INDICATOR_THRESHOLD_CONFIG_ITEMS_RED,
    };

    const THRESHOLD_GROUP_CONFIG_ITEMS: Array<ThresholdConfig> = [
        {
            textColorThreshold: '#CB2821',
            isConditionAnd: false,
            thresholdConfigItems: INDICATOR_THRESHOLD_CONFIG_ITEMS_RED,
        },
        {
            textColorThreshold: '#ffbf00',
            isConditionAnd: false,
            thresholdConfigItems: INDICATOR_THRESHOLD_CONFIG_ITEMS_AMBER,
        },
    ];

    export let thresholdGroupConfigs: ThresholdGroupConfig = {
        isEnableThreshold: false,
        thresholdGroupConfigItems: THRESHOLD_GROUP_CONFIG_ITEMS,
    };

    export const configDefaults: SingleSeriesChartConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        showLegend: true,
        positionLegend: 'topLegend',
        showTotal: true,
        fontSizeTotal: DEFAULT_FONT_SIZE_TOTAL,
        textColorTotal: DEFAULT_TEXT_COLOR_TOTAL,
        dataset: [],
        colorsSeries: COLORS_SERIES_DEFAULT,
        chartSegment: CHART_SEGMENT_DEFAULT,
        thresholdGroupConfigs: JSON.parse(JSON.stringify(thresholdGroupConfigs)),
    };
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { Label, Input, Helper } from 'flowbite-svelte';
    import { AccordionItem, Accordion } from 'flowbite-svelte';
    import WidgetConfigItem from '$lib/core/modules/dashboard/components/WidgetConfig/WidgetItem.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';
    import WidgetConfigSelect from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigSelect/index.svelte';
    import { e } from 'mathjs';

    let doughtnutChartConfig: HTMLElement;

    export let config: SingleSeriesChartConfig;

    const expectedFormat = [
        { name: 'name', type: 'String' },
        { name: 'value', type: 'numeric' },
    ];

    const positionsLegend = [
        {
            name: 'Top',
            value: 'topLegend',
        },
        {
            name: 'Right',
            value: 'rightLegend',
        },
    ];

    // List of data source names (valid GraphQL query roots)
    const validQueries: Array<DropdownItem> = [];

    // Look for aggregate > sum queries with expected format
    // aggregate > sum follows Hasura's aggregate sum schema
    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => field?.name == format.name && field?.type?.ofType?.name == format.type);
        });

        if (isQueryValid) {
            validQueries.push({ label: query.name, value: query.name });
        }
    });

    const propertyFields = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: validQueries,
                setClass: 'border-b border-b-outline bg-transparent mb-4',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
            },
        },

        {
            id: 'showTotal',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Total',
                checked: config.showTotal,
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end',
            },
            children: [
                {
                    id: 'fontSizeTotal',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Total Text Size',
                        customClass: 'w-1/2 mb-4',
                        pattern: '^[0-9]\\d*$',
                        min: 0,
                    },
                },
                {
                    id: 'textColorTotal',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        customClass: 'w-1/4 text-center mb-4',
                        type: 'color',
                        title: 'Text Color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
        {
            id: 'showLegend',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Legend',
                checked: config.showLegend,
            },
        },

        {
            checkVisible: 'showLegend',
            id: 'positionLegend',
            componentDisplay: WidgetConfigSelect,
            extraProps: {
                title: 'Position Legend',
                items: positionsLegend,
            },
        },
    ];

    // function updateSizeConfigWidget() {
    //     if (doughtnutChartConfig) {
    //         let configurationPanel = doughtnutChartConfig.parentElement.parentElement.parentElement;
    //         configurationPanel.classList.remove('w-1/5');
    //         configurationPanel.classList.add('overflow-x-auto', 'h-[calc(100%-4rem)]', 'overflow-y-auto', 'w-2/5');
    //     }
    // }
    // function resetSizeConfigWidget() {
    //     if (doughtnutChartConfig) {
    //         let configurationPanel = doughtnutChartConfig.parentElement.parentElement.parentElement;
    //         configurationPanel.classList.remove('overflow-x-auto', 'h-[calc(100%-4rem)]', 'overflow-y-auto', 'w-2/5');
    //         configurationPanel.classList.add('w-1/5');
    //     }
    // }

    export function validationSegment() {
        for (let i = 0; i < config.chartSegment.length - 2; i++) {
            config.chartSegment[i].valid = {
                isValid: true,
                errorMessage: '',
            };
            if (Number(config.chartSegment[i].value) >= Number(config.chartSegment[i + 1].value)) {
                config.chartSegment[i].valid = {
                    isValid: false,
                    errorMessage: 'The current segment value must be less than next segment value.',
                };

                return;
            }
        }

        config.chartSegment = config.chartSegment;
        return;
    }
    let column1 = config.chartSegment.filter((item, index) => index % 3 === 0);

    $: if (Boolean(config.chartSegment)) {
        validationSegment();
    }

    // onMount(async () => {
    //     updateSizeConfigWidget();
    // });
    // onDestroy(async () => {
    //     resetSizeConfigWidget();
    // });
</script>

<div class="text-on-surface flex flex-col gap-3" bind:this={doughtnutChartConfig}>
    <Accordion>
        <AccordionItem open>
            <span slot="header">Properties</span>
            <WidgetConfigItem bind:config widgetFields={propertyFields} />
        </AccordionItem>
        <AccordionItem>
            <span slot="header">Series Chart Config</span>
            <Accordion flush>
                <AccordionItem>
                    <span slot="header">Chart Segment</span>
                    {#if config?.chartSegment}
                        {#each config?.chartSegment as item, i}
                            <div class="flex-warp mb-2 flex-col">
                                <div class="flex">
                                    <Label class="mr-1 self-center text-white">Segment {i + 1}:</Label>
                                    {#if i === config.chartSegment.length - 1}
                                        <span class="mr-0.5 w-24 self-center text-center text-sm text-white"
                                            >Max WT</span>
                                    {:else}
                                        <Input
                                            bind:value={item.value}
                                            type="number"
                                            class="mr-0.5 h-10 w-24"
                                            min="0"
                                            oninput="validity.valid||(value='');"
                                            required />
                                    {/if}

                                    <Input bind:value={item.colorSegment} type="color" class="ml-[1%] h-10 w-10 p-0" />
                                </div>
                                {#if !item.valid.isValid}
                                    <div class="flex">
                                        <Helper class="text-sm font-medium" color="red">
                                            <span>{item.valid.errorMessage}</span>
                                        </Helper>
                                    </div>
                                {/if}
                            </div>
                        {/each}
                    {/if}
                </AccordionItem>
                <AccordionItem>
                    <span slot="header">Threshold</span>

                    <Accordion flush>
                        <div class="px-2">
                            <ThresholdGroupConfigComponent
                                bind:thresholdGroupConfigs={config.thresholdGroupConfigs}
                                isDisplayBgColor={false} />
                        </div>
                    </Accordion>
                </AccordionItem>
            </Accordion>
        </AccordionItem>
    </Accordion>
</div>
