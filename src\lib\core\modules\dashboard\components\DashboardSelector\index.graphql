fragment UserDashboards on aoh_web_layout {
    id
    name
    description
    favourite
    layout__tags {
        tag {
            text
        }
    }
    configuration
}

fragment DashboardTags on aoh_web_tag {
    id
    text
}

query FetchUserDashboards($userId: uuid) {
    aoh_web_layout(where: { user_id: { _eq: $userId } }, order_by: { name: asc }) {
        ...UserDashboards
    }
}

query FetchDashboardTags {
    aoh_web_tag {
        ...DashboardTags
    }
}

subscription ObserveUserDashboards($userId: uuid) {
    aoh_web_layout(where: { user_id: { _eq: $userId } }, order_by: { name: asc }) {
        ...UserDashboards
    }
}

subscription ObserveDashboardTags {
    aoh_web_tag {
        ...DashboardTags
    }
}
