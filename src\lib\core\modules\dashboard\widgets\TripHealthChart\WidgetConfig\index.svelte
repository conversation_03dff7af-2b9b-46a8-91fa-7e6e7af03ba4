<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface Config {
        title?: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'Trip Health Picture';

    export const configDefaults: Config = {
        title: DEFAULT_TITLE,
    };
</script>

<script lang="ts">
    //Components
    import TextField from '$lib/core/components/TextField/index.svelte';
    import { INPUT_CLASS, LABEL_CLASS } from '../../utils';

    export let config: Config;
</script>

<div class="text-on-surface flex flex-col gap-3">
    <TextField placeholder={'Title'} bind:value={config.title} setClass={INPUT_CLASS} setLabelClass={LABEL_CLASS} />
</div>
