<script lang="ts">
    import { createEventDispatcher, onMount } from 'svelte';
    import { Heading } from 'flowbite-svelte';

    import CRUDSearchBar from '$lib/shared/components/table/CRUDSearchBar.svelte';
    import Table from './Table.svelte';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import Loading from '$lib/shared/components/loading/Loading.svelte';
    import { generateId } from '$lib/shared/util/customId';
    import { parseJSON } from '$lib/shared/util/dataConvert';
    import { POPUP_MESSAGES } from '$lib/shared/util/popupMessage';
    import { displayMessage } from '$lib/shared/util/validation';
    import { ColumnDataType, DataInputType, INV_STYLES, type ColumnName } from '$lib/shared/constants/general';
    import { TriggerService } from '$lib/service/trigger';

    export let figureName = '';
    export let baseURL;
    export let columnNames: ColumnName[] = [];
    export let searchColumns = null;
    export let defaultSortKey = '';
    export let toggleConfigName: string = null;
    export let toggleBars = [];
    export let triggerTableNames = null;

    export let validateAllData = null;
    export let validateNewData = null;

    export let subData = null;
    export let abilities = [];

    let tableData;
    let searchData;

    let popupModal = false;
    let modalTitle = 'Notification';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;

    // for adv search
    $: if (!searchColumns && columnNames?.length) {
        searchColumns = columnNames?.map(col => ({ label: col.value, type: col.type, name: col.key }));
    }
    let searchText = '';
    let displayRecordIds = [];

    let newRecords = new Set();
    let selectedRow = null;
    let isDelete = false;
    let isLoading = false;
    let isRefresh = false;

    let updateList = [];

    let cfgParameter = null;
    const dispatch = createEventDispatcher<{ saveSuccess: boolean }>();

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    const defaultPosition: UpdatePosition = { id: '', columnName: '', isEditing: false };
    let updatePosition: UpdatePosition = structuredClone(defaultPosition);

    const getData = async () => {
        const response = await fetch(baseURL);
        if (response.status === 200) {
            const json = await response.json();
            tableData = json?.data || [];
        } else {
            tableData = [];
        }

        if (tableData?.length) {
            displayRecordIds = tableData?.map(item => item.id);
            searchData = structuredClone(tableData);
            if (subData) {
                searchData = searchData.map(row => {
                    let newRow = { ...row };
                    Object.entries(subData).forEach(([key, data]) => {
                        let text = '';
                        let vals = newRow[key];
                        if (vals) {
                            if (typeof vals === 'string') vals = vals.split(',');
                            vals.forEach(val => {
                                const row = (data as any)?.find(e => e.value === val);
                                text += row?.name || '';
                            });
                        }
                        newRow[key] = text;
                    });
                    return newRow;
                });
            }
        }
    };

    const loadCfgParameter = async () => {
        if (!toggleConfigName) return;

        try {
            let response = await fetch(`/api/CfgParameterService/${toggleConfigName}`, { method: 'GET' });
            let json = await response.json();
            cfgParameter = json.cfgParam;
            if (cfgParameter?.id) {
                const cfg = parseJSON(cfgParameter?.key_value_json);
                if (cfg) {
                    for (const [key, value] of Object.entries(cfg)) {
                        const idx = toggleBars?.findIndex(t => t.key === key);
                        if (idx !== -1) toggleBars[idx].value = value;
                    }
                }
            }
        } catch (e) {
            console.error('Fail to get cfg parameter', e);
        }
    };

    const mergeData = (tableData, updateList) => {
        const mergedMap = new Map();
        let mergeData = [];

        updateList.forEach(item => {
            mergedMap.set(item.id, { ...item });
        });

        tableData.forEach(item => {
            if (mergedMap.has(item.id)) {
                mergeData.push(mergedMap.get(item.id));
            } else {
                mergeData.push(item);
            }
        });
        return mergeData;
    };

    const showMessage = (message, isRefresh = false, title = null) => {
        popupModal = true;
        modalTitle = title ? title : 'Notification';
        modalMessage = message;
        onlyOkBtn = true;
        confirmAction = isRefresh ? async () => await onRefresh() : () => {};
    };

    const mapDataToSave = data => {
        return data?.map(item => {
            let newItem = { ...item };
            columnNames.forEach(col => {
                const colName = col.key;
                const colType = col.type;
                const colInputType = col.inputType;
                newItem[colName] = item[colName];

                // Specific case for List of Specialty
                if (colName === 'alt_tgt_days') {
                    newItem[colName] = item[colName] || item[colName] === 0 ? item[colName] + '' : null;
                }

                if (!newItem[colName]) {
                    if (colType === ColumnDataType.Boolean) newItem[colName] = false;
                    if (colType === ColumnDataType.String) {
                        newItem[colName] = '';
                        if (colInputType === DataInputType.MultiSelect) newItem[colName] = null;
                    }
                }
            });
            return newItem;
        });
    };

    const handleSaveData = async (data, isUpdated = false) => {
        const method = isUpdated ? 'PATCH' : 'POST';

        const res = await fetch(baseURL, {
            method,
            body: JSON.stringify({ data: mapDataToSave(data) }),
        });
        if (res.status === 200) {
            const response = await res.json();
            return response.res;
        }
        return false;
    };

    const saveData = async (tableData, updateList, newRecords) => {
        let validateMsg = '';

        if (!!validateNewData) {
            validateMsg = validateNewData(updateList);
            if (validateMsg) {
                showMessage(validateMsg);
                return;
            }
        }

        if (!!validateAllData) {
            const finalData = mergeData(tableData, updateList);
            validateMsg = validateAllData(finalData);
            if (validateMsg) {
                showMessage(validateMsg);
                return;
            }
        }

        let newTypes = [];
        let updateTypes = [];

        const tableSet = new Set(tableData.map(item => JSON.stringify(Object.entries(item).sort())));
        const newUpdateList = updateList?.filter(item => !tableSet.has(JSON.stringify(Object.entries(item).sort())));

        for (const row of newUpdateList) {
            if (newRecords.has(row.id)) {
                newTypes.push(row);
            } else {
                updateTypes.push(row);
            }
        }

        let isCreateOK = newTypes.length ? await handleSaveData(newTypes) : null;
        let isUpdateOK = updateTypes.length ? await handleSaveData(updateTypes, true) : null;
        dispatch('saveSuccess', true);

        const { key, message } = displayMessage(isCreateOK, isUpdateOK);
        if (message) showMessage(message, true);
        if (key?.includes('true')) TriggerService.trigger(triggerTableNames);
    };

    const onSave = async () => {
        isLoading = true;
        await saveData(tableData, updateList, newRecords);
        isLoading = false;
    };

    const onAddNew = () => {
        const newId = generateId();
        let newRow = { id: newId, isNew: true };
        columnNames.forEach(col => {
            const colName = col.key;
            const colType = col.type;
            const colInputType = col.inputType;
            if (colType === ColumnDataType.Boolean) newRow[colName] = false;
            else if (colType === ColumnDataType.String) {
                newRow[colName] = '';
                if (colInputType === DataInputType.MultiSelect) newRow[colName] = null;
            } else newRow[colName] = null;
        });

        // Specific for OT Room Configuration
        if (figureName === 'OT Room Configuration' && 'case_priority' in newRow) {
            newRow.case_priority = 'Elective OT';
        }

        tableData = [...tableData, newRow];
        displayRecordIds.push(newId);
        newRecords.add(newId);

        setTimeout(() => {
            document.getElementById(newId)?.scrollIntoView({ block: 'end', behavior: 'smooth' });
        }, 300);
    };

    const onDelete = () => {
        isDelete = !!selectedRow;
    };

    const deleteRows = async typeId => {
        const res = await fetch(baseURL, {
            method: 'DELETE',
            body: JSON.stringify({ data: [{ ID: typeId }] }),
        });
        if (res.status === 200) {
            const response = await res.json();
            return response.res;
        }

        return false;
    };

    const handleDeleteRow = async () => {
        isLoading = true;
        let result;
        if (!newRecords.has(selectedRow)) {
            result = await deleteRows(selectedRow);
            if (result) showMessage(POPUP_MESSAGES.deleteSuccess, true);
            else showMessage(POPUP_MESSAGES.deleteFail);
        }
        if (result == true || newRecords.has(selectedRow)) {
            tableData = tableData.filter(item => item.id !== selectedRow);
            updateList = updateList.filter(item => item.id !== selectedRow);
            selectedRow = null;
        }
        isLoading = false;
    };

    const onRefresh = async () => {
        isLoading = true;
        isRefresh = true;
        selectedRow = null;
        searchText = '';
        updateList = [];
        updatePosition = structuredClone(defaultPosition);
        await loadCfgParameter();
        await getData();
        isLoading = false;
    };

    const onSaveToggle = async () => {
        if (!toggleConfigName) return;

        isLoading = true;
        const newKeyValueJson = {};
        toggleBars.forEach(bar => (newKeyValueJson[bar.key] = bar.value));

        const newCfgParameter = { ...cfgParameter, key_value_json: newKeyValueJson };
        let method = 'PATCH';
        let url = '/api/CfgParameterService';

        if (!cfgParameter?.id) {
            newCfgParameter.name = toggleConfigName;
            newCfgParameter.title = toggleConfigName;
            newCfgParameter.category = 'data_aggregation';
            newCfgParameter.form_fields_json = null;
            newCfgParameter.sequence = 1;
            method = 'POST';
        } else {
            url += `/${cfgParameter?.id}`;
        }

        try {
            const res = await fetch(url, { method, body: JSON.stringify(newCfgParameter) });
            if (!res.ok) {
                showMessage(POPUP_MESSAGES.cfgParamSaveFail, true);
            } else {
                // Update filtering config for all SOC US
                await fetch(`/api/soc/cfg-parameter`, { method: 'PATCH', body: JSON.stringify(newCfgParameter) });
            }
        } catch (e) {
            showMessage(POPUP_MESSAGES.cfgParamSaveFail, true);
        }
        await loadCfgParameter();
        isLoading = false;
    };

    onMount(async () => {
        isLoading = true;
        await getData();
        await loadCfgParameter();
        isLoading = false;
    });
</script>

<div class={INV_STYLES.fullTable}>
    <div class={INV_STYLES.header}>
        <Heading tag="h5">{figureName}</Heading>
    </div>
    <div class="m-4">
        <CRUDSearchBar
            bind:searchText
            bind:displayRecordIds
            {abilities}
            {onSave}
            {onAddNew}
            {onDelete}
            {onRefresh}
            {searchColumns}
            items={searchData}
            {toggleBars}
            {onSaveToggle} />
    </div>
    <div>
        {#if tableData}
            <Table
                {abilities}
                data={tableData}
                {subData}
                {columnNames}
                {displayRecordIds}
                {defaultSortKey}
                bind:isRefresh
                bind:updateList
                bind:updatePosition
                bind:selectedRow
                allowSelected={true} />
        {/if}
    </div>
</div>
<Notification
    bind:isOpen={isDelete}
    modalTitle="Confirmation"
    modalMessage={POPUP_MESSAGES.deleteConfirm}
    confirmAction={handleDeleteRow} />
<Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
<Loading bind:isOpen={isLoading} />
