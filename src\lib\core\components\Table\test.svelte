<script lang="ts">
    import Table from '$lib/core/components/Table/index.svelte';

    export const BED_TABLE = [
        {
            title: 'Name',
            key: 'name',
            sortable: true,
            value: (v: any) => {
                return v.name;
            },
        },
        {
            title: 'Age',
            key: 'age',
            sortable: true,
            value: (v: any) => {
                return v.age;
            },
        },
    ];

    let data = [
        {
            name: 'tester1',
            age: 21,
        },
        {
            name: 'tester2',
            age: 22,
        },
        {
            name: 'tester3',
            age: 23,
        },
        {
            name: 'tester4',
            age: 24,
        },
        {
            name: 'tester5',
            age: 25,
        },
        {
            name: 'tester6',
            age: 26,
        },
        {
            name: 'tester7',
            age: 27,
        },
        {
            name: 'tester8',
            age: 28,
        },
        {
            name: 'tester9',
            age: 29,
        },
    ];
</script>

<div class="flex flex-col space-y-5 bg-surface-1 p-5 text-on-surface">
    <Table columns={BED_TABLE} rows={data} />
</div>
