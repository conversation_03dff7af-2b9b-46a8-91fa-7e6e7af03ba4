<script lang="ts">
    import { onMount } from 'svelte';

    import OTRoomCRUD from '$lib/core-soc/modules/crud/OTRoomCRUD.svelte';
    import { user } from '$lib/stores/Auth';
    import { get } from 'svelte/store';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';

    export let data;
    const subData = {
        room: data?.rooms?.map(i => ({ value: i.room_external_id, name: i.room_name })),
        case_priority: [{ name: 'Blank', value: '' }, ...data?.casePriority],
        surgical_service: data?.surgical?.map(i => ({ value: i.surgical_service, name: i.surgical_service })),
    };
    let user_id = '';

    onMount(() => {
        user_id = get(user)?.claims?.hasura_access?.['x-hasura-user-id'];
    });
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <div class="flex flex-col gap-20 p-3 text-on-surface-1">
        {#if user_id !== ''}
            <OTRoomCRUD {subData} abilities={data.abilities} />
        {/if}
    </div>
</Guard>