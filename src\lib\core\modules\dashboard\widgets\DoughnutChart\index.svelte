<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { faPieChart } from '@fortawesome/free-solid-svg-icons';

    export const widgetTitle = 'Donut Chart';
    export const widgetIcon = faPieChart;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetMinWidth = 3;
    export const widgetMinHeight = 3;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import type { EChartsOption, EChartsType } from 'echarts';
    import { onDestroy, onMount } from 'svelte';
    import { type Subscription } from 'wonka';
    import type { SingleSeriesChartConfig } from './WidgetConfig/index.svelte';
    import { COMPARISON_OPERATORS_HASH } from '$lib/core/modules/dashboard/widgets/utils';
    import type { forEach } from 'lodash';

    export let config: SingleSeriesChartConfig;

    let chartContainer: HTMLElement;
    let chart: EChartsType;
    let option: EChartsOption;
    let subscription: Subscription;
    let data = [
        1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 5, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5, 5, 6, 5, 6, 5, 6, 5, 6,
        5, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5, 6, 5, 8, 9, 10, 8, 9, 10, 8, 9, 10, 8, 9, 10, 8, 9, 10, 8, 9, 10, 8, 9, 10, 8,
        12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 13, 14, 12, 15, 16, 17, 18, 19, 20, 21, 22, 23, 15, 16, 17, 18, 19, 20,
        21, 22, 23, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 25, 26, 27, 28, 29, 25, 26, 27, 28, 29, 25,
    ];
    let dataSeries = [
        { name: '4hr', valueSegment: 4, value: 10, selected: true, color: '#E63244' },
        { name: '7hr', valueSegment: 7, value: 40, selected: true, color: '#2271B3' },
        { name: '11hr', valueSegment: 11, value: 22, selected: true, color: '#8A9597' },
        { name: '14hr', valueSegment: 14, value: 13, selected: true, color: '#955F20' },
        { name: '24hr', valueSegment: 24, value: 27, selected: true, color: '#CB2821' },
        { name: '30hr', valueSegment: 30, value: 16, selected: true, color: '#C7B446' },
    ];

    let dataSeriesCustom = [];
    let dataSeriesCustomLabel = [];

    config.dataset = JSON.parse(JSON.stringify(dataSeries));
    let titleColor = '#FFFFFF';
    let titleFontSize = 12;

    function customLegend() {
        let tempDataSeriesCustom = [];
        let tempDataSeriesCustomLabel = [];

        let count = 0;
        if (!config.chartSegment.some(x => x.valid.isValid === false)) {
            for (let i = 0; i < config.chartSegment.length; i++) {
                for (let j = 0; j < data.length; j++) {
                    if (i === 0 && 0 < Number(data[j]) && Number(data[j]) <= Number(config.chartSegment[i].value)) {
                        count++;
                    } else if (
                        i !== 0 &&
                        Number(config.chartSegment[i - 1].value) < Number(data[j]) &&
                        Number(data[j]) <= Number(config.chartSegment[i].value)
                    ) {
                        count++;
                    }
                }

                if (i === 5 && count === 0) {
                    break;
                }

                let arr = dataSeriesCustom.length > 0 ? dataSeriesCustom : dataSeries;
                let itemSeries = arr.filter(item => {
                    return item.name == config.chartSegment[i].value + 'hr';
                });

                let arr1 = dataSeriesCustomLabel.length > 0 ? dataSeriesCustom : dataSeries;
                let itemSeries1 = arr1.filter(item => {
                    return item.name == config.chartSegment[i].value + 'hr';
                });

                tempDataSeriesCustomLabel.push({
                    name: config.chartSegment[i].value + 'hr',
                    value: count,
                    selected: itemSeries1.length > 0 ? itemSeries1[0].selected : count === 0 ? false : true,
                });
                if (count === 0) {
                    count = 8;
                }

                tempDataSeriesCustom.push({
                    name: config.chartSegment[i].value + 'hr',
                    value: count,
                    selected: itemSeries.length > 0 ? itemSeries[0].selected : count === 0 ? false : true,
                    color: config.chartSegment[i].colorSegment,
                });

                count = 0;
            }
        }
        return { tempDataSeriesCustom, tempDataSeriesCustomLabel };
    }

    function __setColorByThreshold() {
        if (!dataSeries && !config.thresholdGroupConfigs) {
            return;
        }
        let totaPaitient = __total(dataSeriesCustomLabel);
        let maxWaitTime = parseInt(dataSeries[dataSeries.length - 1].name.replace('hr', ''));
        let thresholdConfigArr = config.thresholdGroupConfigs.thresholdGroupConfigItems;

        titleColor = '#4c4c4c';
        function __setColorByConfigThreshold(arrConfigThreshold, valuePatientToCompare, valueWTToCompare) {
            let thresholdSeleted = false;
            for (let i = 0; i < arrConfigThreshold.length; i++) {
                let checkPaitent = true;
                let checkWT = true;
                arrConfigThreshold[i].thresholdConfigItems.forEach((j, k) => {
                    const comparatorValue = j.comparatorThreshold;
                    const thresholdValue = Number.parseInt(j.valueOfThreshold);

                    if (j.fieldThresholdItem === 'Patient') {
                        checkPaitent = COMPARISON_OPERATORS_HASH[comparatorValue](
                            valuePatientToCompare,
                            thresholdValue
                        );
                    }

                    if (j.fieldThresholdItem === 'Max WT') {
                        checkWT = COMPARISON_OPERATORS_HASH[comparatorValue](valueWTToCompare, thresholdValue);
                    }
                });

                if ((checkPaitent || checkWT) && !arrConfigThreshold[i].isConditionAnd) {
                    titleColor = arrConfigThreshold[i].textColorThreshold;
                    thresholdSeleted = true;
                }
                if (checkPaitent && checkWT && arrConfigThreshold[i].isConditionAnd) {
                    titleColor = arrConfigThreshold[i].textColorThreshold;
                    thresholdSeleted = true;
                }

                if (thresholdSeleted) {
                    break;
                }
            }
        }

        __setColorByConfigThreshold(thresholdConfigArr, totaPaitient, maxWaitTime);
    }

    function __total(arrSeries): number {
        return arrSeries
            .filter(item => {
                return item.selected == true;
            })
            .map(item => {
                return item.value;
            })
            .reduce((cumulative, currentValue) => {
                return cumulative + currentValue;
            }, 0);
    }

    function __configDynamic() {
        let heightLegend = getHeight();
        let widthLegend = getWidth();
        let widthChart = chart.getWidth();
        let heightChart = chart.getHeight();
        let centerPoint = { x: (widthChart - widthLegend) / 2, y: (heightChart - heightLegend) / 2 };
        let centerPointToClosestEdge = centerPoint.x < centerPoint.y ? centerPoint.x : centerPoint.y;
        let r0 = (centerPointToClosestEdge * 40) / 100;
        __setColorByThreshold();

        option = {
            ...option,
            legend: {
                ...option.legend,
                show: config?.showLegend,
                textStyle: {
                    color: '#fff',
                    fontSize: r0 / 6,
                },
            },
            title: {
                ...option.title,
                show: config?.showTotal,
                left: valuePositionLeftTextTotal(config.fontSizeTotal),
                top: valuePositionTopTextTotal(config.fontSizeTotal),
                textStyle: {
                    color: config.thresholdGroupConfigs.isEnableThreshold ? titleColor : config.textColorTotal,
                    fontSize: config.fontSizeTotal,
                },
            },
            color: dataSeriesCustom.map(item => {
                return item.color;
            }),
        };

        chart?.setOption(option);
    }

    const valueToAngel = (value, arrSeries) => {
        return (((value * 100) / __total(arrSeries)) * 360) / 100;
    };
    const angelToRadian = angel => {
        return (angel * Math.PI) / 180;
    };
    const valueToRadian = (value, arrSeries) => {
        return angelToRadian(valueToAngel(value, arrSeries) - 90);
    };

    const valuePositionTopTextTotal = value => {
        if (config.positionLegend !== 'topLegend') {
            return 'center';
        }
        return (chart.getHeight() + (config.showLegend ? 25 : -10) - Number(value)) / 2;
    };

    const valuePositionLeftTextTotal = value => {
        if (config.positionLegend !== 'rightLegend') {
            return 'center';
        }
        return (chart.getWidth() - 20 + (config.showLegend ? -58 : 15)) / 2 - Number(value);
    };

    const dictType = {
        get topLegend() {
            return {
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 0,
                    right: undefined,
                    left: 'center',
                    textStyle: {
                        color: '#fff',
                    },
                },
            };
        },
        get rightLegend() {
            return {
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    top: 'middle',
                    right: '4%',
                    left: undefined,
                    textStyle: {
                        color: '#fff',
                    },
                },
            };
        },
    };

    function getHeight() {
        if (config.showLegend && config.positionLegend === 'topLegend') {
            return 25 + 10;
        }
        return 0;
    }

    function getWidth() {
        if (config.showLegend && config.positionLegend === 'rightLegend') {
            return 25 + 10 + 40;
        }
        return 0;
    }

    const __configStatic = () => {
        let cl = customLegend();
        dataSeriesCustom = cl.tempDataSeriesCustom;
        dataSeriesCustomLabel = cl.tempDataSeriesCustomLabel;

        let heightLegend = getHeight();
        let widthLegend = getWidth();
        let widthChart = chart.getWidth();
        let heightChart = chart.getHeight();
        let centerPoint = { x: (widthChart - widthLegend) / 2, y: (heightChart - heightLegend) / 2 };
        let centerPointToClosestEdge = centerPoint.x < centerPoint.y ? centerPoint.x : centerPoint.y;
        let r0 = (centerPointToClosestEdge * 40) / 100;
        let r1 = (centerPointToClosestEdge * 70) / 100;
        titleFontSize = r0 / 2;
        option = {
            title: {
                show: true,
                text: __total(dataSeriesCustomLabel).toString(),
                textStyle: {
                    color: 'white',
                    fontSize: titleFontSize,
                },
            },

            series: [
                {
                    type: 'pie',
                    radius: [r0, r1],
                    center: [centerPoint.x, centerPoint.y + heightLegend],
                    label: {
                        show: false,
                    },
                    labelLayout: {
                        hideOverlap: false,
                    },
                },
            ],
        };
        option = { ...option, ...dictType[config.positionLegend] };
        option.series[0].data = dataSeriesCustom;
        if (!option.series[0].markLine) {
            __calculateLine(centerPoint, r0, r1, heightLegend);
        }
        if (!option.series[0].markPoint) {
            __calculatePoint(centerPoint, r1, heightLegend);
        }
    };

    onMount(async () => {
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            if (chart) {
                chart.resize();

                __configStatic();
                __configDynamic();
            }
        });

        const echarts = await import('echarts');

        if (chartContainer) {
            chart = echarts.init(chartContainer, null, { renderer: 'svg' });
            __configStatic();
            __configDynamic();

            chart.on('legendselectchanged', function (params: any) {
                for (let i = 0; i < dataSeriesCustom.length; i++) {
                    if (dataSeriesCustom[i].name == params.name) {
                        dataSeriesCustom[i].selected = params.selected[dataSeriesCustom[i].name];
                        break;
                    }
                }

                for (let i = 0; i < dataSeriesCustomLabel.length; i++) {
                    if (dataSeriesCustomLabel[i].name == params.name) {
                        dataSeriesCustomLabel[i].selected = params.selected[dataSeriesCustomLabel[i].name];
                        break;
                    }
                }
                __configStatic();
                __configDynamic();
            });
        }
        window.addEventListener('resize', () => {
            chart.resize();
        });
    });

    function __calculateLine(centerPoint, r0, r1, heightLegend) {
        let markLineOption = {
            symbol: 'none',
            lineStyle: {
                width: 2,
                type: 'solid',
                color: '#fff',
            },
            label: {
                show: true,
                position: 'start',
                distance: r0 / 4,
                fontSize: r0 / 5,
                fontWeight: 'bold',
                color: '#fff',
            },
        };
        let markLineData = [];
        let cumulative = 0;
        let minor = r1;

        let arrLegend = dataSeriesCustom.filter(item => {
            return item.selected == true;
        });
        for (let i = 0; i < arrLegend.length; i++) {
            if (i == arrLegend.length - 1) {
                minor = r1 + r0 / 4;
                markLineOption.label = { ...markLineOption.label, distance: 5 };
            }
            cumulative += arrLegend[i].value;
            markLineData.push([
                {
                    name: arrLegend[i].name,
                    x: centerPoint.x + Math.cos(valueToRadian(cumulative, dataSeriesCustom)) * minor,
                    y: centerPoint.y + heightLegend + Math.sin(valueToRadian(cumulative, dataSeriesCustom)) * minor,
                    ...markLineOption,
                },
                {
                    x: centerPoint.x + Math.cos(valueToRadian(cumulative, dataSeriesCustom)) * r0,
                    y: centerPoint.y + heightLegend + Math.sin(valueToRadian(cumulative, dataSeriesCustom)) * r0,
                    symbol: 'none',
                },
            ]);
        }
        option.series[0].markLine = { data: markLineData };
    }

    function __calculatePoint(centerPoint, r1, heightLegend) {
        let centerPointToClosestEdge = centerPoint.x < centerPoint.y ? centerPoint.x : centerPoint.y;
        let r0 = (centerPointToClosestEdge * 40) / 100;
        let markPointOption = {
            symbol: 'circle',
            symbolSize: r0 / 2,

            label: {
                color: '#000',
                fontSize: r0 / 4,
                fontFamily: 'arial',
                fontWeight: 'bold',
            },
        };
        let markPointData = [];
        let cumulative = 0;
        let preValue = 0;
        let colorOuterLablel = '';

        let arrLegend = dataSeriesCustom.filter(item => {
            return item.selected == true;
        });

        let arrLegendLable = dataSeriesCustomLabel.filter(item => {
            return item.selected == true;
        });

        for (let i = 0; i < arrLegendLable.length; i++) {
            cumulative = arrLegend[i].value / 2 + preValue;
            preValue += arrLegend[i].value;
            colorOuterLablel = arrLegend[i].color;
            markPointData.push({
                x: centerPoint.x + Math.cos(valueToRadian(cumulative, dataSeriesCustom)) * r1,
                y: centerPoint.y + heightLegend + Math.sin(valueToRadian(cumulative, dataSeriesCustom)) * r1,
                value: arrLegendLable[i].value,
                itemStyle: {
                    color: '#fff',
                    borderWidth: 2,
                    borderColor: colorOuterLablel,
                },
                ...markPointOption,
            });
        }
        option.series[0].markPoint = { data: markPointData };
    }

    $: if (Boolean(chart) && Boolean(config)) {
        chart.resize();
        __configStatic();

        __configDynamic();
    }

    onDestroy(() => {
        subscription?.unsubscribe();
    });
</script>

<div class="flex h-full w-full flex-col">
    <header
        class="flex h-12 items-center gap-4 px-3 py-1 pb-1 text-on-background {config.title.length > 0
            ? ''
            : 'hidden'}">
        <p class="bg-transparent text-base">{config.title}</p>
    </header>
    <div data-tag="chart-container" class="h-full w-full" bind:this={chartContainer} />
</div>
