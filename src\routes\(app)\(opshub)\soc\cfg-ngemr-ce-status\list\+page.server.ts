import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
import { checkPermissions } from '$lib/shared/util/acl';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, fetch }) => {
    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const access_token = locals.user.access_token;
    let permissionNeedToCheck = [
        {
            scopeId: SCOPE.READ,
            resourceId: RESOURCE.SOC_CE_STATUS,
        },
        {
            scopeId: SCOPE.CREATE,
            resourceId: RESOURCE.SOC_CE_STATUS,
        },
        {
            scopeId: SCOPE.UPDATE,
            resourceId: RESOURCE.SOC_CE_STATUS,
        },
        {
            scopeId: SCOPE.DELETE,
            resourceId: RESOURCE.SOC_CE_STATUS,
        },
        {
            scopeId: SCOPE.TOGGLE,
            resourceId: RESOURCE.SOC_CE_STATUS,
        },
    ];
    const abilities = await checkPermissions({
        token: access_token,
        tenantId,
        userId,
        permissions: permissionNeedToCheck,
    });
    return {
        abilities: abilities,
    };
};
