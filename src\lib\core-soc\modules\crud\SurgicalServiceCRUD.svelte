<script lang="ts">
    import SocCrud from '$lib/core-soc/components/SocCRUD.svelte';
    import { ColumnDataType, DataInputType, type ColumnName } from '$lib/shared/constants/general';
    import { POPUP_MESSAGES } from '$lib/shared/util/popupMessage';
    import { validateCharacterLength } from '$lib/shared/util/validation';
    import { isNullOrEmpty } from '$routes/(app)/(opshub)/incident/utils/input_utils';

    export let subData = null;
    export let abilities;

    const baseURL = '/api/ot/cfg-ngemr-surgical-service';
    const figureName = 'Surgery Service';
    const triggerTableNames = 'cc3_hoc.cfg_ngemr_surgical_service';

    const columnNames: ColumnName[] = [
        {
            key: 'surgical_service',
            value: 'Surgical Service',
            editable: true,
            type: ColumnDataType.String,
            required: true,
        },
        {
            key: 'specialty',
            value: 'Specialty',
            editable: true,
            type: ColumnDataType.String,
            inputType: DataInputType.DropDown,
        },
    ];
    const validateAllData = (allData): string | null => {
        const serviceSet = new Set();
        for (const row of allData) {
            if (isNullOrEmpty(row.surgical_service)) {
                return POPUP_MESSAGES.emptyValue.replace('{field}', 'Surgical Service');
            }

            const service = row.surgical_service?.replaceAll(' ', '');
            if (serviceSet.has(service)) {
                return POPUP_MESSAGES.duplicateRow.replace('{value}', row.surgical_service);
            }
            serviceSet.add(service);
        }

        return null;
    };

    const validateNewData = (newData): string | null => {
        if (!validateCharacterLength(newData, columnNames, true)) {
            return POPUP_MESSAGES.lengthLimit;
        }

        return null;
    };
</script>

<SocCrud
    {baseURL}
    {subData}
    {figureName}
    {columnNames}
    {triggerTableNames}
    {validateAllData}
    {validateNewData}
    {abilities}
    defaultSortKey="surgical_service" />
