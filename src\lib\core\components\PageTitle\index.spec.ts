import { get } from 'svelte/store';
import { describe, expect, it } from 'vitest';

import { isDirty, title } from '$lib/core/components/PageTitle/index.svelte';

describe('page store', () => {
    it('has a store that contains the title', () => {
        expect(get(title)).toBe('');
    });

    it('has a store that tracks whether the page has been dirtied so the user can be prompted before switching pages', () => {
        expect(get(isDirty)).toBe(false);
    });
});
