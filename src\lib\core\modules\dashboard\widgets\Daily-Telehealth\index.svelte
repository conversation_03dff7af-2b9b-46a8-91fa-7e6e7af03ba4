<script lang="ts" context="module">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { logger } from '$lib/stores/Logger';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import { onMount } from 'svelte';
    import type { DailyTeleHealthConfig } from './WidgetConfig/index.svelte';
    import {
        GetDailyTelehealthThresholdDocument,
        type GetDailyTelehealthThresholdSubscription,
        GetFilteringByUserIdDocument,
        type GetFilteringByUserIdQuery,
        GetOverviewDailyTelehealthServiceDocument,
        type GetOverviewDailyTelehealthServiceSubscription,
        GetSpecialtiesByGroupsDocument,
        type GetSpecialtiesByGroupsQuery,
        type Cc3_Soc_Overview_Daily_Telehealth_Service,
        type Cc3_Soc_Filtering,
        type Cc3_Soc_Specialties,
    } from '$generated-types';
    import weekOfYear from 'dayjs/plugin/weekOfYear';
    import quarterOfYear from 'dayjs/plugin/quarterOfYear';

    dayjs.extend(isToday);
    dayjs.extend(weekOfYear);
    dayjs.extend(quarterOfYear);

    export const widgetIcon = faTty
    export const widgetCategory = 'Specialist Outpatient Clinics';
    export const widgetLimit = 999;
    export const widgetTitle = 'Daily Telehealth';
    export const widgetEnabled = true;
    export const widgetMinWidth = 10;
    export const widgetMinHeight = 2;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { queryStore, subscriptionStore } from '@urql/svelte';
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { faGear, faTty } from '@fortawesome/free-solid-svg-icons';
    import { Button, Heading, Modal, TabItem, Tabs, Tooltip } from 'flowbite-svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import type { DailyTelehealthRecord } from '$lib/shared/types/dailyTelehealth';
    import VisitTypeConfiguration from '$lib/core-soc/modules/daily-telehealth/VisitTypeConfiguration.svelte';
    import ThresholdConfiguration from '$lib/core-soc/modules/daily-telehealth/ThresholdConfiguration.svelte';
    import { user } from '$lib/stores/Auth';

    export let config: DailyTeleHealthConfig;

    const client = get(gqlClientStore);

    const groups = ['SOC', 'AHS'];

    let specialties: Cc3_Soc_Specialties[] = [];
    let filtering: Partial<Cc3_Soc_Filtering> = {};

    const getFilteringConfig = () => {
        let query = queryStore<GetFilteringByUserIdQuery>({
            client,
            query: GetFilteringByUserIdDocument,
            variables: { user_id },
        });

        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                filtering = res.data.cc3_soc_filtering?.[0];
                unsubscriber();
            } else if (!res.fetching && res.error) {
                filtering = {};
                unsubscriber();
            }
        });
    };

    const getSpecialties = () => {
        let query = queryStore<GetSpecialtiesByGroupsQuery>({
            client,
            query: GetSpecialtiesByGroupsDocument,
            variables: { groups },
        });

        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                specialties = res.data.cc3_soc_specialties;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                specialties = [];
                unsubscriber();
            }
        });
    };

    type threshold = {
        amber: number;
        red: number;
        selectedThreshold: boolean;
        sumHoursPlanned: number;
        sumHoursUtilized: number;
        specialty_id: string;
    };

    let amberStyle = `--tw-shadow-color: ${config.mediumColor}; --tw-shadow: 0 4px 6px -1px ${config.mediumColor} , 0 2px 4px -2px ${config.mediumColor};`;
    let redStyle = `--tw-shadow-color: ${config.highColor}; --tw-shadow: 0 4px 6px -1px ${config.highColor} , 0 2px 4px -2px ${config.highColor};`;
    let fontSize = 20;
    let squareSize = 20;
    let user_id = '';
    let filteringOpen = false;
    const defaultData: DailyTelehealthRecord = {
        bookedAppointments: 0,
        arrived: 0,
        hoursPlanned: 0,
        hoursUtilised: 0,
        utilisation: 0,
        isAmber: false,
        isRed: false,
    };
    let dataSOC: DailyTelehealthRecord = structuredClone(defaultData);
    let dataAHS: DailyTelehealthRecord = structuredClone(defaultData);

    const resetData = () => {
        dataSOC = structuredClone(defaultData);
        dataAHS = structuredClone(defaultData);
    };

    const updateTextSizes = () => {
        let vmin = 0;
        const telehealthElement = document.getElementById('telehealth');
        if (telehealthElement?.clientWidth < telehealthElement?.clientHeight) {
            vmin = telehealthElement?.clientWidth;
        } else {
            vmin = telehealthElement?.clientHeight;
        }
        squareSize = vmin / 5;
        fontSize = squareSize / 3;
    };

    $: thresholds = subscriptionStore<GetDailyTelehealthThresholdSubscription>({
        client,
        query: GetDailyTelehealthThresholdDocument,
        variables: { user_id },
    });

    const dailyTelehealthService = subscriptionStore<GetOverviewDailyTelehealthServiceSubscription>({
        client,
        query: GetOverviewDailyTelehealthServiceDocument,
    });

    const processData = (data: Cc3_Soc_Overview_Daily_Telehealth_Service[]) => {
        // let sumHoursPlannedSOCSelectedThreshold = 0;
        // let sumHoursUtilizedSOCSelectedThreshold = 0;
        // let sumHoursPlannedAHSSelectedThreshold = 0;
        // let sumHoursUtilizedAHSSelectedThreshold = 0;
        let threshSOCs: threshold[] = [];
        let threshAHSs: threshold[] = [];
        data.forEach(d => {
            const groupOfRecord = specialties.find(e => e.id === d.specialty_id)?.group;
            const filterGroupIds = filtering.visit_type_group_ids;
            const selectedSpecialties = $thresholds.data.cc3_soc_daily_telehealth_thresholds.filter(e => e.select);
            if (groupOfRecord === 'SOC' && filtering?.enable_SOC_threshold) {
                if (
                    filterGroupIds.includes(d.visit_type_group_id) &&
                    selectedSpecialties.find(e => e.specialty_id === d.specialty_id)
                ) {
                    dataSOC.bookedAppointments += d.booked_consult;
                    dataSOC.arrived += d.arrived_consult;
                    dataSOC.hoursPlanned += d.planned_hours;
                    dataSOC.hoursUtilised += d.utilized_hours;
                    dataSOC.utilisation = dataSOC.hoursUtilised / dataSOC.hoursPlanned;
                }
                const selectedThreshold = $thresholds.data.cc3_soc_daily_telehealth_thresholds.find(
                    e => e.specialty_id === d.specialty_id
                );
                if (selectedThreshold.selected_threshold || selectedThreshold.select) {
                    if (threshSOCs.find(e => e.specialty_id === selectedThreshold.specialty_id)) {
                        threshSOCs = threshSOCs.map(e => {
                            if (e.specialty_id === selectedThreshold.specialty_id) {
                                e.sumHoursPlanned += d.planned_hours;
                                e.sumHoursUtilized += d.utilized_hours;
                            }
                            return e;
                        });
                    } else {
                        threshSOCs = [
                            ...threshSOCs,
                            {
                                specialty_id: selectedThreshold.specialty_id,
                                sumHoursPlanned: d.planned_hours,
                                sumHoursUtilized: d.utilized_hours,
                                selectedThreshold: selectedThreshold.selected_threshold,
                                amber: selectedThreshold.amber_threshold,
                                red: selectedThreshold.red_threshold,
                            },
                        ];
                    }
                }
            } else if (groupOfRecord === 'AHS' && filtering?.enable_AHS_threshold) {
                if (
                    filterGroupIds.includes(d.visit_type_group_id) &&
                    selectedSpecialties.find(e => e.specialty_id === d.specialty_id)
                ) {
                    dataAHS.bookedAppointments += d.booked_consult;
                    dataAHS.arrived += d.arrived_consult;
                    dataAHS.hoursPlanned += d.planned_hours;
                    dataAHS.hoursUtilised += d.utilized_hours;
                    dataAHS.utilisation = dataAHS.hoursUtilised / dataAHS.hoursPlanned;
                }
                const selectedThreshold = $thresholds.data.cc3_soc_daily_telehealth_thresholds.find(
                    e => e.specialty_id === d.specialty_id
                );
                if (selectedThreshold.selected_threshold || selectedThreshold.select) {
                    if (threshAHSs.find(e => e.specialty_id === selectedThreshold.specialty_id)) {
                        threshAHSs = threshAHSs.map(e => {
                            if (e.specialty_id === selectedThreshold.specialty_id) {
                                e.sumHoursPlanned += d.planned_hours;
                                e.sumHoursUtilized += d.utilized_hours;
                            }
                            return e;
                        });
                    } else {
                        threshAHSs = [
                            ...threshAHSs,
                            {
                                specialty_id: selectedThreshold.specialty_id,
                                sumHoursPlanned: d.planned_hours,
                                sumHoursUtilized: d.utilized_hours,
                                selectedThreshold: selectedThreshold.selected_threshold,
                                amber: selectedThreshold.amber_threshold,
                                red: selectedThreshold.red_threshold,
                            },
                        ];
                    }
                }
            }
        });
        if (!filtering.SOC_threshold_multiple) {
            threshSOCs = threshSOCs.filter(e => e.selectedThreshold);
        }
        threshSOCs.forEach(element => {
            const SOCPercentage = (element.sumHoursUtilized * 100) / element.sumHoursPlanned;
            if (SOCPercentage < element.red) {
                dataSOC.isRed = true;
            } else if (SOCPercentage < element.amber && !dataSOC.isRed) {
                dataSOC.isAmber = true;
            }
        });

        if (!filtering.AHS_threshold_multiple) {
            threshAHSs = threshAHSs.filter(e => e.selectedThreshold);
        }
        threshAHSs.forEach(element => {
            const AHSPercentage = (element.sumHoursUtilized * 100) / element.sumHoursPlanned;
            if (AHSPercentage < element.red) {
                dataAHS.isRed = true;
            } else if (AHSPercentage < element.amber && !dataAHS.isRed) {
                dataAHS.isAmber = true;
            }
        });
    };

    $: if (
        specialties?.length &&
        filtering &&
        $thresholds &&
        $thresholds.data &&
        $dailyTelehealthService &&
        $dailyTelehealthService.data
    ) {
        resetData();
        processData($dailyTelehealthService.data.cc3_soc_overview_daily_telehealth_service);
    }

    const onGearClick = () => {
        filteringOpen = true;
    };

    onMount(async () => {
        updateTextSizes();
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            updateTextSizes();
        });
        user_id = get(user).claims.hasura_access['x-hasura-user-id']

        if (user_id) {
            getFilteringConfig();
            getSpecialties();
        }
    });
</script>

{#if config.title && config.title !== ''}
    <div class="group flex items-center justify-between rounded-lg bg-gray-800 p-3 text-on-surface-1">
        <Heading tag="h5">{config.title}</Heading>
        <Button size="md" on:click={onGearClick} class="opacity-0 transition-all duration-300 group-hover:opacity-100">
            <span>
                {@html icon(faGear).html}
            </span>
            <Tooltip>
                {`Filter configuration`}</Tooltip>
        </Button>
    </div>
{/if}

<div class="flex flex-col gap-5 p-3 text-on-surface-1" id="telehealth">
    <div class="flex font-bold">SOC Telehealth Services</div>
    <div class="grid grid-cols-12 gap-3 bg-gray-800 pb-2">
        <div class="col-span-12 p-2 md:col-span-4">
            <div class="mb-2 flex">
                <h2 class="text-xl font-semibold">Consultations</h2>
            </div>
            <div class="flex flex-wrap justify-around gap-3 rounded-lg bg-gray-700 p-3">
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Booked Appt</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                        id="indicator"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataSOC.isAmber ? amberStyle : ''} ${
                            dataSOC.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataSOC.bookedAppointments}</span>
                    </div>
                </div>
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Arrived</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataSOC.isAmber ? amberStyle : ''} ${
                            dataSOC.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataSOC.arrived}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-span-12 p-2 md:col-span-6">
            <div class="mb-2 flex">
                <h2 class="text-xl font-semibold">Patient Turn Up Rate</h2>
            </div>
            <div class="flex flex-wrap justify-around gap-3 rounded-lg bg-gray-700 p-3">
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Hours Planned</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataSOC.isAmber ? amberStyle : ''} ${
                            dataSOC.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataSOC.hoursPlanned}</span>
                    </div>
                </div>
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Hours Utilised</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataSOC.isAmber ? amberStyle : ''} ${
                            dataSOC.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataSOC.hoursUtilised}</span>
                    </div>
                </div>
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Utilisation</span>
                    <div
                        class="flex h-10 w-10 flex-col items-center justify-center rounded-full shadow-md shadow-gray-400 transition-all duration-500"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataSOC.isAmber ? amberStyle : ''} ${
                            dataSOC.isRed ? redStyle : ''
                        }`}>
                        <span class="text-wrap text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{(dataSOC.utilisation * 100).toFixed(0)}<span style={`font-size: ${fontSize / 2}px`}
                                >%</span
                            ></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-span-12 p-2 md:col-span-2">
            {#if $thresholds && $thresholds.data && specialties?.length}
                <div class="flex max-h-[200px] flex-col gap-2 overflow-auto">
                    {#each $thresholds.data.cc3_soc_daily_telehealth_thresholds.filter(e => e.select && specialties.find(s => s.id === e.specialty_id).group === 'SOC') as threshold}
                        <div class="rounded-lg bg-gray-700 p-2">
                            <span class="text-ellipsis text-wrap">
                                {specialties.find(e => e.id === threshold.specialty_id)?.description}
                            </span>
                        </div>
                    {/each}
                </div>
            {/if}
        </div>
    </div>

    <div class="flex font-bold">AHS Telehealth Services</div>
    <div class="grid grid-cols-12 gap-3 bg-gray-800">
        <div class="col-span-12 p-2 md:col-span-4">
            <div class="mb-2 flex">
                <h2 class="text-xl font-semibold">Consultations</h2>
            </div>
            <div class="flex flex-wrap justify-around gap-3 rounded-lg bg-gray-700 p-3">
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Booked Appt</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400"
                        id="indicator"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataAHS.isAmber ? amberStyle : ''} ${
                            dataAHS.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataAHS.bookedAppointments}</span>
                    </div>
                </div>
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Arrived</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataAHS.isAmber ? amberStyle : ''} ${
                            dataAHS.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataAHS.arrived}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-span-12 p-2 md:col-span-6">
            <div class="mb-2 flex">
                <h2 class="text-xl font-semibold">Patient Turn Up Rate</h2>
            </div>
            <div class="flex flex-wrap justify-around gap-3 rounded-lg bg-gray-700 p-3">
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Hours Planned</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataAHS.isAmber ? amberStyle : ''} ${
                            dataAHS.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataAHS.hoursPlanned}</span>
                    </div>
                </div>
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Hours Utilised</span>
                    <div
                        class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataAHS.isAmber ? amberStyle : ''} ${
                            dataAHS.isRed ? redStyle : ''
                        }`}>
                        <span class="text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{dataAHS.hoursUtilised}</span>
                    </div>
                </div>
                <div class="flex flex-col items-center justify-center gap-3">
                    <span class="text-center">Utilisation</span>
                    <div
                        class="flex h-10 w-10 flex-col items-center justify-center rounded-full shadow-md shadow-gray-400"
                        style={`height: ${squareSize}px; width: ${squareSize}px; ${dataAHS.isAmber ? amberStyle : ''} ${
                            dataAHS.isRed ? redStyle : ''
                        }`}>
                        <span class="text-wrap text-5xl underline underline-offset-4" style={`font-size: ${fontSize}px`}
                            >{(dataAHS.utilisation * 100).toFixed(0)}<span style={`font-size: ${fontSize / 2}px`}
                                >%</span
                            ></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-span-12 p-2 md:col-span-2">
            {#if $thresholds && $thresholds.data && specialties?.length}
                <div class="flex max-h-[200px] flex-col gap-2 overflow-auto">
                    {#each $thresholds.data.cc3_soc_daily_telehealth_thresholds.filter(e => e.select && specialties.find(s => s.id === e.specialty_id).group === 'AHS') as threshold}
                        <div class="rounded-lg bg-gray-700 p-2">
                            <span class="text-ellipsis text-wrap">
                                {specialties.find(e => e.id === threshold.specialty_id)?.description}
                            </span>
                        </div>
                    {/each}
                </div>
            {/if}
        </div>
    </div>
</div>

<Modal size="lg" title="Filter Configuration for Telehealth" bind:open={filteringOpen}>
    <Tabs
        style="full"
        defaultClass="flex rounded-lg divide-x rtl:divide-x-reverse divide-gray-200 shadow dark:divide-gray-700">
        <TabItem class="w-full" open>
            <span slot="title">SOC & AHS Clinics</span>
            <VisitTypeConfiguration
                {user_id}
                type="SOC"
                on:onSave={() => {
                    getFilteringConfig();
                    // filteringOpen = false;
                }} />
            <VisitTypeConfiguration
                {user_id}
                type="AHS"
                on:onSave={() => {
                    getFilteringConfig();
                    // filteringOpen = false;
                }} />
        </TabItem>
        <TabItem class="w-full">
            <span slot="title">Telehealth Thresholds</span>
            <ThresholdConfiguration {user_id} />
        </TabItem>
    </Tabs>
</Modal>
