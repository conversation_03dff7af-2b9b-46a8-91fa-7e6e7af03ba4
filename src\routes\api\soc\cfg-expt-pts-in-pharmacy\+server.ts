import { apiGetRequest } from '$lib/shared/util/socApiRequest';
import { locations } from '$lib/stores/Locations';
import { json } from '@sveltejs/kit';
import qs from 'querystring';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

const exptPtsInPharmacyURL = `${get(locations).soc}/soc/cfg-expt-pts-in-pharmacy`;
export const GET: RequestHandler = async ({ locals, url }) => {
    const page = url.searchParams.get('page') ?? '1';
    const limit = url.searchParams.get('limit') ?? '9999';
    const day_of_week = url.searchParams.get('day_of_week') ?? 'mon';
    const time_point = url.searchParams.get('time_point') ?? '12:30:00';
    const token = locals.user.access_token;
    return apiGetRequest({ url: exptPtsInPharmacyURL, token, query: { page, limit, day_of_week, time_point } });
};

export const PATCH: RequestHandler = async ({ url, request, locals }) => {
    const body = await request.json();
    const token = locals.user.access_token;
    const day_of_week = url.searchParams.get('day_of_week');
    const time_point = url.searchParams.get('time_point');
    const is_calculated_at_time_point = url.searchParams.get('is_calculated_at_time_point');
    console.log('okk');
    if (day_of_week && time_point && is_calculated_at_time_point) {
        try {
            const res = await fetch(
                `${exptPtsInPharmacyURL}?${qs.stringify({ day_of_week, time_point, is_calculated_at_time_point })}`,
                {
                    method: 'PATCH',
                    body: JSON.stringify(body),
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                    },
                }
            );
            return json({ res: res?.ok }, { status: res?.status });
        } catch (error) {
            return json({ res: false }, { status: 500 });
        }
    }
    return json({ res: false }, { status: 400 });
};
