<script lang="ts" context="module">
    //Utils
    import { logger } from '$lib/stores/Logger';
    import { faLineChart } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';

    //svelte
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';

    //Graphql
    import { gqlClientStore } from '$lib/stores/Clients';
    import gql from 'graphql-tag';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';

    import type { NetflowConfig } from './WidgetConfig/index.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);

    export const widgetTitle = 'Netflow';
    export const widgetIcon = faLineChart;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetEnabled = true;
    export const widgetMinWidth = 5;
    export const widgetMinHeight = 5;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { SeriesModel, type EChartsOption, type EChartsType } from 'echarts';

    /* ---------------------------------- data ---------------------------------- */
    let subscriptions: Subscription[] = [];
    let todayInflow: number = 0;
    let todayOutflow: number = 0;
    let flowDifference: number = 0;
    /* --------------------------------- echart --------------------------------- */
    let echarts;
    let chart: EChartsType;
    let lineChartEl: HTMLElement;
    let option: EChartsOption;

    const bbsymbol: unique symbol = Symbol();

    const MAX_NUM_OF_DAYS: number = 5;

    export let config: NetflowConfig;

    const INFLOW_CHART_INDEX = 0;
    const OUTFLOW_CHART_INDEX = 1;
    const THRESHOLD_CHART_INDEX = 2;

    function loadChart(data: any[]) {
        let inflowPoints = data?.map(d => {
            return [d[config.label], d[config.inflowTimestamp]];
        });

        let outflowPoints = data?.map(d => {
            return [d[config.label], d[config.outflowTimestamp]];
        });

        let thresholdPoints = data?.map(d => {
            return [d[config.label], config.threshold];
        });

        option.series[INFLOW_CHART_INDEX].data = inflowPoints;
        option.series[OUTFLOW_CHART_INDEX].data = outflowPoints;
        option.series[THRESHOLD_CHART_INDEX].data = thresholdPoints;
        option = option;
    }

    async function render() {
        echarts = await import('echarts');
        chart = echarts.init(lineChartEl);

        window.addEventListener('resize', () => {
            chart?.resize();
        });

        const series: EChartsOption['series'] = [
            {
                smooth: 0,
                symbol: 'number',
                type: 'line',
                lineStyle: {
                    color: config.inflowLineColor,
                    width: 5,
                },
                data: [],
                label: {
                    formatter: param => {
                        return param.data[1] === 0 ? '' : param.data[1];
                    },
                    show: true,
                    padding: 3,
                    borderRadius: 5,
                    color: '#FFFFFF',
                },

                triggerLineEvent: true,
            },
            {
                smooth: 0,
                symbol: 'number',
                type: 'line',
                lineStyle: {
                    color: config.outflowLineColor,
                    width: 5,
                },
                data: [],
                label: {
                    formatter: param => {
                        return param.data[1] === 0 ? '' : param.data[1];
                    },
                    show: true,
                    padding: 3,
                    borderRadius: 5,
                    color: '#FFFFFF',
                },

                triggerLineEvent: true,
            },
            {
                symbol: 'none',
                type: 'line',
                lineStyle: {
                    type: 'dashed',
                    color: config.thresholdColor,
                    width: 3,
                    opacity: config.showThreshold ? 1 : 0,
                },
                data: [],
                label: {
                    formatter: param => {
                        return param.data[1] === 0 ? '' : param.data[1];
                    },
                    show: true,
                    padding: 3,
                    borderRadius: 5,
                    color: '#FFFFFF',
                },
                triggerLineEvent: true,
            },
        ];

        option = {
            grid: {
                left: 30,
                top: 40,
                right: 30,
                bottom: 20,
            },

            xAxis: {
                type: 'category',
                boundaryGap: false,
                axisLine: {
                    show: true,
                    lineStyle: {
                        width: 1,
                    },
                },
                axisTick: {
                    show: false,
                },
            },
            yAxis: {
                type: 'value',
                position: 'right',
                splitLine: {
                    show: false,
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        width: 3,
                    },
                },
                axisTick: {
                    show: false,
                },
            },
            series,
        };

        window.addEventListener('resize', () => {
            chart?.resize();
        });
    }

    const FetchDataDocument = gql`
        query  {
            ${config?.dataSource} {
                ${config?.inflowTimestamp}
                ${config?.outflowTimestamp}
                ${config?.label}
            }
        }
    `;

    const ObserveDataDocument = gql`
        subscription  {
            ${config?.dataSource} {
                ${config?.inflowTimestamp}
                ${config?.outflowTimestamp}
                ${config?.label}
            }
        }
    `;

    const client = get(gqlClientStore);
    client
        .query(FetchDataDocument, {})
        .toPromise()
        .then(result => {
            if (result?.data) loadChart(result.data[config.dataSource]);
        });

    onMount(async () => {
        //Initialize echarts and channel
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            chart?.resize();
        });

        const numOfDays = config?.numOfDays ? config?.numOfDays : MAX_NUM_OF_DAYS;

        if (config?.dataSource?.length) {
            subscriptions.push(
                wPipe(
                    client.subscription(ObserveDataDocument, {}),
                    wSubscribe(result => {
                        if (result?.data) loadChart(result.data[config.dataSource]);
                    })
                )
            );

            await render();
        }
    });

    onDestroy(() => {
        subscriptions.forEach(s => {
            s?.unsubscribe();
        });

        browserBroadcaster.unsub(Channel.AOH_WIDGET_RESIZED, bbsymbol);
    });

    $: if (Boolean(option) && Boolean(lineChartEl) && Boolean(config)) {
        chart?.setOption(option);
    }
</script>

<div class="text-s flex h-full w-full flex-col p-4">
    <header class="flex items-center gap-4 text-on-background">
        <p class="bg-transparent text-base">{config.title}</p>
        <div class="flex items-center gap-2 text-xs">
            <span class="h-[5px] w-[32px]" style="background-color:{config.inflowLineColor}" />
            <span>{config.inflowLabel}</span>
        </div>

        <div class="flex items-center gap-2 text-xs">
            <span class="h-[5px] w-[32px]" style="background-color:{config.outflowLineColor}" />
            <span>{config.outflowLabel}</span>
        </div>

        <div class="flex items-center gap-2 text-xs">
            <span class="h-[5px] w-[32px] border-b-2 border-dashed" style="border-color:{config.thresholdColor}" />
            <span>Threshold</span>
        </div>
    </header>
    <section class="h-full w-full" bind:this={lineChartEl} />
</div>
