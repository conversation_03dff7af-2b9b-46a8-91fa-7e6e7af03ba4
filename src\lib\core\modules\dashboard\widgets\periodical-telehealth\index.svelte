<script lang="ts" context="module">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { logger } from '$lib/stores/Logger';
    import { faFilter, faGear } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import { onMount } from 'svelte';
    import type { PeriodicalTelehealthConfig } from './WidgetConfig/index.svelte';
    import weekOfYear from 'dayjs/plugin/weekOfYear';
    import quarterOfYear from 'dayjs/plugin/quarterOfYear';

    dayjs.extend(isToday);
    dayjs.extend(weekOfYear);
    dayjs.extend(quarterOfYear);

    export const widgetIcon = faFileWaveform;
    export const widgetCategory = 'Specialist Outpatient Clinics';
    export const widgetLimit = 999;
    export const widgetTitle = 'Periodical Telehealth Situation';
    export const widgetEnabled = true;
    export const widgetMinWidth = 10;
    export const widgetMinHeight = 2;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { get } from 'svelte/store';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { queryStore, subscriptionStore } from '@urql/svelte';
    import { gqlClientStore } from '$lib/stores/Clients';
    import {
        GetClinicsDocument,
        type GetClinicsQuery,
        GetFilteringByUserIdDocument,
        type GetFilteringByUserIdQuery,
        GetOverviewTelehealthSituationStreamingDocument,
        type GetOverviewTelehealthSituationStreamingSubscription,
        GetSpecialtiesByGroupsDocument,
        type GetSpecialtiesByGroupsQuery,
        type Cc3_Soc_Overview_Telehealth_Situation,
        type Cc3_Soc_Clinics,
        type Cc3_Soc_Specialties,
        type Cc3_Soc_Filtering,
        type Cc3_Soc_Visit_Type_Groups,
        type GetVisitTypeGroupsQuery,
        GetVisitTypeGroupsDocument,
    } from '$generated-types';
    import { Button, Heading, Modal, Tooltip } from 'flowbite-svelte';
    import { faFileWaveform } from '@fortawesome/free-solid-svg-icons';
    import type { EChartsOption, EChartsType } from 'echarts';
    import lodash from 'lodash';
    import Filtering from '$lib/core-soc/modules/filtering/Filtering.svelte';
    import { VIEW_CHART_BY_ENUM } from '$lib/shared/enum/general';
    import { user } from '$lib/stores/Auth';

    const groupBy = lodash.groupBy;
    const sumBy = lodash.sumBy;

    export let config: PeriodicalTelehealthConfig;

    const client = get(gqlClientStore);

    let fontSize = 20;
    let squareSize = 20;
    let user_id = '';
    let filteringOpen = false;

    let chartContainer: HTMLElement;
    let chart: EChartsType;

    const labelOption = {
        show: config.showLabel,
        position: 'insideBottom',
        distance: 15,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 16,
        rich: {
            name: {},
        },
    };

    const defaultOption: EChartsOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        toolbox: {
            show: true,
            feature: {
                saveAsImage: { show: true },
                dataView: { show: true, readOnly: false },
                dataZoom: { show: true },
            },
        },
        legend: {
            textStyle: {
                color: '#FFF',
            },
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        xAxis: [
            {
                type: 'category',
                data: [
                    `Online Avg Past 7 months`,
                    `Physical Avg Past 7 months`,
                    'Online Apr',
                    'Physical Apr',
                    'Online May',
                    'Physical May',
                    'Online June',
                    'Physical June',
                    'Online July',
                    'Physical July',
                ],
                axisLabel: {
                    interval: 0,
                    overflow: 'break',
                    width: 50,
                },
            },
        ],
        yAxis: [
            {
                name: 'Total number of booked appointments',
                nameRotate: 90,
                type: 'value',
                nameLocation: 'middle',
                nameGap: 30,
            },
        ],
        series: [
            {
                name: 'Phone Call Consults',
                type: 'bar',
                stack: 'phone-video-consults',
                emphasis: {
                    focus: 'series',
                },
                data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            },
            {
                name: 'Video Call Consults',
                type: 'bar',
                stack: 'phone-video-consults',
                emphasis: {
                    focus: 'series',
                },
                data: [100, 90, 80, 70, 60, 50, 40, 30, 20, 10],
            },
            {
                name: 'Physical Consults',
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
                data: [10, 15, 20, 25, 30, 35, 40, 45, 50, 65],
            },
        ],
    };
    let option = defaultOption;
    let periodicalData: Record<string, Cc3_Soc_Overview_Telehealth_Situation[]>;
    let viewByLabel = '';
    let number = 0;

    let defaultData = {
        phoneConsult: {
            bookedAppointment: 0,
            arrived: 0,
        },
        videoConsult: {
            bookedAppointment: 0,
            arrived: 0,
        },
        slotPlanned: 0,
        slotUtilized: 0,
        numberOfPatient: 0,
        physicalConsult: 0,
    };
    let data = structuredClone(defaultData);

    let clinics: Cc3_Soc_Clinics[] = [];
    let visitTypeGroups: Cc3_Soc_Visit_Type_Groups[] = [];
    let specialties: Cc3_Soc_Specialties[] = [];
    let filtering: Partial<Cc3_Soc_Filtering>;

    const getFilteringConfig = () => {
        let filteringConfig = queryStore<GetFilteringByUserIdQuery>({
            client,
            query: GetFilteringByUserIdDocument,
            variables: { user_id },
        });

        const unsubscriber = filteringConfig.subscribe(res => {
            if (!res.fetching && !res.error) {
                filtering = res.data.cc3_soc_filtering?.[0];
                unsubscriber();
            } else if (!res.fetching && res.error) {
                filtering = {};
                unsubscriber();
            }
        });
    };

    const getVisitTypeGroups = () => {
        const query = queryStore<GetVisitTypeGroupsQuery>({
            client,
            query: GetVisitTypeGroupsDocument,
        });
        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                visitTypeGroups = res.data.cc3_soc_visit_type_groups;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                visitTypeGroups = [];
                unsubscriber();
            }
        });
    };

    const getClinics = () => {
        const query = queryStore<GetClinicsQuery>({
            client,
            query: GetClinicsDocument,
        });
        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                clinics = res.data.cc3_soc_clinics;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                clinics = [];
                unsubscriber();
            }
        });
    };

    const getSpecialties = () => {
        let query = queryStore<GetSpecialtiesByGroupsQuery>({
            client,
            query: GetSpecialtiesByGroupsDocument,
            variables: { groups: ['SOC', 'AHS'] },
        });

        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                specialties = res.data.cc3_soc_specialties;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                specialties = [];
                unsubscriber();
            }
        });
    };

    const resetData = () => {
        data = structuredClone(defaultData);
    };

    let periodicalTelehealth;
    $: {
        if (filtering)
            periodicalTelehealth = subscriptionStore<GetOverviewTelehealthSituationStreamingSubscription>({
                client,
                query: GetOverviewTelehealthSituationStreamingDocument,
                variables: {
                    fromDate: filtering.view_date_from,
                    toDate: filtering.view_date_to,
                    clinic_ids: filtering.clinic_ids,
                    specialty_ids: filtering.specialty_ids,
                    visit_type_group_ids: filtering.visit_type_group_ids,
                },
            });
    }

    const mergeData = (teleHealthData: Cc3_Soc_Overview_Telehealth_Situation[]) => {
        teleHealthData.sort((a, b) => {
            return new Date(b.day).getTime() - new Date(a.day).getTime();
        });
        const startDate = dayjs(filtering.view_date_from);
        const endDate = dayjs(filtering.view_date_to);
        switch (filtering.view_chart_by) {
            case VIEW_CHART_BY_ENUM.DAY:
                periodicalData = groupBy(teleHealthData, item => {
                    return dayjs(item.day).format('DD-MM-YYYY');
                });
                number = endDate.diff(startDate, 'days');
                viewByLabel = `${number} Days`;
                break;
            case VIEW_CHART_BY_ENUM.WEEK:
                periodicalData = groupBy(teleHealthData, item => {
                    return `${dayjs(item.day).week()}-${dayjs(item.day).year()}`;
                });
                number = endDate.diff(startDate, 'weeks');
                viewByLabel = `${number} Weeks`;
                break;
            case VIEW_CHART_BY_ENUM.MONTH:
                periodicalData = groupBy(teleHealthData, item => {
                    return dayjs(item.day).format('MM-YYYY');
                });
                number = endDate.diff(startDate, 'months');
                viewByLabel = `${number} Months`;
                break;
            case VIEW_CHART_BY_ENUM.QUARTER:
                periodicalData = groupBy(teleHealthData, item => {
                    return `${dayjs(item.day).quarter()}-${dayjs(item.day).year()}`;
                });
                number = endDate.diff(startDate, 'quarters');
                viewByLabel = `${number} Quarters`;
                break;
            case VIEW_CHART_BY_ENUM.YEAR:
                periodicalData = groupBy(teleHealthData, item => {
                    return dayjs(item.day).format('YYYY');
                });
                number = endDate.diff(startDate, 'years');
                viewByLabel = `${number} Years`;
                break;
            default:
                periodicalData = groupBy(teleHealthData, item => {
                    return dayjs(item.day).format('MM-YYYY');
                });
                number = endDate.diff(startDate, 'months');
                viewByLabel = `${number} Months`;
                break;
        }
    };

    const updateXAxis = () => {
        const lastFour = [];
        Object.keys(periodicalData)
            .slice(0, 4)
            .reverse()
            .forEach(e => {
                lastFour.push(`Online ${e}`);
                lastFour.push(`Physical ${e}`);
            });
        option.xAxis = [
            {
                type: 'category',
                data: [`Online Avg Past ${viewByLabel}`, `Physical Avg Past ${viewByLabel}`, ...lastFour],
                axisLabel: {
                    interval: 0,
                    overflow: 'break',
                    width: 50,
                },
            },
        ];
    };

    const updateSeries = () => {
        const lastFourOnline: Array<Cc3_Soc_Overview_Telehealth_Situation[]> = [];
        const lastFourPhysical: Array<Cc3_Soc_Overview_Telehealth_Situation[]> = [];
        Object.keys(periodicalData)
            .slice(0, 4)
            .reverse()
            .forEach(e => {
                lastFourOnline.push([]);
                lastFourOnline.push(periodicalData[e]);

                lastFourPhysical.push(periodicalData[e]);
                lastFourPhysical.push([]);
            });
        option.series = [
            {
                name: 'Phone Call Consults',
                type: 'bar',
                stack: 'phone-video-consults',
                emphasis: {
                    focus: 'series',
                },
                data: [
                    data.phoneConsult.bookedAppointment / number,
                    0,
                    ...lastFourOnline.map(e => sumBy(e, 'phone_booked')),
                ],
            },
            {
                name: 'Video Call Consults',
                type: 'bar',
                stack: 'phone-video-consults',
                emphasis: {
                    focus: 'series',
                },
                data: [
                    data.videoConsult.bookedAppointment / number,
                    0,
                    ...lastFourOnline.map(e => sumBy(e, 'video_booked')),
                ],
            },
            {
                name: 'Physical Consults',
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
                data: [0, data.physicalConsult / number, ...lastFourPhysical.map(e => sumBy(e, 'physical_consult'))],
            },
        ];
    };

    const processData = async (teleHealthData: Cc3_Soc_Overview_Telehealth_Situation[]) => {
        teleHealthData.forEach(d => {
            data.phoneConsult.bookedAppointment += d.phone_booked;
            data.phoneConsult.arrived += d.phone_arrived;

            data.videoConsult.bookedAppointment += d.video_booked;
            data.videoConsult.arrived += d.video_arrived;

            data.numberOfPatient += d.total_unique_patient;
            data.physicalConsult += d.physical_consult;
        });

        data.slotPlanned = data.phoneConsult.bookedAppointment + data.videoConsult.bookedAppointment;
        data.slotUtilized = data.phoneConsult.arrived + data.videoConsult.arrived;

        mergeData(teleHealthData);
        updateSeries();
        updateXAxis();
        if (option && chart) {
            chart.dispose();
            const echarts = await import('echarts');
            chart = echarts.init(chartContainer);
            chart.clear();
            chart.setOption({
                ...chart.getOption(),
                ...option,
            });
        }
    };

    $: if ($periodicalTelehealth?.data && filtering) {
        resetData();
        processData($periodicalTelehealth.data.cc3_soc_overview_telehealth_situation);
    }

    const updateTextSizes = () => {
        let vmin = 0;
        const telehealthElement = document.getElementById('periodical-telehealth');

        if (telehealthElement && telehealthElement?.clientWidth < telehealthElement?.clientHeight) {
            vmin = telehealthElement?.clientWidth;
        } else {
            vmin = telehealthElement?.clientHeight;
        }
        squareSize = vmin / 9;
        fontSize = squareSize / 3;
    };

    const onGearClick = () => {
        filteringOpen = true;
    };

    onMount(async () => {
        updateTextSizes();
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, () => {
            if (chart) {
                chart.resize();
                updateTextSizes();
            }
        });

        const echarts = await import('echarts');

        if (chartContainer) chart = echarts.init(chartContainer);
        if (chart) {
            chart.setOption(defaultOption);
            updateTextSizes();
        }

        window.addEventListener('resize', () => {
            if (chart) {
                chart.resize();
                updateTextSizes();
            }
        });
        user_id = get(user).claims.hasura_access['x-hasura-user-id']
        if (user_id) {
            getFilteringConfig();
            getClinics();
            getSpecialties();
            getVisitTypeGroups();
        }
    });
</script>

{#if config.title && config.title !== ''}
    <div class="flex items-center justify-between rounded-lg bg-gray-900 p-3 text-on-surface-1">
        <Heading tag="h5">{config.title}</Heading>
        <Button size="md" on:click={onGearClick}>
            <span>
                {@html icon(faGear).html}
            </span>
            <Tooltip>
                {`Filter configuration`}</Tooltip>
        </Button>
    </div>
{/if}
<div class="flex h-full flex-col gap-5 p-3 text-on-surface-1" id="periodical-telehealth">
    <div class="grid grid-cols-12 gap-3">
        <div class="col-span-12 bg-gray-900 px-2 pb-2 md:col-span-8">
            <div class="grid grid-cols-12 gap-3">
                <div class="col-span-12 p-2 md:col-span-7">
                    <div class="mb-2 flex">
                        <Heading tag="h6">Phone Call Consults</Heading>
                    </div>
                    <div class="flex flex-wrap justify-around gap-3 rounded-lg bg-gray-800 p-3">
                        <div class="flex flex-col items-center justify-center gap-3">
                            <span class="text-center">Booked Appt</span>
                            <div
                                class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                id="indicator"
                                style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                <span
                                    class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                    style={`font-size: ${fontSize}px`}>{data.phoneConsult.bookedAppointment}</span>
                            </div>
                        </div>
                        <div class="flex flex-col items-center justify-center gap-3">
                            <span class="text-center">No shows</span>
                            <div
                                class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                <span
                                    class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                    style={`font-size: ${fontSize}px`}
                                    >{data.phoneConsult.bookedAppointment - data.phoneConsult.arrived}</span>
                            </div>
                        </div>
                        <div class="flex flex-col items-center justify-center gap-3">
                            <span class="text-center">Arrived</span>
                            <div
                                class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                <span
                                    class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                    style={`font-size: ${fontSize}px`}>{data.phoneConsult.arrived}</span>
                            </div>
                        </div>
                    </div>
                    <div class="my-2 flex">
                        <Heading tag="h6">Video Call Consults</Heading>
                    </div>
                    <div class="flex flex-wrap justify-around gap-3 rounded-lg bg-gray-800 p-3">
                        <div class="flex flex-col items-center justify-center gap-3">
                            <span class="text-center">Booked Appt</span>
                            <div
                                class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                id="indicator"
                                style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                <span
                                    class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                    style={`font-size: ${fontSize}px`}>{data.videoConsult.bookedAppointment}</span>
                            </div>
                        </div>
                        <div class="flex flex-col items-center justify-center gap-3">
                            <span class="text-center">No shows</span>
                            <div
                                class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                <span
                                    class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                    style={`font-size: ${fontSize}px`}
                                    >{data.videoConsult.bookedAppointment - data.videoConsult.arrived}</span>
                            </div>
                        </div>
                        <div class="flex flex-col items-center justify-center gap-3">
                            <span class="text-center">Arrived</span>
                            <div
                                class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                <span
                                    class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                    style={`font-size: ${fontSize}px`}>{data.videoConsult.arrived}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-12 flex flex-col p-2 md:col-span-5">
                    <div class="mb-2 flex">
                        <Heading tag="h6">Resource Utilisation</Heading>
                    </div>
                    <div class="flex flex-1 flex-col justify-between rounded-lg bg-gray-800">
                        <div class="flex flex-wrap justify-around gap-3 rounded-lg p-3">
                            <div class="flex flex-col items-center justify-center gap-3">
                                <span class="text-center">Slots Planned (Hours)</span>
                                <div
                                    class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                    id="indicator"
                                    style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                    <span
                                        class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                        style={`font-size: ${fontSize}px`}>{data.slotPlanned}</span>
                                </div>
                            </div>
                            <div class="flex flex-col items-center justify-center gap-3">
                                <span class="text-center">Utilisation</span>
                                <div
                                    class="flex flex-col items-center justify-center rounded-full shadow-md shadow-gray-400 transition-all duration-500"
                                    id="indicator"
                                    style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                    <span class="text-5xl" style={`font-size: ${fontSize}px`}
                                        >{((data.slotUtilized * 100) / data.slotPlanned).toFixed(0)}<span
                                            style={`font-size: ${fontSize / 2}px`}>%</span
                                        ></span>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-wrap justify-around gap-3 rounded-lg p-3">
                            <div class="flex flex-col items-center justify-center gap-3">
                                <span class="text-center">Slots Utilised (Hours)</span>
                                <div
                                    class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                    id="indicator"
                                    style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                    <span
                                        class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                        style={`font-size: ${fontSize}px`}>{data.slotUtilized}</span>
                                </div>
                            </div>
                            <div class="flex flex-col items-center justify-center gap-3">
                                <span class="text-center">No of Patients</span>
                                <div
                                    class="flex flex-col items-center justify-center rounded-lg shadow-md shadow-gray-400 transition-all duration-500"
                                    id="indicator"
                                    style={`height: ${squareSize}px; width: ${squareSize}px;`}>
                                    <span
                                        class="text-5xl underline decoration-double decoration-2 underline-offset-4"
                                        style={`font-size: ${fontSize}px`}>{data.numberOfPatient}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-span-12 md:col-span-1" />
        <div class="col-span-12 md:col-span-3">
            {#if filtering && clinics?.length && specialties?.length}
                <div class="flex flex-col gap-2 px-2">
                    <div class="flex">
                        <div class="flex h-10 flex-col items-center justify-center bg-gray-900 p-3">
                            <span>
                                {@html icon(faFilter).html}
                            </span>
                        </div>
                        <span
                            class="flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                            >{`${dayjs(filtering.view_date_from).format('DD MM YYYY')} to ${dayjs(filtering.view_date_to).format('DD MM YYYY')}`}</span>
                        <Tooltip>
                            {`${dayjs(filtering.view_date_from).format('DD/MM/YYYY')} to ${dayjs(filtering.view_date_to).format('DD/MM/YYYY')}`}
                        </Tooltip>
                    </div>
                    <div class="flex">
                        <div class="flex h-10 flex-col items-center justify-center bg-gray-900 p-3">
                            <span>
                                {@html icon(faFilter).html}
                            </span>
                        </div>
                        <span
                            class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                            >{`${filtering.clinic_ids
                                ?.slice(0, 1)
                                ?.map(id => clinics.find(c => c.id === id)?.description)
                                ?.filter(des => des)
                                ?.join(', ')} ...`}</span>
                        <Tooltip>
                            {filtering.clinic_ids
                                ?.map(id => clinics.find(c => c.id === id)?.description)
                                ?.filter(des => des)
                                ?.join(', ')}
                        </Tooltip>
                    </div>
                    <div class="flex">
                        <div class="flex h-10 flex-col items-center justify-center bg-gray-900 p-3">
                            <span>
                                {@html icon(faFilter).html}
                            </span>
                        </div>
                        <span
                            class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                            >{`${filtering.visit_type_group_ids
                                ?.slice(0, 1)
                                ?.map(id => visitTypeGroups.find(v => v.id === id)?.name)
                                ?.filter(name => name)
                                ?.join(', ')} ...`}</span>
                        <Tooltip>
                            {filtering.visit_type_group_ids
                                ?.map(id => visitTypeGroups.find(v => v.id === id)?.name)
                                ?.filter(name => name)
                                ?.join(', ')}
                        </Tooltip>
                    </div>
                    <div class="flex">
                        <div class="flex h-10 flex-col items-center justify-center bg-gray-900 p-3">
                            <span>
                                {@html icon(faFilter).html}
                            </span>
                        </div>
                        <span
                            class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                            >{`${filtering.specialty_ids
                                ?.slice(0, 1)
                                ?.map(id => specialties.find(s => s.id === id)?.code)
                                ?.filter(code => code)
                                ?.join(', ')} ...`}</span>
                        <Tooltip>
                            {filtering.specialty_ids
                                ?.map(id => specialties.find(s => s.id === id)?.code)
                                ?.filter(code => code)
                                ?.join(', ')}
                        </Tooltip>
                    </div>
                </div>
            {/if}
        </div>
    </div>

    <div id="periodical-chart" class="mt-5 h-full w-full flex-1">
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <div on:click data-tag="chart-container" class="h-full w-full" bind:this={chartContainer} />
    </div>
</div>

<Modal title="Filter Configuration for Periodical Telehealth Situation" bind:open={filteringOpen} size="lg">
    <Filtering
        userId={user_id}
        on:onSave={result => {
            filtering = result.detail;
        }} />
</Modal>
