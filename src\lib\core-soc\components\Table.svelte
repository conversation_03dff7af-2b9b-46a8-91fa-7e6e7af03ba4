<script lang="ts">
    import { writable } from 'svelte/store';
    import * as _ from 'lodash';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faSortUp, faSortDown, faPencilAlt } from '@fortawesome/free-solid-svg-icons';
    import {
        Table,
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Heading,
        Checkbox,
    } from 'flowbite-svelte';
    import MultiSelect from './MultiSelect.svelte';
    import Select from './Select.svelte';
    import { ColumnDataType, DataInputType, type ColumnName } from '$lib/shared/constants/general';
    import { hasAccess } from '$lib/shared/util/validation';
    import { SCOPE } from '$lib/shared/enum/general';

    export let data = [];
    export let subData = null;
    export let updateList = [];
    export let searchText = '';
    export let displayRecordIds: string[] = null;
    export let columnNames: ColumnName[];
    export let max_height: number = 96;
    export let hasSN = true;
    export let defaultSortKey: string = '';
    export let selectedRow = undefined;
    export let allowSelected = false;
    export let isComponentTable = false;
    export let isRefresh = false;
    export let abilities = [];

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };

    export let updatePosition: UpdatePosition = { id: '', columnName: '', isEditing: false };

    const ASC: number = 1;
    const sortKey = writable(defaultSortKey);
    const sortDirection = writable(ASC);
    const sortItems = writable([]);
    const checkAllMapped = writable({});

    const sortTable = (event: any, key: string) => {
        if (event.target.type === 'checkbox') {
            event.stopPropagation();
            return;
        }

        if ($sortKey === key) {
            sortDirection.update(val => -val);
        } else {
            sortKey.set(key);
            sortDirection.set(ASC);
        }
    };

    $: {
        if (isRefresh) {
            sortKey.set(defaultSortKey || '');
            sortDirection.set(ASC);
            isRefresh = false;
        }
    }

    $: {
        if (data?.length) {
            const headerKeys = columnNames?.map(n => n.key);
            const key = $sortKey;
            const direction = $sortDirection;
            const sorted = data
                .filter(d => {
                    if (displayRecordIds !== null) {
                        return displayRecordIds?.includes(d.id);
                    }
                    if (!searchText) return true;
                    return headerKeys.some(key => {
                        if (!d[key] || typeof d[key] !== 'string') return false;
                        return d[key]?.toLowerCase()?.includes(searchText.toLowerCase());
                    });
                })
                .map(row => {
                    const update = updateList?.find(i => i.id === row.id);
                    if (update) return update;
                    return row;
                })
                .sort((a, b) => {
                    let aVal = a[key];
                    let bVal = b[key];

                    if (!aVal) aVal = '';
                    if (!bVal) bVal = '';
                    if (Array.isArray(aVal)) aVal = aVal.sort().join(',');
                    if (Array.isArray(bVal)) bVal = bVal.sort().join(',');

                    if (typeof aVal === 'string' && typeof bVal === 'string') {
                        if (aVal?.includes('%') && bVal?.includes('%')) {
                            aVal = aVal.replace('%', '');
                            bVal = bVal.replace('%', '');
                        }

                        if (!isNaN(Number(aVal)) && !isNaN(Number(bVal))) {
                            aVal = Number(aVal);
                            bVal = Number(bVal);
                        } else {
                            aVal = (aVal + '').toLowerCase();
                            bVal = (bVal + '').toLowerCase();
                        }
                    }

                    if (aVal < bVal) {
                        return -direction;
                    } else if (aVal > bVal) {
                        return direction;
                    }
                    return 0;
                });
            sortItems.set(sorted);
        } else {
            sortItems.set([]);
        }

        // Init check all checkbox
        const mappedBoolCol = {};
        for (const col of columnNames) {
            if (col?.type === 'boolean') {
                mappedBoolCol[col.key] = $sortItems?.length ? $sortItems.every(d => d[col.key]) : false;
            }
        }
        checkAllMapped.set(mappedBoolCol);
    }

    interface Identifiable {
        id: string;
        isNew: boolean;
    }

    const addToUpdateList = <T extends Identifiable>(item: T) => {
        const idx = updateList.findIndex(e => e.id === item.id);
        if (idx === -1) {
            updateList = [...updateList, { ...structuredClone(item) }];
        }
    };

    const updateItem = <T extends Identifiable>(item: T, column: ColumnName) => {
        if (!column?.editable && !item.isNew) return;
        // Specific case for SOC CRUD
        if (column?.key === 'code' && item[column.key] && !item.isNew) return;
        addToUpdateList(item);
        updatePosition.isEditing = true;
        updatePosition.id = item.id;
        updatePosition.columnName = column.key;
    };

    const onKeyUp = (event: KeyboardEventInit) => {
        if (event.key === 'Enter') {
            updatePosition.isEditing = false;
        }
    };

    const onFocusOut = <T extends Identifiable>(event: any, item: T, column: ColumnName) => {
        updateField(event, item, column);
        updatePosition.isEditing = false;
    };

    const updateField = <T extends Identifiable>(event: any, item: T, col: ColumnName) => {
        const { type, key } = col;
        updateList = updateList.map(update => {
            if (update.id !== item.id) return update;
            const value = event?.currentTarget?.value ?? event?.detail?.value ?? '';
            const u = { ...update };
            if (type === ColumnDataType.Number) {
                u[key] = value ? Number(value) : null;
            } else if (type === ColumnDataType.String) {
                u[key] = value;
                // Specific case for SOC CRUD, apply for populate column
                if (col.populateColumn) {
                    u[col.populateColumn] = subData?.[col.populateColumn]?.find(d => d.ref === value)?.value;
                }
            }

            // Specific logic for Visit Type Group Mapping
            if (
                u.service !== undefined &&
                u.method_of_visit !== undefined &&
                u.type_of_appointment !== undefined &&
                u.visit_type_group !== undefined
            ) {
                const getName = (list, val) => list?.find(i => i.value === val)?.name || '';

                u.visit_type_group = [
                    getName(subData?.service, u.service),
                    getName(subData?.method_of_visit, u.method_of_visit),
                    getName(subData?.type_of_appointment, u.type_of_appointment),
                ]
                    .join(' ')
                    .trim();
            }
            return u;
        });
    };

    const updateMultiSelectField = <T extends Identifiable>(event: any, item: T, col: ColumnName) => {
        let values = event.detail?.map(v => v.value)?.filter(v => v);
        if (!values?.length) values = null;
        updateList = updateList.map(u => {
            if (u.id === item.id) {
                const updated = structuredClone(u);
                updated[col.key] = values;
                return updated;
            }
            return u;
        });
    };

    const getFilteredItems = colKey => {
        return subData?.[colKey] ?? [];
    };

    const updateBooleanField = <T extends Identifiable>(event: any, item: T, field: string) => {
        const checked = event?.currentTarget?.checked ?? false;
        const idx = updateList.findIndex(e => e.id === item.id);
        if (idx === -1) {
            updateList = [...updateList, { ...item, [field]: checked }];
        } else {
            const updatedItem = { ...updateList[idx], [field]: checked };
            updateList = [...updateList.slice(0, idx), updatedItem, ...updateList.slice(idx + 1)];
        }
    };

    const onCheckAll = (field = null) => {
        if (!field) return;
        let current = $checkAllMapped[field] ?? false;
        checkAllMapped.update(prev => ({ ...prev, [field]: !current }));

        $sortItems.forEach(item => {
            const idx = updateList.findIndex(e => e.id === item.id);
            if (idx === -1) {
                updateList = [...updateList, { ...item, [field]: !current }];
            } else {
                updateList[idx][field] = !current;
            }
        });
    };

    const isEdited = <T extends Identifiable>(item: T, col: string): boolean => {
        const foundItem = data.find(e => e.id === item.id);
        if (foundItem) {
            return !_.isEqual(foundItem[col], item[col]);
        }
        return false;
    };
    const isChange = <T extends Identifiable>(item: T): boolean => {
        const oldItem = $sortItems.find(i => i.id === item.id);
        return !_.isEqual(oldItem, item);
    };
    const isSelected = <T extends Identifiable>(position: UpdatePosition, item: T, column: ColumnName): boolean => {
        return position.id === item.id && position.columnName === column.key;
    };

    const zeroString = value => {
        return value === 0 ? '0' : value === null || value === undefined ? '' : value;
    };

    const getValue = <T extends Identifiable>(list: T[], item: T, col: string, isSelect: boolean = false): string => {
        if (subData?.[col]?.length && !isSelect) {
            const row = subData?.[col]?.find(x => x.value === item?.[col]);
            if (row) return zeroString(row.name);
        }

        let foundItem = list?.find(e => e.id === item.id);
        if (foundItem) {
            return zeroString(foundItem[col]);
        } else {
            foundItem = data.find(e => e.id === item.id);
            const value = foundItem?.[col];
            if (value) {
                if (Array.isArray(value)) return value.join(', ');
                return zeroString(value);
            }
            return zeroString(item[col]);
        }
    };

    const getMultiValue = <T extends Identifiable>(list: T[], item: T, col: string): string[] => {
        let foundItem = list?.find(e => e.id === item.id);
        if (foundItem) {
            return foundItem[col] ?? [];
        } else {
            foundItem = data.find(e => e.id === item.id);
            return foundItem?.[col] ?? [];
        }
    };

    const getBoolValue = <T extends Identifiable>(list: T[], item: T, col: string): boolean => {
        let foundItem = list?.find(e => e.id === item.id);
        if (foundItem) {
            return foundItem[col];
        } else {
            foundItem = data.find(e => e.id === item.id);
            return foundItem?.[col] ? foundItem[col] : item[col];
        }
    };
    const isCheckboxDisabled = <T extends Identifiable>(item: T): boolean => {
        if (item.isNew && hasAccess(abilities, SCOPE.CREATE)) return false;
        if (!item.isNew && hasAccess(abilities, SCOPE.UPDATE)) return false;
        return true;
    };

    // Styles
    const inputStyle =
        'block w-full rounded-lg p-2.5 text-sm text-gray-900 ' +
        'border border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-700 ' +
        'focus:border-blue-500 focus:ring-blue-500 ' +
        'dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500';
    const headCellStyle = 'relative border border-t-0 border-gray-600 border-t dark:text-white text-gray-900';
    const bodyCellStyle = 'text-nowrap border-x border-gray-600  border-b ';
    const autoBreakLineStyle = 'min-w-40 whitespace-normal break-words';
</script>

<div class={isComponentTable ? '' : 'h-[calc(56rem-12.5rem)] px-2 text-gray-700 dark:bg-gray-900 dark:text-gray-400'}>
    <div class="relative max-h-[calc(56rem-13rem)] overflow-auto rounded-b-xl rounded-t-xl border border-gray-500">
        <Table
            striped
            shadow
            style="border-collapse: separate; border-spacing: 0;"
            divClass={`relative !overflow-visible max-h-${max_height === 96 ? '96' : '(100%-80px'} rounded-t-xl`}>
            <TableHead defaultRow={false} theadClass="sticky top-0 bg-gray-50 dark:bg-gray-900 rounded-t-xl">
                {#if hasSN}<TableHeadCell class={`${headCellStyle} w-20 first:rounded-tl-xl`}>SN</TableHeadCell>{/if}
                {#if columnNames?.length}
                    {#each columnNames as col, index}
                        <TableHeadCell
                            on:click={e => sortTable(e, col.key)}
                            class={`${headCellStyle} cursor-pointer border border-l-0 border-t-0 border-gray-500 text-base font-medium capitalize text-white last:border-r-0 dark:text-white ${
                                index === columnNames.length - 1 ? 'rounded-tr-xl' : ''
                            }`}>
                            <div class="flex items-center text-nowrap">
                                {@html col.value}
                                {#if col.required}
                                    <span class="ml-1 text-red-600">*</span>
                                {/if}
                                {#if col.editable}
                                    <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>
                                {/if}
                                {#if col.type === ColumnDataType.Boolean}
                                    <Checkbox
                                        class="ml-2"
                                        disabled={!hasAccess(abilities, SCOPE.UPDATE)}
                                        on:change={() => onCheckAll(col.key)}
                                        checked={$checkAllMapped[col.key]} />
                                {/if}
                                {#if col.key === $sortKey && $sortDirection === ASC}
                                    <span class="ml-2">{@html icon(faSortUp).html}</span>
                                {/if}
                                {#if col.key === $sortKey && $sortDirection === -ASC}
                                    <span class="ml-2">{@html icon(faSortDown).html}</span>
                                {/if}
                            </div>
                        </TableHeadCell>
                    {/each}
                {/if}
            </TableHead>
            <TableBody>
                {#if !$sortItems?.length}
                    <TableBodyRow>
                        <TableBodyCell class="border border-gray-600" colSpan={columnNames.length + 1}>
                            <Heading tag="h6" class="p-4 text-center">No data</Heading>
                        </TableBodyCell>
                    </TableBodyRow>
                {/if}
                {#each $sortItems as item, i}
                    <TableBodyRow class={`${i === $sortItems.length - 1 ? 'last:rounded-b-xl' : ''}`}>
                        {#if hasSN}<TableBodyCell
                                class={`${bodyCellStyle} ${
                                    selectedRow === item.id && allowSelected && '!bg-gray-500'
                                } border border-l-0 border-t-0 border-gray-500 ${
                                    i === $sortItems.length - 1 ? 'first:rounded-bl-xl' : ''
                                }`}>{i + 1}</TableBodyCell
                            >{/if}
                        {#each columnNames as col, index}
                            <TableBodyCell
                                class={`${bodyCellStyle} ${col.customStyle ? '' : 'px-6 py-4'} ${
                                    isSelected(updatePosition, item, col) ? ' dark:bg-gray-900' : ''
                                } ${
                                    selectedRow === item.id && allowSelected && '!bg-gray-500'
                                }  border border-l-0 border-t-0 border-gray-500 ${
                                    i === $sortItems.length - 1 && index === columnNames.length - 1
                                        ? 'last:rounded-br-xl'
                                        : ''
                                }`}
                                tdClass={`${isEdited(item, col.key) && `relative editing`} `}>
                                {#if typeof item[col.key] === ColumnDataType.Boolean}
                                    <Checkbox
                                        disabled={isCheckboxDisabled(item)}
                                        checked={getBoolValue(updateList, item, col.key)}
                                        on:change={e => updateBooleanField(e, item, col.key)} />
                                {:else if updatePosition.isEditing && updatePosition.id === item.id && updatePosition.columnName === col.key && (col.editable || item.isNew)}
                                    {#if col.inputType === DataInputType.TextArea}
                                        <textarea
                                            maxlength="300"
                                            on:change={event => updateField(event, item, col)}
                                            on:focusout={e => onFocusOut(e, item, col)}
                                            value={getValue(updateList, item, col.key)}
                                            id={col.key}
                                            name={col.key}
                                            class={inputStyle}
                                            placeholder={`Input ${col.value}`} />
                                    {:else if col.inputType === DataInputType.DropDown}
                                        <div class={autoBreakLineStyle}>
                                            <Select
                                                value={getValue(updateList, item, col.key, true)}
                                                items={getFilteredItems(col?.key)}
                                                on:change={event => {
                                                    updateField(event, item, col);
                                                }} />
                                        </div>
                                    {:else if col.inputType === DataInputType.MultiSelect}
                                        <div class={autoBreakLineStyle}>
                                            <MultiSelect
                                                value={getMultiValue(updateList, item, col.key)}
                                                items={getFilteredItems(col?.key)}
                                                on:change={event => {
                                                    updateMultiSelectField(event, item, col);
                                                }} />
                                        </div>
                                    {:else}
                                        <input
                                            maxlength="300"
                                            on:change={event => updateField(event, item, col)}
                                            on:keyup={event => onKeyUp(event)}
                                            on:keypress={event => {
                                                if (event.key === 'Enter') {
                                                    event.preventDefault();
                                                }
                                            }}
                                            on:focusout={e => onFocusOut(e, item, col)}
                                            value={getValue(updateList, item, col.key)}
                                            type={col.type === ColumnDataType.Number ? 'number' : 'text'}
                                            id={col.key}
                                            name={col.key}
                                            class={inputStyle}
                                            placeholder={`Input ${col.value}`} />
                                    {/if}
                                {:else if col.customStyle}
                                    {#if col.slotName === 'user-custom'}
                                        <slot
                                            name="user-custom"
                                            rowData={getValue(updateList, item, col.key)}
                                            colData={col}
                                            itemData={item} />
                                    {:else}
                                        <slot
                                            name="row-custom"
                                            rowData={getValue(updateList, item, col.key)}
                                            colData={col}
                                            itemData={item} />
                                    {/if}
                                {:else}
                                    <button
                                        type="button"
                                        class={`${
                                            col?.disable ||
                                            (!col.editable && !item.isNew) ||
                                            (col.editable && col?.key === 'code' && item[col.key])
                                                ? 'cursor-text'
                                                : 'cursor-pointer'
                                        } w-full text-left`}
                                        disabled={col?.disable}
                                        on:dblclick={() => {
                                            if (!item.isNew && hasAccess(abilities, SCOPE.UPDATE))
                                                updateItem(item, col);
                                            if (item.isNew && hasAccess(abilities, SCOPE.CREATE)) updateItem(item, col);
                                        }}
                                        on:click={() => {
                                            selectedRow = item.id;
                                        }}>
                                        <div class="w-full whitespace-normal break-words">
                                            {#if !item[col.key] && !getValue(updateList, item, col.key)}
                                                <div>&nbsp;</div>
                                            {:else}
                                                {getValue(updateList, item, col.key)}
                                            {/if}
                                        </div>
                                    </button>
                                {/if}
                            </TableBodyCell>
                        {/each}
                    </TableBodyRow>
                {/each}
            </TableBody>
        </Table>
    </div>
</div>

<style>
    :global(.editing)::after {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 10px 10px 0;
        border-color: transparent #c54343 transparent transparent;
        right: 0;
        top: 0;
        position: absolute;
    }
</style>
