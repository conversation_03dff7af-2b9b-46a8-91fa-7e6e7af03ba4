<script lang="ts">
    import { v4 as uuidv4 } from 'uuid';

    import { createFieldValidator } from './validation';
    import { rangeValidator } from './validators';

    const [validity, validate] = createFieldValidator(rangeValidator());

    export let placeholder;

    // Optional
    export let id = undefined;
    export let value = undefined;
    export let minlength = undefined;
    export let maxlength = undefined;
    export let required = undefined;
    export let disabled = undefined;
    export let pattern = undefined;

    export let compareValue = undefined;
    export let compareOperator = undefined;

    export let enableAnimation = false;

    export let setClass =
        'border border-outline rounded text-on-surface bg-transparent invalid:border-error focus:border-primary';
    export let appendClass = '';

    export let setLabelClass = 'bg-inherit text-3xs text-on-surface peer-focus:font-bold';
    export let appendLabelClass = '';

    export let type = 'text';

    if (id == null) {
        id = uuidv4();
    }

    function typeAction(node) {
        node.type = type;
    }
</script>

<div data-testid={$$restProps['data-testid']} class="relative flex w-full" tabIndex={-1}>
    <input
        bind:value
        {id}
        {minlength}
        {maxlength}
        {pattern}
        {disabled}
        {required}
        on:change
        on:focus
        on:focusout
        on:focusin
        on:input
        on:invalid
        {placeholder}
        use:typeAction
        class="peer px-2.5 pb-0.5 pt-4 placeholder-transparent outline-none {setClass} {appendClass}"
        class:text-error={!$validity.valid && $validity.dirty}
        use:validate={{ value: value, compareValue: compareValue, compareOperator: compareOperator }} />

    {#if enableAnimation}
        <label
            for={id}
            class="absolute left-1 top-0 z-10 origin-[0] -translate-y-2 scale-75 transform px-2 transition-all
                duration-300 ease-in-out peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2
                peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-2 peer-focus:scale-75
                peer-focus:px-2 peer-focus:font-semibold {setLabelClass} {appendLabelClass}">{placeholder}</label>
    {:else}
        <label
            for={id}
            class="absolute top-0 px-2 transition-all duration-100 ease-in-out peer-focus:font-semibold
            {setLabelClass} {appendLabelClass}">{placeholder}</label>
    {/if}
</div>
{#if $validity.dirty && !$validity.valid}
    <span class="text-xs text-error">
        * {$validity.message}
    </span>
{/if}
