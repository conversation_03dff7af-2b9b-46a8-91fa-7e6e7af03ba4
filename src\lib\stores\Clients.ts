import {
  createClient as createUrqlCoreClient,
  fetchExchange,
  mapExchange,
  subscriptionExchange,
  type Client,
  type Operation,
} from '@urql/core';

import { devtoolsExchange } from '@urql/devtools';
import { retryExchange } from '@urql/exchange-retry';
import { createClient as createGraphQlWebSocketClient, type Client as GqlWsClient } from 'graphql-ws';
import { get, writable } from 'svelte/store';
import { WebSocket } from 'ws';

import { browser } from '$app/environment';
import { env } from '$env/dynamic/public';

import { AuthState, authState, RefreshTokens } from './Auth';
import { logger } from './Logger';

const log = logger.child({ src: new URL(import.meta.url).pathname });

let MinIo;

if (!browser) {
  MinIo = await import('minio');
}

/**
* MinIO is only used for the server
* @param endPoint the minio endpoint to connect to
* @param port the port for the endpoint
* @param accessKey the minio access key
* @param secretKey the minio secret key
* @param useSSL set to true to use SSL (https)
* @returns the minio client used to connect to minio
*/
export const createMinIoClient = (
  endPoint: string,
  port: number,
  accessKey: string,
  secretKey: string,
  useSSL?: boolean
) => {
  if (!browser) {
      const minIoClient = new MinIo.Client({
          endPoint,
          port,
          useSSL,
          accessKey,
          secretKey,
      });

      minIoClientStore.set(minIoClient);

      log.debug({ clientHost: minIoClient.host, clientPort: minIoClient.port }, 'MinIO Client Created');

      return minIoClient;
  } else {
      logger.error('MinIO not importable by browser!');

      return null;
  }
};

/** Svelte Store of the GQL Client - initialize this in the hooks */
export const minIoClientStore = writable();

const INITIAL_RETRY_DELAY_MS = 5_000;
const MAX_RETRY_DELAY_MS = 60_000;

/**
* Users may extend operations by providing an 'onConnectionClosed' or 'onConnectionOpened' callback
* function into the operation context. We will then call these functions when the WebSocket closes and opens
* respectively. This is to accomodate certain operations, particularly subscriptions that require their variables
* to be updated, especially for timestamp cursors.
*/
export interface HookedOperation extends Operation {
  context: Operation['context'] & {
      onConnectionClosed?: (operation: Operation) => void;
      onConnectionConnected?: (operation: Operation) => void;
      isMarkedForDeletion?: boolean;
  };
}

type OperationKey = number;
let hookedOperations: Map<OperationKey, HookedOperation> = new Map();

/**
* Creates an Urql Graph QL {@link Client}:
*
* @param {string} [url] The url for the Graph QL endpoint - assumes protocol to be (http[s]) and replaces it to 'ws'
* for the subscription URL.
* @param {boolean} [isSubscriptionEnabled] If subscriptions are enabled, then a WebSocket client must be created as
* well to facilitate the live connection.
*/
const createUrqlClient = (url = '', isSubscriptionEnabled?: boolean) => {
  /** List of exchanges to be used in urql client (order matters), more to be added depending on options */
  const exchanges = [];

  exchanges.push(devtoolsExchange);

  // Attempt retry when error (after reauth)
  exchanges.push(
      retryExchange({
          initialDelayMs: INITIAL_RETRY_DELAY_MS,
          maxDelayMs: MAX_RETRY_DELAY_MS,
          randomDelay: true,
          maxNumberAttempts: Number.POSITIVE_INFINITY,
          retryIf: (_err, _operation) => {
              // Only retry on the browser (for now)
              return browser;
          },
      })
  );

  let wsClient: GqlWsClient;

  // Attempt reauth when error
  exchanges.push(
      mapExchange({
          async onError(error, operation) {
              let reason: string;
              let reasonFound = false;

              if (browser) {
                  // We might have this error due to the access token expiring
                  error.graphQLErrors.every(gqlError => {
                      if (gqlError.message.toLowerCase().includes('could not verify jwt')) {
                          const newReason = 'Could not verify JWT';
                          reason = reason ? ' | ' + newReason : newReason;

                          if (get(authState) !== AuthState.TOKEN_REFRESHING) {
                              authState.set(AuthState.UNAUTHENTICATED);
                          }

                          reasonFound = true;
                          return false; // continue .every() loop
                      }

                      return true; // break out of .every() loop
                  });

                  // Continue checks if auth state is still not recognized as unauthenticated
                  if (!reasonFound) {
                      // Typed from inspecting errors returned
                      type NetworkError = {
                          message: string;
                          extensions: {
                              code: string;
                              path: string;
                          };
                      };

                      if (error.graphQLErrors) {
                          if (Array.isArray(error.graphQLErrors)) {
                              error.graphQLErrors.every(gqlError => {
                                  if (
                                      (gqlError.extensions.code as string)
                                          ?.toLowerCase()
                                          .includes('validation-failed')
                                  ) {
                                      // This happens when we a query legitimately doesn't exist, or we don't have
                                      // access to that query, it can also happen when we're connected with a
                                      // token that has become invalid and Hasura only allows us to access the system
                                      // as an anonymous user (with no access to root queries)
                                      if (get(authState) !== AuthState.TOKEN_REFRESHING) {
                                          authState.set(AuthState.UNAUTHENTICATED);
                                      }

                                      reasonFound = true;
                                      return true; // continue .every() loop
                                  }
                              });
                          }
                      }

                      if (error.networkError) {
                          if (Array.isArray(error.networkError)) {
                              (error.networkError as unknown as Array<NetworkError>).every(netErr => {
                                  if (netErr.extensions.code?.toLowerCase().includes('validation-failed')) {
                                      const newReason = 'Validation Failed';
                                      reason = reason ? ' | ' + newReason : newReason;

                                      // Validation can fail for multiple reasons, we only want to refresh the token
                                      // when we think its due to being unauthenticated.

                                      // No subscriptions exist happens when an anonymous user has to access
                                      // to the API
                                      if (netErr.message.includes('no subscriptions exist')) {
                                          if (get(authState) !== AuthState.TOKEN_REFRESHING) {
                                              authState.set(AuthState.UNAUTHENTICATED);
                                          }

                                          reasonFound = true;
                                          return false; // continue .every() loop
                                      }
                                  }

                                  return true; // break out of .every() loop
                              });
                          } else if (error.networkError['type'] === 'close') {
                              // Highly likely that Hasura kicked us out due to being unauthenticated
                              const newReason = 'Connection closed - possibly forcibly due to being unauthenticated.';
                              reason = reason ? ' | ' + newReason : newReason;
                              reasonFound = true;

                              if (get(authState) !== AuthState.TOKEN_REFRESHING) {
                                  authState.set(AuthState.UNAUTHENTICATED);
                              }
                          }
                      }
                  }

                  switch (get(authState)) {
                      case AuthState.UNAUTHENTICATED: {
                          authState.set(AuthState.TOKEN_REFRESHING);
                          const newReason = await RefreshTokens();
                          reason = reason ? ' | ' + newReason : newReason;

                          if (isSubscriptionEnabled) {
                              // We must terminate the WebSocket connection so a new one will be reformed with the
                              // new access token sent as cookies
                              log.info('Terminating WebSocket for subscription reconnection.');
                              wsClient.terminate();
                          }
                          break;
                      }
                      case AuthState.TOKEN_REFRESHING: {
                          const newReason = 'Token still being refreshed.';
                          reason = reason ? ' | ' + newReason : newReason;
                          break;
                      }
                      default: {
                          authState.set(AuthState.UNKNOWN);
                          const newReason = 'Unknown';
                          reason = reason ? ' | ' + newReason : newReason;
                          break;
                      }
                  }
              }

              log.warn(
                  {
                      error,
                      authState: get(authState),
                      url: operation.context.url,
                      opDefsName: operation.context.name,
                      retry: operation.context.retry,
                      reason,
                  },
                  'GraphQL Op Error'
              );
          },
          onOperation(operation) {
              // Add operation name to context for easy debugging
              if (!operation.context.name) {
                  operation.context.name = operation?.query?.definitions
                      ?.map(def => def['name']?.value)
                      .reduce((accumulator, current) => (accumulator ? accumulator + ' ' + current : current));
              }

              if (
                  (operation as HookedOperation).context.onConnectionClosed ||
                  (operation as HookedOperation).context.onConnectionConnected
              ) {
                  if (operation.kind === 'teardown') {
                      hookedOperations.get(operation.key).context.isMarkedForDeletion = true;
                  } else {
                      hookedOperations.set(operation.key, operation);
                  }
              }

              return operation;
          },
      })
  );

  // Fetch exchange before subscription
  exchanges.push(fetchExchange);

  const isUnsecureWebsocket = env.PUBLIC_SECURE_MODE === '0';

  //javahoe: fix for websocket handshake for UAT
  // const wsUrl = env.PUBLIC_HASURA_WSS_ENDPOINT;
  const wsUrl = isUnsecureWebsocket ? url?.replace(/https?/i, 'ws') : url?.replace(/https?/i, 'wss');

  // Note: Potential bug from invalid usage - currently assumes subscription is always using cookies
  const access_token =
      'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJXNXFwOEROYmZHQ2dhWWlrblFEWTRWWkQ2Y2lzSHZzRE1XTEVBVFFDT2N3In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GpsodiHCbtCEcvCbF4gcaGmSB9pvGW5HSOdJKDv0e4fT9YJwjSG4MkozhVAuvenzolH6zLxg_Y4lA66xyfHELpo3EmKvbvzsgJ3pN4I5aICbGlkvHihE5pvSaZUB5P9dE3jRH1tHNSo-_55xcLw4p6SKmouoSvsQxeFfWikUgTnuu9Fv5TwIXnuLPghaZ4u-n0Hi47WUHgkldS4N8Dmve-BuqkDXZOUzBAwurKelL66CYneGmOu9SN7VEXSxE4mYfUKneb380B0LJ5_gQhZ4o3t5xrStC3fGGYRXwzwd-gz2enOetDFSFTlPiEdhDc9hVIvfCKPcdkW0PoRnDt0V-A';
  // TODO: Potential bug from invalid usage - currently assumes subscription is always using cookies
  // Access token is passed through cookies so no connection paramters (header with access token) required
  if (isSubscriptionEnabled) {
      // Create WebSocket Client & Subscription Exchange to enable subscriptions
      wsClient = createGraphQlWebSocketClient({
          webSocketImpl: WebSocket,
          connectionParams:
              env.PUBLIC_HASURA_COOKIE_MODE === '0'
                  ? async () => {
                      return {
                          headers: {
                              // Authorization: `Bearer ${access_token}`,
                              'x-hasura-admin-secret': 'admin@123'
                          },
                      };
                  }
                  : null,
          url: wsUrl,
          retryAttempts: Number.POSITIVE_INFINITY,
          on: {
              opened: socket => {
                  log.debug({ url, socket }, 'GQL WS: Opened');
              },
              connecting: () => {
                  log.debug({ url }, 'GQL WS: Connecting');
              },
              connected: (socket, payload) => {
                  let keys = [];
                  hookedOperations.forEach(operation => {
                      if (operation.context.isMarkedForDeletion) {
                          keys.push(operation.key);
                      }

                      operation.context.onConnectionConnected(operation);
                  });

                  keys.forEach(k => hookedOperations.delete(k));

                  log.debug({ url, socket, payload }, 'GQL WS: Connected');
              },
              error: err => {
                  log.error({ url, err }, 'GQL WS: Error');
              },
              closed: (event: CloseEvent) => {
                  hookedOperations.forEach(operation => {
                      operation.context.onConnectionClosed(operation);
                  });

                  log.debug({ url, event }, `GQL WS: Closed - Code ${event.code}`);
              },
          },
          retryWait: async numberOfRetries => {
              const retryDelayMilliseconds =
                  Math.random() * 1000 * numberOfRetries * numberOfRetries * numberOfRetries;
              log.debug({ numberOfRetries, retryDelay: retryDelayMilliseconds }, 'GQL WS: Retry Count');

              if (numberOfRetries > 0) {
                  await new Promise(resolve => {
                      setTimeout(resolve, retryDelayMilliseconds);
                  });
              }
          },
      });

      exchanges.push(
          subscriptionExchange({
              forwardSubscription: (request, _operation) => {
                  const input = { ...request, query: request.query || '' };
                  return {
                      subscribe: sink => {
                          const subscription = wsClient.subscribe(input, sink);

                          return { unsubscribe: subscription };
                      },
                  };
              },
          })
      );
  }

  const gqlClient = createUrqlCoreClient({
      url,
      fetchOptions: () => {
          return {
              headers: {
                  // authorization: `Bearer ${access_token}`,
                  'x-hasura-admin-secret': 'admin@123'
              },
          };
      },
      exchanges,
  });

  return gqlClient;
};

/**
* Creates the URQL client and sets it in the Svelte store. This client is then meant to be re-used throughout the app
* through the Svelte store.
* @param url the GraphQL endpoint to connect to
* @param isSubscriptionEnabled true to enable subscriptions
* @returns The URQL client
*/
export const createGqlClient = (url = '', isSubscriptionEnabled?: boolean) => {
  const client = createUrqlClient(url, isSubscriptionEnabled);
  gqlClientStore.set(client);

  log.debug({ url, client }, 'Urql Client Created');

  return client;
};

/**
* Creates the URQL client and sets it in the Svelte store. This client is then meant to be re-used throughout the app
* through the Svelte store. This client is meant to always connect to the main GraphQL endpoint, and never the
* replay endpoint (which is used in the record and replay module).
*
* @param url the graphql endpoint to connect to
* @param isSubscriptionEnabled whether or not subscriptions are enabled
* @returns The URQL client
*/
export const createGqlNeverReplayClient = (url = '', isSubscriptionEnabled?: boolean) => {
  const client = createUrqlClient(url, isSubscriptionEnabled);
  gqlNeverReplayClientStore.set(client);

  log.debug({ url, client }, 'Urql Never Replay Client Created');

  return client;
};

/**
* The Svelte Store of the GQL client - this will be initiailized in the hooks and used throughout the application
*/
export const gqlClientStore = writable<Client>();

/**
* The Svelte Store of the 'Never Replay' GQL client - this will be initiailized in the hooks and
* used throughout the application.
*
* Never replay means this GQL client will always connect to the primary GQL endpoint - the normal client will switch
* to the replay endpoint when in replay mode.
*/
export const gqlNeverReplayClientStore = writable<Client>();
