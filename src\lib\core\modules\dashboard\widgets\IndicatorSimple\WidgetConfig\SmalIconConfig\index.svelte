<script lang="ts" context="module">
    import { type SimpleWidgetConfigItem } from '../index.svelte';

    export interface SmallIconConfigItem extends SimpleWidgetConfigItem {
        type?: string;
        iconSize?: number;
        isShowBorder?: boolean;
        borderColor: string;
    }
</script>

<script lang="ts">
    export let smallIconConfigItem: SmallIconConfigItem = {
        value: 'BS',
        fontSize: 20,
        isEnable: false,
        bgColor: '#4D4D4D',
        textColor: '#FFFFFF',
        underlineStyle: 'none',
        type: 'circle',
        iconSize: 6,
        isShowBorder: false,
        borderColor: '#FFFFFF',
    };
    export let isTopIcon: boolean = false;

    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';
    import WidgetConfigItem from '$lib/core/modules/dashboard/components/WidgetConfig/WidgetItem.svelte';
    import WidgetRange from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetRange/index.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';

    const INDICATOR_TYPE: string[] = isTopIcon ? ['Circle', 'Square', 'Prediction'] : ['Circle', 'Square'];
    const DECORATION: string[] = ['None', 'Single', 'Double'];

    const smallIconConfigPropertyFields = [
        {
            id: 'type',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Icon Type',
                labels: INDICATOR_TYPE,
            },
        },
        {
            checkVisible: configData => configData.type !== 'prediction',
            id: 'underlineStyle',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Underline',
                labels: DECORATION,
            },
        },
        {
            checkVisible: configData => configData.type !== 'prediction',
            extraProps: {
                customClass: 'flex w-full items-end gap-2',
            },
            children: [
                {
                    id: 'value',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Value',
                        customClass: 'w-1/4 mb-4',
                    },
                },
                {
                    id: 'fontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Icon Text Size',
                        customClass: 'w-1/4 mb-4',
                    },
                },
                {
                    id: 'bgColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        title: 'Bg Color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                        customClass: 'w-1/4 text-center mb-4',
                    },
                },
                {
                    id: 'textColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        title: 'Text Color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                        customClass: 'w-1/4 text-center mb-4',
                    },
                },
            ],
        },
        {
            checkVisible: configData => configData.type !== 'prediction',
            id: 'iconSize',
            componentDisplay: WidgetRange,
            extraProps: {
                title: 'Icon Size',
                min: 6,
                max: 11,
                step: 0.5,
            },
        },
        {
            checkVisible: configData => configData.type !== 'prediction',
            extraProps: {
                customClass: 'flex flex-row items-center h-10 w-1/2',
            },
            children: [
                {
                    id: 'isShowBorder',
                    componentDisplay: WidgetToggle,
                    extraProps: {
                        title: 'Show Border',
                        checked: smallIconConfigItem.isShowBorder,
                        customClass: 'self-center',
                    },
                },
                {
                    checkVisible: 'isShowBorder',
                    id: 'borderColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        inputClass: 'h-10 w-10 p-0',
                        customClass: 'ml-auto',
                    },
                },
            ],
        },
    ];
</script>

<div class="flex flex-col {smallIconConfigItem.isEnable ? '' : 'hidden'}">
    <WidgetConfigItem bind:config={smallIconConfigItem} widgetFields={smallIconConfigPropertyFields} />
</div>
