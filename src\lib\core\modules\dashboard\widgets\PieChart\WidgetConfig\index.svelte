<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import type { SeriesOption } from 'echarts';

    export interface PieChartConfig {
        dataSource?: string;
        title?: string;
        showLegend?: boolean;
        dataset: SeriesOption | SeriesOption[];
        colorSeries: string[];
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = '';
    const DEFAULT_TITLE = 'Pie Chart';

    export const configDefaults: PieChartConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        showLegend: true,
        dataset: [],
        colorSeries: ['#E63244', '#2271B3', '#8A9597', '#955F20', '#CB2821', '#C7B446'],
    };
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import { Label, Input } from 'flowbite-svelte';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { AccordionItem, Accordion } from 'flowbite-svelte';
    import WidgetConfigItem from '$lib/core/modules/dashboard/components/WidgetConfig/WidgetItem.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';

    export let config: PieChartConfig;

    const expectedFormat = [
        { name: 'name', type: 'String' },
        { name: 'value', type: 'numeric' },
    ];

    // List of data source names (valid GraphQL query roots)
    const validQueries: Array<DropdownItem> = [];

    // Look for aggregate > sum queries with expected format
    // aggregate > sum follows Hasura's aggregate sum schema
    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => field?.name == format.name && field?.type?.ofType?.name == format.type);
        });

        if (isQueryValid) {
            validQueries.push({ label: query.name, value: query.name });
        }
    });

    const propertyFields = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: validQueries,
                setClass: 'border-b border-b-outline bg-transparent mb-4',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
            },
        },
        {
            id: 'showLegend',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Legend',
                checked: config.showLegend,
            },
        },
    ];
</script>

<div class="text-on-surface flex flex-col gap-3">
    <Accordion>
        <AccordionItem open>
            <span slot="header">Properties</span>
            <WidgetConfigItem bind:config widgetFields={propertyFields} />
        </AccordionItem>
        <AccordionItem>
            <span slot="header">Pie Chart Config</span>
            <Accordion flush>
                <AccordionItem>
                    <span slot="header">Chart Segment Color</span>
                    {#if Array.isArray(config?.dataset)}
                        {#each config?.dataset as dataItem, i (dataItem.name)}
                            <div class="flex flex-warp mb-2">
                                <div class="w-2/3 self-center">
                                    <Label>Segment {dataItem.name}</Label>
                                </div>
                                <div class="mx-auto self-end">
                                    <Input
                                        bind:value={config.colorSeries[i]}
                                        type="color"
                                        class="h-10 w-10 p-0 min-w-[2.5rem]" />
                                </div>
                            </div>
                        {/each}
                    {/if}
                </AccordionItem>
                <!-- <AccordionItem>
                    <span slot="header">Threshold Config</span>
                </AccordionItem> -->
            </Accordion>
        </AccordionItem>
    </Accordion>
</div>
