<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface EntityCountConfig {
        title?: string;
        dataSource?: string;
        label?: string;
        count?: string;
        order?: string;
        group?: string;
        groupKeys?: string[];
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    export const configDefaults: EntityCountConfig = {
        dataSource: 'aoh_charts_incidents_by_type',
        title: 'Trips Last Month',
        label: 'label',
        count: 'count',
        order: 'order',
        group: 'group',
        groupKeys: ['progress', 'list'],
    };
</script>

<script lang="ts">
    //Components
    import Admonition from '$lib/core/components/Admonition/index.svelte';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';

<<<<<<< HEAD
    //Svelte
    import { cubicInOut } from 'svelte/easing';
    import { fly, slide } from 'svelte/transition';

    //Utils
    //Utils
    import type { QueryFieldsQuery } from '$generated-types';
    import { dataSource } from '$lib/core/core';
    import { DROPDOWN_CLASS, INPUT_CLASS, LABEL_CLASS, type Query } from '$lib/core/modules/dashboard/widgets/utils';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faCaretDown,
        faDatabase,
        faEquals,
        faSquareCaretDown,
        faPieChart,
        faSliders,
    } from '@fortawesome/free-solid-svg-icons';
=======
    import { dataSource } from '$lib/core/core';
    import { DROPDOWN_CLASS, INPUT_CLASS, LABEL_CLASS, type Query } from '$lib/core/modules/dashboard/widgets/utils';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCaretDown, faDatabase, faEquals, faPieChart, faSliders } from '@fortawesome/free-solid-svg-icons';
    import { faListDropdown } from '@fortawesome/pro-solid-svg-icons';
>>>>>>> aoh-web/release/v2.2.0

    export let config: EntityCountConfig;

    let currentDatasource: Query = null;

    function updateDatasources() {
        //Construct the expected format
        dataSources = [];

        $dataSource.__schema?.types?.forEach(query => {
            if (config.dataSource === query.name) currentDatasource = query;

            dataSources.push({
                label: query.name,
                value: query.name,
                action: () => {
                    currentDatasource = query;
                },
            });
        });

        dataSources = [
            {
                label: 'None',
                value: undefined,
                action: () => {
                    currentDatasource = undefined;
                },
            },
            ...dataSources,
        ];

        if (!dataSources.length) {
            config.dataSource = '';
            currentDatasource = null;
        }
    }

    // List of data source names (valid GraphQL query roots)
    let dataSources: Array<DropdownItem> = [];
    let options: Array<DropdownItem> = [];
    let labelOptions: Array<DropdownItem> = [];
    let orderByOptions: Array<DropdownItem> = [];

    function reset() {
        orderByOptions = [];
        options = [];
        labelOptions = [];
    }

    function update(query: Query) {
        if (!query) return;

        reset();

        query?.fields?.forEach(field => {
            if (field?.type?.ofType?.name === 'Int') {
                options.push({
                    label: field.name,
                    value: field.name,
                });
            }

            if (field?.type?.ofType?.name === 'String') {
                labelOptions.push({
                    label: field.name,
                    value: field.name,
                });
            }

            if (field?.type?.ofType?.name === 'String' || field?.type?.ofType?.name === 'Int') {
                orderByOptions.push({
                    label: field.name,
                    value: field.name,
                });
            }
        });

        options = options;
        labelOptions = labelOptions;
        orderByOptions = orderByOptions;
    }

    $: update(currentDatasource);

    updateDatasources();
</script>

<div class="text-on-surface flex flex-col gap-3">
    <TextField placeholder={'Title'} bind:value={config.title} setClass={INPUT_CLASS} setLabelClass={LABEL_CLASS} />

    <!-- Datasource column-->
    <section class="flex flex-col gap-2">
        <header class="flex w-full items-center gap-2 text-xs">
            Data Source
            {@html icon(faDatabase).html}
        </header>

        <Dropdown
            title={'Data Source'}
            bind:value={config.dataSource}
            enableSearch={true}
            options={dataSources}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />
    </section>

    <!-- Properties Column-->
    <div
        class="w-full {currentDatasource
            ? ''
            : 'pointer-events-none brightness-50'} flex flex-col gap-2 transition-colors duration-200 ease-in-out">
        <p class="py-2 text-center">{@html icon(faCaretDown).html}</p>

        <header class="flex w-full items-center gap-2 border-b-2 border-outline py-1 text-xs">
            Properties
            {@html icon(faSliders).html}
        </header>

        <section class="flex flex-col gap-1">
            <Dropdown
                title={'Sort by'}
                bind:value={config.order}
                options={labelOptions}
                appendMenuClass="w-full"
                setClass={DROPDOWN_CLASS}
                setLabelClass={LABEL_CLASS} />

            <Dropdown
                title={'Label'}
                bind:value={config.label}
                options={labelOptions}
                appendMenuClass="w-full"
                setClass={DROPDOWN_CLASS}
                setLabelClass={LABEL_CLASS} />

            <Dropdown
                title={'Value'}
                bind:value={config.count}
                {options}
                appendMenuClass="w-full"
                setClass={DROPDOWN_CLASS}
                setLabelClass={LABEL_CLASS} />

            <header class="mb-1 mt-2 text-xs before:content-['Chart_Data_']">{@html icon(faPieChart).html}</header>

            <div class="flex items-center gap-2">
                <Dropdown
                    title={'Group'}
                    bind:value={config.group}
                    options={labelOptions}
                    appendMenuClass="w-full"
                    setClass={`${DROPDOWN_CLASS} w-1/2`}
                    setLabelClass={LABEL_CLASS} />

                <span>{@html icon(faEquals).html}</span>

                <input class={`${INPUT_CLASS} grow`} bind:value={config.groupKeys[0]} />
            </div>

            {#if config.group && config.groupKeys[0].length}
                <Admonition
                    title="Note"
                    message={`
            Rows with the value of <i>${config.groupKeys[0]}</i> in the <i>${config.group}</i> column will be loaded
                        into the piechart.`} />
            {/if}
        </section>

        <section>
            <header class="mb-1 mt-2 text-xs before:content-['Total_From_Last_']">
                {@html icon(faSquareCaretDown).html}
            </header>

            <div class="flex items-center gap-2">
                <span class={`w-1/2 border-b border-outline py-1 pl-2 text-sm`}>{config.group}</span>

                <span>{@html icon(faEquals).html}</span>

                <input class={`${INPUT_CLASS} grow`} bind:value={config.groupKeys[1]} />
            </div>
        </section>

        {#if config.group && config.groupKeys[1].length}
            <Admonition
                title="Note"
                message={`
            Rows with the value of <i>${config.groupKeys[1]}</i> in the <i>${config.group}</i> column will be loaded
                        into the dropdown menu in the footer.`} />
        {/if}
    </div>
</div>
