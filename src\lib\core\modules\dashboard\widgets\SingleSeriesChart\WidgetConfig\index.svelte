<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface SingleSeriesChartConfig {
        dataSource?: string;
        title?: string;
        chartType?: keyof RegisteredSeriesOption;
        showXAxis: boolean;
        showYAxis: boolean;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'aoh_charts_generic_sample';
    const DEFAULT_TITLE = 'Sample Chart';
    const DEFAULT_CHART_TYPE = 'pie';

    export const configDefaults: SingleSeriesChartConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        chartType: DEFAULT_CHART_TYPE,
        showXAxis: true,
        showYAxis: true,
    };

    const supportedCharts: Array<DropdownItem> = [
        {
            label: 'Pie Chart',
            value: 'pie',
        },
        {
            label: 'Bar Chart',
            value: 'bar',
        },
    ];
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';
    import type { RegisteredSeriesOption } from 'echarts';
    import { cubicInOut } from 'svelte/easing';
    import { slide } from 'svelte/transition';

    export let config: SingleSeriesChartConfig;

    const expectedFormat = [
        { name: 'name', type: 'String' },
        { name: 'value', type: 'numeric' },
    ];

    // List of data source names (valid GraphQL query roots)
    const validQueries: Array<DropdownItem> = [];

    // Look for aggregate > sum queries with expected format
    // aggregate > sum follows Hasura's aggregate sum schema
    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => field?.name == format.name && field?.type?.ofType?.name == format.type);
        });

        if (isQueryValid) {
            validQueries.push({ label: query.name, value: query.name });
        }
    });

    const textClass = 'scroll w-full text-on-surface ';
    const inputClass =
        textClass +
        ' w-full p-1 bg-transparent border border-outline transition-all duration-200 ease-in-out rounded focus:border-2 focus:border-primary font-light';
</script>

<div class="flex flex-col gap-2">
    <div>
        <Dropdown
            title="Data Source"
            options={validQueries}
            bind:value={config.dataSource}
            setClass="border-b border-b-outline bg-transparent" />
    </div>
    <div>
        <Dropdown
            title="Chart Type"
            options={supportedCharts}
            bind:value={config.chartType}
            setClass="border-b border-b-outline bg-transparent" />
    </div>
    <div>
        <TextField
            placeholder={'Title'}
            bind:value={config.title}
            setClass="{inputClass} h-10 text-xs"
            setLabelClass="text-primary" />
    </div>

    {#if config.chartType === 'bar'}
        <div
            transition:slide|global={{ easing: cubicInOut, axis: 'y' }}
            class="flex w-full items-center justify-between px-2">
            <p class="text-on-surface text-sm">Show X-axis</p>
            <input type="checkbox" bind:checked={config.showXAxis} />
        </div>

        <div
            transition:slide|global={{ easing: cubicInOut, axis: 'y' }}
            class="flex w-full items-center justify-between px-2">
            <p class="text-on-surface text-sm">Show Y-axis</p>
            <input type="checkbox" bind:checked={config.showYAxis} />
        </div>
    {/if}
</div>
