.grid-stack > .grid-stack-item.grid-stack-sub-grid > .grid-stack-item-content {
    position: relative;
    inset: 0;
    flex: 1;
    overflow-x: auto;
}

.grid-stack-nested {
    min-width: 100%;
    display: block;
    scroll-behavior: smooth;
    flex: 1;
}

.grid-stack-nested > .grid-stack-item {
    width: 100%;
}

@for $columns from 1 through 48 {
    @for $i from 1 through $columns {
        .gs-#{$columns} > .grid-stack-item[gs-x='#{$i}'] {
            left: calc(var(--cell-height) * $i * 1px);
        }
    }
}

@for $i from 1 through 48 {
    [gs-w='#{$i}'] {
        width: calc(var(--cell-height) * #{$i} * 1px) !important;
    }
    [gs-x='#{$i}'] {
        left: calc(var(--cell-height) * #{$i} * 1px);
    }
    .gs-#{$i} {
        width: calc(var(--cell-height) * #{$i} * 1px);
    }
}
