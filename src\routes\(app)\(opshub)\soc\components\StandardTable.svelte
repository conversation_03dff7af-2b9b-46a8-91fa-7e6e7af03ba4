<script lang="ts">
    import { writable } from 'svelte/store';
    import { createEventDispatcher, onMount } from 'svelte';
    import { getInventoryBackend } from '../utils/function';
    import { get } from 'svelte/store';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import * as _ from 'lodash';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faSortUp,
        faSortDown,
        faPencilAlt,
        faAdd,
        faTrash,
        faRefresh,
        faSave,
        faClose,
    } from '@fortawesome/free-solid-svg-icons';
    import {
        Table,
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Heading,
        Checkbox,
        Button,
        Toggle,
        Toolbar,
        Dropdown,
    } from 'flowbite-svelte';
    import MultiSelect from '$lib/core-soc/components/MultiSelect.svelte';
    import Select from '$lib/core-soc/components/Select.svelte';
    import { ColumnDataType, type ColumnNameCustom } from '$lib/shared/constants/general';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import Search from '$lib/shared/components/search/Search.svelte';
    import { isNullOrEmpty } from '../../incident/utils/input_utils';
    import { onApplyAdvancedSearch, removeAdvancedSearchLabel } from '$lib/shared/util/advancedSearch';
    import type { ColumnType } from '$lib/shared/enum/search';
    import { isLoading } from '$lib/stores/Loading';
    import { hasAccess } from '$lib/shared/util/validation';
    import { SCOPE } from '$lib/shared/enum/general';

    export let data = [];
    export let updateList = [];
    export let searchText;
    export let columnNames: ColumnNameCustom[];
    export let hasSN = true;
    export let defaultSortKey: string = '';
    export let selectedRow = undefined;
    export let allowSelected = false;
    export let tableName: string;
    export let uniqueKey: string | string[] = 'code';
    export let configName = '';
    export let subData = {};
    export let extraData: any = {};
    export let FieldNames: any = {};
    export let customFuntion = (data?: any, keys?: any) => undefined;
    export let isDependentField: boolean = false;
    export let initData = [];
    export let newData;
    export let newRecords;
    export let abilities = [];

    export function resetAllTableValue() {
        updateList = [];
        updatePosition = { id: '', columnName: '', isEditing: false };
        selectedRow = undefined;
        dataSearch = data;
        displayLabels = [];
        filteredRecords = null;
        searchText = '';
        isSearch = false;
        popupModal = false;
        modalTitle = '';
        modalMessage = '';
        onlyOkBtn = true;
        confirmAction = undefined;
        sortDirection.set(ASC);
        sortKey.set(defaultSortKey || '');
        resetSearchParams?.();
    }

    let dataConfig;
    let isDisplayCategory;
    let previousStatus;
    let dataSearch = [];
    let displayLabels: string[] = [];
    let resetSearchParams;
    let applyAdvancedSearch;
    let advancedSearchVariables: Record<string, any> = {};
    let advancedSearchLabels: Record<string, string> = {};
    let displayRecordIds: string[];
    const checkAllMapped = writable({});
    let event: any;
    let searchColumns =
        columnNames
            ?.filter(col => col.isSearchable !== false)
            ?.map(item => {
                return {
                    name: item.name,
                    label: item.label,
                    type: item.type as unknown as ColumnType,
                    isUseDataDisplay: item.isUseDataDisplay ?? false,
                };
            }) ?? [];

    let toggleKey = 'DisplayCategory'; // Refer SIT 511 Issue 4

    if (FieldNames) {
        searchColumns = searchColumns.map(col => {
            if (col.isUseDataDisplay) {
                return {
                    ...col,
                    name: `${col.name}_display`,
                };
            }
            return col;
        });
    }
    $: console.log({ abilities });

    onMount(async () => {
        initData = data;
        dataSearch = data;
        checkSelectAll();

        if (configName) {
            const data = await getInventoryBackend(configName);
            dataConfig = data.data;
            isDisplayCategory = JSON.parse(dataConfig?.key_value_json?.[toggleKey] ?? 'false');
            previousStatus = isDisplayCategory;
        }
    });

    $: {
        if (newData && !initData.some(item => item.id === newData.id)) {
            initData = [...initData, newData];
        }
    }

    async function handleSaveConfig() {
        isLoading.set(true);
        const userName = get(user).claims?.preferred_username;
        const newData = {
            ...dataConfig,
            UpdatedBy: userName,
            KeyValueJson: {
                [toggleKey]: isDisplayCategory,
            },
            FormFieldsJson: dataConfig?.form_fields_json ?? '',
        };
        if (dataConfig?.ID) {
            await fetch(`${get(locations).common_web}/cfg-parameter/${dataConfig.ID}`, {
                method: 'PATCH',
                body: JSON.stringify(newData),
            }).then(async response => {
                if (response.status === 200) {
                    // This API is for update filtering label for all SOC US when toggling the Display Category
                    const triggerData = {
                        key_value_json: { [toggleKey]: isDisplayCategory },
                        name: dataConfig?.Name || dataConfig?.name,
                    };
                    await fetch(`/api/soc/cfg-parameter`, { method: 'PATCH', body: JSON.stringify(triggerData) });
                    // End
                    isLoading.set(false);
                    return true;
                }
            });
        }
        isLoading.set(false);
    }

    type UpdatePosition = {
        id: string;
        columnName: string;
        isEditing: boolean; // determine whether the positon is being edit
    };

    export let updatePosition: UpdatePosition = { id: '', columnName: '', isEditing: false };

    const ASC: number = 1;
    const sortKey = writable(defaultSortKey || '');
    const sortDirection = writable(ASC);
    const sortItems = writable([]);
    let filteredRecords: any[] = null;

    let popupModal = false;
    let modalTitle = '';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;
    let isSearch = false;
    let isUpdateColumn = true;

    type ComponentEvents = {
        save: any;
        add: null;
        delete: any;
        refresh: any;
    };
    const dispatch = createEventDispatcher<ComponentEvents>();

    const sortTable = (event: any, key: string) => {
        if (event.target.type === 'checkbox') {
            event.stopPropagation();
            return;
        }

        if ($sortKey === key) {
            sortDirection.update(val => -val);
        } else {
            sortKey.set(key);
            sortDirection.set(ASC);
        }
    };

    const functionSort = (key: string, direction: number) => (a: any, b: any) => {
        let aVal = a[key];
        let bVal = b[key];

        if (typeof aVal === 'string' && typeof bVal === 'string') {
            if (aVal?.includes('%') && bVal?.includes('%')) {
                aVal = aVal.replace('%', '');
                bVal = bVal.replace('%', '');
            }
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }
        if (aVal === null || bVal === null) {
            return 0;
        }

        return aVal < bVal ? -direction : aVal > bVal ? direction : 0;
    };

    interface Identifiable {
        id: string;
    }

    const addToUpdateList = <T extends Identifiable>(item: T) => {
        const idx = updateList.findIndex(e => e.id === item.id);
        if (idx === -1) {
            updateList = [...updateList, { ...structuredClone(item) }];
        }
    };

    const updateItem = <T extends Identifiable>(item: T, column: ColumnNameCustom) => {
        if (
            !column?.editable ||
            (!(newRecords && typeof newRecords.has === 'function' && newRecords.has(item.id)) &&
                column.isNotEditableAfterSave)
        ) {
            return;
        }
        addToUpdateList(item);
        updatePosition.isEditing = true;
        updatePosition.id = item.id;
        updatePosition.columnName = column.name;
    };

    const onKeyUp = (event: KeyboardEventInit) => {
        if (event.name === 'Enter') {
            updatePosition.isEditing = false;
        }
    };

    const onFocusOut = <T extends Identifiable>(item: T, column: ColumnNameCustom) => {
        updatePosition.isEditing = false;
        // check if not change, remove item from list update
        if (!isChange(updateList.find(i => i.id === item.id))) {
            updateList = updateList.filter(i => i.id != item.id);
        }
    };

    const updateField = <T extends Identifiable>(event: any, item: T, col: ColumnNameCustom) => {
        const { type, name } = col;
        let dataDisplay;
        updateList = updateList.map(u => {
            if (u.id === item.id) {
                if (type === ColumnDataType.Array) {
                    if (event.detail && Array.isArray(event.detail)) {
                        u[name] = event.detail.map((e: { value: any }) => e.value);
                    } else {
                        u[name] = [];
                    }
                } else {
                    const value = event.detail?.value ?? event.currentTarget?.value ?? '';
                    if (type === ColumnDataType.Number) {
                        u[name] = value ? Number(value) : null;
                    } else if (type === ColumnDataType.String || type === ColumnDataType.SingleStringSelect) {
                        if (name === FieldNames.ScheduleName) {
                            dataDisplay = extraData?.roomSchedulingData?.find(st => st.schedule_id === value);
                            u[`${name}_display`] = dataDisplay ? dataDisplay.schedule_name : value;
                        } else if (name === FieldNames.SessionType) {
                            dataDisplay = extraData?.sessionTypeData?.find(st => st.id === value);
                            u[`${name}_display`] = dataDisplay ? dataDisplay.category : value;
                        } else if (name === FieldNames.LevelOfCare) {
                            dataDisplay = extraData?.classOfPatientData?.find(st => st.id === value);
                            u[`${name}_display`] = dataDisplay ? dataDisplay.code : value;
                        }
                        u[name] = value?.trim() || '';
                    } else if (type === ColumnDataType.SingleNumberSelect) {
                        u[name] = value;
                    }
                }

                if (col.dependedByColumns && col.dependedByColumns.length > 0) {
                    col.dependedByColumns.forEach(depCol => {
                        const dependentValue = subData[depCol].find(
                            (d: { key: string; value: any }) =>
                                d.key === (type === ColumnDataType.Array ? u[name] : event.detail?.value)
                        )?.value;
                        u[depCol] = dependentValue || '';
                        if (depCol === FieldNames.RoomName) {
                            u[`${depCol}_display`] = dataDisplay?.room_name || '';
                        }
                    });
                }

                if (isDependentField) {
                    const keyDependent = {
                        [FieldNames.RoomName]: u[FieldNames.RoomName],
                        [FieldNames.Year]: u[FieldNames.Year],
                        [FieldNames.Month]: u[FieldNames.Month],
                    };
                    const codeData = customFuntion(extraData?.clinicRoomFileData, keyDependent);
                    u[FieldNames.ClinicCode] = codeData || '';
                }
            }
            return u;
        });
        if (type !== ColumnDataType.Array) {
            dataSearch = getNewData();
        }
    };

    const updateBooleanField = <T extends Identifiable>(event: any, item: T, field: string) => {
        const idx = updateList.findIndex(e => e.id === item.id);
        if (idx === -1) {
            updateList = [...updateList, { ...item, [field]: event.currentTarget.checked }];
        } else {
            updateList[idx][field] = event.currentTarget.checked;
        }
        dataSearch = getNewData();

        // Handle check all when change boolean field
        let isCheckedAll = true;
        const mappedUpdateList = new Map(updateList.map(obj => [obj.id, obj]));
        for (const item of $sortItems) {
            if (mappedUpdateList.has(item.id)) {
                if (!mappedUpdateList.get(item.id)[field]) {
                    isCheckedAll = mappedUpdateList.get(item.id)[field];
                    break;
                }
                continue;
            }
            if (!item[field]) {
                isCheckedAll = false;
                break;
            }
        }
        checkAllMapped.set({
            ...$checkAllMapped,
            [field]: isCheckedAll,
        });
        checkAllMapped[field] = isUpdateColumn === true ? event.currentTarget.checked : isCheckedAll;
    };

    const onCheckAll = (key = undefined) => {
        checkAllMapped[key] = !checkAllMapped[key];
        data = data.map(u => {
            const item = $sortItems.find(item => item.id === u.id);
            if (item) {
                if (key) {
                    const e = { currentTarget: { checked: checkAllMapped[key] } };
                    updateBooleanField(e, item, key);
                }
            }
            return u;
        });
    };

    const isEdited = <T extends Identifiable>(list: T[], item: T, col: string): boolean => {
        const foundItem = list.find(e => e.id === item.id);
        const foundInitItem = initData.find(e => e.id === item.id);

        if (foundItem && foundInitItem) {
            if (foundItem[col] instanceof Array || foundInitItem[col] instanceof Array) {
                return !_.isEqual(foundItem[col], foundInitItem[col]);
            }
            return foundItem[col] !== foundInitItem[col];
        }

        return false;
    };

    const isChange = <T extends Identifiable>(item: T): boolean => {
        const oldItem = $sortItems.find(i => i.id === item.id);
        return !_.isEqual(oldItem, item);
    };
    const isSelected = <T extends Identifiable>(
        position: UpdatePosition,
        item: T,
        column: ColumnNameCustom
    ): boolean => {
        return position.id === item.id && position.columnName === column.name;
    };

    function isColumnUsingDisplay(name: string, columns: ColumnNameCustom[]): boolean {
        const column = columns.find(col => col.name === name);
        return column?.isUseDataDisplay === true;
    }

    const getValue = <T extends Identifiable>(list: T[], item: T, col: string): string => {
        const findById = (arr: T[]) => arr?.find(e => e.id === item.id);
        const zeroString = (value: any): string => {
            return value === 0 ? '0' : value == null || value === '' ? '\u00A0' : value;
        };

        let foundItem = findById(list) || findById(data);

        if (!foundItem) return '\u00A0';

        const usesDisplay = isColumnUsingDisplay(col, columnNames);

        const colInfo = columnNames?.find(c => c.name === col);

        if (colInfo?.type === ColumnDataType.Array) {
            const values: string[] = foundItem[col] ?? [];

            if (!Array.isArray(values) || values.length === 0) {
                return '\u00A0';
            }

            const items = subData?.[col] ?? [];

            const names = values
                .map(val => {
                    const found = items.find(i => i.name === val || i.value === val);
                    return found ? found.name : null;
                })
                .filter(name => name != null);

            return zeroString(names.join(', '));
        }

        const value = usesDisplay
            ? foundItem?.[col]
                ? foundItem[`${col}_display`]
                : item[`${col}_display`]
            : foundItem?.[col] ?? item[col];
        return zeroString(value);
    };

    const getValueDropdown = <T extends Identifiable>(list: T[], item: T, col: string): string => {
        const zeroString = (value: any): string => {
            return value === 0 ? '0' : value == null || value === '' ? '\u00A0' : value;
        };
        let foundItem = list?.find(e => e.id === item.id);
        if (foundItem) {
            return zeroString(foundItem[col]);
        } else {
            foundItem = data.find(e => e.id === item.id);
            return zeroString(item[col]);
        }
    };

    const getBoolValue = <T extends Identifiable>(list: T[], item: T, col: string): boolean => {
        let foundItem = list?.find(e => e.id === item.id);
        if (foundItem) {
            return foundItem[col];
        } else {
            foundItem = data.find(e => e.id === item.id);
            return foundItem?.[col] ? foundItem[col] : item[col];
        }
    };
    const getNewData = () => {
        const mergedMap = new Map();
        let mergeData = [];

        updateList.forEach(item => {
            mergedMap.set(item.id, { ...item });
        });

        data.forEach(item => {
            if (mergedMap.has(item.id)) {
                mergeData.push(mergedMap.get(item.id));
            } else {
                mergeData.push(item);
            }
        });
        return mergeData;
    };

    const validateData = () => {
        let mergeData = getNewData();

        function showPopup(title: string, message: string) {
            popupModal = true;
            modalTitle = title;
            modalMessage = message;
            onlyOkBtn = true;
            confirmAction = () => {};
        }

        function validateEntries(data: any[]): boolean {
            return data.every(entry =>
                Object.entries(entry).some(([key, value]) => key !== 'id' && value !== null && value !== '')
            );
        }

        if (!validateEntries(mergeData)) {
            const requiredLabels = columnNames
                .filter(col => col.isRequired)
                .map(col => col.label || col.name)
                .join(' or ');
            showPopup(
                'Notification',
                `The value in ${requiredLabels} column${requiredLabels.includes(' or ') ? 's' : ''} cannot be empty.`
            );
            return false;
        }

        const requiredFields = columnNames.reduce((acc, col) => {
            if (col.isRequired) {
                acc.push(col.name);
            }
            return acc;
        }, []);

        if (requiredFields) {
            for (const row of mergeData) {
                // Find all missing required fields in this row
                const missingFields = requiredFields?.filter(field => isNullOrEmpty(row[field]));
                if (missingFields.length) {
                    // Get their labels for the message
                    const colLabels = missingFields
                        .map(field => columnNames.find(col => col.name === field)?.label || field)
                        .join(' or ');
                    showPopup(
                        'Notification',
                        `The value in ${colLabels} column${missingFields.length > 1 ? 's' : ''} cannot be empty.`
                    );
                    return false;
                }
            }
        }

        let codeSet = new Set();
        for (let item of mergeData) {
            const compositeKey = Array.isArray(uniqueKey) ? uniqueKey.map(key => item[key]).join('|') : item[uniqueKey];

            if (codeSet.has(compositeKey)) {
                popupModal = true;
                modalTitle = 'Notification';
                const isArrayOfUniqueKey = Array.isArray(uniqueKey);

                // Get the duplicate value(s)
                const duplicateValue = isArrayOfUniqueKey
                    ? uniqueKey.map(key => `'${item[key]}'`).join(', ')
                    : `'${item[uniqueKey]}'`;

                modalMessage = `${duplicateValue} already exists.`.replace(/_/g, ' ');
                onlyOkBtn = true;
                confirmAction = () => {};
                return false;
            }
            codeSet.add(compositeKey);
        }

        if (updateList.length === 0 && isDisplayCategory === previousStatus) {
            return false;
        }

        return true;
    };

    const onSave = () => {
        if (!validateData()) {
            return;
        }
        dispatch('save');
    };
    const onAdd = () => {
        dispatch('add');
    };

    const handleDeleteData = () => {
        if (selectedRow) {
            popupModal = true;
            modalTitle = 'Confirmation';
            modalMessage = `Are you sure you want to delete the selected record(s)?<br />Click Yes to confirm.`;
            confirmAction = () => onDelete();
            onlyOkBtn = false;
        } else {
            modalTitle = 'Notification';
            popupModal = true;
            modalMessage = `Please selected one record.`;
            onlyOkBtn = true;
        }
    };

    const onDelete = () => {
        if (selectedRow) {
            dispatch('delete', { data: selectedRow });
        }
    };

    const onRefresh = () => {
        resetAllTableValue();
        dispatch('refresh');
    };

    function checkSelectAll() {
        const mappedBoolCol = {};
        for (const col of columnNames) {
            if (col?.type === 'boolean') {
                mappedBoolCol[col.name] = data.every(d => d[col.name]);
            }
        }
        checkAllMapped.set(mappedBoolCol);
    }

    $: {
        if (initData) {
            checkSelectAll();
        }
    }

    $: {
        data = getNewData();
        if (data?.length) {
            const headerKeys = columnNames?.map(n => (n?.isUseDataDisplay ? `${n.name}_display` : n.name));
            const key = $sortKey;
            const direction = $sortDirection;
            const sorted = data.filter(d => {
                if (!searchText) return true;
                return headerKeys.some(key => {
                    if (!d[key]) return false;
                    const value = typeof d[key] === 'string' ? d[key] : d[key]?.toString();
                    return value?.toLowerCase()?.includes(searchText.toLowerCase());
                });
            });

            if ((filteredRecords && !filteredRecords.length) || !data?.length) {
                $sortItems = [];
            } else {
                const temp = filteredRecords && isSearch ? filteredRecords : data;
                $sortItems = searchText === '' ? temp : sorted;
            }
            $sortItems.sort(functionSort(key, direction));
        } else {
            sortItems.set([]);
        }
    }

    // Styles
    const inputStyle =
        'block w-full rounded-lg p-2.5 text-sm text-gray-900 ' +
        'border border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-700 ' +
        'focus:border-blue-500 focus:ring-blue-500 ' +
        'dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500';
    const headCellStyle =
        'border border-b border-r border-gray-500 border-t-0 border-l-0 dark:text-white text-gray-900 last:border-r-0';
    const bodyCellStyle = 'text-nowrap border-b border-r border-gray-500 last:border-r-0';

    let isScrollable = true;
    let position;

    function checkScroll(event) {
        const hasArrayColumn = columnNames.some(
            col =>
                col.type === ColumnDataType.Array ||
                col.type === ColumnDataType.SingleNumberSelect ||
                col.type === ColumnDataType.SingleStringSelect
        );
        if (!hasArrayColumn) {
            isScrollable = true;
            return;
        }
        position = event?.clientY || 0;
        if (data.length <= 10) {
            isScrollable = false;
        } else {
            isScrollable = true;
        }
    }
</script>

<div class="m-4 h-[calc(100%-12rem)]">
    <div
        class="w-full rounded-t-xl border border-gray-200 bg-gray-50 p-3 font-bold text-white dark:border-gray-600 dark:bg-gray-700">
        <Heading tag="h5">{tableName}</Heading>
    </div>
    <div
        class="h-[calc(100vh-160px)] overflow-y-auto rounded-b-xl px-2 pt-5 text-gray-700 dark:bg-gray-900 dark:text-gray-400">
        <Toolbar embedded>
            <div class="flex gap-4">
                <div class="flex">
                    <Search
                        bind:searchText
                        bind:resetSearchParams
                        columns={searchColumns}
                        bind:applySearch={applyAdvancedSearch}
                        bind:data={dataSearch}
                        bind:isSearch
                        on:apply={e => {
                            if (!e?.detail) {
                                return;
                            }
                            const newData = onApplyAdvancedSearch(e, {
                                advancedSearchVariables,
                                advancedSearchLabels,
                                displayLabels,
                                displayRecordIds,
                                searchColumns,
                            });
                            advancedSearchVariables = newData.advancedSearchVariables;
                            advancedSearchLabels = newData.advancedSearchLabels;
                            displayLabels = newData.displayLabels;
                            filteredRecords = e.detail ? e.detail.filteredData : data;
                        }} />
                </div>
                {#if hasAccess(abilities, SCOPE.UPDATE) || hasAccess(abilities, SCOPE.CREATE)}
                    <Button size="md" color="light" on:click={onSave} class="dark:bg-gray-700">
                        <span class="mr-2">{@html icon(faSave).html}</span>
                        <span>Save</span>
                    </Button>
                {/if}
                {#if hasAccess(abilities, SCOPE.CREATE)}
                    <Button size="md" color="light" on:click={onAdd} class="dark:bg-gray-700">
                        <span class="mr-2">{@html icon(faAdd).html}</span>
                        <span>New</span>
                    </Button>
                {/if}
                {#if hasAccess(abilities, SCOPE.DELETE)}
                    <Button size="md" color="light" on:click={handleDeleteData} class="dark:bg-gray-700">
                        <span class="mr-2">{@html icon(faTrash).html}</span>
                        <span>Delete</span>
                    </Button>
                {/if}
                <Button size="md" color="light" on:click={onRefresh} class="dark:bg-gray-700">
                    <span class="mr-2">{@html icon(faRefresh).html}</span>
                    <span>Refresh</span>
                </Button>
                {#if configName}
                    {#if hasAccess(abilities, SCOPE.TOGGLE)}
                        <Toggle color="blue" on:change={handleSaveConfig} bind:checked={isDisplayCategory}
                            >Display Category</Toggle>
                    {/if}
                {/if}

                <slot name="header-button" {selectedRow} {onRefresh} />
            </div>
        </Toolbar>
        {#if displayLabels.length > 1}
            <div class="mt-3">
                {#each displayLabels as label, i}
                    <div class="mb-2 mr-2 inline-block rounded-md bg-gray-700 px-3 py-2 text-sm">
                        <span>{@html label}</span>
                        {#if i > 0}
                            <button
                                class="pl-2 text-red-600"
                                on:click={() =>
                                    removeAdvancedSearchLabel({
                                        index: i,
                                        advancedSearchVariables,
                                        advancedSearchLabels,
                                        applyAdvancedSearch,
                                    })}>{@html icon(faClose).html}</button>
                        {/if}
                    </div>
                {/each}
            </div>
        {/if}
        <div
            class="mt-5 max-h-[calc(100%-80px)] {isScrollable
                ? 'overflow-y-auto'
                : 'overflow-visible'} border border-gray-500">
            <Table
                striped
                shadow
                placeholder="Search by maker name"
                style="border-collapse: separate; border-spacing: 0;"
                divClass="">
                <TableHead
                    defaultRow={false}
                    class="z-[1] bg-gray-50 text-base text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                    theadClass="sticky top-0 z-[-0]">
                    {#if hasSN}<TableHeadCell class={`${headCellStyle} w-20`}>SN</TableHeadCell>{/if}
                    {#if columnNames?.length}
                        {#each columnNames as col}
                            <TableHeadCell
                                on:click={e => sortTable(e, col.name)}
                                class={`${headCellStyle} w-80 cursor-pointer text-base font-medium`}>
                                <div class="flex items-center text-nowrap">
                                    {@html col.label}
                                    {#if col.isRequired}
                                        <span class="text-red-500">*</span>
                                    {/if}
                                    {#if col.editable}
                                        <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>
                                    {/if}
                                    {#if col.type === ColumnDataType.Boolean}
                                        <Checkbox
                                            class="ml-2"
                                            disabled={!hasAccess(abilities, SCOPE.UPDATE)}
                                            on:change={() => {
                                                isUpdateColumn = true;
                                                onCheckAll(col.name);
                                            }}
                                            checked={$checkAllMapped[col.name]} />
                                    {/if}
                                    {#if col.name === $sortKey && $sortDirection === ASC}
                                        <span class="ml-2">{@html icon(faSortUp).html}</span>
                                    {/if}
                                    {#if col.name === $sortKey && $sortDirection === -ASC}
                                        <span class="ml-2">{@html icon(faSortDown).html}</span>
                                    {/if}
                                </div>
                            </TableHeadCell>
                        {/each}
                    {/if}
                </TableHead>
                <TableBody>
                    {#if !$sortItems?.length}
                        <TableBodyRow>
                            <TableBodyCell class="border border-gray-600" colSpan={columnNames.length + 1}>
                                <Heading tag="h6" class="p-1 text-center">No data</Heading>
                            </TableBodyCell>
                        </TableBodyRow>
                    {/if}
                    {#each $sortItems as item, i}
                        <TableBodyRow>
                            {#if hasSN}<TableBodyCell
                                    class={`${bodyCellStyle} ${
                                        selectedRow === item.id && allowSelected && '!bg-gray-500'
                                    }`}>{i + 1}</TableBodyCell
                                >{/if}
                            {#each columnNames as col}
                                <TableBodyCell
                                    class={`${bodyCellStyle}  ${col.customStyle ? '' : 'px-6 py-4'} ${
                                        isSelected(updatePosition, item, col) ? ' dark:bg-gray-900' : ''
                                    } ${selectedRow === item.id && allowSelected && '!bg-gray-500'}`}
                                    tdClass={`${isEdited(updateList, item, col.name) && `relative editing`}`}>
                                    {#if typeof item[col.name] === ColumnDataType.Boolean}
                                        <Checkbox
                                            disabled={!hasAccess(abilities, SCOPE.UPDATE)}
                                            checked={getBoolValue(updateList, item, col.name)}
                                            on:change={e => {
                                                isUpdateColumn = false;
                                                updateBooleanField(e, item, col.name);
                                            }} />
                                    {:else if col.type === ColumnDataType.Array && updatePosition.isEditing && updatePosition.id === item.id && updatePosition.columnName === col.name && col.editable}
                                        <span
                                            on:click={event => checkScroll(event)}
                                            class="min-w-40 whitespace-normal break-words">
                                            <MultiSelect
                                                items={subData[col.name] ?? []}
                                                value={item?.[col.name] ?? []}
                                                dropdownClass={i + 2 > $sortItems.length - 1 && position > 750
                                                    ? '-mt-80'
                                                    : ''}
                                                on:change={event => {
                                                    updateItem(item, col);
                                                    updateField(event, item, col);
                                                }} />
                                        </span>
                                    {:else if (col.type === ColumnDataType.SingleStringSelect || col.type === ColumnDataType.SingleNumberSelect) && updatePosition.isEditing && updatePosition.id === item.id && updatePosition.columnName === col.name && col.editable}
                                        <span
                                            on:click={event => checkScroll(event)}
                                            class="min-w-40 whitespace-normal break-words">
                                            <Select
                                                value={getValueDropdown(updateList, item, col.name)}
                                                items={subData[col.name] ?? []}
                                                dropdownClass={i + 2 > $sortItems.length - 1 && position > 750
                                                    ? '-mt-80'
                                                    : ''}
                                                on:change={event => {
                                                    updateItem(item, col);
                                                    updateField(event, item, col);
                                                }} />
                                        </span>
                                    {:else if updatePosition.isEditing && updatePosition.id === item.id && updatePosition.columnName === col.name && col.editable}
                                        <input
                                            maxlength={col.type === ColumnDataType.Number
                                                ? 5
                                                : col.name.toLowerCase() === 'remark' ||
                                                  col.name.toLowerCase() === 'remarks'
                                                ? 300
                                                : 100}
                                            on:input={event => {
                                                if (col.type === ColumnDataType.Number) {
                                                    const input = event.currentTarget;
                                                    const oldValue = input.value;

                                                    // Only allow digits, max 5
                                                    let newValue = oldValue.replace(/\D/g, '').slice(0, 5);

                                                    if (oldValue !== newValue) {
                                                        input.value = newValue;
                                                        // Only set selection range if input type is text
                                                        if (input.type === 'text') {
                                                            input.setSelectionRange(newValue.length, newValue.length);
                                                        }
                                                    }
                                                }
                                            }}
                                            on:change={event => updateField(event, item, col)}
                                            on:keyup={event => onKeyUp(event)}
                                            on:focusout={() => onFocusOut(item, col)}
                                            value={getValue(updateList, item, col.name)}
                                            type={col.type === ColumnDataType.Number ? 'number' : 'text'}
                                            max="99999"
                                            step="1"
                                            min="0"
                                            id={col.name}
                                            name={col.name}
                                            class={inputStyle}
                                            placeholder={`Input ${col.label}`} />
                                    {:else if col.customStyle}
                                        <slot
                                            name="row-custom"
                                            rowData={getValue(updateList, item, col.name)}
                                            colData={col}
                                            itemData={item} />
                                    {:else}
                                        <button
                                            type="button"
                                            class={`max-w-xs whitespace-pre-line break-words ${
                                                col.editable ? 'cursor-pointer' : 'cursor-text'
                                            } min-h-5 w-full text-left`}
                                            on:dblclick={() => {
                                                if (!item.isNew && hasAccess(abilities, SCOPE.UPDATE))
                                                    updateItem(item, col);
                                                if (item.isNew && hasAccess(abilities, SCOPE.CREATE))
                                                    updateItem(item, col);
                                            }}
                                            on:click={() => {
                                                selectedRow = item.id;
                                            }}>
                                            {#if !item[col.name] && !getValue(updateList, item, col.name)}
                                                <div>&nbsp;</div>
                                            {:else}
                                                <div>
                                                    {getValue(updateList, item, col.name)}
                                                </div>
                                            {/if}
                                        </button>
                                    {/if}
                                </TableBodyCell>
                            {/each}
                        </TableBodyRow>
                    {/each}
                </TableBody>
            </Table>
        </div>
    </div>
</div>

<Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />

<style>
    :global(.editing)::after {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 10px 10px 0;
        border-color: transparent #c54343 transparent transparent;
        right: 0;
        top: 0;
        position: absolute;
    }
</style>
