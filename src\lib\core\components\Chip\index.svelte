<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    /***
     * A note on chips:
     * Consider using 80dp (which is 80px in CSS or min-w-20 in TailwindCSS) when we add actions to chips. This is to
     * ensure there's always space for the action (e.g. deleting a chip when it is clicked); this is copying Material's
     * spec.
     **/
</script>

<div
    class="flex w-fit items-center justify-center text-nowrap rounded-sm bg-primary
    p-1 text-xs font-bold leading-[115%] text-on-primary">
    <slot />
</div>
