<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';

    // Plugin to allow usage of durations
    dayjs.extend(dayjs.duration);

    export const widgetTitle = 'Arrow';
    export const widgetIcon = faArrowRight;
    export const widgetMinWidth = 2;
    export const widgetMinHeight = 2;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 10;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { onDestroy, onMount } from 'svelte';
    import { type Subscription } from 'wonka';
    import { type IndicatorConfig } from './WidgetConfig/index.svelte';

    export let config: IndicatorConfig;

    let subscription: Subscription;
    let LineElement: HTMLElement;
    let ArrowHeadElement: HTMLElement;
    let TextElement: HTMLElement;
    let Element: HTMLElement;
    let titleElement: HTMLElement;
    let HorizontalElement: HTMLElement;
    let VerticalElement: HTMLElement;
    let ContainerElement: HTMLElement;
    let ArrowElement: HTMLElement;
    let mounted = false;
    let elementClass = {
        left: 'flex h-full w-full flex-col items-center justify-center overflow-hidden px-4 py-1 text-on-background',
        upright: 'flex h-full w-full items-end justify-center overflow-hidden px-4 py-1 text-on-background',
        downright: 'flex h-full w-full items-end justify-center overflow-hidden px-4 py-1 text-on-background',
        up: 'flex h-full w-full flex-col overflow-hidden px-4 pt-4 pb-1 text-on-background',
    };
    let directionsObj = {
        right: 'right',
        left: 'left',
        up: 'up',
        down: 'down',
        upright: 'upright',
        upleft: 'upleft',
        downright: 'downright',
        downleft: 'downleft',
    };

    function setClassByDirection(direction) {
        if (direction == 'right' || direction == 'left') {
            return elementClass.left;
        } else if (direction == 'upright' || direction == 'upleft') {
            return elementClass.upright;
        } else if (direction == 'downright' || direction == 'downleft') {
            return elementClass.downright;
        } else if (direction == 'up' || direction == 'down') {
            return elementClass.up;
        }
    }

    function resize(element: HTMLElement, referenceElement?: HTMLElement) {
        let referenceHeight = referenceElement?.clientHeight;
        let referenceWidth = referenceElement?.clientWidth;
        let direction = config?.direction;
        if (!element) {
            return;
        }
        if (!referenceElement) {
            referenceElement = element;
        }

        let vmin = 0;

        if (referenceWidth / referenceHeight < 5 / 3) {
            vmin = (referenceWidth * 3) / 6;
        } else {
            vmin = referenceHeight;
        }
        let arrowWidth = 0;
        let lineWeight = 0;
        if (config?.thicknessType == '1') {
            lineWeight = 10;
            arrowWidth = 15;
        } else if (config?.thicknessType == '2') {
            lineWeight = 20;
            arrowWidth = 28;
        } else if (config?.thicknessType == '3') {
            lineWeight = 30;
            arrowWidth = 40;
        } else if (config?.thicknessType == '4') {
            lineWeight = 40;
            arrowWidth = 55;
        } else if (config?.thicknessType == '5') {
            lineWeight = 50;
            arrowWidth = 70;
        }

        if (element == ArrowHeadElement) {
            element.style.width = arrowWidth + 'px';
            element.style.height = arrowWidth + 'px';
        }

        if (element == TextElement) {
            if (config.direction == directionsObj.down || config.direction == directionsObj.up) {
                element.style.right =
                    ContainerElement.clientWidth / 2 + lineWeight - TextElement.clientHeight / 2 + 'px';
            } else {
                element.style.bottom = ContainerElement.clientHeight / 2 + lineWeight / 2 + 'px';
            }
        }

        if (direction == directionsObj.right || direction == directionsObj.left) {
            if (element == LineElement) {
                element.style.width = referenceWidth - arrowWidth + 'px';
                element.style.height = lineWeight + 'px';
                if (direction == directionsObj.right){
                    element.style.right =  arrowWidth + 'px';
                    element.style.left =  '0px';
                } else {
                    element.style.left = arrowWidth + 'px';
                    element.style.right =  '0px';
                }
            }
            if (element == ArrowHeadElement){
                if (direction == directionsObj.right){
                    element.style.right =  '0px';
                    element.style.left = referenceWidth - arrowWidth + 'px';
                } else {
                    element.style.left = '0px';
                    element.style.right = referenceWidth - arrowWidth + 'px';
                }
            }
        } else if (direction == directionsObj.up || direction == directionsObj.down) {
            if (element == LineElement) {
                element.style.height = (referenceHeight * 0.9 - arrowWidth - titleElement.clientHeight) * 0.9 + 'px';
                element.style.width = lineWeight + 'px';
            }
        } else if (direction == directionsObj.upright) {
            if (element == HorizontalElement) {
                element.style.width = referenceWidth / 2 - arrowWidth + lineWeight / 2 + 'px';
                element.style.height = lineWeight + 'px';
                element.style.right = arrowWidth + 'px';
            }
            if (element == VerticalElement) {
                element.style.width = lineWeight + 'px';
                element.style.height = referenceHeight / 2 - lineWeight / 2 + 'px';
                element.style.right = arrowWidth + referenceWidth / 2 - arrowWidth - lineWeight / 2 + 'px';
            }
        } else if (direction == directionsObj.upleft) {
            if (element == HorizontalElement) {
                element.style.width = referenceWidth / 2 - arrowWidth + lineWeight / 2 + 'px';
                element.style.height = lineWeight + 'px';
                element.style.left = arrowWidth + 'px';
            }
            if (element == VerticalElement) {
                element.style.width = lineWeight + 'px';
                element.style.height = referenceHeight / 2 - lineWeight / 2 + 'px';
                element.style.left = arrowWidth + referenceWidth / 2 - arrowWidth - lineWeight / 2 + 'px';
            }
        } else if (direction == directionsObj.downright) {
            if (element == HorizontalElement) {
                element.style.width = referenceWidth / 2 + 'px';
                element.style.height = lineWeight + 'px';
                element.style.top = (-1 * lineWeight) / 2 + 'px';
                element.style.right = HorizontalElement.clientWidth - lineWeight / 2 + 'px';
            }
            if (element == VerticalElement) {
                element.style.width = lineWeight + 'px';
                element.style.height = (referenceHeight / 2 - arrowWidth) * 0.9 + 'px';
            }
            if (ArrowElement)
                ArrowElement.style.right = HorizontalElement.clientWidth - ArrowHeadElement.clientHeight / 2 + 'px';
        } else if (direction == directionsObj.downleft) {
            if (element == HorizontalElement) {
                element.style.width = referenceWidth / 2 + 'px';
                element.style.height = lineWeight + 'px';
                element.style.top = (-1 * lineWeight) / 2 + 'px';
                element.style.left = HorizontalElement.clientWidth - lineWeight / 2 + 'px';
            }
            if (element == VerticalElement) {
                element.style.width = lineWeight + 'px';
                element.style.height = (referenceHeight / 2 - arrowWidth) * 0.9 + 'px';
            }
            if (ArrowElement)
                ArrowElement.style.left = HorizontalElement.clientWidth - ArrowHeadElement.clientHeight / 2 + 'px';
        }
    }

    function setMaxTextSize(element: HTMLElement) {
        if (!ContainerElement) {
            return;
        }
        if (!element) {
            return;
        }
        let calTextSize = 0;
        calTextSize =
            config.direction == directionsObj.up || config.direction == directionsObj.down
                ? ContainerElement.clientWidth / 2 - ArrowHeadElement.clientHeight - TextElement.clientHeight / 2
                : ContainerElement.clientHeight / 2 - ArrowHeadElement.clientHeight;

        if (config.textSize > Math.abs(calTextSize)) {
            element.style.fontSize = (calTextSize <= 10 ? '10' : calTextSize.toString()) + 'px';
        } else {
            element.style.fontSize = config.textSize + 'px';
        }
    }

    function updateSizes() {
        if (
            [directionsObj.right, directionsObj.left, directionsObj.up, directionsObj.down].includes(config?.direction)
        ) {
            resize(LineElement, ContainerElement);
        } else if (
            [directionsObj.upright, directionsObj.downright, directionsObj.upleft, directionsObj.downleft].includes(
                config?.direction
            )
        ) {
            resize(HorizontalElement, ContainerElement);
            resize(VerticalElement, ContainerElement);
        }
        resize(ArrowHeadElement, ContainerElement);
        resize(TextElement, Element);
        setMaxTextSize(TextElement);
    }

    onMount(async () => {
        updateSizes();
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            updateSizes();
        });
        mounted = true;
    });

    onDestroy(async () => {
        subscription?.unsubscribe();
    });
    let uomConfigValue = '';
    $: {
        if (
            mounted &&
            (config.direction ||
                config?.textSize ||
                ArrowHeadElement ||
                TextElement ||
                config.thicknessType ||
                config.throughputTime ||
                ContainerElement)
        ) {
            updateSizes();
        }
        uomConfigValue = config.uom.charAt(0).toLowerCase();
    }
</script>

<div bind:this={Element} class="{setClassByDirection(config?.direction)} relative">
    <header bind:this={titleElement} class="absolute top-0 left-0 w-full">
        <div class="px-2 text-{config.titleAlign}">
            {config.title}
        </div>
    </header>
    {#if config?.direction == directionsObj.right || config?.direction == directionsObj.left}
        <div bind:this={ContainerElement} class="flex h-full w-full flex-col items-center justify-center text-center">
            <span
                bind:this={TextElement}
                class="absolute mb-4 {config.isShowText ? '' : 'hidden'}"
                style="color: {config?.textColor}; line-height: 80%;">0/{config?.throughputTime == 1 || config?.throughputTime == 0 ? '' : config?.throughputTime}{uomConfigValue}</span>
            <div
                class="relative flex w-full items-center justify-center text-center {config?.direction == 'right'
                    ? 'flex-row'
                    : 'flex-row-reverse'}">
                <span bind:this={LineElement} class="absolute" style="background-color: {config.arrowColor};" />
                <span
                    bind:this={ArrowHeadElement}
                    class="absolute flow-arrow {config?.direction == 'right' ? '' : 'rotate-180'}"
                    style="width: 20px; height:20px">
                    <svg
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 100 100"
                        style="enable-background:new 0 0 100 100;"
                        xml:space="preserve">
                        <g>
                            <path id="rec" d="M0,0L0,100L100,50z" style="fill: {config?.arrowColor};" />
                        </g>
                    </svg>
                </span>
            </div>
        </div>
    {:else if config?.direction == directionsObj.up || config?.direction == directionsObj.down}
        <div
            bind:this={ContainerElement}
            class="relative flex h-full w-full flex-row items-center justify-center text-center">
            <span
                bind:this={TextElement}
                class="absolute mb-4 {config.isShowText ? '' : 'hidden'}"
                style="color: {config?.textColor}; line-height: 80%;transform: rotate(-90deg);white-space: nowrap;">
                0/{config.throughputTime ==1 || config?.throughputTime == 0 ? '' : config.throughputTime}{uomConfigValue}
            </span>
            <div
                class="flex items-center justify-center py-1 text-center {config?.direction == 'up'
                    ? 'flex-col-reverse'
                    : 'flex-col'}">
                <span bind:this={LineElement} class="" style="background-color: {config.arrowColor};" />
                <span
                    bind:this={ArrowHeadElement}
                    class="flow-arrow {config?.direction == 'up' ? '-rotate-90' : 'rotate-90'}"
                    style="width: 20px; height:20px">
                    <svg
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 100 100"
                        style="enable-background:new 0 0 100 100;"
                        xml:space="preserve">
                        <g>
                            <path id="rec" d="M0,0L0,100L100,50z" style="fill: {config?.arrowColor};" />
                        </g>
                    </svg>
                </span>
            </div>
        </div>
    {:else if config?.direction == directionsObj.upright}
        <div
            bind:this={ContainerElement}
            class="relative flex h-full w-full flex-col items-center justify-center text-center">
            <span
                bind:this={TextElement}
                class="absolute mb-4 {config.isShowText ? '' : 'hidden'}"
                style="color: {config?.textColor}; line-height: 80%;bottom:50%;"
                >0/{config?.throughputTime == 1 || config?.throughputTime == 0 ? '' : config?.throughputTime}{uomConfigValue}</span>
            <div bind:this={ArrowElement} class="relative flex w-full items-center justify-end py-1 text-center">
                <span
                    bind:this={VerticalElement}
                    class="absolute top-1/2 bg-white"
                    style="background-color: {config.arrowColor};" />
                <span bind:this={HorizontalElement} class="absolute bg-white" style="background-color: {config.arrowColor};" />
                <span bind:this={ArrowHeadElement} class="flow-arrow" style="width: 20px; height:20px;">
                    <svg
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 100 100"
                        style="enable-background:new 0 0 100 100;"
                        xml:space="preserve">
                        <g>
                            <path id="rec" d="M0,0L0,100L100,50z" style="fill: {config?.arrowColor};" />
                        </g>
                    </svg>
                </span>
            </div>
        </div>
    {:else if config?.direction == directionsObj.upleft}
        <div bind:this={ContainerElement} class="flex h-full w-full flex-col items-center justify-center text-center">
            <span
                bind:this={TextElement}
                class="absolute mb-4 {config.isShowText ? '' : 'hidden'}"
                style="color: {config?.textColor};line-height: 80%;">0/{config?.throughputTime == 1 || config?.throughputTime == 0 ? '' : config?.throughputTime}{uomConfigValue}</span>
            <div
                bind:this={ArrowElement}
                class="relative flex w-full flex-row-reverse items-center justify-end py-1 text-center">
                <span
                    bind:this={VerticalElement}
                    class="absolute top-1/2 bg-white"
                    style="background-color: {config.arrowColor};" />
                <span bind:this={HorizontalElement} class="absolute bg-white" style="background-color: {config.arrowColor};" />
                <span bind:this={ArrowHeadElement} class="flow-arrow rotate-180" style="width: 20px; height:20px;">
                    <svg
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 100 100"
                        style="enable-background:new 0 0 100 100;"
                        xml:space="preserve">
                        <g>
                            <path id="rec" d="M0,0L0,100L100,50z" style="fill: {config?.arrowColor};" />
                        </g>
                    </svg>
                </span>
            </div>
        </div>
    {:else if config?.direction == directionsObj.downright}
        <div bind:this={ContainerElement} class="flex h-full w-full flex-col items-center justify-end text-center">
            <span
                bind:this={TextElement}
                class="absolute mb-4 {config.isShowText ? '' : 'hidden'}"
                style="color: {config?.textColor};line-height: 80%;">0/{config?.throughputTime == 1 || config?.throughputTime == 0 ? '' : config?.throughputTime}{uomConfigValue}</span>
            <div class="relative flex h-1/2 w-full items-start py-1 text-center">
                <span
                    bind:this={HorizontalElement}
                    class="absolute bg-white"
                    style="background-color: {config.arrowColor};" />
                <div bind:this={ArrowElement} class="absolute flex flex-col items-center justify-center">
                    <span bind:this={VerticalElement} class="bg-white" style="background-color: {config.arrowColor};" />
                    <span
                        bind:this={ArrowHeadElement}
                        class="flow-arrow"
                        style="width: 20px; height:20px;transform: rotate(90deg)">
                        <svg
                            version="1.1"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            x="0px"
                            y="0px"
                            viewBox="0 0 100 100"
                            style="enable-background:new 0 0 100 100;"
                            xml:space="preserve">
                            <g>
                                <path id="rec" d="M0,0L0,100L100,50z" style="fill: {config?.arrowColor};" />
                            </g>
                        </svg>
                    </span>
                </div>
            </div>
        </div>
    {:else if config?.direction == directionsObj.downleft}
        <div bind:this={ContainerElement} class="flex h-full w-full flex-col items-center justify-end text-center">
            <span
                bind:this={TextElement}
                class="absolute mb-4 {config.isShowText ? '' : 'hidden'}"
                style="color: {config?.textColor}; line-height: 80%;">0/{config?.throughputTime == 1 || config?.throughputTime == 0 ? '' : config?.throughputTime}{uomConfigValue}</span>
            <div class="relative flex h-1/2 w-full flex-row-reverse items-start py-1 text-center">
                <span
                    bind:this={HorizontalElement}
                    class="absolute bg-white"
                    style="background-color: {config.arrowColor};" />
                <div bind:this={ArrowElement} class="absolute flex flex-col items-center justify-center">
                    <span bind:this={VerticalElement} class="bg-white" style="background-color: {config.arrowColor};" />
                    <span
                        bind:this={ArrowHeadElement}
                        class="flow-arrow"
                        style="width: 20px; height:20px;transform: rotate(90deg)">
                        <svg
                            version="1.1"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            x="0px"
                            y="0px"
                            viewBox="0 0 100 100"
                            style="enable-background:new 0 0 100 100;"
                            xml:space="preserve">
                            <g>
                                <path id="rec" d="M0,0L0,100L100,50z" style="fill: {config?.arrowColor}" />
                            </g>
                        </svg>
                    </span>
                </div>
            </div>
        </div>
    {/if}
</div>
