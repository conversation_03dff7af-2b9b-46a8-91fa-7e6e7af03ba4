<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faMagnifyingGlass,
        faCaretDown,
        faStar,
        faArrowUpRightFromSquare,
    } from '@fortawesome/free-solid-svg-icons';
    import type { FormEventHandler } from 'svelte/elements';
    import DashboardList, {
        type DashboardListItem,
        type DashboardListUpdateEvent,
    } from '$lib/core/modules/dashboard/components/DashboardList/index.svelte';
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import { clickOutside } from '$lib/utils';
    import { createEventDispatcher, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { locations } from '$lib/stores/Locations';
    import { page } from '$app/stores';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const MAX_SEARCH_INPUT_LENGTH = 35;

    type ComponentEvents = {
        update: DashboardSelectorDetail;
        selection: DashboardSelectorSelectionDetail;
    };

    export type DashboardSelectorDetail = {
        dashboards: Array<DashboardListItem>;
        selected_dashboard: DashboardListItem;
    };

    export type DashboardSelectorSelectionDetail = {
        dashboards: Array<DashboardListItem>;
        selected_dashboard: DashboardListItem;
    };

    export type DashboardSelectorUpdateEvent = CustomEvent<DashboardSelectorDetail>;
    export type DashboardSelectorSelectionEvent = CustomEvent<DashboardSelectorSelectionDetail>;

    /***
     * TODO Improvements: Allow user of dashboard selector to provide initial data - for server-side rendering.
     */
</script>

<script lang="ts">
    export let selected_dashboard: DashboardListItem = undefined; // should be controlled internally but allow external override
    export let dashboard_list_items: Array<DashboardListItem> = [];

    const dispatch = createEventDispatcher<ComponentEvents>();

    let searchValue: string;
    let isOpen = false;
    let is_dashboard_list_open = false;

    let filteredValues = filterList(dashboard_list_items);

    function filterList(list: Array<DashboardListItem>) {
        const favouriteOrSelectedOnly = list.filter(val => {
            return val?.favourite || val?.id === selected_dashboard?.id;
        });

        if (searchValue && favouriteOrSelectedOnly.length > 0) {
            const searchTerm = searchValue.toLowerCase().trim();

            // Set results based on filter of full list using search term
            return favouriteOrSelectedOnly.filter(val => {
                const match =
                    val?.description?.toLowerCase().includes(searchTerm) ||
                    val?.name?.toLowerCase().includes(searchTerm) ||
                    val?.tags?.some(t => {
                        return t?.text.toLowerCase().includes(searchTerm);
                    });

                return match;
            });
        } else {
            return favouriteOrSelectedOnly;
        }
    }

    const updateResultsHandler: FormEventHandler<HTMLInputElement> = (_event: Event) => {
        filteredValues = filterList(dashboard_list_items);
    };

    const resetResultsHandler: FormEventHandler<HTMLInputElement> = (_event: Event) => {
        searchValue = '';
        filteredValues = filterList(dashboard_list_items);
    };

    const dashboardListUpdateHandler = (e: DashboardListUpdateEvent) => {
        selected_dashboard = e.detail.selected_dashboard;

        dispatch('selection', {
            dashboards: e.detail.dashboards,
            selected_dashboard: selected_dashboard,
        });
    };

    $: if (dashboard_list_items) {
        filteredValues = filterList(dashboard_list_items);
    }

    onMount(() => {
        dispatch('update', {
            dashboards: dashboard_list_items,
            selected_dashboard: selected_dashboard,
        });
    });
</script>

<!-- Dashboard Selector Container -->
<div
    tabindex="0"
    role="button"
    class="w-[16rem] lg:w-[20rem] xl:w-[25rem]"
    use:clickOutside
    on:outclick={() => {
        isOpen = false;
    }}
    on:click={() => {
        isOpen = !isOpen;
    }}>
    <!-- Dashboard Selector -->
    <div
        class="box-border h-14 border-primary px-4 py-2 text-base
        hover:border-b-3 hover:border-primary hover:text-primary"
        class:border-b-3={isOpen}
        class:border-primary={isOpen}
        class:border-b={!isOpen}
        class:border-on-background={!isOpen}>
        <div class="text-xs text-primary">Selected View</div>
        <div class="flex justify-between">
            {#if selected_dashboard}
                {selected_dashboard.name}
            {:else}
                No Selected View
            {/if}
            <span>{@html icon(faCaretDown).html}</span>
        </div>
    </div>
    <!-- Dashboard Selector List -->
    <div
        class:hidden={!isOpen}
        class:block={isOpen}
        class="absolute max-h-[80vh] w-[415px] overflow-y-auto bg-surface-2">
        <!-- Search Input -->
        <div class="sticky top-0 rounded-sm border-b-2 border-outline bg-inherit p-2 text-base text-subheader">
            <!-- svelte-ignore a11y-no-static-element-interactions -->
            <div
                class="flex"
                on:click={e => {
                    e.stopPropagation();
                }}>
                <span class="pointer-events-none absolute left-4">{@html icon(faMagnifyingGlass).html}</span>
                <input
                    bind:value={searchValue}
                    on:input={updateResultsHandler}
                    on:blur={resetResultsHandler}
                    class="grow bg-surface-2 pl-8 focus:outline-none"
                    placeholder="Search"
                    type="text"
                    maxlength={MAX_SEARCH_INPUT_LENGTH}
                    tabindex="0" />
            </div>
        </div>
        <!-- Search Results -->
        {#each filteredValues as filterValue}
            <!-- We keep both favourite & the one selected dashboard (regardless if its favourite or not), but we don't
            render the non-favourite one. This behaviour requires UI/UX discussion -->
            {#if filterValue.favourite}
                <div
                    tabindex="0"
                    role="button"
                    class="flex flex-col py-2 text-base hover:cursor-pointer hover:bg-surface-5 active:brightness-125"
                    on:click={event => {
                        event.stopPropagation();

                        selected_dashboard = filterValue;

                        dispatch('selection', {
                            dashboards: dashboard_list_items,
                            selected_dashboard: selected_dashboard,
                        });

                        isOpen = false;
                    }}>
                    <div class="flex justify-between px-4 py-2">
                        <div class="overflow-hidden overflow-ellipsis text-nowrap pr-2">{filterValue.name}</div>
                        <div class="flex gap-3">
                            <Button
                                variant={ButtonVariant.EMPTY}
                                on:click={async event => {
                                    event.stopPropagation();

                                    // API Call to un-favourite an item
                                    const response = await fetch(
                                        `${get(locations).dash_web}/favourite/${filterValue.id}`,
                                        {
                                            method: 'PATCH',
                                            body: JSON.stringify({
                                                favourite: false,
                                            }),
                                        }
                                    );

                                    const result = await response.json();

                                    if (result && result.data) {
                                        log.debug({ result }, 'Result from updating dashboard favourite.');

                                        filterValue.favourite = false;
                                    } else {
                                        log.warn(
                                            { result },
                                            'Unexpected: No result returned from dashboard favourite API'
                                        );
                                    }
                                }}
                                iconDefinition={faStar}
                                appendClass="text-primary"
                                setModifierClasses="hover:text-mono-2" />
                            <Button
                                variant={ButtonVariant.EMPTY}
                                on:click={async event => {
                                    // Open dashboard in a new tab
                                    event.stopPropagation();
                                    const params = new URLSearchParams();
                                    params.append('id', filterValue.id);
                                    window.open(`${$page.url.origin}${$page.url.pathname}?${params}`);
                                }}
                                iconDefinition={faArrowUpRightFromSquare}
                                appendClass="text-mono-1"
                                setModifierClasses="hover:text-mono-2" />
                        </div>
                    </div>
                </div>
            {/if}
        {/each}
        {#if dashboard_list_items && dashboard_list_items.length > 0}
            {#if filteredValues && filteredValues.filter(val => {
                    return val.favourite;
                }).length <= 0}
                <!-- No favourited dashboards -->
                <div
                    class="flex w-full items-center justify-center border-mono-3 bg-surface-2 px-3 py-4
                    text-xs italic">
                    No favourites to show. Favourite a dashboard to see it on this list.
                </div>
            {/if}
            <!-- Browse All Button -->
            <Button
                appendClass="w-full py-4 {filteredValues && filteredValues.length <= 0
                    ? 'border-t'
                    : ''} border-mono-3 sticky bottom-0"
                setColorClasses="bg-surface-2 active:brightness-150"
                variant={ButtonVariant.FLAT}
                on:click={() => {
                    is_dashboard_list_open = true;
                }}>BROWSE ALL</Button>
        {:else}
            <!-- No Dashboards -->
            <div
                class="flex w-full items-center justify-center border-mono-3 bg-surface-2 px-3 py-4
                text-xs italic">
                No dashboards found. Create a new dashboard and favourite it to see it on this list.
            </div>
        {/if}
    </div>
</div>
<DashboardList
    bind:dashboards={dashboard_list_items}
    bind:isOpen={is_dashboard_list_open}
    on:update={dashboardListUpdateHandler} />
