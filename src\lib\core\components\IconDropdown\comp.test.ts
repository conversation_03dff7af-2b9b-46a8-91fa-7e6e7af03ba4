import { expect, test } from '@playwright/test';
import dayjs from 'dayjs';

test('test clock against local machine time', async ({ page }) => {
    // Go to http://localhost:4173/tests/components/Clock
    await page.goto('http://localhost:4173/tests/components/Clock');

    const now = dayjs();

    const date = now.format('DD/MM/YYYY');
    expect(page.locator('#test-container > div > div >> nth=0')).toHaveText(date);

    const day = now.format('ddd').toLocaleUpperCase();
    expect(page.locator('#test-container > div > div >> nth=1')).toHaveText(day);

    const time = now.format('HH:mm:ss');
    expect(page.locator('#test-container > div > div >> nth=2')).toHaveText(time);
});
