<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import type { FormEventHandler } from 'svelte/elements';
    import { faMagnifyingGlass, faArrowUpRightFromSquare, faStar } from '@fortawesome/free-solid-svg-icons';
    import { faStar as faStarReg } from '@fortawesome/free-regular-svg-icons';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import Modal from '$lib/core/components/Modal/index.svelte';
    import Chip from '$lib/core/components/Chip/index.svelte';
    import ChipSelector, {
        type ChipUpdateEvent,
        type ChipForDisplay,
    } from '$lib/core/components/ChipSelector/index.svelte';
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import { createEventDispatcher } from 'svelte';
    import { get } from 'svelte/store';
    import { locations } from '$lib/stores/Locations';
    import { page } from '$app/stores';
    import DashboardPreview from '$lib/core/modules/dashboard/components/DashboardPreview/index.svelte';
    import { DashboardStore } from '$lib/core/modules/dashboard/module';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const MAX_SEARCH_INPUT_LENGTH = 35;

    export type DashboardListItem = {
        id: string;
        name: string;
        description?: string;
        widgets?: Array<Widget>;
        favourite?: boolean;
        filterHit?: boolean;
        isPreview?: boolean;
        tags?: Array<Tag>;
        occ_lock?: number;
    };

    export type DashboardTags = Array<ChipForDisplay>;

    type ComponentEvents = {
        update: DashboardListDetail;
    };

    export type DashboardListDetail = {
        dashboards: Array<DashboardListItem>;
        selected_dashboard: DashboardListItem;
    };

    export type DashboardListUpdateEvent = CustomEvent<DashboardListDetail>;
</script>

<script lang="ts">
    export let isOpen = false;

    const dispatch = createEventDispatcher<ComponentEvents>();

    let dashTags: DashboardTags = [];
    let checkedLabelCount = 0;

    // ### Dashboard Search
    let dashboardSearchValue: string;
    let dashboardListIsOpen = false;
    let filteredDashboardsCount = 0;
    let dashboardConfigForPreview: Array<WidgetGridItem>;

    export let dashboards: Array<DashboardListItem>;

    function filterDashboards(searchValue: string, data: typeof dashboards, tagData: typeof dashTags): void {
        filteredDashboardsCount = 0;

        if (searchValue) {
            const searchTerm = searchValue.toLowerCase().trim();

            // Set results based on filter of full list using search term
            data.forEach(d => {
                let hit =
                    (d?.description?.toLowerCase().includes(searchTerm) ||
                        d?.name?.toLowerCase().includes(searchTerm)) &&
                    // Make sure all selected labels are contained in the dashboard
                    tagData.every(t => {
                        return (
                            !t?.checked ||
                            (t?.checked &&
                                d?.tags?.some(t2 => {
                                    return t2?.text?.toLowerCase() === t?.text?.toLowerCase();
                                }))
                        );
                    });

                if (hit) {
                    filteredDashboardsCount = filteredDashboardsCount + 1;
                }

                d.filterHit = hit;
            });
        } else if (checkedLabelCount > 0) {
            // Only filter using labels
            data.forEach(d => {
                // Make sure all selected labels are contained in the dashboard
                let hit = tagData.every(lblData => {
                    return (
                        !lblData?.checked ||
                        (lblData?.checked &&
                            d?.tags?.some(l => {
                                return l?.text?.toLowerCase() === lblData?.text?.toLowerCase();
                            }))
                    );
                });

                if (hit) {
                    filteredDashboardsCount = filteredDashboardsCount + 1;
                }

                d.filterHit = hit;
            });
        } else {
            data.forEach(d => {
                d.filterHit = true;
                filteredDashboardsCount = filteredDashboardsCount + 1;
            });
        }

        dashboards = dashboards;
    }

    const updateDashboardResultsHandler: FormEventHandler<HTMLInputElement> = (_event: Event) => {
        filterDashboards(dashboardSearchValue, dashboards, dashTags);
    };

    const chipUpdateHandler = (e: ChipUpdateEvent) => {
        checkedLabelCount = e?.detail?.checkedCount;
        filterDashboards(dashboardSearchValue, dashboards, dashTags);
    };

    // Close the label selector if the modal is closed
    $: if (!isOpen) {
        // isLabelSelectorOpen = false;
    }

    $: if (dashboards) {
        dashboards = dashboards;
        filterDashboards(dashboardSearchValue, dashboards, dashTags);
    }

    DashboardStore.subscribe(ds => {
        dashTags = [];

        ds.tag_map.forEach(t => {
            dashTags.push({ id: t.id, text: t.text, checked: false, filterHit: false });
        });

        dashTags = dashTags;
    });
</script>

<Modal bind:isOpen title="Dashboard List" widthClass="w-[95%]" heightClass="h-[95%]">
    <div class="flex w-full grow divide-x-2 divide-mono-3 overflow-hidden">
        <div class="h-full">
            <div class="mx- flex w-[600px] grow flex-col gap-4 p-4">
                <!-- Filter Controls -->
                <div class="w-full grow">
                    <!-- Search Input -->
                    <div class="rounded-sm border-b-2 border-outline p-2 text-base text-subheader">
                        <!-- svelte-ignore a11y-no-static-element-interactions -->
                        <div
                            class="relative flex"
                            on:click={e => {
                                e.stopPropagation();
                            }}>
                            <span class="pointer-events-none absolute left-2"
                                >{@html icon(faMagnifyingGlass).html}</span>
                            <input
                                bind:value={dashboardSearchValue}
                                on:input={updateDashboardResultsHandler}
                                class="grow bg-surface-2 pl-8 focus:outline-none"
                                placeholder="Search"
                                type="text"
                                maxlength={MAX_SEARCH_INPUT_LENGTH}
                                tabindex="0" />
                        </div>
                    </div>
                    <div class="relative z-10">
                        <ChipSelector
                            label="label"
                            bind:chips={dashTags}
                            createChipEnabled={false}
                            on:update={chipUpdateHandler}
                            openedListWidth="400px"
                            openedListAlign="right" />
                    </div>
                </div>
            </div>
            <div class="mx- flex h-full w-[600px] flex-col gap-4 overflow-y-auto p-4">
                <!-- Filtered Results -->
                {#if dashboards && filteredDashboardsCount > 0}
                    {#each dashboards as dashboard}
                        {#if dashboard.filterHit}
                            <div
                                tabindex="0"
                                role="button"
                                class="relative z-0 flex flex-col py-2 text-base hover:cursor-pointer
                                hover:bg-surface-5 active:brightness-125"
                                class:bg-surface-3={dashboard.isPreview}
                                on:click={event => {
                                    event.stopPropagation();

                                    let widgets = [];

                                    for (const [_, w] of $DashboardStore.widget_map) {
                                        if (w.dashboard_id === dashboard.id) {
                                            widgets.push(w);
                                        }
                                    }

                                    // Update Preview
                                    dashboardConfigForPreview = widgets;
                                    dashboards.forEach(d => {
                                        d.isPreview = false;
                                    });

                                    log.debug({ dashboardConfigForPreview, dashboards }, 'Preview updated');

                                    dashboard.isPreview = true;
                                }}>
                                <div class="flex justify-between px-4 pb-2 pt-3">
                                    <div class="flex flex-col items-start justify-center gap-1 self-stretch">
                                        <div
                                            class="overflow-hidden overflow-ellipsis text-nowrap pr-2 font-bold leading-[115%]">
                                            {#if dashboard.name}
                                                {dashboard.name}
                                            {:else}
                                                <span class="italic">No Name</span>
                                            {/if}
                                        </div>
                                        <div
                                            class="overflow-hidden overflow-ellipsis text-nowrap pr-2 text-sm
                                font-normal leading-[120%]">
                                            {#if dashboard.description}
                                                {dashboard.description}
                                            {:else}
                                                <span class="italic">No Description</span>
                                            {/if}
                                        </div>
                                        <div class="flex flex-wrap items-center justify-start gap-2 py-1 pl-0 pr-1">
                                            {#if dashboard.tags && dashboard.tags.length > 0}
                                                {#each dashboard.tags as tag}
                                                    {#if tag}
                                                        <Chip>{tag?.text?.toUpperCase()}</Chip>
                                                    {/if}
                                                {/each}
                                            {:else}
                                                <span class="text-sm font-normal italic leading-[120%]">Unlabeled</span>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        {#if dashboard.favourite}
                                            <Button
                                                variant={ButtonVariant.EMPTY}
                                                iconDefinition={faStar}
                                                appendClass="text-primary"
                                                setModifierClasses="hover:text-mono-2"
                                                on:click={async event => {
                                                    event.stopPropagation();

                                                    // API Call to un-favourite an item
                                                    const response = await fetch(
                                                        `${get(locations).dash_web}/favourite/${dashboard.id}`,
                                                        {
                                                            method: 'PATCH',
                                                            body: JSON.stringify({
                                                                favourite: false,
                                                            }),
                                                        }
                                                    );

                                                    const result = await response.json();

                                                    if (result && result.data) {
                                                        log.debug(
                                                            { result },
                                                            'Result from updating dashboard favourite.'
                                                        );

                                                        dashboard.favourite = false;
                                                        filterDashboards(dashboardSearchValue, dashboards, dashTags);
                                                    } else {
                                                        log.warn(
                                                            { result },
                                                            'Unexpected: No result returned from dashboard favourite API'
                                                        );
                                                    }
                                                }} />
                                        {:else}
                                            <Button
                                                variant={ButtonVariant.EMPTY}
                                                iconDefinition={faStarReg}
                                                appendClass="text-mono-1"
                                                setModifierClasses="hover:text-primary active:brightness-125"
                                                on:click={async event => {
                                                    event.stopPropagation();

                                                    // API Call to favourite an item
                                                    const response = await fetch(
                                                        `${get(locations).dash_web}/favourite/${dashboard.id}`,
                                                        {
                                                            method: 'PATCH',
                                                            body: JSON.stringify({
                                                                favourite: true,
                                                            }),
                                                        }
                                                    );

                                                    const result = await response.json();

                                                    if (result && result.data) {
                                                        log.debug(
                                                            { result },
                                                            'Result from updating dashboard favourite.'
                                                        );

                                                        dashboard.favourite = true;
                                                        filterDashboards(dashboardSearchValue, dashboards, dashTags);
                                                    } else {
                                                        log.warn(
                                                            { result },
                                                            'Unexpected: No result returned from dashboard favourite API'
                                                        );
                                                    }
                                                }} />
                                        {/if}
                                        <Button
                                            variant={ButtonVariant.EMPTY}
                                            iconDefinition={faArrowUpRightFromSquare}
                                            appendClass="text-mono-1"
                                            setModifierClasses="hover:text-mono-2"
                                            on:click={event => {
                                                // Open dashboard in a new tab
                                                event.stopPropagation();
                                                const params = new URLSearchParams();
                                                params.append('id', dashboard.id);
                                                window.open(`${$page.url.origin}${$page.url.pathname}?${params}`);
                                            }} />
                                        <Button
                                            variant={ButtonVariant.DEFAULT}
                                            setColorClasses="bg-primary"
                                            appendClass="h-fit"
                                            on:click={event => {
                                                dispatch('update', {
                                                    dashboards: dashboards,
                                                    selected_dashboard: dashboard,
                                                });

                                                isOpen = false;
                                            }}>Select</Button>
                                    </div>
                                </div>
                            </div>
                        {/if}
                    {/each}
                {:else}
                    <div class="flex h-full w-full items-center justify-center text-sm italic text-header">
                        No search results
                    </div>
                {/if}
            </div>
        </div>
        <div class="flex h-full w-full flex-col p-4">
            <div>Preview</div>
            <DashboardPreview showBackground={false} showGrid={false} dashboard_config={dashboardConfigForPreview} />
        </div>
    </div>
</Modal>
