.grid-stack .grid-stack-placeholder > .placeholder-content {
    background-color: var(--on-primary) !important;
    box-shadow: 0 0 4px var(--on-primary) !important;
    border-radius: 1.5rem;
    border-color: var(--outline);
    border-width: 1px;
}

@media (prefers-color-scheme: dark) {
    .grid-stack .grid-stack-placeholder > .placeholder-content {
        background-color: var(--on-primary) !important;
        box-shadow: 0 0 4px var(--on-primary) !important;
        border-radius: 1.5rem;
        border-color: var(--outline);
        border-width: 1px;
    }
}

.grid-stack.gs-36 > .grid-stack-item {
    width: 2.778%;
    min-width: 2.778%;
}
.grid-stack.gs-36 > .grid-stack-item {
    left: 0rem;
}

@for $i from 1 through 36 {
    .grid-stack.gs-36 > .grid-stack-item[gs-w='#{$i}'] {
        width: $i * 2.778%;
        min-width: $i * 2.778%;
    }
    .grid-stack.gs-36 > .grid-stack-item[gs-x='#{$i}'] {
        left: calc(var(--style-cell-height) * $i * 1px);
    }
}

.ui-resizable-se {
    right: 0.5rem !important;
    bottom: 0.5rem !important;
}

.grid-display {
    background-image: url('/images/dashboard/grid-square.png');
    background-size: calc(var(--style-cell-height) * 1px); // Adjusted for 34 columns
    background-position: 0px 0px, 0px 0px;
}

.grid-display .grid-stack-sub-grid > .grid-stack-item-content {
    background-image: url('/images/dashboard/grid-square.png');
    background-size: calc(var(--cell-height) * 1px) calc(var(--cell-height) * 1px);
    background-position: 0px 0px, 0px 0px;
    background-attachment: local;
}
