<script lang="ts">
    import Button from '$lib/core/components/Button/index.svelte';
    import { icon as fontAwesomeIcon, type IconDefinition } from '@fortawesome/fontawesome-svg-core';
    import type { EventHandler } from 'svelte/elements';

    export let title: string = undefined;
    export let icon: IconDefinition;
    export let onCancel: EventHandler<MouseEvent>;
    export let onSubmit: EventHandler<MouseEvent>;
    export let showHeader = true;
    export let isInvalid = true;
</script>

<form>
    <header
        class:hidden={!showHeader || !title}
        class="flex items-center gap-2 bg-primary px-2 py-1 text-base font-semibold text-primary">
        <span class="text-xl font-normal">{@html fontAwesomeIcon(icon).html}</span>
        {title}
    </header>
    <div class="p-6">
        <slot />
        <div class="mt-4 flex flex-row justify-end gap-4">
            <Button on:click={onCancel}>Cancel</Button>
            <Button setDisabled={isInvalid} on:click={onSubmit}>Submit</Button>
        </div>
    </div>
</form>
