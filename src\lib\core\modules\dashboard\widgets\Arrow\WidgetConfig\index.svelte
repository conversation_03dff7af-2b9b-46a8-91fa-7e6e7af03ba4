<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface IndicatorConfig {
        dataSource?: string;
        title?: string;
        titleAlign?: string;
        isShowText?: boolean;
        throughputTime: number;
        direction: string;
        uom: string;
        thicknessType: string;
        arrowColor: string;
        textSize: number;
        textColor: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'c3hoc_VW_SCDF_Amb_aggregate';
    const DEFAULT_TITLE = '';
    const MINUTES = 1;
    const DIRECTION = 'right';
    const UOM = 'hour';
    const THICKNESS_TYPE = '1';
    const DEFAULT_ARROW_COLOR = '#ffffff';
    const DEFAULT_TEXT_SIZE = 24;
    const DEFAULT_TEXT_COLOR = '#ffffff';

    export const configDefaults: IndicatorConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        titleAlign: 'center',
        isShowText: false,
        throughputTime: MINUTES,
        direction: DIRECTION,
        uom: UOM,
        thicknessType: THICKNESS_TYPE,
        arrowColor: DEFAULT_ARROW_COLOR,
        textSize: DEFAULT_TEXT_SIZE,
        textColor: DEFAULT_TEXT_COLOR,
    };
</script>

<script lang="ts">
    import WidgetConfig from '$lib/core/modules/dashboard/components/WidgetConfig/index.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetConfigSelect from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigSelect/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';

    const configElementWidth = 'w-1/3';
    export let config: IndicatorConfig;

    const directionOptions = [
        { name: 'Up', value: 'up' },
        { name: 'Down', value: 'down' },
        { name: 'Left', value: 'left' },
        { name: 'Right', value: 'right' },
        { name: 'Up Right', value: 'upright' },
        { name: 'Up Left', value: 'upleft' },
        { name: 'Down Right', value: 'downright' },
        { name: 'Down Left', value: 'downleft' },
    ];

    const ALIGNMENT: string[] = ['Left', 'Center', 'Right'];
    const LIST_UOMS: string[] = ['Hour', 'Minute', 'Day'];
    const THICKNESS_TYPE: string[] = ['1', '2', '3', '4', '5'];

    let datasources: Array<DropdownItem> = [];

    const propertyFields = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: datasources,
                setClass: 'border-b border-b-outline bg-transparent mb-4',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
                customClass: 'w-full mb-4',
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'titleAlign',
                    componentDisplay: WidgetGroupButton,
                    extraProps: {
                        title: 'Title Alignment',
                        labels: ALIGNMENT,
                    },
                },
            ],
        },
        {
            id: 'isShowText',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Throughput Text',
                checked: config.isShowText,
            },
        },
        {
            checkVisible: 'isShowText',
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'throughputTime',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Throughput Time',
                        type: 'number',
                        required: true,
                        customClass: 'mb-4 w-1/3',
                        pattern: "^[0-9]\\d*$",
                        min:1,
                        excludeZeroFirst: true,
                    },
                },
                {
                    id: 'uom',
                    componentDisplay: WidgetGroupButton,
                    extraProps: {
                        title: 'UOM',
                        labels: LIST_UOMS,
                    },
                },
            ],
        },
        {
            checkVisible: 'isShowText',
            extraProps: {
                customClass: 'flex w-full items-end gap-2',
            },
            children: [
                {
                    id: 'textSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Throughput Text Size',
                        type: 'number',
                        required: true,
                        customClass: 'mb-4 w-1/3',
                    },
                },
                {
                    id: 'textColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full items-end gap-2',
            },
            children: [
                {
                    id: 'thicknessType',
                    componentDisplay: WidgetGroupButton,
                    extraProps: {
                        title: 'Arrow Thickness',
                        labels: THICKNESS_TYPE,
                    },
                },
                {
                    id: 'arrowColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
        {
            id: 'direction',
            componentDisplay: WidgetConfigSelect,
            extraProps: {
                title: 'Direction',
                items: directionOptions,
                customClass: 'w-1/3',
            },
        },
    ];
</script>

<WidgetConfig bind:config {propertyFields} {configElementWidth} />
