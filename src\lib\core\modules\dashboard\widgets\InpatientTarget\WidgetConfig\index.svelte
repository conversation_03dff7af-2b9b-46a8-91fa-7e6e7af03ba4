<script lang="ts" context="module">
    import ThresholdConfigComponent, { type ThresholdItem, type ThresholdConfig } from './ThresholdConfig/index.svelte';

    export interface IndicatorTitleStyle {
        align?: string;
        fontSize?: number;
        comparator?: string;
    }

    export interface IndicatorValueStyle {
        fontSize?: number;
        bgColor?: string;
        textColor?: string;
    }

    export interface IndicatorStyleConfig {
        indicatorTitleStyle: IndicatorTitleStyle;
        indicatorValueStyle: IndicatorValueStyle;
    }

    export interface WidgetInpatientTargetConfig {
        title?: string;
        titleAlign?: string;
        titleFontSize?: number;
        indicatorValue?: number;
        indicatorStyleConfig?: IndicatorStyleConfig;
        thresholdConfig: ThresholdConfig;
    }

    export const indicatorTitleStyle: IndicatorTitleStyle = {
        align: 'center',
        fontSize: 16,
        comparator: '<',
    };

    export const indicatorValueStyle: IndicatorValueStyle = {
        fontSize: 16,
        bgColor: '#000000',
        textColor: '#FFFFFF',
    };

    export const indicatorStyleConfig: IndicatorStyleConfig = {
        indicatorTitleStyle: indicatorTitleStyle,
        indicatorValueStyle: indicatorValueStyle,
    };

    export const configIndicatorThresholdItem: ThresholdItem = {
        titleThresholdItem: 'InpatientTarget',
        inpatientTarget: 20,
        targetUntilTime: '23:59',
        displayLabel: '23:59',
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
    };

    const INDICATOR_THRESHOLD_CONFIG_ITEMS: Array<ThresholdItem> = [configIndicatorThresholdItem];

    export const configThresholdConfigs: ThresholdConfig = {
        isEnableThreshold: true,
        thresholdConfigItems: INDICATOR_THRESHOLD_CONFIG_ITEMS,
    };

    export const configDefaults: WidgetInpatientTargetConfig = {
        title: 'Wards',
        titleAlign: 'center',
        titleFontSize: 16,
        indicatorValue: 10,
        indicatorStyleConfig: JSON.parse(JSON.stringify(indicatorStyleConfig)),
        thresholdConfig: JSON.parse(JSON.stringify(configThresholdConfigs)),
    };
</script>

<script lang="ts">
    export let config: WidgetInpatientTargetConfig;
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { AccordionItem, Accordion } from 'flowbite-svelte';
    import WidgetConfigItem from '$lib/core/modules/dashboard/components/WidgetConfig/WidgetItem.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';
    import { onMount, onDestroy } from 'svelte';

    let tripleIndicatorConfig: HTMLElement;

    // function updateSizeConfigWidget() {
    //     if (tripleIndicatorConfig) {
    //         let configurationPanel = tripleIndicatorConfig.parentElement.parentElement.parentElement;
    //         configurationPanel.classList.remove('w-1/5');
    //         configurationPanel.classList.add('overflow-x-auto', 'h-[calc(100%-4rem)]', 'overflow-y-auto', 'w-[42%]');
    //     }
    // }
    // function resetSizeConfigWidget() {
    //     if (tripleIndicatorConfig) {
    //         let configurationPanel = tripleIndicatorConfig.parentElement.parentElement.parentElement;
    //         configurationPanel.classList.remove('overflow-x-auto', 'h-[calc(100%-4rem)]', 'overflow-y-auto', 'w-[42%]');
    //         configurationPanel.classList.add('w-1/5');
    //     }
    // }

    // onMount(async () => {
    //     updateSizeConfigWidget();
    // });
    // onDestroy(async () => {
    //     resetSizeConfigWidget();
    // });

    let datasources: Array<DropdownItem> = [];
    const ALIGNMENTS = ['Left', 'Center', 'Right'];
    const COMPARATORS = ['<=', '<', '>', '>='];

    const propertyFields = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: datasources,
                setClass: 'border-b border-b-outline bg-transparent mb-4',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
    ];
    const propertyTitleFields = [
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'titleFontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Font Size',
                        min: 0,
                        pattern: '^[0-9]\\d*$',
                        customClass: 'w-1/3 mb-4',
                    },
                },
                {
                    id: 'titleAlign',
                    componentDisplay: WidgetGroupButton,
                    extraProps: {
                        title: 'Title Alignment',
                        labels: ALIGNMENTS,
                        customClass: 'ml-2 w-2/3 mb-4',
                    },
                },
            ],
        },
    ];
    const propertyIndicatorTitleFields = [
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'fontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Font Size',
                        min: 0,
                        pattern: '^[0-9]\\d*$',
                        customClass: 'mb-4 w-1/3',
                    },
                },
                {
                    id: 'align',
                    componentDisplay: WidgetGroupButton,
                    extraProps: {
                        title: 'Title Alignment',
                        labels: ALIGNMENTS,
                        customClass: 'ml-2 mb-4',
                    },
                },
            ],
        },
        {
            id: 'comparator',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Comparator',
                labels: COMPARATORS,
                customClass: 'mb-4',
            },
        },
    ];
    const propertyIndicatorValueFields = [
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'fontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Font Size',
                        min: 0,
                        pattern: '^[0-9]\\d*$',
                        customClass: 'mb-4 w-1/3',
                    },
                },
                {
                    id: 'bgColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Bg Color',
                        type: 'color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
                {
                    id: 'textColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Text Color',
                        type: 'color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
    ];
</script>

<div bind:this={tripleIndicatorConfig} class="text-on-surface flex flex-col">
    <Accordion>
        <AccordionItem>
            <span slot="header">Properties</span>
            <WidgetConfigItem bind:config widgetFields={propertyFields} />
            <Accordion>
                <AccordionItem>
                    <span slot="header">Title</span>
                    <WidgetConfigItem bind:config widgetFields={propertyTitleFields} />
                </AccordionItem>
                <AccordionItem>
                    <span slot="header">Indicator</span>
                    <Accordion flush>
                        <AccordionItem>
                            <span slot="header">Indicator Title</span>
                            <WidgetConfigItem
                                bind:config={config.indicatorStyleConfig.indicatorTitleStyle}
                                widgetFields={propertyIndicatorTitleFields} />
                        </AccordionItem>
                        <AccordionItem>
                            <span slot="header">Indicator Value</span>
                            <WidgetConfigItem
                                bind:config={config.indicatorStyleConfig.indicatorValueStyle}
                                widgetFields={propertyIndicatorValueFields} />
                        </AccordionItem>
                    </Accordion>
                </AccordionItem>
            </Accordion>
        </AccordionItem>
        <AccordionItem open>
            <span slot="header">Thresholds</span>
            <ThresholdConfigComponent bind:configThresholdConfigs={config.thresholdConfig} />
        </AccordionItem>
    </Accordion>
</div>
