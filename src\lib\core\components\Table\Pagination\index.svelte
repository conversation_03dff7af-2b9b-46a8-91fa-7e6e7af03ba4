<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faAngleLeft, faAngleRight, faAnglesLeft, faAnglesRight } from '@fortawesome/free-solid-svg-icons';

    import Button from '$lib/core/components/Button/index.svelte';

    export let noOfContent: number;

    let contentShowing: number;

    let offset = 0; //Current first item
    export let page = 0; //Page number
    export let pageSize = 0; //No of rows

    let minPage = 0;

    let menuItems = [
        {
            label: 5,
        },
        {
            label: 10,
        },
    ];

    $: if (noOfContent < pageSize) contentShowing = noOfContent;
    $: offset = page * pageSize;
    $: contentShowing = page * pageSize + pageSize > noOfContent ? noOfContent : page * pageSize + pageSize;
    $: lastPage = Math.ceil(noOfContent / pageSize);
</script>

<span class="{$$props.class} text-on-surface flex h-fit items-center justify-center text-xs">
    <p class="px-2">Rows:</p>
    <div class="flex w-fit items-center gap-2 rounded p-0.5 px-1.5">
        <!-- <div>5</div> -->
        <p>{offset + 1}-{contentShowing} of {noOfContent}</p>
    </div>

    <div class="text-on-surface grid grid-cols-4 gap-6">
        <Button
            disabled={page === 0}
            setClass="bg-inherit {page === 0 ? 'text-on-surface' : ''} "
            on:click={() => {
                page = 0;
            }}>{@html icon(faAnglesLeft).html}</Button>
        <Button
            disabled={page === 0}
            setClass="bg-inherit {page === 0 ? 'text-on-surface' : ''}"
            on:click={() => {
                if (page > 0) page--;
            }}>{@html icon(faAngleLeft).html}</Button>
        <Button
            setClass="bg-inherit {page >= lastPage - 1 ? 'text-on-surface' : ''}"
            on:click={() => {
                if (page < lastPage - 1) page++;
            }}>{@html icon(faAngleRight).html}</Button>
        <Button
            setClass="bg-inherit {page >= lastPage - 1 ? 'text-on-surface' : ''}"
            on:click={() => {
                page = lastPage - 1;
            }}>{@html icon(faAnglesRight).html}</Button>
    </div>
</span>
