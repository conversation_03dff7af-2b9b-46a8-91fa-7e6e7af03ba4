import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
import { checkPermissions } from '$lib/shared/util/acl';
import type { PageServerLoad } from './$types';
export const load: PageServerLoad = async ({ locals, params, fetch }) => {
    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const access_token = locals.user.access_token;
    let permissionNeedToCheck = [
        {
            scopeId: SCOPE.READ,
            resourceId: RESOURCE.WORKFLOW,
        },
        {
            scopeId: SCOPE.DELETE,
            resourceId: RESOURCE.WORKFLOW,
        },
        {
            scopeId: SCOPE.STUDIO_WORKFLOW,
            resourceId: RESOURCE.WORKFLOW,
        },
        {
            scopeId: SCOPE.ACTIVATION,
            resourceId: RESOURCE.WORKFLOW,
        },
    ];
    const abilities = await checkPermissions({
        token: access_token,
        tenantId,
        userId,
        permissions: permissionNeedTo<PERSON>heck,
    });
    return {
        abilities: abilities,
    };
};
