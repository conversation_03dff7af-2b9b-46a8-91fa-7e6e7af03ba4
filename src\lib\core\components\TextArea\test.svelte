<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faArrowLeft } from '@fortawesome/free-solid-svg-icons';

    import TextArea from './index.svelte';

    let value;
    let isClicked = false;
</script>

<div class="flex h-[120px] w-full gap-4 p-2 text-sm">
    <TextArea
        rows={10}
        on:focus={() => {
            isClicked = true;
        }}
        bind:value
        setClass="w-64 border"
        appendClass={isClicked ? '' : 'animate-pulse'}
        label="Insert text here..." />
    <p class="w-full overflow-auto border p-1">
        {#if isClicked === false}{@html icon(faArrowLeft).html}{/if}
        {value ? value : 'Please type in the textfield'}
    </p>
</div>
