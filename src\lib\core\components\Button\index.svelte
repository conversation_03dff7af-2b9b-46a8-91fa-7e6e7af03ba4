<!--
    @component
    But<PERSON> can take on:click callbacks. The Button component also accepts a slot for a child element.

    Props:
    - `variant`: The variant of the button, controls the default styles.
    - `appendClass`: Pass in a class string as `appendClass` to append to the built-in component styles.

    - `setDisabled`: Pass in false to disable the button.
    - `setClass`: Pass in a class string to override the default built-in component styles.
    - `setStyle`: Pass in a styles to override styles and classes (higher specificity than TailWind)

    - Usage:
      ```tsx
      <script lang="ts">
        import Button from '$lib/components/input/basic/Button/index.svelte';
      </script>
      <Button setDisabled={ true }>Push My Button</Button>
    ```
-->
<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export enum ButtonVariant {
        DEFAULT = 'DEFAULT',
        EMPTY = 'EMPTY',
        FLAT = 'FLAT',
        OUTLINE = 'OUTLINE',
        FLOATING = 'FLOATING',
        ROUNDED = 'ROUNDED',
        WIDGET_ITEM = 'WIDGET_ITEM',
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    import { icon, type IconDefinition } from '@fortawesome/fontawesome-svg-core';

    // Style overrides - more specific than Tailwind CSS
    export let setStyle: string = undefined;
    export let setClass: string = undefined;
    export let setModifierClasses: string = undefined;
    export let setColorClasses: string = undefined;
    export let setDisabled = false;
    export let iconDefinition: IconDefinition = undefined;
    export let init: (node: Node) => void = () => {};
    export let variant: ButtonVariant = undefined;

    /* @tw */
    export let appendClass = '';

    /* @tw */
    let variantClasses = '';
    /* @tw */
    let baseColors = 'text-on-surface-1 bg-secondary-btn';
    /* @tw */
    let baseModifiers = 'hover:brightness-125 active:brightness-150 disabled:brightness-50 disabled:cursor-not-allowed';

    if (setColorClasses) {
        baseColors = setColorClasses;
    }

    if (setModifierClasses) {
        baseModifiers = setModifierClasses;
    }

    /* @tw */
    let baseClasses =
        'relative flex cursor-pointer items-center justify-center overflow-hidden text-center text-sm px-4 py-2 \
        font-semibold leading-[115%]';

    switch (variant) {
        case ButtonVariant.EMPTY:
            /* @tw */
            variantClasses = baseModifiers + ' flex cursor-pointer items-center justify-center';
            break;
        case ButtonVariant.OUTLINE:
            /* @tw */
            variantClasses =
                `${baseClasses} ${baseModifiers}` +
                ' border box-border rounded-md text-on-background bg-background py-[calc(0.5rem-1px)]';
            break;
        case ButtonVariant.FLAT:
            /* @tw */
            variantClasses = `${baseClasses} ${baseColors} ${baseModifiers}`;
            break;
        case ButtonVariant.ROUNDED:
            /* @tw */
            variantClasses = `${baseClasses} ${baseColors} ${baseModifiers}` + ' rounded-full';
            break;
        case ButtonVariant.WIDGET_ITEM:
            /* @tw */
            variantClasses =
                baseColors +
                ' w-full group bg-surface-2 text-on-background gap-6 flex h-10 px-5 \
                items-center rounded-md hover:brightness-125 disabled:brightness-100';
            break;
        case ButtonVariant.DEFAULT:
        default:
            /* @tw */
            variantClasses = `${baseClasses} ${baseColors} ${baseModifiers}` + ' rounded-md';
            break;
    }
</script>

<button
    tabindex="0"
    data-testid={$$props['data-testid']}
    data-subject={$$props['data-subject']}
    use:init
    on:click
    on:pointerover
    on:pointerout
    class={setClass ? setClass : variantClasses + ' ' + appendClass}
    style={setStyle ? setStyle : ''}
    disabled={setDisabled}
    title={$$props['title']}>
    {#if iconDefinition}
        <div class="flex flex-row items-center justify-between gap-3">
            <div class="text-xl leading-[115%]">
                {@html icon(iconDefinition).html}
            </div>
            <slot />
        </div>
    {:else}
        <slot />
    {/if}
</button>
