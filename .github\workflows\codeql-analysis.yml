# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
#
# ******** NOTE ********
# We have attempted to detect the languages in your repository. Please check
# the `language` matrix defined below to confirm you have the correct set of
# supported CodeQL languages.
#
name: 'hcc3-web - Code security vulnerabilities scan'

env:
    org_name: mssfoobar
    app_name: hcc3-web

on:
    workflow_dispatch:
    workflow_call:
    # push:
    #   branches: [ "main" ]
    #   paths:
    #   - '**'
    #   - '!.github/workflows/**'
    # pull_request:
    #   # The branches below must be a subset of the branches above
    #   branches: [ "main" ]
    #   paths:
    #   - '**'
    #   - '!.github/workflows/**'
    # schedule:
    #   - cron: '39 1 * * 0'

jobs:
    analyze:
        name: Analyze
        runs-on: ubuntu-latest
        permissions:
            actions: read
            contents: read
            security-events: write

        strategy:
            fail-fast: false
            matrix:
                language: ['javascript']
                # CodeQL supports [ 'cpp', 'csharp', 'go', 'java', 'javascript', 'python', 'ruby' ]
                # Learn more about CodeQL language support at https://aka.ms/codeql-docs/language-support

        steps:
            - name: Checkout repository
              uses: actions/checkout@v3

            # Initializes the CodeQL tools for scanning.
            - name: Set visibility for CodeQL
              run: gh repo edit ${{ env.org_name }}/${{ env.app_name }} --visibility public
              env:
                  GITHUB_TOKEN: ${{ secrets.PAT }}
            - name: Initialize CodeQL
              uses: github/codeql-action/init@v2
              with:
                  languages: ${{ matrix.language }}
                  # If you wish to specify custom queries, you can do so here or in a config file.
                  # By default, queries listed here will override any specified in a config file.
                  # Prefix the list here with "+" to use these queries and those in the config file.

                  # Details on CodeQL's query packs refer to : https://docs.github.com/en/code-security/code-scanning/automatically-scanning-your-code-for-vulnerabilities-and-errors/configuring-code-scanning#using-queries-in-ql-packs
                  # queries: security-extended,security-and-quality

            #     - name: Cache
            #       uses: actions/cache@v3.0.8
            #       with:
            #         # A list of files, directories, and wildcard patterns to cache and restore
            #         path:
            #         # An explicit key for restoring and saving the cache
            #         key:
            #         # An ordered list of keys to use for restoring stale cache if no cache hit occurred for key. Note `cache-hit` returns false in this case.
            #         restore-keys: # optional
            #         # The chunk size used to split up large files during upload, in bytes
            #         upload-chunk-size: # optional

            # Autobuild attempts to build any compiled languages  (C/C++, C#, or Java).
            # If this step fails, then you should remove it and run the build manually (see below)
            - name: Autobuild
              uses: github/codeql-action/autobuild@v2

            # ℹ️ Command-line programs to run using the OS shell.
            # 📚 See https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#jobsjob_idstepsrun

            #   If the Autobuild fails above, remove it and uncomment the following three lines.
            #   modify them (or add more) to build your code if your project, please refer to the EXAMPLE below for guidance.

            # - run: |
            #   echo "Run, Build Application using script"
            #   ./location_of_script_within_repo/buildscript.sh

            - name: Perform CodeQL Analysis
              uses: github/codeql-action/analyze@v2
              with:
                  category: '/language:${{matrix.language}}'

            - name: Restore visibility
              run: gh repo edit ${{ env.org_name }}/${{ env.app_name }} --visibility private
              env:
                  GITHUB_TOKEN: ${{ secrets.PAT }}

            - name: Notify results
              uses: kunalnagarco/action-cve@46009c95365f8835facc116aa3b2cf8e5e605f33 #v1.7.12
              with:
                  token: ${{ secrets.PAT }}
                  slack_webhook: ${{ secrets.SLACK_WEBHOOK_URL }}
                  # pager_duty_integration_key: ${{ secrets.PAGER_DUTY_INTEGRATION_KEY }}
                  # zenduty_api_key: ${{ secrets.ZENDUTY_API_KEY }}
                  # zenduty_service_id: ${{ secrets.ZENDUTY_SERVICE_ID }}
                  # zenduty_escalation_policy_id: ${{ secrets.ZENDUTY_ESCALATION_POLICY_ID }}
                  # count: 10
