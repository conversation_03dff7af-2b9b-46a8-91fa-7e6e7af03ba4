<script lang="ts" context="module">
    //Utils
    import { faGear, faLineChart } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import { getCurrentTimezoneOffset, JUSTIFY_iTEMS_OBJ } from '../utils';
    //svelte
    import { onDestroy, onMount } from 'svelte';

    //Graphql

    import { type Subscription } from 'wonka';

    import { type SimpleChart } from './WidgetConfig/index.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);

    export const widgetTitle = 'Simple Chart';
    export const widgetIcon = faLineChart;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetMinWidth = 4;
    export const widgetMinHeight = 4;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
</script>

<script lang="ts">
    import {
        GetCfgFilteringLabelDocument,
        GetCfgUserFilteringByCfgParamUserIdDocument,
        GetCfgUserFilteringByCfgParamUserIdSubscriptionDocument,
        type GetCfgUserFilteringByCfgParamUserIdQuery,
        type GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription,
        type GetCfgFilteringLabelSubscription,
    } from '$generated-types';
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import FormModal from '$lib/core/modules/DynamicForm/FormModal.svelte';
    import CfgParameterViewer from '$lib/shared/components/cfg-parameter/CfgParameterViewer.svelte';
    import type { UpdateCfgUserFilteringDto } from '$lib/shared/dto/cfg_user_filtering';
    import { user } from '$lib/stores/Auth';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { locations } from '$lib/stores/Locations';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        mutationStore,
        queryStore,
        subscriptionStore,
        type OperationResultStore,
        type Pausable,
    } from '@urql/svelte';
    import type { EChartsOption, EChartsType, YAXisComponentOption } from 'echarts';
    import { Button } from 'flowbite-svelte';
    import gql from 'graphql-tag';
    import { get } from 'svelte/store';
    import lodash from 'lodash';
    import {
        convertDateMonthQuarterYearToDateString,
        convertMonthToDate,
        convertWeekToDate,
        convertYearToDate,
    } from '$lib/core/modules/DynamicForm/utils';
    import moment from 'moment';
    import { SOC_CFG_POPUP_INFO, SOC_CFG_POPUP_KEYS } from '$lib/shared/enum/general';
    import { getToggleStatus, mappingDataByKeys, standardizedFilterLabel } from '../../utils/utils';
    import { setHiddenFieldValue } from '$lib/shared/util/customFunction';
    import { isNullOrEmpty } from '$routes/(app)/(opshub)/incident/utils/input_utils';

    /* ---------------------------------- data ---------------------------------- */
    let subscriptions: Subscription[] = [];
    export let config: SimpleChart;
    export let sendDataToDashboard;

    /* --------------------------------- echart --------------------------------- */
    let libECharts;
    let eChartsType: EChartsType;
    let elementChart: HTMLElement;
    let option: EChartsOption;
    let chartData = [];
    let titleFiltering;
    let minValue;
    let maxValue;
    let maxDataTextLength;
    let isModalOpen: boolean = false;
    let modalMessage: string = '';
    export let json_values;
    let defaultEmptySeriesType = [];
    $: {
        if (json_values && Array.isArray(json_values)) {
            if (!isNullOrEmpty(config.col_default_empty_series_type)) {
                defaultEmptySeriesType = getArrayConfig(config.col_default_empty_series_type);
            }
            chartData = json_values;
            reBuildData();
            if (defaultEmptySeriesType.length === 0) {
                chartData = chartData;
            } else {
                chartData = chartData.map(series => {
                    if (defaultEmptySeriesType.includes(series.name)) {
                        return {
                            ...series,
                            data: series.data.map(item => ({
                                ...item,
                                value: item.value / 1000,
                            })),
                        };
                    }
                    return series;
                });
            }
            const allDataPoints = chartData.flatMap(series => series.data);

            const minDataPoint = allDataPoints?.reduce((min, item) => (item.value < min.value ? item : min), {
                value: Infinity,
            });
            minValue = minDataPoint?.value;

            const maxDataPoint = allDataPoints?.reduce((max, item) => (item.value > max.value ? item : max), {
                value: -Infinity,
            });
            maxValue = maxDataPoint?.value;

            const seriesChart = chartData.map(item => item.name);
            maxDataTextLength = Math.max(...seriesChart?.map(name => name.length));
        } else {
            chartData = [];
            minValue = 0;
            maxValue = 0;
            maxDataTextLength = 0;
        }
        render();
    }

    function sortDataByDate(dataArray) {
        return (
            dataArray?.map(category => ({
                ...category,
                data: category?.data?.slice().sort((a, b) => {
                    // Specific for SOC US
                    const isAAvg = a.datetime?.includes('AVG Past');
                    const isBAvg = b.datetime?.includes('AVG Past');
                    if (isAAvg && !isBAvg) return -1;
                    if (!isAAvg && isBAvg) return 1;

                    const isAQuarter = a.datetime?.includes('Q');
                    const isBQuarter = b.datetime?.includes('Q');
                    if (isAQuarter && isBQuarter) {
                        let [aYear, aQuarter] = a.datetime?.split('-');
                        let [bYear, bQuarter] = b.datetime?.split('-');
                        aYear = parseInt(aYear);
                        bYear = parseInt(bYear);
                        aQuarter = parseInt(aQuarter?.replace('Q', ''));
                        bQuarter = parseInt(bQuarter?.replace('Q', ''));

                        return aYear === bYear ? aQuarter - bQuarter : aYear - bYear;
                    }
                    // End

                    return new Date(a.datetime).getTime() - new Date(b.datetime).getTime();
                }),
            })) || dataArray
        );
    }

    function reBuildData() {
        let dataXAxis = [...new Set(chartData.flatMap(series => series.data).map(item => item.datetime))].sort();
        let arr = [];
        chartData.forEach(item => {
            // Using a Set improves performance because .has() in a Set is O(1), while .includes() in an array is O(n).
            const dataInSeries = new Set(item?.data?.map(itemInSeries => itemInSeries.datetime));
            const dataMissSeries = dataXAxis?.filter(itemXAxis => !dataInSeries.has(itemXAxis));
            dataMissSeries?.forEach(itemMissSeries => {
                item.data.push({ value: 0, datetime: itemMissSeries });
            });
            item?.data.sort((datetime1, datetime2) => {
                return new Date(datetime1.datetime) > new Date(datetime2.datetime);
            });
            arr?.push(item);
        });
        chartData = sortDataByDate(arr);
    }

    function configDistance() {
        const maxAbsoluteValue = Math.max(Math.abs(minValue), maxValue);
        if (maxAbsoluteValue <= 1000) return 0;
        const digitCount = Math.ceil(Math.log10(maxAbsoluteValue));
        return digitCount * 5;
    }

    const labelSeried = {
        formatter: param => {
            return param.data[1] === 0 ? '' : param.data[1];
        },
        show: true,
        color: '#FFFFFF',
    };
    const dictType = {
        get line() {
            return {
                areaStyle: null,
                label: { ...labelSeried },
                lineStyle: {
                    width: 5,
                },
                type: 'line',
                stack: null,
            };
        },
        get area() {
            return { areaStyle: {}, label: { ...labelSeried }, type: 'line', stack: null };
        },
        get bar() {
            return { areaStyle: null, label: { ...labelSeried, show: true }, type: 'bar', stack: null };
        },
        get stackedBar() {
            return { areaStyle: null, label: { ...labelSeried, show: true }, type: 'bar', stack: 'stacked' };
        },
        get top() {
            return {
                grid: {
                    left: __setLeftGrid(),
                    right: __setRightGrid(),
                    top: __setTopGrid(),
                    bottom: __setBottomGrid(),
                },
                legend: {
                    show: true,
                    type: config?.showLegendScroll ? 'scroll' : 'plain',
                    pageIconSize: 20,
                    orient: 'horizontal',
                    top: config?.toolbox?.show ? '7%' : '',
                    right: undefined,
                    left: 'center',
                    textStyle: {
                        color: '#fff',
                    },
                    formatter: function (name) {
                        const series = option?.series?.find(item => item.name === name);
                        return series?.displayName ? series.displayName : name;
                    },
                },
            };
        },
        get right() {
            return {
                grid: {
                    left: __setLeftGrid(),
                    right: __setRightGrid(),
                    top: __setTopGrid(),
                    bottom: __setBottomGrid(),
                },
                legend: {
                    show: true,
                    orient: 'vertical',
                    top: 'middle',
                    right: 0,
                    left: undefined,
                    textStyle: {
                        color: '#fff',
                    },
                    formatter: function (name) {
                        const series = option?.series?.find(item => item.name === name);
                        return series?.displayName ? series.displayName : name;
                    },
                },
            };
        },
        get none() {
            return {
                grid: {
                    left: __setLeftGrid(),
                    right: __setRightGrid(),
                    top: __setTopGrid(),
                    bottom: __setBottomGrid(),
                },
                legend: {
                    show: false,
                },
            };
        },
    };
    const __yAxis: YAXisComponentOption | YAXisComponentOption[] = {
        type: 'value',
        boundaryGap: ['5%', '5%'],
        axisTick: {
            show: false,
        },
    };

    const __setTopGrid = () => {
        return !config?.toolbox?.show && ['right', 'none'].includes(config.positionLegend)
            ? 25
            : config.positionLegend === 'top' && config?.toolbox?.show
            ? 110
            : 65;
    };
    const __setBottomGrid = () => {
        if (config.xAxis?.show && config.xAxis?.name.trim()) {
            return 90;
        }

        if (!config.xAxis?.show && config.xAxis?.name.trim() && config?.title?.trim()) {
            return 10;
        }

        const maxLength = Math.max(...chartData.flatMap(c => c.data.map(item => item?.datetime?.length || 0)));
        if (maxLength > 10) {
            return 60 + (maxLength - 10) * 5;
        }

        return 60;
    };
    const __setLeftGrid = () => {
        const maxLength = Math.max(...(chartData[0]?.data?.map(item => item.datetime.length) ?? []));
        const additionalMargin = maxLength > 10 ? (maxLength - 6) * 5 : 0;

        return config.positionLegend != 'right'
            ? config?.yAxis?.[0].show
                ? config?.yAxis?.[0].name.trim()
                    ? 60 + configDistance()
                    : 40 + additionalMargin + configDistance()
                : 20
            : 60 + configDistance();
    };
    const __setRightGrid = () => {
        return config.positionLegend != 'right'
            ? config?.yAxis?.[1].show
                ? config?.yAxis?.[1].name.trim()
                    ? 55 + configDistance()
                    : 55 + configDistance()
                : 10
            : 140 + configDistance() + maxDataTextLength * 5;
    };

    async function initChart() {
        libECharts = await import('echarts');
        //Initialize echarts and channel
        eChartsType = libECharts.init(elementChart, null, { renderer: 'svg' });
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            eChartsType.resize();
        });
        window.addEventListener('resize', () => {
            setTimeout(() => eChartsType.resize(), 100);
        });
    }

    function hasData() {
        return chartData.length > 0;
    }

    let oldIds = [
        'xaxisTitle',
        'showXAxis',
        'leftYaxisTitle',
        'showLeftYAxis',
        'rightYaxisTitle',
        'showRightYAxis',
        'minLeftYAxis',
        'maxLeftYAxis',
        'minRightYAxis',
        'maxRightYAxis',
        'showToolbox',
    ];
    let newIds = [
        'xAxis.name',
        'xAxis.show',
        'yAxis[0].name',
        'yAxis[0].show',
        'yAxis[1].name',
        'yAxis[1].show',
        'yAxis[0].min',
        'yAxis[0].max',
        'yAxis[1].min',
        'yAxis[1].max',
        'toolbox.show',
    ];
    async function render() {
        if (config && oldIds.some(key => key in config)) {
            for (let i = 0; i < oldIds.length; i++) {
                if (oldIds[i] in config) {
                    lodash.set(config, newIds[i], config[oldIds[i]]);
                    delete config[oldIds[i]];
                }
            }
        }
        if (!hasData()) {
            option = {};
            return;
        }
        if (!eChartsType) {
            await initChart();
        }
        /*
         * ######################## data from aoh_dash.widget config ########################
         */
        BackwardsCompatible();
        option = {
            toolbox: {
                show: config?.toolbox?.show,
                feature: {
                    saveAsImage: {},
                    dataView: {
                        readOnly: false,
                    },
                    dataZoom: {
                        yAxisIndex: 'none',
                    },
                },
            },
            tooltip: {
                axisPointer: {
                    type: 'cross',
                    label: { show: false },
                },
                formatter: '<b>{c}</b>',
            },
            xAxis: [
                {
                    ...config.xAxis,
                    axisLabel: {
                        show: true,
                        interval: 0,
                        rotate: 45,
                    },
                    nameTextStyle: {
                        verticalAlign: 'top',
                    },
                    type: 'category',
                    boundaryGap: config?.showBoundaryGap ?? true,
                    data: [...new Set(chartData.flatMap(series => series.data).map(item => item.datetime))],
                    axisLine: {
                        show: true,
                        symbol: config.showArrowAxis ? ['none', 'arrow'] : null,
                        lineStyle: {
                            width: 1,
                            color: '#ffffff',
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                    nameLocation: 'middle',
                    nameGap: 70,
                },
            ],
            yAxis: [
                {
                    ...config.yAxis[0],
                    id: 'leftYaxisTitle',
                    position: 'left',
                    ...__yAxis,
                    nameTextStyle: {
                        align: 'center',
                    },
                    axisLine: {
                        show: !config.showHorizontalLines,
                        lineStyle: {
                            width: 1,
                            color: '#ffffff',
                        },
                        symbol: config.showArrowAxis ? ['none', 'arrow'] : null,
                    },
                    nameLocation: 'middle',
                    nameGap: 45 + configDistance(),
                    splitLine: {
                        show: config.showHorizontalLines,
                        lineStyle: {
                            width: 1,
                            color: '#ffffff',
                        },
                    },
                    min: config?.yAxis?.[0].min ? config?.yAxis?.[0].min : minValue >= 0 ? 0 : undefined,
                    max: config?.yAxis?.[0].max ? config?.yAxis?.[0].max : undefined,
                },
                {
                    ...config.yAxis[1],
                    id: 'rightYaxisTitle',
                    position: 'right',
                    ...__yAxis,
                    nameTextStyle: {
                        align: 'center',
                    },
                    axisLine: {
                        show: !config.showHorizontalLines,
                        lineStyle: {
                            width: 1,
                            color: '#ffffff',
                        },
                        symbol: config.showArrowAxis ? ['none', 'arrow'] : null,
                    },
                    nameLocation: 'middle',
                    nameGap: 40 + configDistance(),
                    splitLine: {
                        show: config.showHorizontalLines,
                        lineStyle: {
                            width: 1,
                            color: '#EF5A6F',
                        },
                    },
                    min: config?.yAxis?.[1].min ? config?.yAxis?.[1].min : minValue >= 0 ? 0 : undefined,
                    max: config?.yAxis?.[1].max ? config?.yAxis?.[1].max : undefined,
                },
            ],
        };
        /*
         * ######################## data from bi_flipboard ########################
         */
        option.series = [...config.series];
        for (let i = 0; i < chartData.length; i++) {
            option.series[i].data = chartData[i].data.map(item => item.value);
        }
        option.xAxis['boundaryGap'] = option.series.map(obj => obj.type)?.includes('bar');
        option = {
            ...option,
            ...dictType[config.positionLegend],
            legend: {
                ...dictType[config.positionLegend].legend,
                data: showedLegend,
            },
        };
        option.title = { show: !hasData(), textStyle: { color: '#fff', fontSize: 20 }, text: 'No data' };
    }
    let showedLegend = [];
    $: {
        showedLegend = config.series?.filter(item => item?.displayType).map(item => item.name) || [];
    }
    function getRandomColor(i) {
        const hue = (i * 137.508) % 360;
        const saturation = 0.5 + Math.random() * 0.5;
        const lightness = 0.4 + Math.random() * 0.3;

        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1 / 6) return p + (q - p) * 6 * t;
            if (t < 1 / 2) return q;
            if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
            return p;
        };

        const q = lightness < 0.5 ? lightness * (1 + saturation) : lightness + saturation - lightness * saturation;
        const p = 2 * lightness - q;

        const r = Math.round(hue2rgb(p, q, hue / 360 + 1 / 3) * 255);
        const g = Math.round(hue2rgb(p, q, hue / 360) * 255);
        const b = Math.round(hue2rgb(p, q, hue / 360 - 1 / 3) * 255);

        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b
            .toString(16)
            .padStart(2, '0')}`;
    }

    function getArrayConfig(configValue: string): string[] {
        return configValue ? configValue.split(',').map(s => s.trim()) : [];
    }

    function parseGroupConfig(configValue) {
        return configValue
            ? configValue.match(/\[([^\]]+)\]/g)?.map(group =>
                  group
                      .replace(/[\[\]]/g, '')
                      .split(',')
                      .map(s => s.trim())
              ) || []
            : [];
    }

    function createGroupMatcher(groupConfig, listConfig) {
        const groups = parseGroupConfig(groupConfig);
        const list = listConfig ? listConfig.split(',').map(s => s.trim()) : [];

        return chartName => {
            const groupIndex = groups.findIndex(group =>
                group?.some(item => item?.toLowerCase() === chartName?.toLowerCase())
            );
            return list[groupIndex] || '';
        };
    }

    const getStackName = createGroupMatcher(config.col_default_stack_group, config.list_stacks);
    const getPrefix = createGroupMatcher(config.col_default_prefix_series, config.list_prefix_series);
    const getSuffix = createGroupMatcher(config.col_default_suffix_series, config.list_suffix_series);

    function BackwardsCompatible() {
        const defaultRightYAxis = getArrayConfig(config.col_default_right_Yaxis);
        if ('dataset' in config) {
            config.series = [];
            for (let i = 0; i < chartData.length; i++) {
                let indexOld = config['dataset'].findIndex(itemDataset => itemDataset.name == chartData[i].name);
                let itemConfigSeries: any = {
                    name: chartData[i].name,
                    stack:
                        indexOld >= 0
                            ? config.series[indexOld].stack
                            : config.list_stacks?.split(',').includes(chartData[i].stackName)
                            ? chartData[i].stackName
                            : getStackName(chartData[i].name),
                    displayName: indexOld >= 0 ? config['dataNames'][indexOld].customName : chartData[i].name,
                    displayType:
                        indexOld >= 0
                            ? config['chartTypeSeries'][indexOld]
                            : (defaultRightYAxis.includes(chartData[i].name)
                                  ? config.col_default_series_type_right
                                  : config.col_default_series_type_left) || 'bar',
                    label: {
                        ...labelSeried,
                        color: indexOld >= 0 ? config['textColors'][indexOld] : '#FFFFFF',
                        position: indexOld >= 0 ? config['labelPosition'][indexOld] : 'inside',
                        formatter: function (params) {
                            return (
                                `${config?.series[indexOld]?.prefix || ''}` +
                                `${params.value}` +
                                `${config?.series[indexOld]?.suffix || ''}`
                            );
                        },
                    },
                    symbol: 'rectangle',
                    itemStyle: {
                        color: indexOld >= 0 ? config['colorsSeries'][indexOld] : getRandomColor(i),
                    },
                    isRightYaxis:
                        indexOld >= 0
                            ? config['isRightYaxis'][indexOld]
                            : defaultRightYAxis.includes(chartData[i].name),
                };
                itemConfigSeries.label.show = !(itemConfigSeries.label.position == 'hover');
                itemConfigSeries.yAxisIndex = itemConfigSeries.isRightYaxis ? Number(itemConfigSeries.isRightYaxis) : 0;
                itemConfigSeries.tooltip = { show: itemConfigSeries.label.position == 'hover' };
                itemConfigSeries = { ...dictType[itemConfigSeries?.displayType || 'bar'], ...itemConfigSeries };
                config.series.push(itemConfigSeries);
            }
            delete config['dataset'];
            delete config['chartTypeSeries'];
            delete config['textColors'];
            delete config['colorsSeries'];
            delete config['isRightYaxis'];
            delete config['labelPosition'];
            delete config['dataNames'];
        } else {
            let arrSeries = [];
            for (let i = 0; i < chartData.length; i++) {
                let indexOld = config.series?.findIndex(itemSeries => itemSeries.name == chartData[i].name);
                const existingConfig = indexOld >= 0 ? config.series[indexOld] : null;

                const getValueWithFallback = (configValue, fallbackValue) => {
                    if (existingConfig) {
                        return configValue;
                    }
                    return fallbackValue;
                };

                const getStackValue = displayType => {
                    if (existingConfig) {
                        if (isNullOrEmpty(existingConfig.stack) || existingConfig.stack === 'stacked') {
                            if (displayType === 'stackedBar') {
                                return 'stacked';
                            } else {
                                return '';
                            }
                        } else {
                            return existingConfig.stack;
                        }
                    }
                    if (isNullOrEmpty(config.list_stacks)) {
                        if (
                            config.col_default_series_type_right !== 'stackedBar' &&
                            defaultRightYAxis.includes(chartData[i].name)
                        ) {
                            return '';
                        }
                        if (config.col_default_series_type_left === 'stackedBar') {
                            return 'stacked';
                        }
                    } else {
                        if (config.list_stacks?.split(',').includes(chartData[i].stackName)) {
                            return chartData[i].stackName;
                        }
                        return getStackName(chartData[i].name);
                    }
                };

                const getDisplayTypeValue = () => {
                    if (existingConfig) {
                        return existingConfig.displayType;
                    }
                    if (
                        defaultEmptySeriesType.some(series => series.toLowerCase() === chartData[i].name.toLowerCase())
                    ) {
                        return '';
                    }
                    if (defaultRightYAxis.includes(chartData[i].name)) {
                        return config.col_default_series_type_right || 'bar';
                    }
                    return config.col_default_series_type_left || 'bar';
                };

                const getConfigValue = (configKey, chartDataValue, getterFunction) => {
                    if (existingConfig) {
                        return existingConfig[configKey];
                    }
                    if (config[`list_${configKey}_series`]?.split(',').includes(chartDataValue)) {
                        return chartDataValue;
                    }
                    return getterFunction(chartData[i].name);
                };

                const getLabelPositionValue = () => {
                    if (existingConfig?.displayType) {
                        return existingConfig.label.position;
                    }
                    if (existingConfig) {
                        return 'insideBottom';
                    }
                    return 'inside';
                };

                const getItemColorValue = () => {
                    if (existingConfig?.displayType) {
                        if (existingConfig.itemStyle.color !== 'transparent') {
                            return existingConfig.itemStyle.color;
                        }
                        return getRandomColor(i);
                    }
                    if (existingConfig) {
                        return 'transparent';
                    }
                    return getRandomColor(i);
                };

                const getIsRightYaxisValue = () => {
                    if (existingConfig) {
                        return existingConfig.isRightYaxis;
                    }
                    return config.yAxis[1].show && defaultRightYAxis.includes(chartData[i].name);
                };
                const displayType = getDisplayTypeValue();
                const stack = getStackValue(displayType);
                const displayName = getValueWithFallback(existingConfig?.displayName, chartData[i].name);
                const prefix = getConfigValue('prefix', chartData[i].prefix, getPrefix);
                const suffix = getConfigValue('suffix', chartData[i].suffix, getSuffix);
                const labelColor = getValueWithFallback(existingConfig?.label?.color, '#FFFFFF');
                const labelPosition = getLabelPositionValue();
                const itemColor = getItemColorValue();
                const isRightYaxis = getIsRightYaxisValue();

                let itemConfigSeries = {
                    name: chartData[i].name,
                    stack: stack,
                    displayName: displayName,
                    displayType: displayType,
                    prefix: prefix,
                    suffix: suffix,
                    label: {
                        ...labelSeried,
                        color: labelColor,
                        position: labelPosition,
                        formatter: function (params) {
                            if (defaultEmptySeriesType?.includes(chartData[i].name)) {
                                return `${prefix || ''}` + `${params.value * 1000}` + `${suffix || ''}`;
                            } else {
                                return `${prefix || ''}` + `${params.value}` + `${suffix || ''}`;
                            }
                        },
                    },
                    symbol: 'rectangle',
                    itemStyle: {
                        color: itemColor,
                    },
                    isRightYaxis: isRightYaxis,
                };
                itemConfigSeries.label.show = !(itemConfigSeries.label.position == 'hover');
                itemConfigSeries.yAxisIndex = itemConfigSeries.isRightYaxis ? Number(itemConfigSeries.isRightYaxis) : 0;
                itemConfigSeries.tooltip = { show: itemConfigSeries.label.position == 'hover' };
                itemConfigSeries = { ...dictType[itemConfigSeries?.displayType || 'bar'], ...itemConfigSeries };
                arrSeries.push(itemConfigSeries);
            }
            config.series = arrSeries;
        }
    }

    let key_value_json;
    let userFilteringData;
    let user_id = '';
    const client = get(gqlClientStore);
    let formModal;
    let dataCfgParameter: Record<string, string> = {};
    const CommonURL = `${get(locations).common_web}`;

    let subUserFiltering: OperationResultStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription> & Pausable;
    let customFilteringLabel: OperationResultStore<GetCfgFilteringLabelSubscription> & Pausable;

    async function getUserFiltering() {
        const cfgParameter = await fetch(`${CommonURL}/cfg-parameter/${config.cfg_param}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        }).then(response => {
            return response.json();
        });
        if (cfgParameter?.data) {
            dataCfgParameter = {
                ...cfgParameter?.data,
                is_custom_handler: cfgParameter?.data.IsCustomHandler,
                name: cfgParameter?.data.Name,
                fields: cfgParameter?.data.form_fields_json || [],
                end_point: cfgParameter?.data.EndPoint,
            };
            titleFiltering = cfgParameter?.data.Title || '';
        }

        user_id = get(user).claims.hasura_access['x-hasura-user-id'];

        const queryUserFiltering = queryStore<GetCfgUserFilteringByCfgParamUserIdQuery>({
            client,
            query: GetCfgUserFilteringByCfgParamUserIdDocument,
            variables: { id: config.cfg_param, user_id },
        });
        const unsubscriberFiltering = queryUserFiltering.subscribe(res => {
            if (!res.fetching && !res.error) {
                userFilteringData = res.data?.cc3_hoc_cfg_user_filtering[0];
                key_value_json = res.data?.cc3_hoc_cfg_user_filtering[0]?.key_value_json || {};
                unsubscriberFiltering();
            } else if (!res.fetching && res.error) {
                unsubscriberFiltering();
            }
        });
        subUserFiltering = subscriptionStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription>({
            client,
            query: GetCfgUserFilteringByCfgParamUserIdSubscriptionDocument,
            variables: { id: config.cfg_param, user_id },
        });

        if (config.showFilteringLabel && !config.label_use_filter_datasource && config.cfg_param_label) {
            const tenant_id = get(user).claims.active_tenant.tenant_id;
            customFilteringLabel = subscriptionStore<GetCfgFilteringLabelSubscription>({
                client,
                query: GetCfgFilteringLabelDocument,
                variables: { id: config.cfg_param_label, tenant_id },
            });
        }
    }

    $: if ($subUserFiltering?.data?.cc3_hoc_cfg_user_filtering) {
        key_value_json = $subUserFiltering.data?.cc3_hoc_cfg_user_filtering[0]?.key_value_json || {};
        if (userFilteringData)
            userFilteringData.filter_label = $subUserFiltering.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label;
    }
    onMount(async () => {
        await render();
        getUserFiltering();
    });

    onDestroy(() => {
        subscriptions.forEach(s => {
            s?.unsubscribe();
        });
    });

    $: if (Boolean(option) && Boolean(elementChart) && Boolean(config)) {
        render();
        // hotfix for SIT issue 353 point 5, update the code if there is better solution
        if (option?.xAxis?.data?.[0]?.length > 'yyyy-mm'.length && option?.grid?.left < 60) {
            option.grid.left = 60;
        }
        let longestRightNum = 0;
        option?.series?.forEach(item => {
            const last = item?.data[item.data.length - 1] + '';
            if (last?.length > longestRightNum) longestRightNum = last.length;
        });
        if (option?.grid?.right < longestRightNum * 5) {
            option.grid.right = longestRightNum * 5;
        }
        // end of hotfix
        eChartsType?.setOption(option as any, true);
    }

    let openFilter = false;

    const onGearClick = () => {
        openFilter = true;
    };

    $: if (!openFilter) {
        setHiddenFieldValue('cc3_hoc.cfg_ngemr_visit_type_grp_mapping', '');
        setHiddenFieldValue('cc3_hoc.cfg_ngemr_appt_rationale', '');
        setHiddenFieldValue('cc3_hoc.cfg_ngemr_specialty', '');
    }

    let unique = {};

    const cfgSave = async data => {
        // Save to cfg_user_filtering
        let newData;
        let newFilterLabel;
        try {
            newData = JSON.parse(
                JSON.stringify({ ...data, TimeZone: getCurrentTimezoneOffset() }).replaceAll('T00:00:00.000Z', '')
            );
            newFilterLabel = JSON.parse(JSON.stringify(data).replaceAll('T00:00:00.000Z', ''));
            delete newData['undefined'];
        } catch (error) {}
        const userName = get(user)?.claims?.preferred_username;
        const tenant_id = get(user)?.claims?.active_tenant?.tenant_id;

        const dto: UpdateCfgUserFilteringDto = {
            user_id,
            tenant_id,
            filtering_id: config.cfg_param,
            key_value_json: newData,
            filter_label: newFilterLabel,
            updated_by: userName,
            created_by: userFilteringData?.created_by || userName,
        };

        //handle mapping id to name
        const isSocUs = Object.values(SOC_CFG_POPUP_KEYS).some(key => newData[key] !== undefined);
        if (newData['MaterialList'] !== undefined) {
            //mapping id to name for filter label
            if (newData['ViewChartBy'] === 'Group') {
                const materialData = await (await fetch(`/api/MstInvMaterialGroupService?material_type=`)).json();
                const mappedIdToName = mappingDataByKey(newData['MaterialList'], materialData.data.data, 'id', 'name');
                dto.filter_label = {
                    ...(dto.filter_label as any),
                    MaterialList: mappedIdToName,
                };
                //filter selected item because selected include item & group
                dto.key_value_json = {
                    ...(dto.key_value_json as any),
                    MaterialList: filterMaterialByViewChartByGroup(
                        dto.key_value_json['MaterialList'],
                        materialData.data.data
                    ),
                };
            }
            if (newData['ViewChartBy'] === 'Item') {
                const materialData = await (await fetch(`/api/MstSapmmMaterialService?is_selected=true`)).json();

                //filter selected group because selected include item & group
                const filteredData = filterMaterialByViewChartByItem(
                    dto.key_value_json['MaterialList'],
                    materialData.data.data
                );
                dto.filter_label = {
                    ...(dto.filter_label as any),
                    MaterialList: filteredData,
                };
                dto.key_value_json = {
                    ...(dto.key_value_json as any),
                    MaterialList: filteredData,
                };
            }
            dto.filter_label = JSON.stringify(standardizedFilterLabel(dto.filter_label));
            dto.key_value_json = convertDateMonthQuarterYearToDateString(dto.key_value_json);
        } else if (newData['Location'] !== undefined || newData['Equipment'] !== undefined) {
            if (newData['Location']) {
                const locationData = await (await fetch(`/api/RftInvEquipmentLocationUtilization?tenant_id=`)).json();
                const mappedZoneToZoneName = mappingDataByKey(
                    newData['Location'],
                    locationData.res,
                    'zone',
                    'zone_name'
                );
                dto.filter_label = {
                    ...(dto.filter_label as any),
                    Location: mappedZoneToZoneName,
                };
            }
            if (newData['Equipment']) {
                const equipmentData = await (await fetch(`/api/RftRtlsSubTypeService?tenant-id`)).json();
                const mappedEquipmentNameToEquipmentDescription = mappingDataByKey(
                    newData['Equipment'],
                    equipmentData.data,
                    'name',
                    'description'
                );
                dto.filter_label = {
                    ...(dto.filter_label as any),
                    Equipment: mappedEquipmentNameToEquipmentDescription,
                };
            }
            dto.filter_label = JSON.stringify(standardizedFilterLabel(dto.filter_label));
            dto.key_value_json = convertDateMonthQuarterYearToDateString(dto.key_value_json);
        } else if (newData['BloodProductList'] !== undefined || newData['BloodTypeList'] !== undefined) {
            dto.filter_label = JSON.stringify(standardizedFilterLabel(dto.filter_label));
            dto.key_value_json = convertDateMonthQuarterYearToDateString(dto.key_value_json);
        } else if (isSocUs) {
            const filterLabel = structuredClone(newData);
            for (const key of Object.keys(filterLabel)) {
                const info = SOC_CFG_POPUP_INFO.find(info => info.key.toLowerCase() === key.toLowerCase());
                if (info?.key) {
                    const toggleStatus = await getToggleStatus(info.cfgName);
                    // handle for 1 toggle buttons, if >=2 => update here
                    const isOn = toggleStatus ? Object.values(toggleStatus)?.[0] || false : false;
                    const newValue = await mappingDataByKeys(newFilterLabel[key], info, isOn);
                    filterLabel[key] = newValue[key]?.length ? newValue[key].join(', ') : filterLabel[key];
                }
            }
            dto.filter_label = JSON.stringify(standardizedFilterLabel(filterLabel, true));
            dto.key_value_json = convertDateMonthQuarterYearToDateString(dto.key_value_json, true);
        } else {
            dto.filter_label = JSON.stringify(dto.filter_label);
        }
        let res;
        dto.key_value_json = JSON.stringify(dto.key_value_json);
        if (!userFilteringData?.id) {
            res = await fetch(`/api/CfgUserFilteringService/upsert`, {
                method: 'POST',
                body: JSON.stringify(dto),
            });
        } else {
            res = await fetch(`/api/CfgUserFilteringService/${userFilteringData.id}`, {
                method: 'PATCH',
                body: JSON.stringify(dto),
            });
        }
        if (res.ok) {
            modalMessage = 'Configuration is saved successfully.';
            isModalOpen = true;
            openFilter = false;
            getUserFiltering();
            sendDataToDashboard({ cfgParamId: dataCfgParameter?.ID, cfgParamName: dataCfgParameter?.Name });
        } else {
            modalMessage = 'Unable to save configuration successfully.';
            isModalOpen = true;
        }
        unique = {};
    };

    function getJustifyClass(alignment) {
        switch (alignment) {
            case 'left':
                return 'start';
            case 'right':
                return 'end';
            default:
                return 'center';
        }
    }
    //fetch material list to mapping name
    async function getMaterialGroupData() {
        const FetchDataDocument = gql`
            query FetchMaterialGroupMappingData {
                cc3_hoc_mst_inv_material_group(where: { is_deleted: { _eq: false } }) {
                    name
                    id
                    material_type
                }
            }
        `;
        const result = await client.query(FetchDataDocument, {}).toPromise();
        return result.data['cc3_hoc_mst_inv_material_group'];
    }

    function filterMaterialByViewChartByGroup(values: string, mstData: any[]) {
        if (!values) return '';
        let arrayValues = values.split(',');
        return arrayValues.filter(id => mstData.find(item => item.id === id)).join(',');
    }
    function filterMaterialByViewChartByItem(values: string, mstData: any[]) {
        if (!values) return '';
        let arrayValues = values.split(',');
        return arrayValues.filter(name => mstData.find(item => item.name === name)).join(',');
    }

    function mappingDataByKey(inputString: string, mstData: any[], fromKey: string, toKey: string) {
        mstData = mstData || [];
        if (!inputString) return '';
        let arrayData = inputString.split(',');
        let result = arrayData.map(s => mstData.find(c => c[fromKey] === s)?.[toKey]).filter(item => !!item);
        return result.join(',');
    }
</script>

<div class="text-s flex h-full w-full flex-col p-6">
    <header class="flex items-center justify-between gap-4 text-on-background">
        <h3 class="mb-4 w-full text-lg font-bold text-gray-900 dark:text-white">{config.title}</h3>
        {#if config?.showGear && !config.useParentFilter}
            <Button size="md" class="p-2 opacity-0 duration-300 hover:opacity-100" on:click={onGearClick}>
                <span>
                    {@html icon(faGear).html}
                </span>
            </Button>
        {/if}
    </header>
    <div class="mb-10">
        {#if config.showFilteringLabel && !config.useParentFilter}
            <CfgParameterViewer
                showFilteringLabel={config.showFilteringLabel}
                justify_items={JUSTIFY_iTEMS_OBJ[config?.filterLabelAlignment] || ''}
                filter_label={!config.label_use_filter_datasource
                    ? $customFilteringLabel?.data?.cc3_hoc_cfg_parameter?.[0]?.key_value_json
                    : userFilteringData?.filter_label} />
        {/if}
    </div>
    <section class="{config?.showGear ? 'h-[85%]' : 'h-full'} w-full" bind:this={elementChart} />
</div>

{#key unique}
    {#if dataCfgParameter}
        {#if openFilter}
            <div
                id="large-modal-{userFilteringData?.id || ''}"
                tabindex="-1"
                class="fixed inset-0 z-50 flex max-h-full items-center justify-center overflow-y-auto overflow-x-hidden bg-black bg-opacity-80 p-6">
                <div class="relative max-h-full w-full max-w-4xl">
                    <!-- Modal content -->
                    <div class="relative rounded-lg border border-gray-500 bg-white shadow dark:bg-gray-700">
                        <!-- Modal header -->
                        <div
                            class="flex items-center justify-between rounded-t border-b p-6 md:p-6 dark:border-gray-600">
                            <h3 class="text-xl font-medium text-gray-900 dark:text-white">{titleFiltering}</h3>
                            <button
                                type="button"
                                class="ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
                                on:click={() => (openFilter = false)}>
                                <svg
                                    class="h-3 w-3"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 14 14">
                                    <path
                                        stroke="currentColor"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <div class="space-y-4 bg-gray-900 bg-opacity-50 pt-4 dark:bg-opacity-80">
                            <FormModal
                                onSave={cfgSave}
                                {formModal}
                                data={{
                                    ...dataCfgParameter,
                                    key_value_json,
                                }} />
                        </div>
                    </div>
                </div>
            </div>
        {/if}
    {/if}
{/key}
<Notification bind:isOpen={isModalOpen} modalTitle="Notification" {modalMessage} />
