import { locations } from '$lib/stores/Locations';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

import { apiGetRequest, apiUpdateRequest } from '$lib/shared/util/socApiRequest';

const sessionTypeURL = `${get(locations).soc}/soc/cfg-phar-reg-with-med-ords`;

export const GET: RequestHandler = async ({ locals, url }) => {
    const page = url.searchParams.get('page') ?? '1';
    const limit = url.searchParams.get('limit') ?? '9999';
    const token = locals.user.access_token;
    return apiGetRequest({ url: sessionTypeURL, token, query: { page, limit } });
};

export const POST: RequestHandler = async ({ request, locals }) => {
    const body = await request.json();
    const token = locals.user.access_token;
    return apiUpdateRequest({ url: sessionTypeURL, token, body, method: 'POST' });
};

export const PATCH: RequestHandler = async ({ request, locals }) => {
    const body = await request.json();
    const token = locals.user.access_token;
    return apiUpdateRequest({ url: sessionTypeURL, token, body, method: 'PATCH' });
};

export const DELETE: RequestHandler = async ({ locals, request }) => {
    const body = await request.json();
    const token = locals.user.access_token;
    return apiUpdateRequest({ url: sessionTypeURL, token, body, method: 'DELETE' });
};
