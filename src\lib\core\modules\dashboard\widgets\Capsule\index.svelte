<script lang="ts" context="module">
    import {} from '../utils';
    import { logger } from '$lib/stores/Logger';
    import Button from '$lib/core/components/Button/index.svelte';
    import { faDatabase, faCapsules, faCalendarDay } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import gql from 'graphql-tag';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import { Channel, browserBroadcaster } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import LocalizedFormat from 'dayjs/plugin/localizedFormat';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);
    dayjs.extend(LocalizedFormat);

    export const widgetIcon = faCapsules;
    export const widgetHeight = 2;
    export const widgetWidth = 3;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = Number.POSITIVE_INFINITY;
    export const widgetTitle = 'Capsule';
    export const widgetEnabled = true;
    export const widgetMinWidth = 2;
    export const widgetMinHeight = 3;
    export const widgetMaxWidth = 16;
    export const widgetMaxHeight = 16;

    export interface ListSement {
        segmentValue?: number;
        lineSegment?: number;
    }

    export let buttonIcon = faCalendarDay;
    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import type { CapsuleConfig } from './WidgetConfig/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { e, number } from 'mathjs';
    export let config: CapsuleConfig;
    export let json_values;

    let widgetElement: HTMLElement;

    $: unit = config.radioTypeCapsule === 'percentage' ? '%' : '';
    $: listdataTimeMins = json_values?.data?.map(i => ({ time: i.wait_time })) || [];

    let percentileData = [
        {
            label: 'P50',
            value: 50,
            calValue: '',
        },
        {
            label: 'P95',
            value: 95,
            calValue: '',
        },
        {
            label: 'P100',
            value: 100,
            calValue: '',
        },
    ];

    $: if (listdataTimeMins?.length > 0 && config.radioTypeCapsule) {
        percentileData = percentileData.map(data => ({
            ...data,
            calValue: caculatePercentile(data.value),
        }));
    }

    const caculatePercentile = (p: number) => {
        const isTimeMins = config.radioTypeCapsule === 'timemins';
        const data = isTimeMins ? listdataTimeMins : listdataTime24h;
        if (!data || data.length === 0) return null;

        const sortedData = data.sort((a, b) => Number(a.time) - Number(b.time));
        const index = Math.ceil((p / 100) * sortedData.length) - 1;
        const result = sortedData[Math.max(0, Math.min(index, sortedData.length - 1))].time;

        return isTimeMins ? formatTime(Number(result)) : result;
    };

    function formatTime(timeInMinutes: number): string {
        if (timeInMinutes >= 1440) {
            const days = Math.floor(timeInMinutes / 1440);
            const remainingMinutes = timeInMinutes % 1440;
            return `${days}d ${formatTime(remainingMinutes)}`;
        }

        if (timeInMinutes >= 60) {
            const hours = Math.floor(timeInMinutes / 60);
            const minutes = timeInMinutes % 60;
            return `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`;
        }

        return `${timeInMinutes}m`;
    }

    let listdataTimeMinsMock = [
        {
            name: 'A',
            time: '1',
        },
        {
            name: 'B',
            time: '2',
        },

        {
            name: 'B',
            time: '6',
        },
        {
            name: 'C',
            time: '8',
        },
        {
            name: 'D',
            time: '9',
        },
        {
            name: 'E',
            time: '11',
        },
        {
            name: 'F',
            time: '12',
        },
        {
            name: 'G',
            time: '13',
        },
        {
            name: 'H',
            time: '240',
        },
        {
            name: 'I',
            time: '20',
        },
        {
            name: 'k',
            time: '21',
        },
        {
            name: 'k',
            time: '21',
        },
        {
            name: 'L',
            time: '120',
        },
    ];

    export let listdataTime24h = [
        {
            name: 'A',
            time: '10:00',
        },
        {
            name: 'B',
            time: '11:00',
        },

        {
            name: 'B',
            time: '13:00',
        },
        {
            name: 'C',
            time: '14:00',
        },
        {
            name: 'D',
            time: '15:00',
        },
        {
            name: 'E',
            time: '16:00',
        },
        {
            name: 'F',
            time: '20:00',
        },
        {
            name: 'G',
            time: '21:00',
        },
        {
            name: 'H',
            time: '18:00',
        },
        {
            name: 'I',
            time: '17:30',
        },
        {
            name: 'k',
            time: '21:30',
        },
        {
            name: 'L',
            time: '23:01',
        },
    ];

    let dataPercentage = {
        value: 60,
        total: 140,
    };

    let dataPercent = Math.round((dataPercentage.value / dataPercentage.total) * 100);

    const COMPARISON_OPERATORS_HASH = {
        '<': (a, b) => a < b,
        '>': (a, b) => a > b,
        '>=': (a, b) => a >= b,
        '<=': (a, b) => a <= b,
    };

    function getMaxValueTimeMins() {
        return Math.max(...listdataTimeMins.map(item => Number(item.time)));
    }

    function getMaxValueTime24h() {
        const maxTimeInMinutes = Math.max(...listdataTime24h.map(item => convertToMinutes(item.time)));
        const maxTimeItem = listdataTime24h.find(item => convertToMinutes(item.time) === maxTimeInMinutes);
        return maxTimeItem ? maxTimeItem.time : null;
    }

    function calculationSegmentPercent(segmentconfigList) {
        const segmentlist = segmentconfigList;

        arraySegment = {
            segments: [],
            total: 0,
            totalZeroSegment: 0,
        };

        if (!segmentlist.some(x => x.valid.status === false)) {
            for (let i = 0; i < segmentlist.length; i++) {
                if (
                    convertToMinutes(segmentlist[i].timeValue) >=
                        convertToMinutes(config.capsuleSegmentConfig.movingTarget.startTime) &&
                    convertToMinutes(segmentlist[i].timeValue) <=
                        convertToMinutes(config.capsuleSegmentConfig.movingTarget.endTime)
                ) {
                    arraySegment.segments.push({
                        count:
                            i !== 0
                                ? segmentlist[i].percentValue - segmentlist[i - 1].percentValue + 1
                                : segmentlist[i].percentValue,
                        segmentLine: segmentlist[i],
                        valuePosition: 0,
                    });
                }
            }
            arraySegment.total = 100;
            for (let j = 0; j < arraySegment.segments.length; j++) {
                if (
                    j === arraySegment.segments.length - 1 &&
                    Number(arraySegment.segments[j].segmentLine.percentValue) === 100
                ) {
                    displayLineSegment = 'none';
                } else {
                    displayLineSegment = '';
                }
            }
            calculationMovingTarget();
            setColorAndBgColor();
        } else {
            config.capsuleSegmentConfig.movingTarget.movingTargetValue = 0;
        }
    }

    function calculationSegmentTime(segmentconfigList) {
        let segmentList = segmentconfigList;

        let count = 0;

        arraySegment = {
            segments: [],
            total: 0,
            totalZeroSegment: 0,
        };

        if (!segmentList.some(x => x.valid.status === false)) {
            for (let i = 0; i < segmentList.length; i++) {
                if (['timemins', 'time24h'].includes(config.radioTypeCapsule)) {
                    const timeDataList = config.radioTypeCapsule === 'timemins' ? listdataTimeMins : listdataTime24h;

                    for (const timeData of timeDataList) {
                        const timeValue =
                            config.radioTypeCapsule === 'timemins'
                                ? Number(timeData.time)
                                : convertToMinutes(timeData.time);

                        const fromValue =
                            config.radioTypeCapsule === 'timemins'
                                ? segmentList[i].fromValue
                                : convertToMinutes(segmentList[i].fromValue);
                        const toValue =
                            config.radioTypeCapsule === 'timemins'
                                ? segmentList[i].toValue
                                : convertToMinutes(segmentList[i].toValue);

                        const isLastSegment = i === segmentList.length - 1;
                        const withinSegmentRange = fromValue <= timeValue && timeValue <= toValue;
                        const isLastSegmentCount = isLastSegment && fromValue <= timeValue;

                        if (isLastSegmentCount || (!isLastSegment && withinSegmentRange)) {
                            count++;
                        }
                    }
                }
                arraySegment.segments.push({
                    count,
                    segmentLine: segmentList[i],
                    valuePosition: 0,
                });
                count = 0;
            }

            for (const segment of arraySegment.segments) {
                arraySegment.total += segment.count;
                if (segment.count === 0) {
                    arraySegment.totalZeroSegment += 8;
                }
            }

            for (let j = 0; j < arraySegment.segments.length; j++) {
                if (arraySegment.segments[j].count === 0) {
                    arraySegment.segments[j].valuePosition =
                        j === 0 ? 8 : 8 + arraySegment.segments[j - 1].valuePosition;
                } else {
                    arraySegment.segments[j].valuePosition =
                        j === 0
                            ? (arraySegment.segments[j].count / arraySegment.total) *
                              (100 - arraySegment.totalZeroSegment)
                            : (arraySegment.segments[j].count / arraySegment.total) *
                                  (100 - arraySegment.totalZeroSegment) +
                              arraySegment.segments[j - 1].valuePosition;
                }

                if (j === arraySegment.segments.length - 1) {
                    displayLineSegment = 'none';
                }
            }
        }
    }

    function convertToMinutes(time) {
        if (time?.split?.(':')?.map(Number)) {
            const [hours = 0, minutes = 0] = time.split(':').map(Number);
            return hours * 60 + minutes;
        }
        return 0;
    }

    type Segment = {
        count: number;
        segmentLine: typeof config.capsuleSegmentConfig.segment;
        valuePosition: number;
    };
    let arraySegment: {
        segments: Segment[];
        total: number;
        totalZeroSegment: number;
    };

    let displayLineSegment = '';

    function calculationMovingTarget() {
        let movingTarget = 0;
        let movingTargetStartTinme = config.capsuleSegmentConfig.movingTarget.startTime;
        let movingTargetEndTinme = config.capsuleSegmentConfig.movingTarget.endTime;
        let endPercentage = 0;
        let startPercentage = 0;
        let startTime = 0;
        let endTime = 0;
        let currentTime = new Date();

        const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes();
        const arraySegment = config.capsuleSegmentConfig.segment;

        if (
            currentMinutes < convertToMinutes(movingTargetStartTinme) ||
            currentMinutes > convertToMinutes(movingTargetEndTinme) ||
            arraySegment.length === 0
        ) {
            movingTarget = 0;
            config.capsuleSegmentConfig.movingTarget.movingTargetValue = movingTarget;
            return;
        } else {
            if (arraySegment.length === 1) {
                if (
                    currentMinutes > convertToMinutes(movingTargetStartTinme) &&
                    currentMinutes < convertToMinutes(arraySegment[0].timeValue)
                ) {
                    endPercentage = Number(arraySegment[0].percentValue);
                    startPercentage = 0;
                    startTime = convertToMinutes(movingTargetStartTinme);
                    endTime = convertToMinutes(arraySegment[0].timeValue);
                }
                if (
                    currentMinutes > convertToMinutes(arraySegment[0].timeValue) &&
                    currentMinutes < convertToMinutes(movingTargetEndTinme)
                ) {
                    endPercentage = 100;
                    startPercentage = Number(arraySegment[0].percentValue);
                    startTime = convertToMinutes(arraySegment[0].timeValue);
                    endTime = convertToMinutes(movingTargetEndTinme);
                }
            } else {
                for (let i = 0; i < arraySegment.length - 1; i++) {
                    if (
                        currentMinutes > convertToMinutes(movingTargetStartTinme) &&
                        currentMinutes < convertToMinutes(arraySegment[i].timeValue)
                    ) {
                        endPercentage = Number(arraySegment[i].percentValue);
                        startPercentage = 0;
                        startTime = convertToMinutes(movingTargetStartTinme);
                        endTime = convertToMinutes(arraySegment[i].timeValue);
                        break;
                    }

                    if (
                        currentMinutes >= convertToMinutes(arraySegment[i].timeValue) &&
                        currentMinutes <= convertToMinutes(arraySegment[i + 1].timeValue)
                    ) {
                        endPercentage = Number(arraySegment[i + 1].percentValue);
                        startPercentage = Number(arraySegment[i].percentValue);
                        startTime = convertToMinutes(arraySegment[i].timeValue);
                        endTime = convertToMinutes(arraySegment[i + 1].timeValue);
                        break;
                    }

                    if (
                        currentMinutes > convertToMinutes(arraySegment[arraySegment.length - 1].timeValue) &&
                        currentMinutes < convertToMinutes(movingTargetEndTinme)
                    ) {
                        endPercentage = 100;
                        startPercentage = Number(arraySegment[arraySegment.length - 1].percentValue);
                        startTime = convertToMinutes(arraySegment[arraySegment.length - 1].timeValue);
                        endTime = convertToMinutes(movingTargetEndTinme);
                        break;
                    }
                }
            }

            movingTarget = Math.round(
                ((endPercentage - startPercentage) * (currentMinutes - startTime)) / (endTime - startTime) +
                    startPercentage
            );
            config.capsuleSegmentConfig.movingTarget.movingTargetValue = movingTarget;
            return;
        }
    }

    function setColorAndBgColor() {
        let thresholdConfigArr = config.capsuleSegmentConfig.thresholdConfig.thresholdConfigItems;

        if (thresholdConfigArr) {
            if (!thresholdConfigArr.find(x => x.isValidItem.isValid === false)) {
                for (let i = 0; i < thresholdConfigArr.length; i++) {
                    const comparatorValue = thresholdConfigArr[i].comparatorThreshold;
                    const thresholdValue = Number(thresholdConfigArr[i].valueOfThreshold);
                    if (COMPARISON_OPERATORS_HASH[comparatorValue](dataPercent, thresholdValue)) {
                        config.backgroundColorPercent = thresholdConfigArr[i].bgColor;
                        break;
                    } else {
                        config.backgroundColorPercent = '#36A9E7';
                    }
                }
            } else {
                config.backgroundColorPercent = '#4d4d4d';
            }
        }
    }

    let temp;

    $: if (config) {
        if (config.radioTypeCapsule === 'time24h' || config.radioTypeCapsule === 'timemins') {
            calculationSegmentTime(config.capsuleSegmentConfig.segment);
            let segmentList = config.capsuleSegmentConfig.segment;
            temp = JSON.parse(JSON.stringify(arraySegment.segments));
            calculateSegmentsByMaxWT(segmentList);
        } else if (config.capsuleSegmentConfig.movingTarget) {
            calculationSegmentPercent(config.capsuleSegmentConfig.segment);
        }
    }

    function calculateSegmentsByMaxWT(segmentList) {
        if (config.capsuleSegmentConfig.lastSegmentDisplay && segmentList.length !== 0) {
            const maxValueInMin =
                config.radioTypeCapsule === 'timemins' ? getMaxValueTimeMins() : convertToMinutes(getMaxValueTime24h());
            const maxValueReturn =
                config.radioTypeCapsule === 'timemins' ? getMaxValueTimeMins() : getMaxValueTime24h();

            const isTimeMins = config.radioTypeCapsule === 'timemins';

            const targetSegmentIndex = segmentList.findIndex(segment => {
                const fromValue = isTimeMins ? segment.fromValue : convertToMinutes(segment.fromValue);
                const toValue = isTimeMins ? segment.toValue : convertToMinutes(segment.toValue);
                return fromValue <= maxValueInMin && maxValueInMin <= toValue;
            });

            if (targetSegmentIndex !== -1) {
                segmentList[targetSegmentIndex].toValue = maxValueReturn;
                segmentList = segmentList.slice(0, targetSegmentIndex + 1);
            } else if (
                maxValueInMin >
                (isTimeMins
                    ? segmentList[segmentList.length - 1].toValue
                    : convertToMinutes(segmentList[segmentList.length - 1].toValue))
            ) {
                segmentList[segmentList.length - 1].toValue = maxValueReturn;
            }

            calculationSegmentTime(segmentList);
        }
    }

    $: calculationTime = time => {
        // Specific for SOC14
        if (config?.dataSource?.includes('20250714_085704_500572576')) return Math.round((time / 60) * 100) / 100 + 'h';

        let m = time % 60;

        var h = (time - m) / 60;

        return (h === 0 ? '' : h.toString() + 'h') + (m === 0 ? '' : m.toString() + 'm');
    };

    onMount(async () => {
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, m_ => {});
        // subscription = wPipe(client.subscription(ScheduleLoadWidgetCommand, {}), wSubscribe(firstLoadWidget));

        const widgetConfig = document.getElementById('widget-config');
        if (widgetConfig) {
            widgetConfig.style.width = '690px';
        }
    });

    $: titleDateTime = dayjs(new Date()).format(config?.dateFormat == '' ? 'YYYY/MM' : config?.dateFormat);

    const client = get(gqlClientStore);
    // client.query(FirstLoadWidgetCommand, {}).toPromise().then(firstLoadWidget);
</script>

<div class={`flex h-full w-full flex-col dark:text-gray-300`} bind:this={widgetElement}>
    <div
        class={`flex items-center ${
            config.title || config.titleDatetimeVisible || config.showTotal ? 'min-h-[47px]' : ''
        }`}>
        <!-- Title -->
        <div
            class={`mx-[2%] h-auto w-auto flex-1 items-center p-[2%]  text-on-background  text-${config.titleAlign}` +
                (config.titleVisible ? '' : ' invisible') +
                (config.swapIndicatorAndTitle ? ' order-last' : '')}>
            <span class={config.title == '' ? 'hidden' : ''}>
                <p>{config.title}</p>
            </span>
            <span class={config.titleDatetimeVisible ? '' : 'hidden'}>
                <p>{@html icon(buttonIcon).html} {titleDateTime}</p>
            </span>
        </div>
        {#if config.showTotal}
            <div class={`pr-3 pt-3 ${config.titleAlign !== 'right' ? 'absolute right-0' : ''}`}>
                <span class="table-cell h-[35px] min-w-[35px] bg-[rgba(255,255,255,0.3)] text-center align-middle">
                    {config.radioTypeCapsule === 'timemins' ? listdataTimeMins.length : listdataTime24h.length}
                </span>
            </div>
        {/if}
    </div>
    <!-- Capsule -->
    <!-- Vertical -->
    {#if !config.widgetHorizontal}
        <!-- Time-->
        {#if config.radioTypeCapsule === 'timemins' || config.radioTypeCapsule === 'time24h'}
            <div class="relative mx-auto my-[2%] h-4/5 w-1/5 justify-center">
                <div>
                    {#each arraySegment.segments as segment, i}
                        <div
                            class="absolute z-10"
                            style="bottom: {segment.valuePosition}%; width: 130%; border-top: 2px {segment.segmentLine
                                .typeLineSeparator} white; display: {i === arraySegment.segments.length - 1
                                ? displayLineSegment
                                : ''};" />

                        <div
                            class="absolute z-10 text-sm"
                            style="bottom: calc({segment.valuePosition}% - 9px); left: 140%;">
                            {#if ['timemins', 'time24h'].includes(config.radioTypeCapsule)}
                                {i === arraySegment?.segments?.length - 1
                                    ? config.capsuleSegmentConfig.maxWtDisplay &&
                                      config.capsuleSegmentConfig.lastSegmentDisplay
                                        ? config.radioTypeCapsule === 'timemins'
                                            ? calculationTime(getMaxValueTimeMins())
                                            : getMaxValueTime24h()
                                        : ''
                                    : config.radioTypeCapsule === 'timemins'
                                    ? calculationTime(segment.segmentLine.toValue)
                                    : segment.segmentLine.toValue}
                            {/if}
                            {unit}
                        </div>
                    {/each}
                </div>
                <div
                    class="relative flex h-full w-full flex-col-reverse items-center overflow-hidden rounded-b-full rounded-t-full bg-[#4d4d4d]">
                    {#if arraySegment.segments.length === 0 && config.radioTypeCapsule === 'timemins'}
                        <div
                            class="flex w-full items-center justify-center rounded-full text-left text-white"
                            style="height: 100%;background-color:grey;">
                            0
                        </div>
                    {:else if arraySegment.segments.length === 1 && config.radioTypeCapsule === 'timemins'}
                        <div
                            class="flex items-center justify-center rounded-full text-center text-white"
                            style="height: 100%; background-color:{arraySegment.segments[0].segmentLine
                                .bgColorSegment};">
                            {arraySegment.segments[0].count}
                        </div>
                        <div class="z-10 w-full">
                            <div class="absolute -top-3 text-white" style="left: 130%;">
                                {arraySegment.segments[0].segmentLine.toValue}m
                            </div>
                        </div>
                    {:else}
                        {#each arraySegment.segments as segment, i}
                            <div
                                class="flex w-full items-center justify-center text-center"
                                style="height: {segment.count === 0
                                    ? 8
                                    : (segment.count / arraySegment.total) *
                                      (100 - arraySegment.totalZeroSegment)}%; background-color:{segment.segmentLine
                                    .bgColorSegment}; ">
                                {segment.count}
                            </div>
                        {/each}
                    {/if}
                </div>
            </div>
        {/if}

        <!-- Percentage-->
        {#if config.radioTypeCapsule === 'percentage' && config.capsuleSegmentConfig.movingTarget}
            <div class="relative mx-auto my-[2%] h-4/5 w-1/5 justify-center text-white">
                <div>
                    <div class="  flex h-full w-full">
                        {#each arraySegment.segments as segment, i}
                            <div
                                class="absolute z-10"
                                style="bottom: {segment.segmentLine
                                    .percentValue}%; width: 130%; border-top: 2px {segment.segmentLine
                                    .typeLineSegment} white;" />

                            <div
                                class="absolute z-10 text-sm"
                                style="bottom: calc({segment.segmentLine.percentValue}% - 9px);left: 140%;">
                                {segment.segmentLine.percentValue}{unit}
                            </div>
                        {/each}
                    </div>
                </div>
                <div
                    class="absolute z-10 w-full"
                    style="bottom: {config.capsuleSegmentConfig.movingTarget
                        .movingTargetValue}%;  border-top: 2px {config.capsuleSegmentConfig.movingTarget
                        .typeLineMovingTarget} {config.capsuleSegmentConfig.movingTarget
                        .movingTargetColor}; display: {config.capsuleSegmentConfig.movingtargetLineDisplay
                        ? ''
                        : 'none'};" />

                <div
                    class="item-center relative flex h-full w-full flex-col-reverse overflow-hidden rounded-b-full rounded-t-full bg-[#4d4d4d]">
                    <div
                        class="absolute flex w-full flex-col-reverse items-center justify-start rounded-t-full pb-4 text-xs"
                        style="height: {dataPercent}%; background-color:  {config.backgroundColorPercent};">
                        {config.widgetPassenger ? dataPercent + '%' : ''}
                    </div>
                </div>
            </div>
        {/if}
    {:else}
        <!-- Horizontal -->
        <!-- Time-->
        {#if config.radioTypeCapsule === 'timemins' || config.radioTypeCapsule === 'time24h'}
            <div
                class=" h-1/10 flex w-4/5 items-center justify-between text-xs"
                style="display: {config.widgetTextCapsule ? '' : 'none'};margin: 0 auto;">
                {#each percentileData as dataPercentile}
                    <div class=" whitespace-nowrap">
                        {dataPercentile.label}:{dataPercentile.calValue}
                    </div>
                {/each}
            </div>

            <div class="relative mx-auto my-[2%] h-1/5 w-4/5 justify-center">
                <div>
                    {#each arraySegment.segments as segment, i}
                        <div
                            class="absolute z-10"
                            style="left: {segment.valuePosition}%; height: 130%; border-right: 2px {segment.segmentLine
                                .typeLineSeparator} white; display: {i === arraySegment.segments.length - 1
                                ? displayLineSegment
                                : ''};" />

                        <div
                            class="absolute z-10 text-sm"
                            style="left: calc({segment.valuePosition}% - {config.radioTypeCapsule === 'timemins'
                                ? 11
                                : 17}px);top: 130%; ">
                            {#if ['timemins', 'time24h'].includes(config.radioTypeCapsule)}
                                {i === arraySegment?.segments?.length - 1
                                    ? config.capsuleSegmentConfig.maxWtDisplay &&
                                      config.capsuleSegmentConfig.lastSegmentDisplay
                                        ? config.radioTypeCapsule === 'timemins'
                                            ? calculationTime(getMaxValueTimeMins())
                                            : getMaxValueTime24h()
                                        : ''
                                    : config.radioTypeCapsule === 'timemins'
                                    ? calculationTime(segment.segmentLine.toValue)
                                    : segment.segmentLine.toValue}
                            {/if}

                            {unit}
                        </div>
                    {/each}
                </div>

                <div
                    class="item-center relative flex h-full w-full overflow-hidden rounded-l-full rounded-r-full bg-[#4d4d4d]">
                    {#if arraySegment.segments.length === 0 && config.radioTypeCapsule === 'timemins'}
                        <div
                            class=" flex items-center justify-center rounded-full text-left text-white"
                            style="width: 100%;background-color:grey;">
                            0
                        </div>
                    {:else if arraySegment.segments.length === 1 && config.radioTypeCapsule === 'timemins'}
                        <div
                            class="flex items-center justify-center rounded-full text-center text-white"
                            style="width: 100%; background-color:{arraySegment.segments[0].segmentLine
                                .bgColorSegment};">
                            {arraySegment.segments[0].count}
                        </div>
                        <div class="relative z-10">
                            <div class="absolute text-white" style="top: 130%;">
                                {arraySegment.segments[0].segmentLine.toValue}m
                            </div>
                        </div>
                    {:else}
                        {#each arraySegment.segments as segment, i}
                            <div
                                class="flex h-full w-full items-center justify-center"
                                style="width: {segment.count === 0
                                    ? 8
                                    : (segment.count / arraySegment.total) *
                                      (100 - arraySegment.totalZeroSegment)}%; background-color:{segment.segmentLine
                                    .bgColorSegment}; ">
                                {segment.count}
                            </div>
                        {/each}
                    {/if}
                </div>
            </div>
        {/if}

        <!-- Percentage-->
        {#if config.radioTypeCapsule === 'percentage' && config.capsuleSegmentConfig.movingTarget}
            <div class="relative mx-auto my-[2%] h-1/5 w-4/5 justify-center text-white">
                <div>
                    <div class="  flex h-full w-full">
                        {#each arraySegment.segments as segment, i}
                            <div
                                class="absolute z-10"
                                style="left: {segment.segmentLine
                                    .percentValue}%; height: 130%; border-right: 2px {segment.segmentLine
                                    .typeLineSegment} white;" />

                            <div
                                class="absolute text-sm"
                                style="left: calc({segment.segmentLine.percentValue}% - 11px);top: 130%;">
                                {segment.segmentLine.percentValue}{unit}
                            </div>
                        {/each}
                    </div>
                    <div
                        class="absolute z-10 h-full"
                        style="left: {config.capsuleSegmentConfig.movingTarget
                            .movingTargetValue}%;  border-right: 2px {config.capsuleSegmentConfig.movingTarget
                            .typeLineMovingTarget} {config.capsuleSegmentConfig.movingTarget
                            .movingTargetColor}; display: {config.capsuleSegmentConfig.movingtargetLineDisplay
                            ? ''
                            : 'none'};" />
                </div>

                <div
                    class="item-center relative flex h-full w-full overflow-hidden rounded-l-full rounded-r-full bg-[#4d4d4d]">
                    <div
                        class="absolute flex h-full items-center justify-start rounded-l-full rounded-r-full pl-4 text-xs"
                        style="width: {dataPercent}%; background-color:  {config.backgroundColorPercent};">
                        {config.widgetPassenger ? dataPercent + '%' : ''}
                    </div>
                </div>
            </div>
        {/if}
    {/if}
</div>
