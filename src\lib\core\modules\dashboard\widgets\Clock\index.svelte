<script lang="ts" context="module">
    import { faClock } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';

    dayjs.extend(dayjs.duration);

    export const widgetTitle = 'Clock';
    export const widgetIcon = faClock;
    export const widgetLimit = Number.POSITIVE_INFINITY;
    export const widgetCategory = 'Resource Management';
    export const componentName = new URL(import.meta.url).pathname;
    export const widgetMinWidth = 3;
    export const widgetMinHeight = 1;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;
</script>

<script lang="ts">
    import type { ClockConfig } from './WidgetConfig/index.svelte';
    export let config: ClockConfig;
    let clockWidgetElement: HTMLElement;
    let currentDatetime = dayjs(new Date()).format(config?.datetimeFormat == '' ? 'hh:mm:ss' : config?.datetimeFormat);
    function showTime() {
        currentDatetime = dayjs(new Date()).format(
            config?.datetimeFormat == '' ? 'D MMM YYYY HH:mm:ss' : config?.datetimeFormat
        );
        setTimeout(showTime, 1000);
    }
    showTime();
    let datetimeElementClass = 'text-center';
    $: if (config.datetimeAlign) {
        datetimeElementClass = 
            config.datetimeAlign === 'left'
                ? 'text-left'
                : config.datetimeAlign === 'right'
                  ? 'text-right'
                  : 'text-center';
    }
</script>

<div bind:this={clockWidgetElement} class="flex h-full w-full flex-col px-2">
    <header
        class="text-wrap text-on-background text-{config.titleAlign} {config.title == '' ? '' : 'p-1'}"
        style="font-size: {config.titleTextSize}px;">
        {config.title}
    </header>

    <div class="flex h-full w-full text-on-background">
        <span class="self-center w-full {datetimeElementClass}" style="font-size: {config.datetimeTextSize}px;">{currentDatetime}</span>
    </div>
</div>
