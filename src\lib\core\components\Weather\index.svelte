<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faCloudBolt,
        faCloudMoon,
        faCloudRain,
        faCloudShowersHeavy,
        faCloudSun,
        faMoon,
        faSun,
        faWind,
    } from '@fortawesome/free-solid-svg-icons';

    import dayjs from 'dayjs';
    import duration from 'dayjs/plugin/duration.js';
    import { writable } from 'svelte/store';

    const AREA = 'Ang Mo Kio';

    const weatherIcon = writable(icon(faCloudBolt, { classes: 'fa-spin' }));
    let isWeatherExistsForArea = false;

    dayjs.extend(duration);
    const pollDelay = dayjs.duration(30, 'minute').asMilliseconds();

    // Expose this as a configuration?
    let weatherMap = {
        'Clear (Day)': icon(faSun),
        'Clear (Night)': icon(faMoon),
        'Light Rain': icon(faCloudBolt),
        'Light Showers': icon(faCloudBolt),
        'Showers': icon(faCloudBolt),
        'Moderate Rain': icon(faCloudRain),
        'Heavy Rain': icon(faCloudShowersHeavy),
        'Thundery Showers': icon(faCloudBolt),
        'Heavy Thundery Showers with Gusty Winds': icon(faWind),
        'Cloudy': icon(faCloudBolt),
        'Partly Cloudy (Day)': icon(faCloudSun),
        'Partly Cloudy (Night)': icon(faCloudMoon),
        'Fair & Warm': icon(faCloudSun),
        'Fair (Day)': icon(faCloudBolt),
    };

    // Allow configuration for API and format of data? Would that be too granular?
    // Pull weather data from data.gov.sg
    function getWeatherData() {
        fetch('https://api.data.gov.sg/v1/environment/2-hour-weather-forecast')
            .then(response => response.json())
            .then(data => {
                isWeatherExistsForArea = false;
                data.items[0]?.forecasts?.forEach(forecast => {
                    if (forecast.area === AREA) {
                        isWeatherExistsForArea = true;
                        weatherIcon.update(() => weatherMap[forecast.forecast]);
                        return;
                    }
                });
            })
            .catch(err => {
                weatherIcon.update(() => icon(faCloudBolt));
                logger.error({ Error: err }, 'Weather API error');
            });
    }

    // getWeatherData();
    // setInterval(getWeatherData, pollDelay);
</script>

<script lang="ts">
    export let setClass: string = undefined;
</script>

<!-- <i class={setClass}>
    {#if isWeatherExistsForArea && $weatherIcon}
        {@html $weatherIcon['html']}
    {:else}
        {@html icon(faCloudBolt).html}
    {/if}
</i> -->
