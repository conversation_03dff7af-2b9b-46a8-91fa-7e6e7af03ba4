import FilterDisplay from './FilterDisplay';
import SOCViewTypeHandler from './SOCViewTypeHandler';

// SOC23
export function OverallLaboratoryWorkload(form, fields) {
    FilterDisplay(form, fields, ['TypeOfPatient', 'TypeOfLabTest', 'PriorityOfTest']);
}

// SOC24
export function LaboratoryWorkloadForPendingAdmission(form, fields) {
    FilterDisplay(form, fields, ['PriorityOfTest', 'TypeOfLabTest']);
}
export function LaboratoryWorkloadForPendingDischarges(form, fields) {
    FilterDisplay(form, fields, ['PriorityOfTest', 'TypeOfLabTest']);
}

// SOC25
export function BreakdownOfOverallLabWorkloadConfiguration(form, fields) {
    FilterDisplay(form, fields, ['TypeOfPatient', 'TypeOfLabTest']);
}

// SOC 20
export function OverviewOfXRayScansByPatientGroupConfiguration(form, fields) {
    SOCViewTypeHandler(form, fields);
    FilterDisplay(form, fields, ['PriorityOfTest', 'Doctor']);
}

// SOC 21
export function OverviewOfRadiologyNonXRayForEDConfiguration(form, fields) {
    SOCViewTypeHandler(form, fields);
    FilterDisplay(form, fields, ['PriorityOfTest', 'TypeOfPatient', 'Doctor', 'RadModality']);
}
export function OverviewOfRadiologyNonXRayForInpatientConfiguration(form, fields) {
    SOCViewTypeHandler(form, fields);
    FilterDisplay(form, fields, ['PriorityOfTest', 'TypeOfPatient', 'Doctor', 'RadModality']);
}
export function OverviewOfRadiologyNonXRayForOutpatientConfiguration(form, fields) {
    SOCViewTypeHandler(form, fields);
    FilterDisplay(form, fields, ['PriorityOfTest', 'TypeOfPatient', 'Doctor', 'RadModality']);
}

// SOC 22
export function WaitingAndServingTimeTrendsByRadModalitiesConfiguration(form, fields) {
    FilterDisplay(form, fields, ['PriorityOfTest', 'TypeOfPatient', 'RadModality']);
    for (let index = 0; index < fields.length; index++) {
        const element = fields[index];
        if (element?.field === 'StagesByService') {
            element.datasource = {
                ...element.datasource,
                viewFields: ['category'],
                valuesInArrayFilter: {
                    field: 'service',
                    value: ['Radiology'],
                },
            };
        }
    }
}

// SOC 2
export function PharmacyFilteringConfiguration1(form, fields) {
    FilterDisplay(form, fields, ['Pharmacy']);
}

// SOC 2
export function PharmacyFilteringConfiguration2(form, fields) {
    FilterDisplay(form, fields, ['Pharmacy']);
}

// SOC 2
export function PharmacyFilteringConfiguration3(form, fields) {
    FilterDisplay(form, fields, ['Pharmacy']);
}
