<script lang="ts" context="module">
    export type ExceptionModalOptions = {
        title: string;
        message: string;
        oncancel?: () => void;
    };

    export class ExceptionHandler {
        constructor(show: boolean = false, option: ExceptionModalOptions) {
            this.option = option;
            this.show = show;
        }

        showException(option: ExceptionModalOptions): void {
            this.show = true;
            this.option = option;
        }

        close(): void {
            this.show = false;
            this.option = null;
        }

        show: boolean = false;
        option: ExceptionModalOptions;
    }
</script>

<script lang="ts">
    import Modal from '$lib/core/components/Modal/index.svelte';
    export let option: ExceptionModalOptions;
    export let oncancel: () => void;
</script>

<Modal title={option.title.trim().length ? option.title : '-'} onClose={oncancel}>
    <div class="flex w-[285px] flex-col text-xs xl:text-sm">
        <p class="flex items-center justify-center px-4 pb-1 pt-3 text-center text-sm text-on-surface-2">
            {option.message}
        </p>
        <footer class="flex w-full items-center justify-end px-2 py-2">
            <button
                on:click={() => {
                    if (oncancel) oncancel();
                }}
                class="h-8 items-center gap-2 rounded bg-surface-1 px-2 text-on-surface-1 transition-all duration-200 ease-in-out hover:brightness-125"
                >Cancel</button>
        </footer>
    </div>
</Modal>
