<script lang="ts" context="module">
    import { writable, type Subscriber, type Updater, type Writable } from 'svelte/store';
    import { logger } from '$lib/stores/Logger';

    const src = new URL(import.meta.url).pathname;
    const [componentName] = src.split('/').slice(-2, -1);
    const log = logger.child({ src, componentName });

    // export const isDirty = writable(false);
    export const isDirty = (() => {
        let isDirty = false;

        const subscribers = new Set<Subscriber<boolean>>();

        const subscribe = (subscriberCallback: Subscriber<boolean>) => {
            subscribers.add(subscriberCallback);

            subscriberCallback(isDirty);

            return () => {
                subscribers.delete(subscriberCallback);
            };
        };

        const set = (value: boolean) => {
            isDirty = value;

            subscribers.forEach(s => {
                s(isDirty);
            });
        };

        const update = (updater: Updater<boolean>) => {
            set(updater(isDirty));
        };

        return {
            subscribe,
            set,
            update,
        };
    })();

    /**
     * $showTitle must be set after setting the title if you wish to hide your title as $title will automatically
     * set $showTitle to true whenever it is updated
     */
    export const showTitle = writable(true);

    export const title = (() => {
        let title = '';

        const subscribers = new Set<Subscriber<string>>();

        const subscribe = (subscriberCallback: Subscriber<string>) => {
            subscribers.add(subscriberCallback);

            subscriberCallback(title);

            return () => {
                subscribers.delete(subscriberCallback);
            };
        };

        const set = (value: string) => {
            title = value;
            showTitle.set(true);

            subscribers.forEach(s => {
                s(title);
            });
        };

        const update = (updater: Updater<string>) => {
            showTitle.set(true);
            set(updater(title));
        };

        return {
            subscribe,
            set,
            update,
        };
    })();
</script>

{#if $title && $showTitle}
    <div {...$$props}>
        {$title}
    </div>
{:else}
    <!-- Title not rendered due to settings  -->
{/if}
