<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { type ComponentType } from 'svelte/internal';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    export interface WidgetComponent {
        id?: string;
        componentDisplay?: ComponentType;
        extraProps?: Record<string, any>;
        children?: WidgetComponent[];
        checkVisible?: ((config: Record<string, any>) => boolean) | string;
        disabled?: ((config: Record<string, any>) => boolean) | string;
        onChange: (config: Record<string, any>) => void;
    }
</script>

<script lang="ts">
    export let config: any;
    export let widgetFields: WidgetComponent[];
</script>

{#each widgetFields as item}
    {#if !item.checkVisible || (typeof item.checkVisible === 'function' ? item.checkVisible(config) : config[item.checkVisible])}
        {#if !item.children}
            <svelte:component
                this={item.componentDisplay}
                {...item.extraProps}
                bind:value={config[item.id]}
                bind:checked={config[item.id]}
                disabled={item.disabled
                    ? typeof item.disabled === 'function'
                        ? item.disabled(config)
                        : config[item.disabled]
                    : false}
                onChange={() => {
                    if (typeof item.onChange === 'function') {
                        item.onChange(config);
                    }
                }} />
        {:else}
            <div class={item.extraProps?.customClass}>
                <svelte:self widgetFields={item.children} bind:config />
            </div>
        {/if}
    {/if}
{/each}
