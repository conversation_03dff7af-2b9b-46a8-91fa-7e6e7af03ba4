openapi: 3.0.3
info:
    title: Workflow Management
    version: 1.0.0
    description: API for workflow managment
servers:
    - url: http://wfm.dev2.ar2
paths:
    /v1/info/liveness:
        get:
            tags:
                - server
            summary: liveness
            description: API to check liveness of HTTP server
            responses:
                '200':
                    description: OK
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: Live since 2022-09-07T23:10:32+08:00
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is down
    /v1/info/readiness:
        get:
            tags:
                - server
            summary: readiness
            description: API to check readiness of HTTP server
            responses:
                '200':
                    description: OK
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: Ready since 2022-09-07T23:10:32+08:00
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/save:
        post:
            tags:
                - workflow
            summary: Save workflow
            description: API to save workflow. Saved workflows are editable.
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
                                schema_json:
                                    type: object
                                    properties:
                                        root:
                                            type: object
                                            properties:
                                                sequence:
                                                    type: object
                                                    properties:
                                                        elements:
                                                            type: array
                                                            items:
                                                                type: object
                                                                properties:
                                                                    activity:
                                                                        type: object
                                                                        properties:
                                                                            arguments:
                                                                                type: array
                                                                                items:
                                                                                    type: string
                                                                            name:
                                                                                type: string
                                                                            result:
                                                                                type: string
                                        variables:
                                            type: object
                                            properties:
                                                1_arg1:
                                                    type: string
                                                1_arg2:
                                                    type: string
                                                1_arg3:
                                                    type: string
                                                2_arg1:
                                                    type: number
                                                3_arg1:
                                                    type: string
                                                3_arg2:
                                                    type: string
                                                3_arg3:
                                                    type: string
                        example:
                            name: aoh_wf
                            schema_json:
                                root:
                                    sequence:
                                        elements:
                                            - activity:
                                                  arguments:
                                                      - 1_arg1
                                                      - 1_arg2
                                                      - 1_arg3
                                                  name: HttpCall
                                                  result: result1
                                            - activity:
                                                  arguments:
                                                      - 2_arg1
                                                  name: Delay
                                                  result: result2
                                            - activity:
                                                  arguments:
                                                      - 3_arg1
                                                      - 3_arg2
                                                      - 3_arg3
                                                  name: HttpCall
                                                  result: result3
                                variables:
                                    1_arg1: PATCH
                                    1_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                    1_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":300}]}'
                                    2_arg1: 60
                                    3_arg1: PATCH
                                    3_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                    3_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":420}]}'
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                Id:
                                                    type: string
                                                Name:
                                                    type: string
                                                Schema_json:
                                                    type: object
                                                    properties:
                                                        root:
                                                            type: object
                                                            properties:
                                                                sequence:
                                                                    type: object
                                                                    properties:
                                                                        elements:
                                                                            type: array
                                                                            items:
                                                                                type: object
                                                                                properties:
                                                                                    activity:
                                                                                        type: object
                                                                                        properties:
                                                                                            arguments:
                                                                                                type: array
                                                                                                items:
                                                                                                    type: string
                                                                                            name:
                                                                                                type: string
                                                                                            result:
                                                                                                type: string
                                                        variables:
                                                            type: object
                                                            properties:
                                                                1_arg1:
                                                                    type: string
                                                                1_arg2:
                                                                    type: string
                                                                1_arg3:
                                                                    type: string
                                                                2_arg1:
                                                                    type: number
                                                                3_arg1:
                                                                    type: string
                                                                3_arg2:
                                                                    type: string
                                                                3_arg3:
                                                                    type: string
                                                editable:
                                                    type: boolean
                                    sent_at:
                                        type: string
                            example:
                                data:
                                    - Id: a408232b-588a-46b4-a733-a1d6d161b017
                                      Name: aoh_wf
                                      Schema_json:
                                          root:
                                              sequence:
                                                  elements:
                                                      - activity:
                                                            arguments:
                                                                - 1_arg1
                                                                - 1_arg2
                                                                - 1_arg3
                                                            name: HttpCall
                                                            result: result1
                                                      - activity:
                                                            arguments:
                                                                - 2_arg1
                                                            name: Delay
                                                            result: result2
                                                      - activity:
                                                            arguments:
                                                                - 3_arg1
                                                                - 3_arg2
                                                                - 3_arg3
                                                            name: HttpCall
                                                            result: result3
                                          variables:
                                              1_arg1: PATCH
                                              1_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                              1_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":300}]}'
                                              2_arg1: 60
                                              3_arg1: PATCH
                                              3_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                              3_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":420}]}'
                                      editable: true
                                sent_at: 2023-08-25T08:07:44Z
                '400':
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: invalid request body
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/publish:
        post:
            tags:
                - workflow
            summary: Publish workflow
            description: API to publish workflow. Once the workflow is published, editing is not allowed.
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
                                schema_json:
                                    type: object
                                    properties:
                                        root:
                                            type: object
                                            properties:
                                                sequence:
                                                    type: object
                                                    properties:
                                                        elements:
                                                            type: array
                                                            items:
                                                                type: object
                                                                properties:
                                                                    activity:
                                                                        type: object
                                                                        properties:
                                                                            arguments:
                                                                                type: array
                                                                                items:
                                                                                    type: string
                                                                            name:
                                                                                type: string
                                                                            result:
                                                                                type: string
                                        variables:
                                            type: object
                                            properties:
                                                1_arg1:
                                                    type: string
                                                1_arg2:
                                                    type: string
                                                1_arg3:
                                                    type: string
                                                2_arg1:
                                                    type: number
                                                3_arg1:
                                                    type: string
                                                3_arg2:
                                                    type: string
                                                3_arg3:
                                                    type: string
                        example:
                            name: aoh_wf
                            schema_json:
                                root:
                                    sequence:
                                        elements:
                                            - activity:
                                                  arguments:
                                                      - 1_arg1
                                                      - 1_arg2
                                                      - 1_arg3
                                                  name: HttpCall
                                                  result: result1
                                            - activity:
                                                  arguments:
                                                      - 2_arg1
                                                  name: Delay
                                                  result: result2
                                            - activity:
                                                  arguments:
                                                      - 3_arg1
                                                      - 3_arg2
                                                      - 3_arg3
                                                  name: HttpCall
                                                  result: result3
                                variables:
                                    1_arg1: PATCH
                                    1_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                    1_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":300}]}'
                                    2_arg1: 60
                                    3_arg1: PATCH
                                    3_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                    3_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":420}]}'
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                Id:
                                                    type: string
                                                Name:
                                                    type: string
                                                Schema_json:
                                                    type: object
                                                    properties:
                                                        root:
                                                            type: object
                                                            properties:
                                                                sequence:
                                                                    type: object
                                                                    properties:
                                                                        elements:
                                                                            type: array
                                                                            items:
                                                                                type: object
                                                                                properties:
                                                                                    activity:
                                                                                        type: object
                                                                                        properties:
                                                                                            arguments:
                                                                                                type: array
                                                                                                items:
                                                                                                    type: string
                                                                                            name:
                                                                                                type: string
                                                                                            result:
                                                                                                type: string
                                                        variables:
                                                            type: object
                                                            properties:
                                                                1_arg1:
                                                                    type: string
                                                                1_arg2:
                                                                    type: string
                                                                1_arg3:
                                                                    type: string
                                                                2_arg1:
                                                                    type: number
                                                                3_arg1:
                                                                    type: string
                                                                3_arg2:
                                                                    type: string
                                                                3_arg3:
                                                                    type: string
                                                editable:
                                                    type: boolean
                                    sent_at:
                                        type: string
                            example:
                                data:
                                    - Id: a408232b-588a-46b4-a733-a1d6d161b017
                                      Name: aoh_wf
                                      Schema_json:
                                          root:
                                              sequence:
                                                  elements:
                                                      - activity:
                                                            arguments:
                                                                - 1_arg1
                                                                - 1_arg2
                                                                - 1_arg3
                                                            name: HttpCall
                                                            result: result1
                                                      - activity:
                                                            arguments:
                                                                - 2_arg1
                                                            name: Delay
                                                            result: result2
                                                      - activity:
                                                            arguments:
                                                                - 3_arg1
                                                                - 3_arg2
                                                                - 3_arg3
                                                            name: HttpCall
                                                            result: result3
                                          variables:
                                              1_arg1: PATCH
                                              1_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                              1_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":300}]}'
                                              2_arg1: 60
                                              3_arg1: PATCH
                                              3_arg2: http://app-room-mgmt.dev2.ar2/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                              3_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":420}]}'
                                      editable: false
                                sent_at: 2023-08-25T08:07:44Z
                '400':
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: invalid request body
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/start/workflow/{workflowid}/incident/{incidentid}:
        post:
            tags:
                - workflow
            summary: Start workflow
            description: API to start executing workflow by workflow template id for given incident id
            parameters:
                - name: workflowid
                  in: path
                  required: true
                  schema:
                      type: string
                      example: 72421cc8-82a8-45c8-ad52-e188db8fa473
                - name: incidentid
                  in: path
                  required: true
                  schema:
                      type: string
                      example: INCIDENT-0001
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                id:
                                                    type: string
                                                run_id:
                                                    type: string
                                    sent_at:
                                        type: string
                            example:
                                data:
                                    - id: 900622a2-50f8-4d22-89e7-9e95fa10bcf5
                                      run_id: 5a6f609c-3056-4ca7-ad56-08f56094ceb8
                                sent_at: 2023-08-25T08:08:46Z
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/workflow/{workflowid}:
        get:
            tags:
                - workflow
            summary: Get workflow
            description: API to retrieve workflow
            parameters:
                - name: workflowid
                  in: path
                  required: true
                  schema:
                      type: string
                      example: 20edce2b-97fa-459d-b38a-d2e2fce51069
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                root:
                                                    type: object
                                                    properties:
                                                        sequence:
                                                            type: object
                                                            properties:
                                                                elements:
                                                                    type: array
                                                                    items:
                                                                        type: object
                                                                        properties:
                                                                            activity:
                                                                                type: object
                                                                                properties:
                                                                                    arguments:
                                                                                        type: array
                                                                                        items:
                                                                                            type: string
                                                                                    id:
                                                                                        type: string
                                                                                    name:
                                                                                        type: string
                                                                                    result:
                                                                                        type: string
                                                specVersion:
                                                    type: string
                                                variables:
                                                    type: object
                                                    properties:
                                                        1_arg1:
                                                            type: string
                                                        1_arg2:
                                                            type: string
                                                        1_arg3:
                                                            type: string
                                                        2_arg1:
                                                            type: string
                                                        3_arg1:
                                                            type: string
                                                        3_arg2:
                                                            type: string
                                                        3_arg3:
                                                            type: string
                                    sent_at:
                                        type: string
                                        example: 2023-09-04T08:59:31Z
                            example:
                                data:
                                    - root:
                                          sequence:
                                              elements:
                                                  - activity:
                                                        arguments:
                                                            - 1_arg1
                                                            - 1_arg2
                                                            - 1_arg3
                                                        id: eea8276b-7a89-4a03-b63d-6a76128e64bb
                                                        name: Activity
                                                        result: result1
                                                  - activity:
                                                        arguments:
                                                            - 2_arg1
                                                        id: f507b1de-5ad5-408e-a396-8f64120d8ee3
                                                        name: Activity1
                                                        result: result2
                                                  - activity:
                                                        arguments:
                                                            - 3_arg1
                                                            - 3_arg2
                                                            - 3_arg3
                                                        id: 689442e1-acda-48e7-9ae7-6479f686bbea
                                                        name: Activity2
                                                        result: result3
                                      specVersion: '2.0'
                                      variables:
                                          1_arg1: PATCH
                                          1_arg2: http://app-room-mgmt.common-ucs:7070/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                          1_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":300}]}'
                                          2_arg1: '60'
                                          3_arg1: PATCH
                                          3_arg2: http://app-room-mgmt.common-ucs:7070/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                          3_arg3: '{"requestor":"10206739","announcements":[{"message":"test announcement 2","relative_from":"start","relative_time_in_seconds":420}]}'
                                sent_at: 2023-09-04T08:59:31Z
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/designer/{workflowid}:
        get:
            tags:
                - workflow
            summary: Get workflow designer
            description: API to retrieve workflow designer
            parameters:
                - name: workflowid
                  in: path
                  required: true
                  schema:
                      type: string
                      example: 20edce2b-97fa-459d-b38a-d2e2fce51069
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                cells:
                                                    type: array
                                                    items:
                                                        type: object
                                                        properties:
                                                            angle:
                                                                type: number
                                                                example: 0
                                                            attrs:
                                                                type: object
                                                                properties:
                                                                    background:
                                                                        type: object
                                                                        properties:
                                                                            fill:
                                                                                type: string
                                                                    data:
                                                                        type: object
                                                                        properties:
                                                                            activityOptionsString:
                                                                                type: string
                                                                            args:
                                                                                type: array
                                                                                items:
                                                                                    type: string
                                                                            options:
                                                                                type: object
                                                                                properties:
                                                                                    activityId:
                                                                                        type: string
                                                                                    disableEagerExecution:
                                                                                        type: boolean
                                                                                    heartbeatTimeout:
                                                                                        nullable: true
                                                                                    retryPolicy:
                                                                                        type: object
                                                                                        properties:
                                                                                            backoffCoefficient:
                                                                                                nullable: true
                                                                                            initialInterval:
                                                                                                nullable: true
                                                                                            maximumAttempt:
                                                                                                nullable: true
                                                                                            maximumInterval:
                                                                                                nullable: true
                                                                                            name:
                                                                                                type: string
                                                                                    retryPolicyString:
                                                                                        type: string
                                                                                    scheduleToCloseTimeout:
                                                                                        nullable: true
                                                                                    startToCloseTimeout:
                                                                                        type: number
                                                                                    taskQueue:
                                                                                        type: string
                                                                                    useAdvancedRetryPolicy:
                                                                                        type: boolean
                                                                                    waitForCancellation:
                                                                                        nullable: true
                                                                            result:
                                                                                type: string
                                                                            useDetailedActivityOptions:
                                                                                type: boolean
                                                                    label:
                                                                        type: object
                                                                        properties:
                                                                            text:
                                                                                type: string
                                                            condition:
                                                                type: object
                                                                properties:
                                                                    text:
                                                                        type: string
                                                            id:
                                                                type: string
                                                            position:
                                                                type: object
                                                                properties:
                                                                    x:
                                                                        type: number
                                                                    y:
                                                                        type: number
                                                            size:
                                                                type: object
                                                                properties:
                                                                    height:
                                                                        type: number
                                                                    width:
                                                                        type: number
                                                            source:
                                                                type: object
                                                                properties:
                                                                    id:
                                                                        type: string
                                                            target:
                                                                type: object
                                                                properties:
                                                                    id:
                                                                        type: string
                                                            type:
                                                                type: string
                                                            z:
                                                                type: number
                                                name:
                                                    type: string
                                                type:
                                                    type: string
                                                workflowData:
                                                    type: object
                                                    properties:
                                                        specVersion:
                                                            type: string
                                                        variables:
                                                            type: array
                                                            items:
                                                                type: object
                                                                properties:
                                                                    key:
                                                                        type: string
                                                                    value:
                                                                        type: string
                                    sent_at:
                                        type: string
                            example:
                                data:
                                    - cells:
                                          - angle: 0
                                            attrs: {}
                                            id: 3edb8623-0520-4395-89df-cadb8dd909ea
                                            position:
                                                x: 370
                                                y: 265
                                            size:
                                                height: 30
                                                width: 30
                                            type: wf.Start
                                            z: 6
                                          - angle: 0
                                            attrs:
                                                background:
                                                    fill: '#ffffff'
                                                data:
                                                    activityOptionsString: N/A
                                                    args:
                                                        - 2_arg1
                                                    options:
                                                        activityId: ''
                                                        disableEagerExecution: false
                                                        heartbeatTimeout: null
                                                        retryPolicy:
                                                            backoffCoefficient: null
                                                            initialInterval: null
                                                            maximumAttempt: null
                                                            maximumInterval: null
                                                            name: ''
                                                        retryPolicyString: ''
                                                        scheduleToCloseTimeout: null
                                                        startToCloseTimeout: 0
                                                        taskQueue: ''
                                                        useAdvancedRetryPolicy: false
                                                        waitForCancellation: null
                                                    result: result2
                                                    useDetailedActivityOptions: false
                                                label:
                                                    text: Activity1
                                            id: f507b1de-5ad5-408e-a396-8f64120d8ee3
                                            position:
                                                x: 610
                                                y: 240
                                            size:
                                                height: 80
                                                width: 80
                                            type: wf.Activity
                                            z: 17
                                          - angle: 0
                                            attrs: {}
                                            id: a01ee30c-717b-444a-83b1-4b0688e151c4
                                            position:
                                                x: 900
                                                y: 265
                                            size:
                                                height: 30
                                                width: 30
                                            type: wf.End
                                            z: 22
                                          - angle: 0
                                            attrs:
                                                background:
                                                    fill: '#ffffff'
                                                data:
                                                    activityOptionsString: N/A
                                                    args:
                                                        - 1_arg1
                                                        - 1_arg2
                                                        - 1_arg3
                                                    options:
                                                        activityId: ''
                                                        disableEagerExecution: false
                                                        heartbeatTimeout: null
                                                        retryPolicy:
                                                            backoffCoefficient: null
                                                            initialInterval: null
                                                            maximumAttempt: null
                                                            maximumInterval: null
                                                            name: ''
                                                        retryPolicyString: ''
                                                        scheduleToCloseTimeout: null
                                                        startToCloseTimeout: 0
                                                        taskQueue: ''
                                                        useAdvancedRetryPolicy: false
                                                        waitForCancellation: null
                                                    result: result1
                                                    useDetailedActivityOptions: false
                                                label:
                                                    text: Activity
                                            id: eea8276b-7a89-4a03-b63d-6a76128e64bb
                                            position:
                                                x: 470
                                                y: 240
                                            size:
                                                height: 80
                                                width: 80
                                            type: wf.Activity
                                            z: 26
                                          - attrs: {}
                                            condition:
                                                text: 'true'
                                            id: 7122311f-0f44-4fbc-a9d5-dc13a3a979f0
                                            source:
                                                id: 3edb8623-0520-4395-89df-cadb8dd909ea
                                            target:
                                                id: eea8276b-7a89-4a03-b63d-6a76128e64bb
                                            type: wf.Flow
                                            z: 27
                                          - attrs: {}
                                            condition:
                                                text: 'true'
                                            id: b6ee4d46-1417-4cd0-9e2d-91ce893758bd
                                            source:
                                                id: eea8276b-7a89-4a03-b63d-6a76128e64bb
                                            target:
                                                id: f507b1de-5ad5-408e-a396-8f64120d8ee3
                                            type: wf.Flow
                                            z: 27
                                          - angle: 0
                                            attrs:
                                                background:
                                                    fill: '#ffffff'
                                                data:
                                                    activityOptionsString: N/A
                                                    args:
                                                        - 3_arg1
                                                        - 3_arg2
                                                        - 3_arg3
                                                    options:
                                                        activityId: ''
                                                        disableEagerExecution: false
                                                        heartbeatTimeout: null
                                                        retryPolicy:
                                                            backoffCoefficient: null
                                                            initialInterval: null
                                                            maximumAttempt: null
                                                            maximumInterval: null
                                                            name: ''
                                                        retryPolicyString: ''
                                                        scheduleToCloseTimeout: null
                                                        startToCloseTimeout: 0
                                                        taskQueue: ''
                                                        useAdvancedRetryPolicy: false
                                                        waitForCancellation: null
                                                    result: result3
                                                    useDetailedActivityOptions: false
                                                label:
                                                    text: Activity2
                                            id: 689442e1-acda-48e7-9ae7-6479f686bbea
                                            position:
                                                x: 760
                                                y: 240
                                            size:
                                                height: 80
                                                width: 80
                                            type: wf.Activity
                                            z: 28
                                          - attrs: {}
                                            condition:
                                                text: 'true'
                                            id: 5793c8a5-5687-4dc7-a13a-47394461c05c
                                            source:
                                                id: f507b1de-5ad5-408e-a396-8f64120d8ee3
                                            target:
                                                id: 689442e1-acda-48e7-9ae7-6479f686bbea
                                            type: wf.Flow
                                            z: 29
                                          - attrs: {}
                                            condition:
                                                text: 'true'
                                            id: 8c1684d6-edf9-4bb5-9eff-8aa6f5c7ea1b
                                            source:
                                                id: 689442e1-acda-48e7-9ae7-6479f686bbea
                                            target:
                                                id: a01ee30c-717b-444a-83b1-4b0688e151c4
                                            type: wf.Flow
                                            z: 29
                                      name: Test
                                      type: bpmn
                                      workflowData:
                                          specVersion: '2.0'
                                          variables:
                                              - key: 1_arg1
                                                value: PATCH
                                              - key: 1_arg2
                                                value: http://app-room-mgmt.common-ucs:7070/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                              - key: 1_arg3
                                                value: '{"requestor":"10206739","announcements":[{"message":"test announcement 1","relative_from":"start","relative_time_in_seconds":300}]}'
                                              - key: 2_arg1
                                                value: '60'
                                              - key: 3_arg1
                                                value: PATCH
                                              - key: 3_arg2
                                                value: http://app-room-mgmt.common-ucs:7070/rooms/a9f42b88-5f51-4262-95c4-4e9edb464b11
                                              - key: 3_arg3
                                                value: '{"requestor":"10206739","announcements":[{"message":"test announcement 2","relative_from":"start","relative_time_in_seconds":420}]}'
                                sent_at: 2023-09-04T08:59:51Z
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/terminate/workflow/{workflowid}:
        delete:
            tags:
                - workflow
            summary: Terminate Workflow
            description: API to terminate running workflow with reason
            parameters:
                - name: workflowid
                  in: path
                  required: true
                  schema:
                      type: string
                      example: be817eaa-a252-4470-bcd4-e09dc50e624c
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                reason:
                                    type: string
                        example:
                            reason: terminated from frontend
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                            example:
                                sent_at: 2023-09-13T10:04:52Z
                '400':
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: invalid request body
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/signal/workflow/{workflowid}/signalname/{signalname}:
        post:
            tags:
                - workflow
            summary: Signal Workflow
            description: API to send data into workflow using signal name
            parameters:
                - name: workflowid
                  in: path
                  required: true
                  schema:
                      type: string
                      example: 6a7b1af0-9e8b-4a0b-b7fe-6ac332cff578_6
                - name: signalname
                  in: path
                  required: true
                  schema:
                      type: string
                      example: Checklist
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                data:
                                    type: object
                                    properties:
                                        field_0hm6j8d:
                                            type: array
                                            items:
                                                type: string
                        example:
                            data:
                                field_0hm6j8d:
                                    - checked1
                                    - checked2
                                    - checked3
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                field_0hm6j8d:
                                                    type: array
                                                    items:
                                                        type: string
                                    sent_at:
                                        type: string
                            example:
                                data:
                                    - field_0hm6j8d:
                                          - checked1
                                          - checked2
                                          - checked3
                                sent_at: 2023-10-20T08:26:39Z
                '400':
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: invalid request body
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/form/{formid}:
        get:
            tags:
                - form
            summary: Get form template
            description: API to retrieve form template
            parameters:
                - name: formid
                  in: path
                  required: true
                  schema:
                      type: string
                      example: 17f694c0-0b89-425a-aaa7-5651c9cec549
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                id:
                                                    type: string
                                                type:
                                                    type: string
                                                exporter:
                                                    type: object
                                                    properties:
                                                        name:
                                                            type: string
                                                        version:
                                                            type: string
                                                components:
                                                    type: array
                                                    items:
                                                        type: object
                                                        properties:
                                                            id:
                                                                type: string
                                                            key:
                                                                type: string
                                                            type:
                                                                type: string
                                                            label:
                                                                type: string
                                                            layout:
                                                                type: object
                                                                properties:
                                                                    row:
                                                                        type: string
                                                                    columns:
                                                                        type: string
                                                            values:
                                                                type: array
                                                                items:
                                                                    type: object
                                                                    properties:
                                                                        label:
                                                                            type: string
                                                                        value:
                                                                            type: string
                                                schemaVersion:
                                                    type: number
                                    sent_at:
                                        type: string
                            example:
                                data:
                                    - id: Form_1hf6nou
                                      type: default
                                      exporter:
                                          name: form-js (https://demo.bpmn.io)
                                          version: 1.4.0
                                      components:
                                          - id: Field_1ktx3tl
                                            key: checklist_482e3j
                                            type: checklist
                                            label: Checklist
                                            layout:
                                                row: Row_19m5v7b
                                                columns: null
                                            values:
                                                - label: checkbox1
                                                  value: checkbox1
                                      schemaVersion: 12
                                sent_at: 2023-09-04T08:59:51Z
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
    /v1/form/save:
        post:
            tags:
                - form
            summary: Save form template
            description: API to save form template. Saved form template are editiable.
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
                                form_json:
                                    type: array
                                    items:
                                        type: object
                                        properties:
                                            id:
                                                type: string
                                            type:
                                                type: string
                                            exporter:
                                                type: object
                                                properties:
                                                    name:
                                                        type: string
                                                    version:
                                                        type: string
                                            components:
                                                type: array
                                                items:
                                                    type: object
                                                    properties:
                                                        id:
                                                            type: string
                                                        key:
                                                            type: string
                                                        type:
                                                            type: string
                                                        label:
                                                            type: string
                                                        layout:
                                                            type: object
                                                            properties:
                                                                row:
                                                                    type: string
                                                                columns:
                                                                    type: string
                                                        values:
                                                            type: array
                                                            items:
                                                                type: object
                                                                properties:
                                                                    label:
                                                                        type: string
                                                                    value:
                                                                        type: string
                                            schemaVersion:
                                                type: number
                        example:
                            name: TestForm
                            form_json:
                                id: Form_1hf6nou
                                type: default
                                exporter:
                                    name: form-js (https://demo.bpmn.io)
                                    version: 1.4.0
                                components:
                                    - id: Field_1ktx3tl
                                      key: checklist_482e3j
                                      type: checklist
                                      label: Checklist
                                      layout:
                                          row: Row_19m5v7b
                                          columns: null
                                      values:
                                          - label: checkbox1
                                            value: checkbox1
                                schemaVersion: 12
            responses:
                '200':
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                id:
                                                    type: string
                                                name:
                                                    type: string
                                                form_json:
                                                    type: array
                                                    items:
                                                        type: object
                                                        properties:
                                                            id:
                                                                type: string
                                                            type:
                                                                type: string
                                                            exporter:
                                                                type: object
                                                                properties:
                                                                    name:
                                                                        type: string
                                                                    version:
                                                                        type: string
                                                            components:
                                                                type: array
                                                                items:
                                                                    type: object
                                                                    properties:
                                                                        id:
                                                                            type: string
                                                                        key:
                                                                            type: string
                                                                        type:
                                                                            type: string
                                                                        label:
                                                                            type: string
                                                                        layout:
                                                                            type: object
                                                                            properties:
                                                                                row:
                                                                                    type: string
                                                                                columns:
                                                                                    type: string
                                                                        values:
                                                                            type: array
                                                                            items:
                                                                                type: object
                                                                                properties:
                                                                                    label:
                                                                                        type: string
                                                                                    value:
                                                                                        type: string
                                                            schemaVersion:
                                                                type: number
                                                components_key:
                                                    type: array
                                                    items:
                                                        type: string
                                    sent_at:
                                        type: string
                            example:
                                data:
                                    - id: d27a7e49-df9f-462a-9bbf-15fc7926fc3f
                                      name: TestForm
                                      form_json:
                                          - id: Form_1hf6nou
                                            type: default
                                            exporter:
                                                name: form-js (https://demo.bpmn.io)
                                                version: 1.4.0
                                            components:
                                                - id: Field_1ktx3tl
                                                  key: checklist_482e3j
                                                  type: checklist
                                                  label: Checklist
                                                  layout:
                                                      row: Row_19m5v7b
                                                      columns: null
                                                  values:
                                                      - label: checkbox1
                                                        value: checkbox1
                                            schemaVersion: 12
                                      component_keys:
                                          - checklist_xmj6qe
                                sent_at: 2023-09-04T08:59:51Z
                '500':
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    sent_at:
                                        type: string
                                    errors:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                                message:
                                                    type: string
                            example:
                                sent_at: '2023-06-15T18:43:30Z'
                                errors:
                                    - message: service is not yet ready
                '503':
                    description: Service Unavailable
                    content:
                        text/plain:
                            schema:
                                type: string
                            examples:
                                response:
                                    value: service is not yet ready
