<script lang="ts">
    import Ripple from './index.svelte';

    let triggerRipple: EventListener;

    function init(node) {
        // Wrap around lambda func due to triggerRipple being initially undefined
        node.addEventListener('click', event => {
            triggerRipple(event);
        });

        return;
    }
</script>

<!-- Overflow Hidden & Relative are REQUIRED for Ripple to work -->
<div use:init data-testid="ripple example" class="relative h-96 w-96 overflow-hidden bg-primary">
    <Ripple bind:ripple={triggerRipple} />
</div>
