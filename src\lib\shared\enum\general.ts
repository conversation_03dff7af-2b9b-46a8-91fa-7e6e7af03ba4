export enum VIEW_CHART_BY_ENUM {
    DAY = 'day',
    WEEK = 'week',
    MONTH = 'month',
    QUARTER = 'quarter',
    YEAR = 'year',
}

// Final decision: singular
export enum MATERIAL_TYPE {
    CONSUMABLE = 'consumable',
    DRUG = 'drug',
}

export enum MATERIAL_VIEW_TYPE {
    GROUP_VIEW = 'group_view',
    DETAIL_VIEW = 'detail_view',
}

export enum INVENTORY_CONFIGURATION_TYPE {
    CONSUMABLE_THRESHOLD = 'consumable_threshold',
}

export enum CFG_USER_FILTERING_LABEL_FROM {
    'cfg_user_filtering.filter_label' = 'cfg_user_filtering.filter_label',
    'cfg_user_filtering.key_value' = 'cfg_user_filtering.key_value',
    cfg_params = 'cfg_params',
}

export enum CFG_PARAMETER_NAMES {
    BLOOD_IDEAL_STOCK_MIN_STOCK_DAYS_LEFT = 'BloodIdealStockMinStockDaysLeft', // Blood Ideal Stock Min Stock Days Left
    CONSUMABLE_CURRENT_STOCK_NETFLOW_DAYS_LEFT = 'ConsumableCurrentStockNetflowDaysLeft', // Consumable Current Stock Netflow Days Left
    DRUG_CURRENT_STOCK_NETFLOW_DAYS_LEFT = 'DrugCurrentStockNetflowDaysLeft', // Drug Current Stock Netflow Days Left
    CRITICAL_CONSUMABLE = 'CriticalSupplyConfiguration', // Critical Supply Configuration
    CRITICAL_CONSUMABLE_LABEL = 'CriticalSupplyConfigurationLabel', // Critical Supply Configuration Label
    OUTBREAK_SOP_NOTIFICATION_EMAIL = 'OutbreakSOPNotificationEmail', // Outbreak SOP Notification Email
    HAZE_SOP_NOTIFICATION_EMAIL = 'HazeSOPNotificationEmail', // Haze SOP Notification Email
    CONSUMABLE_OUTFLOW_THRESHOLD_COLOR = 'ConsumableOutflowThresholdColor', // Consumable Outflow Threshold Color
    INV_STOCK_TAKE_CONFIGURATION = 'InvStockTakeConfiguration', // Inventory Stock Take Configuration
    CONSUMABLE_CURRENT_STOCK_BALANCE_PAR = 'ConsumableCurrentStockBalancePAR', // Consumable Current Stock Balance PAR
    DRUG_CURRENT_STOCK_BALANCE_PAR = 'DrugCurrentStockBalancePAR', // Drug Current Stock Balance PAR
    X_DAYS_ISSUE_RATE = 'XdaysIssueRate', // X Days Issue Rate
    VIEW_SITUATION_VS_INCIDENT_LABEL = 'ViewInventorySituationVsIncidentLabel', // View Inventory Situation Vs Incident Label
    VIEW_SITUATION_VS_INCIDENT = 'ViewInventorySituationVsIncident', // View Inventory Situation Vs Incident
    INV_CRITICAL_EQUIPMENT_THRESHOLD = 'InvCriticalEquipmentThreshold', // Inventory Critical Equipment Threshold
    DYNAMIC_COLUMNS = 'DynamicColumns', // Dynamic Columns
    SPECIALTY_ENABLEMENT = 'ListOfSpecialtyEnablement', // SOC codeset List of Specialty
    CLINIC_ENABLEMENT = 'ListOfClinicsEnablement', // SOC codeset List of Clinics
    LAB_TEST_ENABLEMENT = 'LabTestEnablement', // SOC codeset List of Lab Test
    RAD_MODALITY_ENABLEMENT = 'RadModalityEnablement', // SOC codeset Radiology Modality
    TEST_PRIORITY_ENABLEMENT = 'TestPriorityEnablement', // SOC codeset List of Test Priority
    VISIT_TYPE_GRP_MAPPING_ENABLEMENT = 'VisitTypeGrpMappingEnablement', // SOC codeset Visit Type Group Mapping
    CLASS_OF_PATIENT_ENABLEMENT = 'ClassOfPatientEnablement', // SOC codeset Class of Patient
    APPOINTMENT_STATUS_ENABLEMENT = 'AppointmentStatusEnablement', // SOC codeset Appointment Status
    DISCHARGE_DISPOSITION_ENABLEMENT = 'DischargeDispositionEnablement', // SOC codeset Discharge Disposition
    QUEUE_SERIES_ENABLEMENT = 'QueueSeriesEnablement', // SOC codeset Queue Series
    REFERRAL_ENABLEMENT = 'ReferralEnablement', // SOC codeset Referral
    APPOINTMENT_RATIONALE_ENABLEMENT = 'AppointmentRationaleEnablement', // SOC codeset Appointment Rationale
    DOCTOR_ENABLEMENT = 'DoctorEnablement', // SOC codeset Doctor
    PHARMACY_ENABLEMENT = 'PharmacyEnablement', // SOC codeset Pharmacy
    TYPE_OF_PATIENT_ENABLEMENT = 'TypeOfPatientEnablement', // SOC codeset Type of Patient
    PHARMACY_EXPECTED_X_HOUR = 'PharmacyExpectedXHour', // SOC16 cfg parameter
    GRACE_PERIOD = 'GracePeriod', // SOC
    PATIENT_COUNT_DROP_OFF_TIME_CONFIGURATION = 'PatientCountDropOffTimeConfiguration', // SOC
}

export enum SYS_DATA_SOURCE_MAIN_CATEGORY {
    ORIGINAL_DATA_SOURCE = 'Original Data Source',
    AGGREGATED_DATA_SOURCE = 'Aggregated Data Source',
    PARAMETER = 'SOP Parameter',
}

export enum SYS_DATA_SOURCE_SUB_CATEGORY {
    THRESHOLD = 'Threshold',
}

export enum SCOPE {
    CREATE = 'd9bafc22-0126-4e79-9142-d38f3219a1b2',
    DELETE = 'a4fc7ad3-e123-49e9-b60f-3e2b05635f4c',
    UPDATE = 'b1bd468a-8dcd-4380-be60-7883c86ad896',
    READ = '52d0a33c-6c32-4391-98a0-e2df61ac9f35',
    TOGGLE = '39d23edc-c84c-4304-8f11-1f7916ddfb0f',
    COPY = '1062665c-299b-46d5-ad77-763db04985d3',
    PREVIEW = 'c45fd1ba-51c3-455e-b082-f23cc2da220d',
    ACTIVATION = 'df69d823-8221-46a3-880f-fb733cf3e2bc',
    DASHBOARD_UI = 'e60823e0-758a-4c0a-b471-83cf50f5e478',
    READ_UI_ENDPOINT = '30c1511b-eb52-4f40-b5a9-b27b422e201c',
    READ_UI_PARAMETER = '9ecf4b9a-2351-4f0c-a9d4-017df2243d77',
    READ_UI_INCIDENT_CAT = 'f2eb06d5-54ab-4d5f-b1b7-96c0a9f61db0',
    STUDIO_WORKFLOW = 'bbfc1f28-545f-40df-8927-ead12a05ab75',
    CFG_PARAMETER_LIST_CATEGORY = 'fa49e581-5b0e-4d7d-bada-fc4f1895781c',
    AUDIT_LOG_UI_INCIDENT = 'f8f0511f-9b82-434b-bb94-b40bd1a05d79',
    RESPONSE_TRACKING_UI_INCIDENT = '29cab7a6-8b62-405d-960f-fd427747a454',
}

export enum RESOURCE {
    BATCH_JOB = 'f58d3fd5-74ae-474e-acee-a2fb60322720',
    INCIDENT = '7a263b2b-4d89-416a-93dd-39c7d380bd01',
    INCIDENT_TYPE = '0eb45731-46d4-49eb-975a-477456d1085e',
    INCIDENT_CAT = 'd536fe1f-f3a1-48f8-a7f7-1ef1cc2e0254',
    INCIDENT_NOTIFICATION = 'f53dc574-e603-4889-a912-fd3cd19b69b0',
    INCIDENT_POPUP_MESSAGE = '8cde942b-122a-4e20-b11a-0640d80b4f99',
    DATA_SOURCE = 'd50a26d8-16ab-47c8-8f97-4439bed07ad5',
    TRIGGER_RULE = 'd4c3bc27-1465-40a9-b4d9-e65081cc5ebf',
    WORKFLOW = '48822582-160d-4b70-8ead-efd2fb9902ef',
    COMMENU = '234a6a49-372c-46f6-b1f5-65c776717a1f',
    DASHBOARD = '74f61803-efb3-47d3-8c6e-b5972b8ee0d3',
    CFG_PARAMETER = 'ec9222b5-23b9-4a3d-b3aa-d467c2c89c75',
    INVENTORY = '593ec936-4acc-42d1-920f-c2217af75063',
    UPLOAD = '7627d0b3-fa3b-4bd9-9621-364059074bcc',

    INVENTORY_COMPARED_INCIDENT = 'd32c8975-f6ef-49f6-abb6-6a10ecb2f99b',
    INVENTORY_MOVEMENT_TYPE = '4811af8b-4b3e-40b2-b3fc-9302e870f728',

    INVENTORY_CONSUMABLE_ITEM = '60f2dd4f-81ef-4fd5-ac0e-1f342f18e630',
    INVENTORY_CONSUMABLE_STOCK_THRESHOLD = '7bbe2752-d546-44b0-bfac-1cc1eda5ca6a',
    INVENTORY_COST_CENTRE_GROUPING = '480fd6fc-cc8c-4294-ad5f-aba503465013',
    INVENTORY_CRITICAL_CONSUMABLES_GROUPING = 'b3e2a968-55fc-4c02-93ab-030ca8982923',

    INVENTORY_DRUG_ITEM = 'c7b69162-1222-4db4-b0fb-f60c2c9c082c',
    INVENTORY_CRITICAL_DRUG_GROUPING = '78b7018f-d544-41ab-b69c-11494a682c2c',
    INVENTORY_DRUG_THRESHOLD = '47208228-6f3b-41e0-a883-5f0efe73d8da',

    INVENTORY_BLOOD_STOCK_THRESHOLD = 'c3b81dba-1995-4454-b7e7-ebe0319a9b2a',

    INVENTORY_CRITICAL_EQUIPMENT = '976f72a1-8936-4cf2-a471-4624297f92dd',
    INVENTORY_EQUIPMENT_THRESHOLD = 'b0b8b772-ec61-4cea-bec8-9f24e3d7a9ab',
    INVENTORY_SEARCH_FOR_EQUIPMENT = '86eddc0c-ac86-4383-98bd-e5d3930ee0ae',

    INVENTORY_OUTBREAK_SOP_REPORT = 'e9a27ae6-17ec-47ad-a438-e5c54d1344cc',
    INVENTORY_HAZE_SOP_REPORT = '12e6a588-c999-414e-87af-8f4dc8843f38',
    INCIDENT_DEPARTMENT = 'a0942134-1e75-4440-af40-1c9bf7c77a1f',

    RENCI = 'd1e4dd1d-daa3-4278-a4bf-f02affaf0b24',

    SOC_CLINIC = '9bf90473-9350-4e77-b045-885be305af18',
    SOC_APPOINTMENT_TYPE = 'eced848e-82b9-4efe-a42e-3c076388681b',
    SOC_APPOINTMENT_RATIONALE = '5db486d1-da81-4883-8e74-c29918a6ebe4',
    SOC_APPOINTMENT_STATUS = '045d5952-3170-4ad8-9a0e-44c863e7d910',
    SOC_CE_STATUS = 'a5e74e40-8296-41af-861b-8c6702d3fdff',
    SOC_CLASS_OF_PATIENT = 'c3658828-bf4b-4da5-a38d-7e7ad191c384',
    SOC_DISCHARGES = '3023e46a-ce4f-4373-bec1-5fe3f8641217',
    SOC_DOCTOR = 'ba032776-def7-463b-b3bc-ebfa42350723',
    SOC_ORIGIN_LOCATION = '7c8265c7-6c2e-443e-a3d6-28a733b50502',
    SOC_PHARMACY = '0f3930bc-52e8-4639-a3d5-1ce4f7035423',
    SOC_PHARMACY_QUEUE_SERIES = '97b5b8a8-0003-487b-8535-04e238ce06d7',
    SOC_PRIORITY_OF_TEST = '63136214-9924-406c-9c3a-668a942e1e15',
    SOC_RAD_MODALITY = '4c9a7ac5-80c9-4eff-8872-144b5fce881f',
    SOC_REFERRALS = '41cf1c93-5601-4087-9798-137124e38e6b',
    SOC_SERVICE_TYPE = 'b3eea979-92d3-4008-bf6c-f91fe0d8ab71',
    SOC_SESSION_TYPE = '0e3c3fb7-cb91-46e9-ad20-26c922b4b828',
    SOC_SPECIALTY = '331c90a6-cf78-4b8e-ac50-93c3fc157f15',
    SOC_STAGES_BY_SERVICE = 'b6a30c78-be59-48ff-9ccb-c766d80eaff9',
    SOC_TREATMENT_CATEGORY = '3c56b52a-f95e-428d-b1e0-1d8227cf9470',
    SOC_TYPE_OF_LAB_TEST = '6bc92b4c-d75a-4340-87a9-5e992a55c1ab',
    SOC_TYPE_OF_PATIENT = '33b3fd25-a10b-405e-8a15-bf189547250e',
    SOC_VISIT_TYPE = 'c0db619d-5df7-4e8d-9787-b86b403f72fc',
    SOC_SCHEDULE_ROOM_MAPPING = 'af787a67-ce29-49f1-a2d5-0184a5e5d337',
    SOC_CLINIC_PHARMACY_MAPPING = '3a1d9b6c-799f-4014-ade7-39b21faf22cc',
    SOC_CONSULTATION_ROOM_SERVICE_TYPE = '2b715b25-0893-49ec-a443-2095a9254e0c',
    SOC_VISIT_TYPE_GRP_MAPPING = 'e61ab346-6806-435b-9f83-fb36f4795b67',
    SOC_NURSING_OU_TYPE = 'ae2642af-5d69-4f50-970c-3b44bb1c916f',
    SOC_NURSING_OU = '769bf58e-680d-4317-9d00-71d1610ae5ac',
    SOC_NUMBER_OF_PATIENTS_WITH_MEDICATION_ORDER = '197ffbcb-f0f3-418a-b539-e7514069b610',
    SOC_WALK_IN_PATIENT = '2b4a3a17-64ce-4ba5-93e8-dc4df664b3f6',
    SOC_EXPT_PTS_IN_PHARMACY = '2a5ddaa6-5cba-4732-9395-177782349336',
    SOC_YEARLY_CLINIC_ROOM_FILE = '40d80811-3739-48e5-b81c-b63f18591dd9',
    SOC_DOCTOR_FILE = '9d03c643-89d3-480f-8197-5ea5ccaf04ee',
    SOC_PUBLIC_HOLIDAY = 'eaacc1ac-50a7-4bc4-a515-8395210d0cde',
    OT_ROOM_CONFIGURATION = '7ad288db-f48f-4ec4-83f1-365b011ad8a5',
    OT_SURGERY_SERVICE = '271ce583-c63f-43d3-8712-d7af33514dc4',
}

export enum WIDGET_TYPE {
    SIMPLE_CHART = '971ddaf2-33ca-4e23-9907-82bdb3dd78fc',
    ADVANCED_TABLE = 'dd221fbe-9595-438a-a00d-6f00fcea216c',
}

export enum DATA_SOURCE {
    LE04_RESPONSE_TRACKING = '20250117_030517_3084560598',
}

export enum CONFIG_TRIGGER_TABLE_LIST {
    BloodStockThreshold = 'cc3_hoc.txn_hcll_blood,cc3_hoc.agg_hcll_blood_supply', // SOP Blood Stock Threshold
    ConsumableOutflowThresholdConfiguration = 'cc3_hoc.agg_sapmm_material_consumption', // Consumable Outflow Threshold Configuration
    CriticalEquipmentRequired = 'cc3_hoc.agg_rtls_critical_equipment', // Critical Equipment Required
    MaterialThresholdConfiguration = 'cc3_hoc.mst_sapmm_material,cc3_hoc.agg_sapmm_material', //  Consumable + Drug Threshold Configuration
}

export const CFG_PARAM_TRIGGER_TABLE_LIST = {
    [CFG_PARAMETER_NAMES.CONSUMABLE_CURRENT_STOCK_BALANCE_PAR]:
        'cc3_hoc.mst_sapmm_material,cc3_hoc.agg_sapmm_material,' + 'cc3_hoc.agg_sapmm_material_by_location', // Consumable Current Stock Balance
    [CFG_PARAMETER_NAMES.DRUG_CURRENT_STOCK_BALANCE_PAR]:
        'cc3_hoc.mst_sapmm_material,cc3_hoc.agg_sapmm_material,' + 'cc3_hoc.agg_sapmm_material_by_location', // Drug Current Stock Balance
    [CFG_PARAMETER_NAMES.CONSUMABLE_OUTFLOW_THRESHOLD_COLOR]: 'cc3_hoc.agg_sapmm_material_consumption', // Consumable Outflow Threshold Configuration
    [CFG_PARAMETER_NAMES.INV_CRITICAL_EQUIPMENT_THRESHOLD]: 'cc3_hoc.f_get_critical_equipments', // Current Number of Equipment Global Threshold
    [CFG_PARAMETER_NAMES.PHARMACY_EXPECTED_X_HOUR]: 'cc3_hoc.cfg_parameter', // SOC16, TODO: update related table later
    [CFG_PARAMETER_NAMES.GRACE_PERIOD]: 'cc3_hoc.cfg_parameter,cfg_eqms_consultation_room_service_type', // SOC
    [CFG_PARAMETER_NAMES.PATIENT_COUNT_DROP_OFF_TIME_CONFIGURATION]: 'cc3_hoc.cfg_parameter', // SOC
} as const;

// For SOC US
export const SOC_CFG_POPUP_KEYS = {
    clinics: 'Clinics',
    specialty: 'Specialty',
    visitTypeGrpMapping: 'VisitTypeGroup',
    classOfPatient: 'ClassOfPatient',
    apptRationale: 'AppointmentRationale',
    priorityOfTest: 'PriorityOfTest',
    doctor: 'Doctor',
    radModality: 'RadModality',
    typeOfPatient: 'TypeOfPatient',
    labTest: 'TypeOfLabTest',
    originLocation: 'OriginLocation',
    CEStatus: 'CEStatus',
    stagesByService: 'StagesByService',
    pharmacy: 'Pharmacy',
    apptStatus: 'AppointmentStatus',
};

export const SOC_CFG_CONFIG_NAMES = {
    clinics: CFG_PARAMETER_NAMES.CLINIC_ENABLEMENT,
    specialty: CFG_PARAMETER_NAMES.SPECIALTY_ENABLEMENT,
    visitTypeGrpMapping: CFG_PARAMETER_NAMES.VISIT_TYPE_GRP_MAPPING_ENABLEMENT,
    classOfPatient: CFG_PARAMETER_NAMES.CLASS_OF_PATIENT_ENABLEMENT,
    apptRationale: CFG_PARAMETER_NAMES.APPOINTMENT_RATIONALE_ENABLEMENT,
    priorityOfTest: CFG_PARAMETER_NAMES.TEST_PRIORITY_ENABLEMENT,
    doctor: CFG_PARAMETER_NAMES.DOCTOR_ENABLEMENT,
    radModality: CFG_PARAMETER_NAMES.RAD_MODALITY_ENABLEMENT,
    typeOfPatient: CFG_PARAMETER_NAMES.TYPE_OF_PATIENT_ENABLEMENT,
    labTest: CFG_PARAMETER_NAMES.LAB_TEST_ENABLEMENT,
    pharmacy: CFG_PARAMETER_NAMES.PHARMACY_ENABLEMENT,
    apptStatus: CFG_PARAMETER_NAMES.APPOINTMENT_STATUS_ENABLEMENT,
};

export const SOC_CFG_POPUP_CHANGE_FIELDS = {
    clinics: { from: 'code', off: 'desc', on: 'category' },
    specialty: { from: 'code', off: 'desc', on: 'category' },
    visitTypeGrpMapping: { from: 'code', off: 'desc', on: 'visit_type_group_display_name' },
    classOfPatient: { from: 'code', off: 'desc', on: 'category' },
    apptRationale: { from: 'code', off: 'desc', on: 'category' },
    priorityOfTest: { from: 'code', off: 'desc', on: 'category' },
    doctor: { from: 'code', off: 'desc', on: 'category' },
    radModality: { from: 'code', off: 'desc', on: 'category' },
    typeOfPatient: { from: 'code', off: 'desc', on: 'category' },
    labTest: { from: 'code', off: 'desc', on: 'category' },
    pharmacy: { from: 'code', off: 'desc', on: 'category' },
    apptStatus: { from: 'code', off: 'desc', on: 'category' },
    originLocation: { from: 'code', off: 'category', on: 'category' },
    CEStatus: { from: 'category', off: 'category', on: 'category' },
    stagesByService: { from: 'category', off: 'category', on: 'category' },
};

export const SOC_CFG_POPUP_TABLE_NAMES = {
    clinics: 'cfg_ngemr_clinic',
    specialty: 'cfg_ngemr_specialty',
    visitTypeGrpMapping: 'cfg_ngemr_visit_type_grp_mapping',
    classOfPatient: 'cfg_ngemr_level_of_care',
    apptRationale: 'cfg_ngemr_appt_rationale',
    priorityOfTest: 'cfg_ngemr_test_priority',
    doctor: 'cfg_ngemr_doctor',
    radModality: 'cfg_ngemr_rad_modality',
    typeOfPatient: 'cfg_ngemr_patient_type',
    labTest: 'cfg_ngemr_lab_test_type',
    pharmacy: 'cfg_ngemr_pharmacy',
    apptStatus: 'cfg_ngemr_appt_status',
    originLocation: 'cfg_ngemr_origin_loc',
    CEStatus: 'cfg_ngemr_ce_status',
    stagesByService: 'cfg_ngemr_stg_services',
};

export const SOC_CFG_POPUP_INFO = Object.keys(SOC_CFG_POPUP_KEYS).map(key => ({
    key: SOC_CFG_POPUP_KEYS[key],
    cfgName: SOC_CFG_CONFIG_NAMES[key] || null,
    tableName: SOC_CFG_POPUP_TABLE_NAMES[key],
    from: SOC_CFG_POPUP_CHANGE_FIELDS[key].from,
    off: SOC_CFG_POPUP_CHANGE_FIELDS[key].off,
    on: SOC_CFG_POPUP_CHANGE_FIELDS[key].on,
}));

export const DISPLAY_FILTER_TABLES = [
    'cc3_hoc.cfg_ngemr_clinic',
    'cc3_hoc.cfg_ngemr_level_of_care',
    'cc3_hoc.cfg_ngemr_specialty',
    'cc3_hoc.cfg_ngemr_appt_rationale',
    'cc3_hoc.cfg_ngemr_pharmacy',
    'cc3_hoc.cfg_ngemr_referral_source',
    'cc3_hoc.cfg_ngemr_test_priority',
    'cc3_hoc.cfg_ngemr_doctor',
    'cc3_hoc.cfg_ngemr_discharge_disposition',
    'cc3_hoc.cfg_ngemr_appt_status',
    'cc3_hoc.cfg_ngemr_rad_modality',
    'cc3_hoc.cfg_ngemr_patient_type',
    'cc3_hoc.cfg_ngemr_lab_test_type',
];
