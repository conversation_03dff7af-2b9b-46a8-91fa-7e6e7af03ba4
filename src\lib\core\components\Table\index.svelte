<!--
    @component
    Table

    Props:
    - `columns`: table column
    - `rows`: table data
    - `pageSize`: number of row per page
    - `thresholds`: column : threshold , to highlight the cell when above this value, only for number
    - `appendClass`: Pass in a class string as `appendClass` to append to the built-in component styles.

    Attribute Forwarding:
    - `class`: Pass in a class string to override the default built-in component styles.

    - Usage:
      ```tsx
      <script lang="ts">
        import Table from '$lib/components/basic/Table/index.svelte';
      </script>
      <Table columns={columnFields} rows={data} />
    ```
-->
<script lang="ts" context="module">
    export type ColumnDefinition = {
        title?: string;
        type?: string;
        key?: string;
        component?: any;
        props?: (v: any) => any | undefined;
        headerStyle?: string;
        value?: (row: any) => string; // function to transform the input data into a string for display
        sortable?: boolean;
        asc?: boolean;
        rowStyle?: (v: any) => string;
        colStyle?: (v: any) => string;
        altRowStyle?: (v: any) => string;
    };

    import { logger } from '$lib/stores/Logger';
    const log = logger.child({ src: new URL(import.meta.url).pathname });
</script>

<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faAngleDown, faAngleUp } from '@fortawesome/free-solid-svg-icons';
    import { createEventDispatcher } from 'svelte';

    import Button from '$lib/core/components/Button/index.svelte';
    import Pagination from '$lib/core/components/Table/Pagination/index.svelte';

    const dispatch = createEventDispatcher();

    export let rows: any[];
    export let columns: ColumnDefinition[];
    export let pageSize = 5;
    export let thresholds: object = undefined;
    export let tableHeaderId = '';
    export let tableRowsId = '';
    export let sortingIconTestId = 'sorting-icon';
    export let usePagination = true;
    export let showBorder = true;

    let page = 0;

    $: columns = columns;
    $: rows = rows;
    $: filteredResults = paginate(rows, pageSize, page);

    $: if (pageSize) {
        filteredResults = paginate(rows, pageSize, page);
    }

    function paginate(results, pageSize, page) {
        let size = parseInt(pageSize);
        if (results && results.length > 0) {
            let num_of_pages = Math.ceil(results.length / size);
            let offset = page * size;
            let limit = page === num_of_pages - 1 ? results.length - offset : size;

            return results.slice(offset, offset + limit);
        }
        return results;
    }

    function SortTable(prop: any, asc: boolean) {
        let sortModifier = asc ? 1 : -1;

        return function (a, b) {
            if (a[prop] > b[prop]) return 1 * sortModifier;
            else if (a[prop] < b[prop]) return -1 * sortModifier;
            return 0;
        };
    }
</script>

{#if rows && columns}
    <!-- Header -->
    <div class="text-on-surface flex h-full w-full flex-1 flex-col">
        <div class="table w-full border-collapse">
            <div data-testid={tableHeaderId} class="table-header-group">
                {#each columns as column, y}
                    <span
                        class="relative table-cell p-2 text-xs {column.headerStyle
                            ? column.headerStyle
                            : 'bg-secondary-container text-on-background'}">
                        <div class="flex flex-row justify-between">
                            <span>{column.title}</span>
                            {#if column.sortable}
                                <Button
                                    data-testid={sortingIconTestId}
                                    setClass="right-0 flex flex-col items-center justify-center text-xs hover:text-on-surface/[0.9]"
                                    on:click={() => {
                                        if (column.asc === undefined) column.asc = true;
                                        column.asc = !column.asc;

                                        for (let col of columns) {
                                            if (col.title !== column.title) col.asc = undefined;
                                        }
                                        rows.sort(SortTable(column.key, column.asc));
                                        filteredResults = paginate(rows, pageSize, page);
                                    }}>
                                    {#if column.asc === false}
                                        {@html icon(faAngleUp).html}
                                    {:else}
                                        {@html icon(faAngleDown).html}
                                    {/if}
                                </Button>
                            {/if}
                        </div>
                    </span>
                {/each}
            </div>

            <div data-testid={tableRowsId} class="text-on-surface table-row-group w-full overflow-y-scroll">
                {#each filteredResults as row, y}
                    <div class="table-row {showBorder ? 'border border-outline' : ''}">
                        {#each columns as column}
                            {#if column.type === 'checkbox'}
                                <label
                                    class="{column.colStyle ? column.colStyle(row) : ''} table-cell p-1 {y % 2 === 0
                                        ? 'bg-secondary-container'
                                        : ''}">
                                    <input
                                        type="checkbox"
                                        class="mt-1 pt-5"
                                        value={row[column.key]}
                                        bind:checked={row[column.key]}
                                        on:change={_event => {
                                            dispatch('checked', { key: column.key, row });
                                        }} />
                                </label>
                            {:else}
                                <span
                                    class="{column.colStyle ? column.colStyle(row) : ''}
                            {thresholds &&
                                    thresholds[column.key] &&
                                    parseInt(column.value(row)) < thresholds[column.key]
                                        ? 'text-error'
                                        : 'text-on-surface'} table-cell p-1 text-sm {y % 2 === 0
                                        ? column.altRowStyle
                                            ? column.altRowStyle(row)
                                            : 'bg-secondary-container'
                                        : column.rowStyle
                                        ? column.rowStyle(row)
                                        : ''}">{column.value(row)}</span>
                            {/if}
                        {/each}
                    </div>
                {/each}
            </div>
        </div>
        {#if usePagination}
            <div class="bg-surface-high mt-auto flex h-fit w-full justify-end">
                <Pagination class="w-fit" bind:page {pageSize} noOfContent={rows.length} />
            </div>
        {/if}
    </div>
{/if}
