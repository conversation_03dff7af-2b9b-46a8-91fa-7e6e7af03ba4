<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faFileCircleCheck,
        faFolderClosed,
        faNoteSticky,
        faPencilAlt,
        faPlus,
        faRefresh,
        faTrashAlt,
        faSortUp,
        faSortDown,
    } from '@fortawesome/free-solid-svg-icons';
    import {
        Button,
        Heading,
        Table,
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Checkbox,
        Radio,
        Toolbar,
    } from 'flowbite-svelte';
    import { apiGet, apiPost, convertISOToUTC } from '$routes/(app)/(opshub)/audittrail/utils/function';
    import { onMount } from 'svelte';
    import { ColumnType } from '$lib/shared/enum/search';
    import { isLoading } from '$lib/stores/Loading';
    import { locations } from '$lib/stores/Locations';
    import { get } from 'svelte/store';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import Search from '$lib/shared/components/search/Search.svelte';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
    import dayjs from 'dayjs';
    import { saveResponseTrackingLabel } from '../../utils/file_utils';
    import { TriggerService } from '$lib/service/trigger';
    import { INCIDENT_TABLES, RESPONSE_TRACKING_TABLES } from '../../schema';
    import { hasAccess } from '$lib/shared/util/validation';

    export let data;
    let selectedRow: any;
    let searchText: string = '';
    const INCIDENT_URL = get(locations).incident_monitoring_web;
    let mohReportData = [];
    let dataFilter = [];
    let advancedSearchRecords: any[];
    const columns = [
        { name: 'report_id', label: 'Report ID', searchType: ColumnType.String, type: 'Config' },
        { name: 'name', label: 'Incident Name', searchType: ColumnType.String },
        { name: 'incident_at', label: 'Incident Date/Time', type: ColumnType.Date, searchType: ColumnType.Date },
        {
            name: 'last_action_by_user',
            label: 'Last Action By (Name)',
        },
        { name: 'last_action_by_role', label: 'Last Action By Role', searchType: ColumnType.String },
        { name: 'incident_category_name', label: 'Last Action', searchType: ColumnType.String },
        { name: 'report_status', label: 'Report Status', searchType: ColumnType.String },
        { name: '', label: 'Send Report to MOH', type: ColumnType.Boolean },
    ];
    const searchColumns = columns
        .filter(i => i.name !== 'is_ongoing_incident' && i.name !== 'is_response_tracking_view')
        .map(i => ({ name: i.name, label: i.label, type: i.searchType }));

    let sortKey: string = 'name';
    let sortDirection: number = 1;
    let checkedRadio = '';
    let triggerResetSearch = false;

    const mohReportList = async () => {
        let res = await apiGet(`${INCIDENT_URL}/moh-report`);
        if (res && res.data) {
            console.log({ data: res.data });
            rebuildData(res.data);
            mohReportData = dataFilter = res.data;
        }
    };

    const rebuildData = dataToRebuild => {};

    const sortFn = (a, b) => {
        const aVal = a[sortKey];
        const bVal = b[sortKey];
        if (!aVal || aVal < bVal) {
            return -sortDirection;
        } else if (!bVal || aVal > bVal) {
            return sortDirection;
        }
        return 0;
    };

    const sortTable = key => {
        if (sortKey === key) {
            sortDirection = -sortDirection;
        } else {
            sortKey = key;
            sortDirection = 1;
        }
        dataFilter = dataFilter.sort(sortFn);
    };

    onMount(async () => {
        await mohReportList();
        defaultSort();
    });

    const defaultSort = () => {
        sortKey = 'incident_at';
        sortDirection = 1;
        sortTable(sortKey);
    };

    const onRefresh = async () => {
        isLoading.set(true);
        await mohReportList();
        //filteredRecords = null;
        defaultSort();
        searchText = '';
        selectedRow = null;
        triggerResetSearch = true;
        isLoading.set(false);
    };

    const onEdit = () => {
        if (checkSelectedRow()) {
            location.href = `/incident/im-moh-report/edit/${selectedRow.id}`;
        }
    };

    const checkSelectedRow = () => {
        if (!selectedRow) {
            modalTitle = 'Notification';
            deleteMessage = 'Please select an incident!';
            onlyOkBtn = true;
            openDelete = true;
            return false;
        }
        return true;
    };
    const onAdd = () => {
        location.href = `/incident/im-incident/new`;
    };

    const onSaveTable = async () => {
        if (mohReportData.length) {
            isLoading.set(true);
            let objResponseTracking =
                mohReportData.find(item => item.id == checkedRadio) ||
                mohReportData.find(i => ['Open', 'Reopen'].includes(i.incident_status)) ||
                {};

            await saveResponseTrackingLabel({
                incidentNo: objResponseTracking.incident_number,
                name: objResponseTracking.name,
            });

            let body = JSON.stringify(mohReportData);
            let url = INCIDENT_URL + '/bulk';
            try {
                const res = await apiPost(url, body, 'PATCH');
                if (res) {
                    await onRefresh();
                    modalTitle = 'Notification';
                    deleteMessage = 'Configuration is saved successfully.';
                    onlyOkBtn = true;
                    openDelete = true;
                }
            } catch (error) {
                isLoading.set(false);
                console.error('Error:', error);
            }

            // no need to wait for the response to update date
            TriggerService.trigger([INCIDENT_TABLES, RESPONSE_TRACKING_TABLES].join(','));
        }
    };

    const onSearch = () => {
        isLoading.set(true);
        dataFilter = advancedSearchRecords || mohReportData;
        if (searchText) {
            dataFilter = dataFilter.filter(item => {
                return (
                    item.name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    item.incident_number?.toLowerCase().includes(searchText.toLowerCase()) ||
                    item.incident_category_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    item.incident_type_name?.toLowerCase().includes(searchText.toLowerCase()) ||
                    item.incident_status?.toLowerCase().includes(searchText.toLowerCase())
                );
            });
        }
        isLoading.set(false);
    };

    let openDelete = false;
    let deleteMessage = '';
    let onlyOkBtn = false;
    let modalTitle = '';
    let headSelected;

    const onDelete = async () => {
        if (checkSelectedRow()) {
            if (selectedRow.incident_status !== 'Draft') {
                modalTitle = 'Notification';
                deleteMessage = 'Only Draft status can be deleted.';
                onlyOkBtn = true;
                openDelete = true;
                return;
            }

            modalTitle = 'Confirmation';
            deleteMessage = `Are you sure you want to delete the selected record(s)?<br />Click Yes to confirm.`;
            onlyOkBtn = false;
            openDelete = true;
        }
    };

    const confirmDelete = async () => {
        isLoading.set(true);

        const response = await fetch(`${INCIDENT_URL}/${selectedRow.id}`, {
            method: 'DELETE',
        });

        if (response.ok) {
            openDelete = true;
            modalTitle = 'Notification';
            deleteMessage = 'Successfully deleted configuration!';
            onlyOkBtn = true;
            await response.json();
            await mohReportList();
        }

        isLoading.set(false);

        // no need to wait for the response to update date
        TriggerService.trigger(INCIDENT_TABLES);
    };

    const onResponseTracking = () => {
        location.href = `/incident/response-tracking${
            selectedRow ? `?incidentNo=${selectedRow.incident_number}&name=${selectedRow.name}` : ''
        }`;
    };

    const openIncidentLog = () => {
        location.href = `/audittrail/sys-action-log/list?moduleName=cc3_hoc.im_incident${
            selectedRow ? `&moduleId=${selectedRow.incident_number}` : ''
        }`;
    };
</script>

<!-- <Guard scopeId={SCOPE.READ} abilities={data.abilities}> -->
<main class="m-4 h-[calc(100%-10rem)]">
    <div
        class="w-full rounded-t-xl border border-gray-200 bg-gray-50 p-3 font-bold text-white dark:border-gray-600 dark:bg-gray-700">
        <Heading tag="h5">MOH Report Management</Heading>
    </div>
    <div
        class="h-[calc(100vh-160px)] overflow-y-auto rounded-b-xl px-2 pt-5 text-gray-700 dark:bg-gray-900 dark:text-gray-400">
        <Toolbar embedded>
            <div class="flex gap-4">
                <div class="flex items-center gap-2">
                    <div class="relative min-w-[200px]">
                        <Search
                            bind:searchText
                            columns={searchColumns}
                            data={mohReportData}
                            bind:triggerResetSearch
                            on:search={onSearch}
                            on:apply={e => {
                                dataFilter = advancedSearchRecords = e.detail ? e.detail.filteredData : mohReportData;
                            }} />
                    </div>
                    <Button color="light" disabled={!mohReportData?.length} on:click={onSaveTable}>
                        <span class="mr-2">{@html icon(faFileCircleCheck).html}</span>
                        <span>Send</span>
                    </Button>
                    <Button color="light" on:click={onRefresh}>
                        <span class="mr-2">{@html icon(faRefresh).html}</span>
                        <span class="text-nowrap">Refresh</span>
                    </Button>
                </div>
            </div>
        </Toolbar>

        <Table
            striped={true}
            shadow
            style="border-collapse: separate; border-spacing: 0;"
            divClass="max-h-[calc(100%-80px)] overflow-y-auto border border-gray-500 mt-5">
            <TableHead
                defaultRow={false}
                class="bg-gray-50 text-base uppercase text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                theadClass="sticky top-0">
                {#each columns as column}
                    <TableHeadCell
                        class="relative border-b border-gray-500 px-4 text-base font-medium capitalize text-on-background text-white"
                        on:click={th => {
                            if (th.target.type !== 'checkbox') sortTable(column.name);
                        }}>
                        <div class="flex items-center gap-2 text-nowrap pr-2">
                            <span
                                >{column.label}
                                {#if column.editable}
                                    [{@html icon(faPencilAlt).html}]
                                {/if}
                                {#if column.type == ColumnType.Boolean}
                                    &nbsp;
                                    <Checkbox
                                        inline={true}
                                        bind:checked={headSelected}
                                        on:click={th => {
                                            const isChecked = th.target.checked;
                                            dataFilter = dataFilter.map(item => {
                                                if (
                                                    ['open', 'reopen'].includes(item['incident_status'].toLowerCase())
                                                ) {
                                                    item.is_ongoing_incident = isChecked;
                                                }
                                                if (!isChecked) {
                                                    item.is_ongoing_incident = isChecked;
                                                }
                                                return item;
                                            });
                                            headSelected = isChecked;
                                        }} />
                                {/if}
                            </span>
                            {#if sortKey === column.name}
                                <span class="ml-2">
                                    {@html icon(sortDirection === 1 ? faSortUp : faSortDown).html}</span>
                            {/if}
                            <div class="absolute right-0 h-full w-[1px] bg-gray-500" />
                        </div>
                    </TableHeadCell>
                {/each}
            </TableHead>
            <TableBody>
                {#if !dataFilter?.length}
                    <Heading tag="h6" class="p-4">No data</Heading>
                {:else}
                    {#each dataFilter as record}
                        <TableBodyRow
                            class="cursor-pointer text-base"
                            style={selectedRow?.id === record.id ? 'background-color:#1976D2' : ''}
                            on:click={() => {
                                // selectedRow = record;
                            }}>
                            {#each columns as column}
                                <TableBodyCell class="truncate border-b border-r border-gray-500 px-4 last:border-r-0">
                                    {#if column.type === ColumnType.Boolean}
                                        <Checkbox bind:checked={record[column.name]} />
                                    {:else if column.type === 'radio'}
                                        <Radio
                                            name={column.name}
                                            bind:group={checkedRadio}
                                            checked={checkedRadio == record.id}
                                            bind:value={record.id}
                                            on:click={() => {
                                                for (let idx = 0; idx < mohReportData.length; idx++) {
                                                    mohReportData[idx][column.name] = false;
                                                }
                                                if (checkedRadio === record.id) {
                                                    checkedRadio = '';
                                                    record[column.name] = false;
                                                } else {
                                                    checkedRadio = record.id;
                                                    record[column.name] = true;
                                                }
                                            }} />
                                    {:else if column.type === ColumnType.Date}
                                        {convertISOToUTC(record[column.name])}
                                    {:else if column.type === 'Config'}
                                        <a
                                            class="underline hover:text-blue-500"
                                            href="/incident/im-moh-report/edit/{record.id}">
                                            {record[column.name] ?? ''}</a>
                                    {:else}
                                        {record[column.name] ?? ''}
                                    {/if}
                                </TableBodyCell>
                            {/each}
                        </TableBodyRow>
                    {/each}
                {/if}
            </TableBody>
        </Table>
    </div>
</main>
<!-- </Guard> -->

<Notification
    bind:isOpen={openDelete}
    {modalTitle}
    modalMessage={deleteMessage}
    {onlyOkBtn}
    confirmAction={onlyOkBtn ? null : confirmDelete} />
