<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import ThresholdConfigComponent, {
        type ThresholdItem,
        type ThresholdConfig,
    } from '$lib/core/components/ThresholdConfig/index.svelte';
    import { TYPEOF_COMPARATOR } from '$lib/core/modules/dashboard/widgets/utils';
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { queryStore } from '@urql/svelte';
    import { GetCfgParameterDocument, type GetCfgParameterQuery } from '$generated-types';

    //Default props for item config
    export interface SimpleWidgetConfigItem {
        isEnable?: boolean;
        value?: string;
        textColor?: string;
        bgColor?: string;
        fontSize?: number;
        underlineStyle?: string;
        widgetName?: string;
    }
    //Title Config Item
    export interface TitleDatetimeItem extends SimpleWidgetConfigItem {
        datetimeFormat?: string;
    }

    export interface TitleConfigItem extends SimpleWidgetConfigItem {
        position?: string;
        titleDatetimeItem?: TitleDatetimeItem;
    }
    //Indicator Config Item
    export interface IndicatorConfigItem extends SimpleWidgetConfigItem {
        type?: string;
        valueFormat?: string;
        isShowBorder?: boolean;
        isShowDash?: boolean;
        isShowConfigColor?: boolean;
        borderColor: string;
        borderType?: string;
        isShowBorderToggle?: boolean;
        timeDisplayFormat?: [];
    }

    //Config Widget Object
    export interface IndicatorSimpleConfig {
        dataSource?: string;
        titleConfigItem?: TitleConfigItem;
        indicatorConfigItem?: IndicatorConfigItem;
        topSmallIconConfigItem: SmallIconConfigItem;
        bottomSmallIconConfigItem: SmallIconConfigItem;
        indicatorThresholdConfig: ThresholdConfig;
        showGear?: boolean;
        showFilteringLabel?: boolean;
        filterLabelAlignment?: string;
        cfg_param?: string;
        label_use_filter_datasource?: boolean;
        cfg_param_label?: string;
        useParentFilter?: boolean;
    }

    //Default value of config item in widget
    export const configWidgetItem: SimpleWidgetConfigItem = {
        isEnable: true,
        value: '',
        textColor: '#FFFFFF',
        bgColor: '#000000',
        fontSize: 12,
        underlineStyle: 'none',
        widgetName: '',
    };

    export const configTitleDatetimeItem: TitleDatetimeItem = {
        ...configWidgetItem,
        datetimeFormat: 'YYYY/MM',
    };

    export const titleConfigItem: TitleConfigItem = {
        ...configWidgetItem,
        position: 'top',
        titleDatetimeItem: { ...configTitleDatetimeItem, value: new Date().toString(), isEnable: false },
    };

    export const indicatorConfigItem: IndicatorConfigItem = {
        ...configWidgetItem,
        type: 'circle',
        valueFormat: 'number',
        isShowBorder: false,
        isShowConfigColor: false,
        borderColor: '#FFFFFF',
        borderType: 'solid',
    };

    export const smallIconConfigItem: SmallIconConfigItem = {
        value: 'BS',
        fontSize: 20,
        isEnable: false,
        bgColor: '#4D4D4D',
        textColor: '#FFFFFF',
        underlineStyle: 'none',
        type: 'circle',
        iconSize: 6,
        isShowBorder: false,
        borderColor: '#FFFFFF',
    };

    export const configIndicatorThresholdItem: ThresholdItem = {
        titleThresholdItem: 'Critical',
        comparatorThreshold: TYPEOF_COMPARATOR[0].value,
        valueOfThreshold: 10,
        textColor: '#FFFFFF',
        bgColor: '#000000',
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
    };

    const INDICATOR_THRESHOLD_CONFIG_ITEMS: Array<ThresholdItem> = [configIndicatorThresholdItem];

    export const configThresholdConfigs: ThresholdConfig = {
        isEnableThreshold: false,
        thresholdConfigItems: INDICATOR_THRESHOLD_CONFIG_ITEMS,
    };

    export const configDefaults: IndicatorSimpleConfig = {
        titleConfigItem: JSON.parse(JSON.stringify(titleConfigItem)),
        indicatorConfigItem: JSON.parse(
            JSON.stringify({
                ...indicatorConfigItem,
                value: '84',
                fontSize: 40,
                bgColor: '#4D4D4D',
                timeDisplayFormat: ['min'],
            })
        ),
        topSmallIconConfigItem: JSON.parse(JSON.stringify(smallIconConfigItem)),
        bottomSmallIconConfigItem: JSON.parse(JSON.stringify(smallIconConfigItem)),
        indicatorThresholdConfig: JSON.parse(JSON.stringify(configThresholdConfigs)),
    };

    export const componentName = new URL(import.meta.url).pathname;
    export const INDICATOR_VALUE_FORMAT: { [key: string]: string } = {
        number: 'Number',
        text: 'Text',
        percentage: 'Percentage',
        time: 'Time',
    };
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { AccordionItem, Accordion, Toggle } from 'flowbite-svelte';
    import SmallIconConfigComponent, {
        type SmallIconConfigItem,
    } from '$lib/core/modules/dashboard/widgets/IndicatorSimple/WidgetConfig/SmalIconConfig/index.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';
    import WidgetConfigItem from '$lib/core/modules/dashboard/components/WidgetConfig/WidgetItem.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';

    export let config: IndicatorSimpleConfig;
    export let datasources: Array<DropdownItem> = [];

    let simpleIndicatorConfig: HTMLElement;
    const datetimeFormatList: Array<String> = ['DD/MM', 'MMM YYYY', 'MM/DD/YY', 'DD MMM, YYYY'];
    const TITLE_POSITION: string[] = ['Top', 'Left', 'Right', 'Bottom'];
    const INDICATOR_TYPE: string[] = ['None', 'Circle', 'Square', 'Rectangle'];
    const DECORATION: string[] = ['None', 'Single', 'Double'];
    const INDICATOR_TIME_FORMAT: string[] = ['Day', 'Hr', 'Min', 'Sec'];
    const BORDER_TYPE: string[] = ['Solid', 'Dashed'];

    let filteringFields = [];
    const ALIGNMENT: string[] = ['Left', 'Center', 'Right'];

    const getFilteringCFG = async () => {
        try {
            const resCfg = await fetch(`/api/CfgParameterService?category=filtering_config&category=filtering_label`);
            const { res } = await resCfg.json();
            const listCfgParametes = res.data;
            if (listCfgParametes) {
                filteringFields = [
                    {
                        id: 'showGear',
                        componentDisplay: WidgetToggle,
                        extraProps: {
                            title: 'Show Filtering',
                            checked: config.showGear,
                        },
                    },
                    {
                        id: 'cfg_param',
                        componentDisplay: Dropdown,
                        extraProps: {
                            title: 'Filter Data Source',
                            options: listCfgParametes
                                .filter(e => e.category === 'filtering_config')
                                .map(item => ({
                                    label: item.title || item.name,
                                    value: item.id,
                                })),
                            setClass: 'border-b border-b-outline bg-transparent mb-4',
                            enableSearch: true,
                            appendMenuClass: 'w-full',
                        },
                    },
                    {
                        id: 'showFilteringLabel',
                        componentDisplay: WidgetToggle,
                        extraProps: {
                            title: 'Show Filtering Label',
                            checked: config.showFilteringLabel,
                        },
                    },
                    {
                        id: 'label_use_filter_datasource',
                        componentDisplay: WidgetToggle,
                        extraProps: {
                            title: 'Use the same as Filter Data Source',
                            checked: config.label_use_filter_datasource,
                        },
                        checkVisible: 'showFilteringLabel',
                        disabled: configData => !configData.showGear,
                    },
                    {
                        id: 'cfg_param_label',
                        componentDisplay: Dropdown,
                        extraProps: {
                            title: 'Label Data Source',
                            options: listCfgParametes
                                .filter(e => e.category === 'filtering_label')
                                .map(item => ({
                                    label: item.title || item.name,
                                    value: item.id,
                                })),
                            setClass: 'border-b border-b-outline bg-transparent mb-4',
                            enableSearch: true,
                            appendMenuClass: 'w-full',
                        },
                        checkVisible: configData =>
                            configData.showFilteringLabel && !configData.label_use_filter_datasource,
                    },
                    {
                        id: 'filterLabelAlignment',
                        componentDisplay: WidgetGroupButton,
                        extraProps: {
                            title: 'Filtering Label Alignment',
                            labels: ALIGNMENT,
                        },
                        checkVisible: 'showFilteringLabel',
                    },
                ];
            }
        } catch (error) {
            console.log(error);
        }
    };

    function updateSizeConfigWidget() {
        if (simpleIndicatorConfig) {
            let configurationPanel =
                simpleIndicatorConfig.parentElement.parentElement.parentElement.parentElement.parentElement;
            configurationPanel.classList.remove('w-1/5');
            configurationPanel.classList.add('overflow-x-auto', 'h-[calc(100%-4rem)]', 'overflow-y-auto', 'w-2/5');
        }
    }

    function resetSizeConfigWidget() {
        if (simpleIndicatorConfig) {
            let configurationPanel =
                simpleIndicatorConfig.parentElement.parentElement.parentElement.parentElement.parentElement;
            configurationPanel.classList.remove('overflow-x-auto', 'h-[calc(100%-4rem)]', 'overflow-y-auto', 'w-2/5');
            configurationPanel.classList.add('w-1/5');
        }
    }

    onMount(async () => {
        getFilteringCFG();
        updateSizeConfigWidget();
    });

    onDestroy(async () => {
        resetSizeConfigWidget();
    });

    $: config.titleConfigItem.isEnable = config.titleConfigItem.value.length > 0 ? true : false;
    $: config.indicatorConfigItem.isShowConfigColor = !config.indicatorThresholdConfig.isEnableThreshold;
    $: config.indicatorConfigItem.isShowBorderToggle = config.indicatorConfigItem.type == 'none' ? false : true;
    $: if (config.showGear !== undefined && config.showGear === false) {
        config.label_use_filter_datasource = false;
    }

    const dataSourcePropertyFields = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: datasources,
                setClass: 'border-b border-b-outline bg-transparent mb-4',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
    ];

    const titleConfigPropertyFields = [
        {
            id: 'value',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
                customClass: 'w-full mb-4',
            },
        },
        {
            id: 'widgetName',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Name',
                customClass: 'w-full mb-4',
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end',
            },
            children: [
                {
                    id: 'fontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Title Text Size',
                        customClass: 'w-1/2 mb-4',
                        pattern: '^[0-9]\\d*$',
                        min: 0,
                    },
                },
                {
                    id: 'textColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        title: 'Text Color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
        {
            id: 'position',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Title Position',
                labels: TITLE_POSITION,
            },
        },
    ];
    const titleDatetimeConfigPropertyFields = [
        {
            id: 'isEnable',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Datetime',
                checked: config.titleConfigItem.titleDatetimeItem.isEnable,
            },
        },
        {
            checkVisible: 'isEnable',
            id: 'datetimeFormat',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Date Format',
                placeholder: 'YYYY/MM',
                pattern: '^[aszhmdyASZHMDY/\\-:s., ]*$',
                toolTip: datetimeFormatList,
            },
        },
        {
            checkVisible: 'isEnable',
            extraProps: {
                customClass: 'flex w-full flex-row items-end',
            },
            children: [
                {
                    id: 'fontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Datetime Text Size',
                        customClass: 'w-1/2 mb-4',
                        pattern: '^[0-9]\\d*$',
                        min: 0,
                    },
                },
                {
                    id: 'textColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        title: 'Text Color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
    ];
    const indicatorConfigPropertyFields = [
        {
            id: 'type',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Indicator Type',
                labels: INDICATOR_TYPE,
            },
        },
        {
            id: 'underlineStyle',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Underline',
                labels: DECORATION,
            },
        },
        {
            id: 'valueFormat',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Value Format',
                labels: Object.values(INDICATOR_VALUE_FORMAT),
            },
        },
        {
            id: 'timeDisplayFormat',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Display Format',
                labels: INDICATOR_TIME_FORMAT,
                isMultiple: true,
                isTimeFormat: true,
            },
            checkVisible: configData => configData.valueFormat === 'time',
        },
        {
            extraProps: {
                customClass: 'flex w-full items-end',
            },
            children: [
                {
                    id: 'fontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Indicator Text Size',
                        customClass: 'w-1/2 mb-4',
                        pattern: '^[0-9]\\d*$',
                        min: 0,
                    },
                },
                {
                    checkVisible: 'isShowConfigColor',
                    id: 'bgColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        customClass: 'w-1/4 text-center mb-4',
                        type: 'color',
                        title: 'Bg Color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
                {
                    checkVisible: 'isShowConfigColor',
                    id: 'textColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        customClass: 'w-1/4 text-center mb-4',
                        type: 'color',
                        title: 'Text Color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
        {
            checkVisible: 'isShowBorderToggle',
            extraProps: {
                customClass: 'flex flex-row self-center h-10 w-1/2',
            },
            children: [
                {
                    id: 'isShowBorder',
                    componentDisplay: WidgetToggle,
                    extraProps: {
                        title: 'Show Border',
                        checked: config.indicatorConfigItem.isShowBorder,
                        customClass: 'self-center',
                    },
                },
                {
                    checkVisible: 'isShowBorder',
                    id: 'borderColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'color',
                        inputClass: 'h-10 w-10 p-0',
                        customClass: 'ml-auto',
                    },
                },
            ],
        },
        {
            checkVisible: 'isShowBorder',
            id: 'borderType',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                labels: BORDER_TYPE,
            },
        },
        {
            id: 'isShowDash',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Dash if 0',
                checked: config.indicatorConfigItem.isShowDash,
                customClass: 'self-center',
            },
        },
    ];
</script>

<div class="text-on-surface flex flex-col" bind:this={simpleIndicatorConfig}>
    <Accordion>
        <AccordionItem>
            <span slot="header">Properties</span>
            <WidgetConfigItem bind:config widgetFields={dataSourcePropertyFields} />
            <Accordion>
                <AccordionItem>
                    <span slot="header">Title</span>
                    <WidgetConfigItem bind:config={config.titleConfigItem} widgetFields={titleConfigPropertyFields} />
                    <WidgetConfigItem
                        bind:config={config.titleConfigItem.titleDatetimeItem}
                        widgetFields={titleDatetimeConfigPropertyFields} />
                </AccordionItem>
                <AccordionItem open>
                    <span slot="header">Indicator</span>
                    <WidgetConfigItem
                        bind:config={config.indicatorConfigItem}
                        widgetFields={indicatorConfigPropertyFields} />
                </AccordionItem>
                {#if config.indicatorConfigItem.type !== 'none'}
                    <AccordionItem>
                        <span slot="header">Indicator Icon</span>
                        <Accordion flush>
                            <AccordionItem>
                                <span slot="header">Small Top Icon</span>
                                <div class="mb-4">
                                    <Toggle bind:checked={config.topSmallIconConfigItem.isEnable} color="blue"
                                        >Show Top Icon</Toggle>
                                </div>
                                <SmallIconConfigComponent
                                    isTopIcon={true}
                                    bind:smallIconConfigItem={config.topSmallIconConfigItem} />
                            </AccordionItem>
                            <AccordionItem>
                                <span slot="header">Small Bottom Icon</span>
                                <div class="mb-4">
                                    <Toggle bind:checked={config.bottomSmallIconConfigItem.isEnable} color="blue"
                                        >Show Bottom Icon</Toggle>
                                </div>
                                <SmallIconConfigComponent bind:smallIconConfigItem={config.bottomSmallIconConfigItem} />
                            </AccordionItem>
                        </Accordion>
                    </AccordionItem>
                {/if}
            </Accordion>
        </AccordionItem>
        <AccordionItem>
            <span slot="header">Filtering</span>
            <WidgetConfigItem bind:config widgetFields={filteringFields} />
        </AccordionItem>
        <AccordionItem open>
            <span slot="header">Thresholds</span>
            <ThresholdConfigComponent bind:configThresholdConfigs={config.indicatorThresholdConfig} />
        </AccordionItem>
    </Accordion>
</div>
