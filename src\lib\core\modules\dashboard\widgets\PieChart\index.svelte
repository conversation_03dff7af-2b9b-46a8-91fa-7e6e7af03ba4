<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { faPieChart } from '@fortawesome/free-solid-svg-icons';

    export const widgetTitle = 'Pie Chart';
    export const widgetIcon = faPieChart;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetMinWidth = 3;
    export const widgetMinHeight = 3;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { gqlClientStore } from '$lib/stores/Clients';
    import type { EChartsOption, EChartsType } from 'echarts';
    import gql from 'graphql-tag';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import type { PieChartConfig } from './WidgetConfig/index.svelte';

    export let config: PieChartConfig;

    let chartContainer: HTMLElement;
    let chart: EChartsType;
    let option: EChartsOption;
    let subscription: Subscription;
    let dataSeries = [
        { name: '0hr', value: 10, selected: true },
        { name: '4hr', value: 40, selected: true },
        { name: '8hr', value: 22, selected: true },
        { name: '10hr', value: 13, selected: true },
        { name: '24hr', value: 27, selected: true },
        { name: '30hr', value: 16, selected: true },
    ];

    config.dataset = JSON.parse(JSON.stringify(dataSeries));

    function __configDynamic() {
        option = {
            ...option,
            legend: {
                ...option.legend,
                show: config?.showLegend,
            },
            color: config.colorSeries,
        };
        option.series[0].radius = config?.showLegend ? '60%' : '70%';
        chart?.setOption(option);
    }

    const __configStatic = () => {
        option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c} ({d}%)',
            },
            legend: {
                type: 'scroll',
                orient: 'horizontal',
                top: 10,
                textStyle: {
                    color: '#fff',
                },
            },
            series: [
                {
                    name: 'Pie Chart',
                    type: 'pie',
                    radius: '60%',
                    label: {
                        color: '#fff',
                        fontSize: 15,
                        fontFamily: 'arial',
                        fontWeight: 'bold',
                    },
                    labelLayout: {
                        hideOverlap: false,
                    },
                },
            ],
        };
        option.series[0].data = dataSeries;
    };

    onMount(async () => {
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            if (chart) {
                chart.resize();
                __configStatic();
                __configDynamic();
            }
        });

        const echarts = await import('echarts');

        if (chartContainer) {
            chart = echarts.init(chartContainer, null, { renderer: 'svg' });
            __configStatic();
            chart.on('legendselectchanged', function (params: any) {
                for (let i = 0; i < dataSeries.length; i++) {
                    if (dataSeries[i].name == params.name) {
                        dataSeries[i].selected = params.selected[dataSeries[i].name];
                        break;
                    }
                }
                __configStatic();
                __configDynamic();
            });
        }
        window.addEventListener('resize', () => {
            chart.resize();
        });
    });

    $: if (Boolean(chart) && Boolean(config)) {
        __configDynamic();
    }

    onDestroy(() => {
        subscription?.unsubscribe();
    });
</script>

<div class="flex h-full w-full flex-col">
    <header
        class="flex h-12 items-center gap-4 px-3 py-1 text-on-background pb-1 {config.title.length > 0
            ? ''
            : 'hidden'}">
        <p class="bg-transparent text-base">{config.title}</p>
    </header>
    <div data-tag="chart-container" class="h-full w-full" bind:this={chartContainer} />
</div>
