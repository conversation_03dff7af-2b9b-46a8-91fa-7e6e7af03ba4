<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import WidgetItemConfig from '$lib/core/modules/dashboard/components/WidgetItemConfig/index.svelte';
    import DashboardDetails, {
        type DashboardDetailsCreatedEvent,
        type DashboardDetailsUpdatedEvent,
    } from '$lib/core/modules/dashboard/components/DashboardDetails/index.svelte';
    import WidgetItem, {
        type GridStackHTMLElement,
    } from '$lib/core/modules/dashboard/components/WidgetItem/index.svelte';
    import WidgetList from '$lib/core/modules/dashboard/components/WidgetList/index.svelte';
    import Tooltip from '$lib/core/components/Tooltip/index.svelte';
    import type { WidgetDetail } from '$lib/core/modules/dashboard/components/WidgetItem/index.svelte';
    import '$lib/core/modules/dashboard/components/WidgetViewer/index.css';
    import { setSidebarDisplay } from '$lib/core/modules/menubar/Sidebar/index.svelte';
    import { setStatusBarDisplay } from '$lib/core/components/StatusBar/index.svelte';
    import { onMount, setContext, tick, onDestroy } from 'svelte';
    import { computePosition, flip, offset, shift } from '@floating-ui/dom';
    import Modal from '$lib/core/components/Modal/index.svelte';
    import {
        type DeleteConfigurationResponseBody,
        type UpdateDashboardLayoutRequestBody,
    } from '$routes/api/dash/config/+server';
    import { DashboardStore, LoadDashboardConfiguration } from '$lib/core/modules/dashboard/module';
    import { dataSource, uiState } from '$lib/core/core';
    import { StatusCodes } from 'http-status-codes';
    import {
        faEdit,
        faFileCirclePlus,
        faGear,
        faPlus,
        faTrash,
        faUpRightAndDownLeftFromCenter,
        faFloppyDisk,
    } from '@fortawesome/free-solid-svg-icons';
    import { locations } from '$lib/stores/Locations';
    import { browser } from '$app/environment';
    import { v4 } from 'uuid';
<<<<<<< HEAD
    import {
        WIDGET_COMPONENTS,
        WIDGET_CONFIG_COMPONENTS,
        displayStateStore,
        loadedWidgetItemsStore,
        DisplayState,
    } from '$lib/core/modules/dashboard/module';
    import { get, writable, type Writable } from 'svelte/store';
=======
    import { get, type Writable } from 'svelte/store';
>>>>>>> aoh-web/develop
    import { fade } from 'svelte/transition';
    import type { GridStack, GridStackOptions } from 'gridstack';
    import { isDirty } from '$lib/core/components/PageTitle/index.svelte';
    import Dialog from '$lib/core/components/Dialog/index.svelte';
    import Spinner from '$lib/core/components/Spinner/index.svelte';
    import Status from '$lib/core/components/StatusBar/Status/index.svelte';
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import WidgetGrid, { GRID_STACK_CONTEXT } from '$lib/core/modules/dashboard/components/WidgetGrid/index.svelte';
    import DashboardSelector, {
        type DashboardSelectorSelectionEvent,
        type DashboardSelectorUpdateEvent,
    } from '$lib/core/modules/dashboard/components/DashboardSelector/index.svelte';

<<<<<<< HEAD
    // TODO:
=======
    export enum DisplayState {
        ViewingDashboard,
        BusySaving,
        EditingDashboard,
        EditingWidget,
    }
>>>>>>> aoh-web/develop

    export const UI_STATE_LAST_SELECTED_DASHBOARD = 'last_selected_dashboard';

    const src = new URL(import.meta.url).pathname;
    const [componentName] = src.split('/').slice(-2, -1);
    const log = logger.child({ src, componentName });

    const MaxColumns = 24;
    const MaxRows = 0;
</script>

<script lang="ts">
    import { page } from '$app/stores';
    import { beforeNavigate, goto } from '$app/navigation';
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
<<<<<<< HEAD
    import { findWidgetById, filterWidgetByExcludeId, pushDataToWidget } from '$lib/core/modules/dashboard/utils/utils';
=======
    import type { UUID } from 'crypto';
    import { loadImageBitmap } from 'pixi.js';
>>>>>>> aoh-web/develop

    export let dashboards: Array<DashboardConfiguration> = [];
    export let tags: Array<Tag & StandardPatchFields> = [];
    export let user_id: UUID;
    export let isEditable = false;
    export let displayState = DisplayState.ViewingDashboard;
    displayStateStore.subscribe(value => (displayState = value));

    let widgetGrid: WidgetGrid; // the grid encapsulating the widgets

    let grid_layout: Array<WidgetGridItem> = []; // the list of widgets to render in the widget grid
    let selected_loaded_widget_item: WidgetGridItem;
    let selected_widget_item_shared_configs: Array<SharedConfig> = [];

    let selectedLayout: DashboardConfiguration; // the selected configuration from the layout list

    let isConfigOpen = false; // Flag to control whether the individual widget's configuration panel is showing or not
    let isEditDashboardDetailsDialogOpen = false; // update dashboard details dialog open flag
    let isCreateDashboardDialogOpen = false; // create dashboard details dialog open flag

    let mounted = false;

<<<<<<< HEAD
    let newPageName: string; // New page name - input from user to rename or create new pages
    let objWidgetList: WidgetList;
    let isInSubGrid = false;

    let GridStackLib;

    enum NewPageTemplate {
        Default,
        Blank,
        Current,
    }

=======
>>>>>>> aoh-web/develop
    let options: GridStackOptions = {
        acceptWidgets: true,
        alwaysShowResizeHandle: false,
        animate: false,
        auto: true,
        cellHeightThrottle: 100,
        column: MaxColumns,
        row: MaxRows,
        disableDrag: false,
        disableResize: false,
        float: false,
        margin: '0.25rem',
        subGridOpts: {
            acceptWidgets: true,
            column: 'auto',
            margin: '0.25rem',
            animate: false,
        },
    };

    $: switch ($displayStateStore) {
        case DisplayState.ViewingDashboard:
            isEditable = false;
            break;
        case DisplayState.EditingDashboard:
            isEditable = true;
            break;
        case DisplayState.EditingWidget:
            isEditable = true;
            break;
        case DisplayState.BusySaving:
            isEditable = true;
            break;
    }

    // Currently used in hidden instantiated components to decide if they should render on page
    // in the future, we should probably values via a separte typescript file instead of instantiating the components
    let props = {
        isPreviewMode: false,
    };

    function resizeGrid() {
        if (widgetGrid) {
            widgetGrid.resize();
        }
    }

    function widget_item_config_action(node: Node) {
        let updatePosition = () => {
            const element = node as HTMLElement;

            if (!selected_loaded_widget_item) {
                log.error({ element }, 'no selected widget item config to update position');
                return;
            }

            const refElement = selected_loaded_widget_item.svelte_component_ref.getGridstackNode()?.el;

            if (!refElement) {
                log.error({ element }, 'selected widget item has no ref element, please investigate');
                return;
            }

            computePosition(refElement, element, {
                placement: 'right-start',
                middleware: [offset(24), flip(), shift()],
            }).then(({ x, y }) => {
                Object.assign(element.style, {
                    left: `${x}px`,
                    top: `${y}px`,
                });
            });
        };

        // Run validate whenever a widget change occurs
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            updatePosition();
        });

        // Run validate once on initialization
        updatePosition();
    }

<<<<<<< HEAD
    async function loadWidgetTypes() {
        allWidgetTypes = [];

        // Look for all components that are eligible to be widgets
        for (let configKey in WIDGET_CONFIG_COMPONENTS) {
            try {
                // Assumes there is always an index.svelte in the parent folder
                let componentKey = configKey.replace(WIDGET_MARKER + '/', '');

                // Use the folder name as default title, override with component's 'const widgetTitle' on initialization
                // Lets not do this anymore, default to undefined and extract the title from the .svelte definition

                // const split = componentKey.split('/');
                // let widgetTitle = capitalCase(split[split.length - 2]);

                let component = await getModule(componentKey, WIDGET_COMPONENTS);
                let configComponent;

                if (WIDGET_CONFIG_COMPONENTS[configKey]) {
                    configComponent = await getModule(configKey, WIDGET_CONFIG_COMPONENTS);
                }

                allWidgetTypes.push({
                    title: component.widgetTitle,
                    path: componentKey,
                    component,
                    configComponent,
                    icon: component.widgetIcon,
                    defaultWidth: component.widgetWidth,
                    defaultHeight: component.widgetHeight,
                    minWidth: component.widgetMinWidth,
                    minHeight: component.widgetMinHeight,
                    maxWidth: component.widgetMaxWidth,
                    maxHeight: component.widgetMaxHeight,
                    category: component.widgetCategory,
                    limit: component.widgetLimit,
                    enabled: component.widgetEnabled,
                    isSubGrid: 'isSubGrid' in component ? component.isSubGrid : false,
                });
            } catch (err) {
                log.error({ err }, 'Error loading Svelte component module for ' + configKey);
            }
        }

        await populateWidgetTypes();
    }

=======
>>>>>>> aoh-web/develop
    const escapeHandler = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            exitFullscreen();
        }
    };

    const exitFullscreenHandler = (event: KeyboardEvent) => {
        if (!document.fullscreenElement) {
            exitFullscreen();
        }
    };

    onMount(async () => {
<<<<<<< HEAD
        // Load widget types before loading other stuff
        await loadWidgetTypes();

        await tick();
        await import('gridstack/dist/gridstack-all');
        GridStackLib = await import('gridstack');

=======
>>>>>>> aoh-web/develop
        mounted = true;
        if (browser) {
            document.addEventListener('keydown', escapeHandler);
            document.addEventListener('fullscreenchange', exitFullscreenHandler);
        }
    });

    onDestroy(() => {
        if (browser) {
            document.removeEventListener('keydown', escapeHandler);
            document.removeEventListener('fullscreenchange', exitFullscreenHandler);
        }
    });

    let gridstackStore: Writable<GridStack>;

    let fullyInitialized = false;

    // Load initial config when gridstack gets initialized
    $: {
        if (!fullyInitialized) {
            if (widgetGrid) {
                gridstackStore = widgetGrid.getGridStackStore();
                setContext(GRID_STACK_CONTEXT, gridstackStore);
            }

            if (gridstackStore && $gridstackStore) {
                // Use all the loaded layouts
                let needToLoad = false;

                if (dashboards && dashboards.length > 0) {
                    needToLoad = true;
                }

                if (needToLoad && mounted) {
                    let deepLinkedDashboardName = $page.url.searchParams.get('name');

                    let shouldUpdateSearchParams = false;

                    if (deepLinkedDashboardName) {
                        selectedLayout = dashboards.find(val => {
                            return val.name === deepLinkedDashboardName;
                        });
                    }

                    // No deep linking OR deep linking couldn't find a layout
                    if (!selectedLayout || !deepLinkedDashboardName) {
                        selectedLayout = dashboards.find(val => {
                            return val.name === $uiState.dashboard[UI_STATE_LAST_SELECTED_DASHBOARD];
                        });

                        shouldUpdateSearchParams = true;
                    }

                    if (selectedLayout && shouldUpdateSearchParams) {
                        // set dashboard names searchParams to history so that people can
                        // copy paste to share the deep link to the dashboard
                        const changeLayout = new URL($page.url);
                        changeLayout.searchParams.set('name', selectedLayout.name);
                        goto(changeLayout, { invalidateAll: false });
                    }

                    LoadConfig();

                    for (const [_, w] of $DashboardStore.widget_map) {
                        grid_layout.forEach(g => {
                            if (g.id === w.id) {
                                g.shared_config_id = w.shared_config_id;

                                if (g.shared_config_id) {
                                    g.config = $DashboardStore.shared_config_map.get(w.shared_config_id).config;
                                }
                            }
                        });
                    }

                    fullyInitialized = true;
                }
            }
        }
    }

    DashboardStore.subscribe(ds => {
        dashboards = [];
        tags = [];

        for (const [_, d] of ds.dashboard_map) {
            dashboards.push(d);
        }

        for (const [_, t] of ds.tag_map) {
            tags.push(t);
        }

        for (const [_, w] of ds.widget_map) {
            grid_layout.forEach(g => {
                if (g.id === w.id) {
                    g.shared_config_id = w.shared_config_id;

                    if (g.shared_config_id) {
                        g.config = ds.shared_config_map.get(w.shared_config_id).config;
                    }
                }
            });
        }

        if (selected_loaded_widget_item) {
            let widget = ds.widget_map.get(selected_loaded_widget_item.id);
            selected_loaded_widget_item.config = widget.config;
            selected_loaded_widget_item.shared_config_id = widget.shared_config_id;
            selected_loaded_widget_item = selected_loaded_widget_item;
        }

        dashboards = dashboards;
        tags = tags;
        grid_layout = grid_layout;
    });

    $: if (selected_loaded_widget_item) {
        selected_widget_item_shared_configs = Array.from($DashboardStore.shared_config_map.values()).filter(
            scm => scm.widget_type_id === selected_loaded_widget_item.widget_type_id
        );
    }

    const dashboardSelectorUpdateHandler = (e: DashboardSelectorUpdateEvent) => {
        if (fullyInitialized) {
            dashboards = e.detail.dashboards.map(d => {
                return {
                    id: d.id,
                    widgets: d.widgets,
                    favourite: d.favourite,
                    name: d.name,
                    description: d.description,
                    tags: d.tags,
                    occ_lock: d.occ_lock,
                };
            });

            selectedLayout = dashboards.find(val => {
                return val.id === e.detail.selected_dashboard.id;
            });

            $uiState.dashboard[UI_STATE_LAST_SELECTED_DASHBOARD] = selectedLayout?.name;

            // set dashboard names searchParams to history so that people can copy
            // paste to share the deep link to the dashboard
            const changeLayout = new URL($page.url);
            changeLayout.searchParams.set('name', selectedLayout.name);

            goto(changeLayout, { invalidateAll: false });
        }
    };

    const dashboardSelectorSelectionHandler = (e: DashboardSelectorSelectionEvent) => {
        if ($isDirty) {
            confirmation_modal[ModalEnum.ChangeDashboardUnsaved] = {
                title: 'Alert',
                content: 'Are you sure you wish to switch dashboards without saving?',
                confirmText: 'Proceed',
                showCancel: true,
                cancelText: 'Cancel',
                specialContent: false,
                accept: async () => {
                    $isDirty = false;
                    dashboardSelectorUpdateHandler(e);
                    await LoadConfig();
                },
                reject: async () => {
                    selectedLayout = selectedLayout;
                },
            };

            openModal(ModalEnum.ChangeDashboardUnsaved);
        } else {
            dashboardSelectorUpdateHandler(e);
            LoadConfig();
        }
    };

    const dashboardDetailsUpdatedHandler = (e: DashboardDetailsUpdatedEvent) => {
        let dashToUpdate = dashboards.find(d => (d.id = e.detail.id));
        dashToUpdate.name = e.detail.name;
        dashToUpdate.description = e.detail.description;
        dashToUpdate.occ_lock = e.detail.occ_lock;

        selectedLayout.name = e.detail.name;
        selectedLayout.description = e.detail.description;
        selectedLayout.occ_lock = e.detail.occ_lock;

        $uiState.dashboard[UI_STATE_LAST_SELECTED_DASHBOARD] = selectedLayout?.name;

        // set dashboard names searchParams to history so that people can copy
        // paste to share the deep link to the dashboard
        const setDashboardQueryParams = new URL($page.url);
        setDashboardQueryParams.searchParams.set('name', selectedLayout.name);

        goto(setDashboardQueryParams, { invalidateAll: false });

        dashboards = dashboards;
        selectedLayout = selectedLayout;
    };

    const dashboardDetailsCreateHandler = (e: DashboardDetailsCreatedEvent) => {
        isCreateDashboardDialogOpen = false;
        selectedLayout = {
            id: e.detail.id,
            name: e.detail.name,
            description: e.detail.description,
            tags: e.detail.tags,
            occ_lock: e.detail.occ_lock,
        };

        log.debug({ selectedLayout }, 'New dashboard created');

        // set dashboard names searchParams to history so that people can copy
        // paste to share the deep link to the dashboard
        const changeLayout = new URL($page.url);
        changeLayout.searchParams.set('name', selectedLayout.name);
        goto(changeLayout, { invalidateAll: false });

        LoadConfig();

        // Remove dirty flag
        $isDirty = false;

        // Close individual widget configs
        isConfigOpen = false;

        DashboardStore.update(ds => {
            ds.dashboard_map.set(selectedLayout.id, selectedLayout);
            return ds;
        });
    };

    let disableInteraction = false;

    async function openModal(modalType: ModalEnum) {
        modal_type = modalType;
        is_confirm_modal_open = true;

        // Consider also focusing on the input text box
    }

    async function delete_dashboard_layout(dashboard_id: Uuid) {
        displayState = DisplayState.ViewingDashboard;

        const response = await fetch(`${get(locations).dash_web}/config`, {
            method: 'DELETE',
            body: JSON.stringify({ id: dashboard_id }),
        });

        let result = await (response.json() as Promise<DeleteConfigurationResponseBody>);

        if (response.status !== StatusCodes.OK) {
            log.error({ errors: result.errors, status: response.status });
            throw new Error('failed to delete dashboard configuration');
        }

        dashboards = dashboards.filter(e => e.id !== dashboard_id);
        if (selectedLayout?.id === dashboard_id) {
            if (dashboards.length > 0) {
                selectedLayout = dashboards[0];
            } else {
                selectedLayout = null;
            }
        }

        DashboardStore.update(ds => {
            ds.dashboard_map.delete(dashboard_id);
            ds.widget_map.forEach(w => {
                if (w.dashboard_id === dashboard_id) {
                    ds.widget_map.delete(w.id);
                }
            });
            return ds;
        });
    }

    function widgetValidationAction(node: Element, widget_type: WidgetType) {
        let validate = () => {
            let gridstack = get(gridstackStore);

            let willItFit = gridstack.willItFit({
                w: widget_type.min_width,
                h: widget_type.min_height,
                autoPosition: true,
            });

            let count = 0;

<<<<<<< HEAD
            if ($loadedWidgetItemsStore) {
                $loadedWidgetItemsStore.forEach(loadedWidgetItem => {
                    if (loadedWidgetItem.widgetType.title === widgetType.title) {
=======
            if (grid_layout) {
                grid_layout.forEach(loaded_widget_item => {
                    if (loaded_widget_item.widget_type.name === widget_type.name) {
>>>>>>> aoh-web/develop
                        count++;
                    }
                });
            }

            let subject: HTMLButtonElement = node.querySelector('[data-subject=button]');

            // Disable ability to add widget if not valid (can't fit or exceeds limit)
            if (subject) {
                subject.disabled = !willItFit || count >= widget_type.limit;

                let errorMessage = '';
                if (count >= widget_type.limit) errorMessage = 'Limit reached';
                if (!willItFit) errorMessage = 'Insufficient workspace';

                (subject.querySelector('#error-msg') as HTMLElement).innerText = errorMessage;
            }
        };

        // Run validate whenever a widget change occurs
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            validate();
        });

        // Run validate once on initialization
        validate();
    }

    // Save Current Grid Configuration
    async function saveConfiguration() {
        let dashboard_widgets: Array<Widget> = [];

        // construct the configForSaving based on what's in the grid.
        for (let i = 0; i < grid_layout.length; i++) {
            let widget_grid_item = grid_layout[i];

            let gsNode = (widget_grid_item.svelte_component_ref as WidgetItem).getGridstackNode();

<<<<<<< HEAD
        // newPageName cannot be undefined or empty trying
        if ((options?.onlyRename || options?.newPage) && !newPageName) {
            alert('You are not allowed to use blank names.');
            return;
        }

        if (!options?.onlyRename) {
            // Use default config for saving
            if (options.newPage?.template === NewPageTemplate.Default) {
                // defaultConfigLayout.configuration is already stringified - parse then stringify again later
                configForSaving = JSON.parse(defaultConfigLayout.configuration);
            } else if (!options.newPage || options.newPage?.template === NewPageTemplate.Current) {
                // If it's not a new page, or if it's a new page that wants to use the currently viewed widgets
                // then we construct the configForSaving based on what's in the grid.

                dataForSave($loadedWidgetItemsStore, configForSaving);
            } else {
                // Leave configForSaving blank for blank template
            }
=======
            dashboard_widgets.push({
                id: widget_grid_item.id,
                row: gsNode.y,
                column: gsNode.x,
                width: gsNode.w,
                height: gsNode.h,
                config: widget_grid_item.config,
                widget_type_id: widget_grid_item.widget_type_id,
                dashboard_id: selectedLayout?.id as UUID,
                shared_config_id: widget_grid_item.shared_config_id,
                occ_lock: widget_grid_item.occ_lock,
            });
>>>>>>> aoh-web/develop
        }

        let response: Response;

        const body: UpdateDashboardLayoutRequestBody = {
            dashboard_id: selectedLayout?.id,
            widgets: dashboard_widgets,
        };

        // Update the dashboard's list of widgets
        response = await fetch(`${get(locations).dash_web}/config`, {
            method: 'PATCH',
            body: JSON.stringify(body),
        });

        if (response.status === StatusCodes.OK) {
            DashboardStore.update(ds => {
                dashboard_widgets.forEach(widget => {
                    ds.widget_map.set(widget.id, widget);
                });

                return ds;
            });
        } else {
            log.error('unexpected response attempting to save dashboard layout');
        }
    }

    function dataForSave(loadedWidgetItems: Array<LoadedWidgetItemConfig>, configForSaving: Array<WidgetItemConfig>) {
        for (let i = 0; i < loadedWidgetItems.length; i++) {
            let widgetItemConfig = loadedWidgetItems[i];

            let gsNode = (widgetItemConfig.widgetItemRef as WidgetItem).getGridstackNode();

            widgetItemConfig.widgetGridStackOptions = {
                w: gsNode.w,
                h: gsNode.h,
                x: gsNode.x,
                y: gsNode.y,
                minW: gsNode.minW,
                minH: gsNode.minH,
                maxW: gsNode.maxW,
                maxH: gsNode.maxH,
            };

            configForSaving.push({
                widgetId: widgetItemConfig.widgetId,
                widgetType: {
                    path: widgetItemConfig.widgetType.path,
                    title: widgetItemConfig.widgetType.title,
                },
                widgetGridStackOptions: widgetItemConfig.widgetGridStackOptions,
                widgetConfig: widgetItemConfig.widgetConfig,
                widgetItems: [],
            });
            if (widgetItemConfig.widgetItems) dataForSave(widgetItemConfig.widgetItems, configForSaving[i].widgetItems);
        }
    }

    // Disallow multiple simultaneous loadConfigs
    let loading = false;

    // Load Current Grid Configuration
    async function LoadConfig() {
        if (!selectedLayout || !mounted || loading) {
            log.debug({ selectedLayout, loading }, 'Skipping LoadConfig');
            return;
        }

        // Flag to control whether multiple loadConfigs can occur
        loading = true;

        // Reset list of loaded widgets, later add the new ones to be loaded in
<<<<<<< HEAD
        $loadedWidgetItemsStore = [];
=======
        grid_layout = [];
>>>>>>> aoh-web/develop
        widgetGrid.resetGrid(options);

        // Pull unloaded widgets from dashboard (this constitutes the dashboard layout)
        // We'll use this to load into grid layout later
        let widgets_to_load_into_layout = [];
        $DashboardStore.widget_map.forEach(widget => {
            if (widget.dashboard_id == selectedLayout.id) {
                widgets_to_load_into_layout.push(widget);
            }
        });

        // Batch addWidgets - for performance
        if (gridstackStore && $gridstackStore) {
            $gridstackStore.batchUpdate(true);
        }

<<<<<<< HEAD
        // Check if array before attempting to parse config - .length call might be on string so we check for array
        if (!Array.isArray(widgetGridConfig)) {
            alert('Failed to load invalid configuration.');

            log.error('Error, attempted to load an invalid configuration.');
            if (gridstackStore && $gridstackStore) {
                $gridstackStore.batchUpdate(false);
            }

            $isDirty = false;
            loading = false;

            return;
        }

        // Iterate through array of configurations
        await LoadConfigRecursive(widgetGridConfig, tempWidgetGridConfig, null);
        $loadedWidgetItemsStore = tempWidgetGridConfig;
=======
        grid_layout = LoadDashboardConfiguration(widgets_to_load_into_layout);
        grid_layout = grid_layout;
>>>>>>> aoh-web/develop

        await tick();

        // Triggers batch update to occur
        if (gridstackStore && $gridstackStore) {
            $gridstackStore.batchUpdate(false);
        }

        $isDirty = false;
        loading = false;
<<<<<<< HEAD
    }

    async function LoadConfigRecursive(
        widgetGridConfig: Array<LoadedWidgetItemConfig>,
        tempWidgetGridConfig: Array<LoadedWidgetItemConfig>,
        parentId: string
    ) {
        if (!widgetGridConfig) return;
        for (let i = 0; i < widgetGridConfig.length; i++) {
            // Validate schema by trying and failing
            try {
                let sw = widgetGridConfig[i];

                try {
                    if (WIDGET_COMPONENTS[sw.widgetType.path]) {
                        sw.widgetType.component = await getModule(sw.widgetType.path, WIDGET_COMPONENTS);

                        const configKey = sw.widgetType.path.replace('index.svelte', WIDGET_MARKER + '/index.svelte');

                        if (WIDGET_CONFIG_COMPONENTS[configKey]) {
                            sw.widgetType.configComponent = await getModule(configKey, WIDGET_CONFIG_COMPONENTS);
                        }

                        if (!sw.widgetGridStackOptions) {
                            sw.widgetGridStackOptions = {
                                w: sw.widgetType.defaultWidth,
                                h: sw.widgetType.defaultHeight,
                                minW: sw.widgetType.minWidth,
                                minH: sw.widgetType.minHeight,
                                maxW: sw.widgetType.maxWidth,
                                maxH: sw.widgetType.maxHeight,
                            };
                        }
                        sw.widgetType.isSubGrid = sw.widgetType.component.isSubGrid;
                        if (sw.widgetConfig) {
                            let widgetForLoadConfig = addWidgetForLoadConfig(
                                sw.widgetType,
                                false,
                                sw.widgetGridStackOptions,
                                sw.widgetId,
                                sw.widgetConfig,
                                parentId
                            );
                            tempWidgetGridConfig.push(widgetForLoadConfig);
                        } else {
                            let widgetForLoadConfig = addWidgetForLoadConfig(
                                sw.widgetType,
                                false,
                                sw.widgetGridStackOptions,
                                sw.widgetId,
                                null,
                                parentId
                            );
                            tempWidgetGridConfig.push(widgetForLoadConfig);
                        }

                        // If we succeed in adding, keep the configuration item
                        await LoadConfigRecursive(sw.widgetItems, tempWidgetGridConfig[i].widgetItems, sw.widgetId);
                    } else {
                        log.warn('Failed to load configuration for ' + sw.widgetType.path);
                    }
                } catch (err) {
                    log.error({ err }, 'Error loading Svelte component module for ' + sw.widgetId);
                }
            } catch (err) {
                log.error({ err, widgetGridConfig }, 'widgetGridConfig is erroneous');
            }
        }
=======
>>>>>>> aoh-web/develop
    }

    function widgetListAddWidgetHandler(new_widget: WidgetGridItem) {
        addWidget(new_widget.widget_type, new_widget.component, {
            widget_id: new_widget.id,
            config: new_widget.config,
            new_widget: true,
        });
    }

    // Add new widget to the list
<<<<<<< HEAD
    function addWidget(
        widget: LoadedWidgetType,
        broadcast: boolean,
        widgetGsOptions?: GridStackWidget & { broadcast?: boolean },
        id?: string,
        config?: Record<string, unknown>,
        widgetItemId?: string
=======
    async function addWidget(
        widget_type: WidgetType,
        widget_component: WidgetComponentRecord,
        options?: {
            widget_id?: string;
            config?: Record<string, unknown>;
            new_widget?: boolean;
        }
>>>>>>> aoh-web/develop
    ) {
        disableInteraction = true;

<<<<<<< HEAD
            if (!willItFit) {
                log.warn({ widget, broadcast, widgetGsOptions, id }, 'Not enough free space to place the widget');
                return;
            }

            widgetGsOptions = { ...widgetGsOptions, broadcast };
        }

        if (!config) {
            config = structuredClone(widget?.configComponent?.configDefaults);
        }

        if (!id) {
            id = v4();
        }

        if (isInSubGrid) {
            pushDataToWidget($loadedWidgetItemsStore, widgetItemId, {
                widgetId: id,
                widgetType: widget,
                widgetGridStackOptions: widgetGsOptions,
                widgetConfig: config,
                parentId: widgetItemId,
            });

            isInSubGrid = false;
        } else {
            $loadedWidgetItemsStore.push({
                widgetId: id,
                widgetType: widget,
                widgetGridStackOptions: widgetGsOptions,
                widgetConfig: config,
                widgetItems: [],
            });
        }

        // trigger reactivity
        $loadedWidgetItemsStore = $loadedWidgetItemsStore;
    }

    // Add new widget to the list
    function addWidgetForLoadConfig(
        widget: LoadedWidgetType,
        broadcast: boolean,
        widgetGsOptions?: GridStackWidget & { broadcast?: boolean },
        id?: string,
        config?: Record<string, unknown>,
        parentId?: string
    ) {
        if (!widgetGsOptions) {
            widgetGsOptions = {
                broadcast,
            };
        } else {
            let willItFit = get(widgetGrid.getGridStackStore()).willItFit({
                x: widgetGsOptions.x,
                y: widgetGsOptions.y,
                w: widgetGsOptions.w,
                h: widgetGsOptions.h,
                autoPosition: widgetGsOptions.autoPosition,
            });

            if (!willItFit) {
                log.warn({ widget, broadcast, widgetGsOptions, id }, 'Not enough free space to place the widget');
                return;
            }

            widgetGsOptions = { ...widgetGsOptions, broadcast };
        }

        if (!config) {
            config = widget?.configComponent?.configDefaults;
        }

        return {
            widgetId: id,
            widgetType: widget,
            widgetGridStackOptions: widgetGsOptions,
            widgetConfig: config,
            widgetItems: [],
            parentId: parentId,
        };
=======
        let willItFit = get(widgetGrid.getGridStackStore()).willItFit({
            w: widget_type.min_width,
            h: widget_type.min_height,
            autoPosition: true,
        });

        if (!willItFit) {
            log.warn({ dashboard_id: options.widget_id, widget_type }, 'not enough free space to place the widget');
            return;
        }

        if (!options.config) {
            options.config = structuredClone(widget_component?.config?.configDefaults);
        }

        if (!options.widget_id) {
            options.widget_id = v4();
        }

        const widget_to_add = {
            id: options.widget_id as UUID,
            width: widget_type.min_width,
            height: widget_type.min_height,
            row: 0,
            column: 0,
            config: options.config ?? widget_component?.config?.configDefaults,
            widget_type: widget_type,
            widget_type_id: widget_type.id as UUID,
            dashboard_id: selectedLayout.id as UUID,
            shared_config_id: null,
            occ_lock: 0,
            component: widget_component,
        };

        grid_layout.push(widget_to_add);
        grid_layout = grid_layout;

        DashboardStore.update(ds => {
            ds.widget_map.set(widget_to_add.id, widget_to_add);
            return ds;
        });

        // Double tick required to propagate changes
        await tick();
        await tick();

        if (options.new_widget) {
            await saveConfiguration();
        }

        disableInteraction = false;
>>>>>>> aoh-web/develop
    }

    // Delete a widget from the grid
    function handleDeleteWidget(event: CustomEvent<WidgetDetail>) {
        // Unselect if deleted widget is the same as the currently selected one
        if (selected_loaded_widget_item) {
            let previousSelectedElement: undefined | WidgetGridItem;

<<<<<<< HEAD
            previousSelectedElement = findWidgetById(
                $loadedWidgetItemsStore,
                event.detail.widgetId === selectedLoadedWidgetItemConfig.widgetId ? event.detail.widgetId : ''
            );
=======
            previousSelectedElement = grid_layout.find(element => {
                return event.detail.widgetId === element.id && element.id === selected_loaded_widget_item.id;
            });
>>>>>>> aoh-web/develop

            if (previousSelectedElement) {
                let widgetRef = previousSelectedElement.svelte_component_ref as WidgetItem;
                widgetRef.setSelected(false);
            }

            // Unset selected widget item config and close the panel
            selected_loaded_widget_item = undefined;
            isConfigOpen = false;
        }

<<<<<<< HEAD
        let parentGrid: GridStackHTMLElement = event.detail.widgetItem.closest('.grid-stack');
        parentGrid?.gridstack?.removeWidget(event.detail.widgetItem, false);
        $loadedWidgetItemsStore = filterWidgetByExcludeId($loadedWidgetItemsStore, event.detail.widgetId);
=======
        grid_layout = grid_layout.filter(widgetItemConfig => {
            return event.detail.widgetId !== widgetItemConfig.id;
        });

        DashboardStore.update(ds => {
            ds.widget_map.delete(event.detail.widgetId);

            return ds;
        });

        fetch(`${$locations.dash_web}/widget/${selectedLayout.id}`, {
            method: 'DELETE',
            body: event.detail.widgetId,
        })
            .then(resp => {
                resp.json()
                    .then(result => {
                        log.debug({ result, selectedLayout }, 'result from deleting the widget');
                    })
                    .catch(err => {
                        log.error({ err, selectedLayout }, 'error from deleting the widget');
                    });
            })
            .catch(err => {
                log.error({ err, selectedLayout }, 'error from deleting the widget');
            });
>>>>>>> aoh-web/develop
    }

    function deselect() {
        let previousSelectedElement: undefined | WidgetGridItem;

        // Unselect any already selected widget
<<<<<<< HEAD
        if (selectedLoadedWidgetItemConfig) {
            previousSelectedElement = findWidgetById($loadedWidgetItemsStore, selectedLoadedWidgetItemConfig.widgetId);
=======
        if (selected_loaded_widget_item) {
            previousSelectedElement = grid_layout.find(element => {
                return element.id === selected_loaded_widget_item.id;
            });
>>>>>>> aoh-web/develop

            if (previousSelectedElement) {
                let widgetRef = previousSelectedElement.svelte_component_ref as WidgetItem;
                widgetRef.setSelected(false);
            }

            // Unset selected widget item config and close the panel
            selected_loaded_widget_item = undefined;
            isConfigOpen = false;
        }

        return previousSelectedElement;
    }

    // Go into edit mode for a specific widget
    async function handleEdit(event: CustomEvent<WidgetDetail>) {
        let previousSelectedElement: undefined | WidgetGridItem = deselect();

<<<<<<< HEAD
        let newSelectedElement = findWidgetById($loadedWidgetItemsStore, event.detail.widgetId);
=======
        await tick();

        let newSelectedElement = grid_layout.find(element => {
            return element.id === event.detail.widgetId;
        });
>>>>>>> aoh-web/develop

        // If the new selection event is on a different widget (not a de-selection), then set it as 'selected'
        if (!previousSelectedElement || newSelectedElement.id !== previousSelectedElement.id) {
            let newWidgetRef = newSelectedElement.svelte_component_ref as WidgetItem;
            newWidgetRef.setSelected(true);

            selected_loaded_widget_item = newSelectedElement;
            isConfigOpen = true;
        } else {
            // Deselection, close panel
            isConfigOpen = false;
        }
    }

    function handleDeleteSubGrid(event: CustomEvent<WidgetDetail>) {
        let parentGrid: GridStackHTMLElement = event.detail.widgetItem.closest('.grid-stack');
        parentGrid?.gridstack?.removeWidget(event.detail.widgetItem, false);
        $loadedWidgetItemsStore = filterWidgetByExcludeId($loadedWidgetItemsStore, event.detail.widgetId);
        isConfigOpen = false;
    }

    function handleEditSubGrid(event: CustomEvent<WidgetDetail>) {
        let previousSelectedElement: undefined | LoadedWidgetItemConfig = deselect();

        let newSelectedElement = findWidgetById($loadedWidgetItemsStore, event.detail.widgetId);

        // If the new selection event is on a different widget (not a de-selection), then set it as 'selected'
        if (!previousSelectedElement || newSelectedElement.widgetId !== previousSelectedElement.widgetId) {
            let newWidgetRef = newSelectedElement.widgetItemRef as WidgetItem;
            newWidgetRef.setSelected(true);

            selectedLoadedWidgetItemConfig = newSelectedElement;
            isConfigOpen = true;
        } else {
            // Deselection, close panel
            isConfigOpen = false;
        }
    }

    function handleAddWidgetInSubGrid(event: CustomEvent<WidgetDetail>) {
        isWidgetListOpen = true;
        isInSubGrid = true;
        objWidgetList.setWidgetItemId(event.detail.widgetId);
    }

    function handleSelect(event: CustomEvent<WidgetDetail>) {
        // Not in use, using handleEdit instead...
    }

    function handleAddWidget(event: CustomEvent<WidgetDetail>) {}

    function handleToggleFullscreen() {
        if (browser && document) {
            if (document.fullscreenElement == document.documentElement) {
                exitFullscreen();
            } else {
                document.documentElement.requestFullscreen();
                setSidebarDisplay('none');
                setStatusBarDisplay('none');
            }
        }
    }

    function exitFullscreen() {
        if (browser && document) {
            if (document.fullscreenElement == document.documentElement) {
                document.exitFullscreen();
            }
            setSidebarDisplay('flex');
            setStatusBarDisplay('flex');
        }
    }

    enum ModalEnum {
        Delete,
        Reset,
        Save,
        LeavePageUnsaved,
        ChangeDashboardUnsaved,
        DeleteSuccess,
        NewPage,
        DuplicatePage,
    }

    type ModalInfo = {
        title: string;
        content: string;
        confirmText: string;
        showCancel: boolean;
        cancelText: string;
        accept: () => Promise<unknown>;
        specialContent?: boolean;
        specialContentName?: string;
        reject?: () => Promise<unknown>;
    };

    let confirmation_modal: Partial<Record<ModalEnum, ModalInfo>> = new Object();

    // Initialize Modals
    confirmation_modal[ModalEnum.Delete] = {
        title: 'Delete Page',
        content: 'Are you sure you wish to delete this layout?',
        confirmText: 'Confirm',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            // Upon confirmation - the user wants to delete the page
            // They accept that they don't care about lost changes, so reset the flag
            $isDirty = false;

            // Open waiting overlay
            disableInteraction = true;
            await tick();
            await delete_dashboard_layout(selectedLayout?.id);
            await tick();
            LoadConfig();

            // Close waiting overlay
            disableInteraction = false;
        },
    };

    confirmation_modal[ModalEnum.Reset] = {
        title: 'Reset Page',
        content: 'Are you sure you wish to undo all changes made?',
        confirmText: 'Confirm',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            await LoadConfig();
        },
    };

    confirmation_modal[ModalEnum.LeavePageUnsaved] = {
        title: 'Alert',
        content: 'Are you sure you wish to leave without saving?',
        confirmText: 'Proceed',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            await LoadConfig();
            $isDirty = false;

            if (routingUrl == undefined) {
                log.warn('route is undefined, preventing navigation');
                return;
            }

            goto(routingUrl);
        },
    };

    confirmation_modal[ModalEnum.DeleteSuccess] = {
        title: 'Layout Deleted Successfully',
        content: 'Your layout has been successfully deleted.',
        confirmText: 'Ok',
        showCancel: false,
        cancelText: 'Cancel',
        specialContent: false,
        accept: async () => {
            // Do nothing
        },
    };

    confirmation_modal[ModalEnum.DuplicatePage] = {
        title: 'Duplicate Page',
        content: '',
        confirmText: 'Confirm',
        showCancel: true,
        cancelText: 'Cancel',
        specialContent: true,
        specialContentName: 'Duplicate Page',
        accept: async () => {
            if (selected_loaded_widget_item && selected_loaded_widget_item.svelte_component_ref) {
                selected_loaded_widget_item.svelte_component_ref.setSelected(false);
                selected_loaded_widget_item = undefined;
            }

            // Open waiting overlay
            disableInteraction = true;
            await tick();

            await saveConfiguration();

            // Close waiting overlay
            disableInteraction = false;

            // Remove dirty flag
            $isDirty = false;

            // Close individual widget configs
            isConfigOpen = false;
        },
    };

    // Just default to any one of them
    let modal_type = ModalEnum.Delete;
    let is_confirm_modal_open = false;

    let routingUrl;

    /* --------------------------- handle widget list --------------------------- */
    let isWidgetListOpen = false;

    beforeNavigate(async ({ to, from, cancel }) => {
        if ($isDirty) {
            cancel();
            openModal(ModalEnum.LeavePageUnsaved);
            routingUrl = to.url;
        }
    });
</script>

{#if disableInteraction}
    <div
        transition:fade={{ duration: 150 }}
        class="fixed z-[60] flex w-full flex-col items-center justify-center overflow-y-auto overflow-x-hidden rounded
        bg-background text-on-primary opacity-50 transition md:inset-0 md:h-full">
        <Spinner appendClass="text-9xl" />
    </div>
{/if}

<!-- We hide everything (but still allow the Svelte to process the DOM) until loading is finished -->
<div class="h-full w-full bg-background p-2">
    <div class:grid-display={isEditable} class="h-full w-full" class:visible={!loading}>
        <!-- Main Content -->
        <!-- Dashboard Selector and other page controls at the top of the page -->
        <Status>
            <!-- Injects the content to where the Status Bar is in the page -->
            <header class="flex h-full items-center px-2 text-lg">
                <div class="flex h-full items-center justify-start gap-6">
                    <DashboardSelector
                        on:update={dashboardSelectorUpdateHandler}
                        on:selection={dashboardSelectorSelectionHandler}
                        dashboard_list_items={dashboards}
                        selected_dashboard={selectedLayout} />
                    <div>
                        <div>
                            <Button
                                on:click={async () => {
                                    isCreateDashboardDialogOpen = !isCreateDashboardDialogOpen;
                                }}
                                variant={ButtonVariant.EMPTY}
                                iconDefinition={faFileCirclePlus}
                                setColorClasses="text-mono-1" />
                            <Tooltip><div class="w-28">Create a new dashboard.</div></Tooltip>
                        </div>
                        <Dialog title="Create New Dashboard" bind:isOpen={isCreateDashboardDialogOpen}>
                            <DashboardDetails
                                mode="insert"
                                details={{
                                    id: '',
                                    name: undefined,
                                    description: undefined,
                                    tags: [],
                                }}
                                chips={tags}
                                on:create={dashboardDetailsCreateHandler} />
                        </Dialog>
                    </div>
                    <!-- Controls Shown Only in View Mode - Toggle Edit and Fullscreen -->
                    {#if $displayStateStore === DisplayState.ViewingDashboard}
                        <!-- Don't show edit and fullscreen button when there's no selected layout -->
                        {#if selectedLayout}
                            <div>
                                <Button
                                    on:click={() => {
                                        $displayStateStore = DisplayState.EditingDashboard;
                                    }}
                                    variant={ButtonVariant.EMPTY}
                                    iconDefinition={faEdit} />
                                <Tooltip><div class="w-36">Edit the current layout.</div></Tooltip>
                            </div>
                            <div>
                                <Button
                                    on:click={handleToggleFullscreen}
                                    variant={ButtonVariant.EMPTY}
                                    iconDefinition={faUpRightAndDownLeftFromCenter} />
                                <Tooltip><div class="w-36">Enter fullscreen mode.</div></Tooltip>
                            </div>
                        {/if}
                    {:else}
                        <!-- Controls Show only in Edit Mode - Toggle edit mode, add widget, change details, 
                            create new dashboard, and delete dashboard-->
                        <div class="flex h-full items-center justify-start gap-2">
                            <Button
                                data-subject="button"
                                on:click={async () => {
                                    if (
                                        selected_loaded_widget_item &&
                                        selected_loaded_widget_item.svelte_component_ref
                                    ) {
                                        selected_loaded_widget_item.svelte_component_ref.setSelected(false);
                                        selected_loaded_widget_item = undefined;
                                    }

                                    $displayStateStore = DisplayState.ViewingDashboard;

                                    // Remove dirty flag
                                    $isDirty = false;

                                    // Close individual widget configs
                                    isConfigOpen = false;

                                    await LoadConfig();
                                }}
                                variant={ButtonVariant.OUTLINE}>
                                CANCEL
                            </Button>
                            {#if $isDirty}
                                <Button
                                    data-subject="button"
                                    on:click={async () => {
                                        if (
                                            selected_loaded_widget_item &&
                                            selected_loaded_widget_item.svelte_component_ref
                                        ) {
                                            selected_loaded_widget_item.svelte_component_ref.setSelected(false);
                                            selected_loaded_widget_item = undefined;
                                        }
                                        // Open waiting overlay
                                        disableInteraction = true;
                                        await tick();
                                        await saveConfiguration();
                                        // Close waiting overlay
                                        disableInteraction = false;

                                        // Remove dirty flag
                                        $isDirty = false;

                                        // Close individual widget configs
                                        isConfigOpen = false;
                                    }}
                                    setColorClasses="bg-primary text-on-background"
                                    iconDefinition={faFloppyDisk}>
                                    SAVE LAYOUT CHANGES
                                </Button>
                            {/if}
                            <Button
                                on:click={() => {
                                    deselect();
                                    isWidgetListOpen = true;
                                }}
                                iconDefinition={faPlus}>ADD WIDGET</Button>
                            <Button
                                on:click={async () => {
                                    let subgridComponent;
                                    let subgridConfigComponent;
                                    let componentKey = '';
                                    for (let configKey in WIDGET_CONFIG_COMPONENTS) {
                                        componentKey = configKey.replace(WIDGET_MARKER + '/', '');
                                        subgridComponent = await getModule(componentKey, WIDGET_COMPONENTS);
                                        if (subgridComponent.isSubGrid) {
                                            subgridConfigComponent = await getModule(
                                                configKey,
                                                WIDGET_CONFIG_COMPONENTS
                                            );
                                            break;
                                        }
                                    }
                                    let w = {
                                        title: subgridComponent.widgetTitle,
                                        path: componentKey,
                                        component: subgridComponent,
                                        configComponent: subgridConfigComponent,
                                        icon: subgridComponent.widgetIcon,
                                        defaultWidth: subgridComponent.widgetWidth,
                                        defaultHeight: subgridComponent.widgetHeight,
                                        minWidth: subgridComponent.widgetMinWidth,
                                        minHeight: subgridComponent.widgetMinHeight,
                                        maxWidth: subgridComponent.widgetMaxWidth,
                                        maxHeight: subgridComponent.widgetMaxHeight,
                                        category: subgridComponent.widgetCategory,
                                        limit: subgridComponent.widgetLimit,
                                        enabled: subgridComponent.widgetEnabled,
                                        isSubGrid: 'isSubGrid' in subgridComponent ? subgridComponent.isSubGrid : false,
                                    };
                                    let opt = {
                                        w: subgridComponent.widgetWidth,
                                        h: subgridComponent.widgetHeight,
                                        minW: subgridComponent.widgetMinWidth,
                                        minH: subgridComponent.widgetMinHeight,
                                        maxW: subgridComponent.widgetMaxWidth,
                                        maxH: subgridComponent.widgetMaxHeight,
                                    };
                                    addWidget(w, true, opt, null, null, null);
                                }}
                                iconDefinition={faPlus}>ADD WIDGET GROUP</Button>
                            <WidgetList
<<<<<<< HEAD
                                bind:this={objWidgetList}
                                testids={{ dashboardPreviewTestId: testids.dashboardWidgetListDashboardPreviewTestId }}
                                onAdd={addWidget}
=======
                                onAdd={widgetListAddWidgetHandler}
>>>>>>> aoh-web/develop
                                {widgetValidationAction}
                                widgets={Array.from($DashboardStore.widget_type_map.values())}
                                bind:isOpen={isWidgetListOpen} />

                            <div>
                                <div>
                                    <Button
                                        data-subject="button"
                                        on:click={async () => {
                                            isEditDashboardDetailsDialogOpen = !isEditDashboardDetailsDialogOpen;
                                        }}
                                        iconDefinition={faGear} />
                                    <Tooltip><div class="w-28">Edit dashboard details.</div></Tooltip>
                                </div>
                                <Dialog title="Edit Dashboard Details" bind:isOpen={isEditDashboardDetailsDialogOpen}>
                                    <DashboardDetails
                                        mode="update"
                                        details={{
                                            id: selectedLayout?.id,
                                            name: selectedLayout?.name,
                                            description: selectedLayout?.description,
                                            tags: selectedLayout?.tags,
                                            occ_lock: selectedLayout?.occ_lock,
                                        }}
                                        chips={tags}
                                        on:update={dashboardDetailsUpdatedHandler} />
                                </Dialog>
                            </div>
                            {#if selectedLayout && selectedLayout.name}
                                <div>
                                    <Button
                                        on:click={async () => {
                                            confirmation_modal[
                                                ModalEnum.Delete
                                            ].content = `Are you sure you wish to delete "${selectedLayout.name}"?`;
                                            await openModal(ModalEnum.Delete);
                                        }}
                                        setColorClasses="text-mono-1"
                                        iconDefinition={faTrash} />
                                    <Tooltip><div class="w-28">Delete this layout.</div></Tooltip>
                                </div>
                            {/if}
                        </div>
                    {/if}
                </div>
            </header>
        </Status>

        <!-- Dashboard - Widget Grid Display -->
        <div class="border-box flex-grow bg-transparent">
<<<<<<< HEAD
            <WidgetGrid
                testid={testids.dashboardMainWidgetGridTestId}
                bind:this={widgetGrid}
                {options}
                showGrid={isEditable}>
                {#each $loadedWidgetItemsStore as item (item.widgetId)}
=======
            <WidgetGrid bind:this={widgetGrid} {options} showGrid={isEditable} editable={isEditable}>
                {#each grid_layout as item (item.id)}
>>>>>>> aoh-web/develop
                    <WidgetItem
                        bind:this={item.svelte_component_ref}
                        id={item.id}
                        title={item.widget_type.name}
                        options={{
                            w: item.width,
                            h: item.height,
                            x: item.column,
                            y: item.row,
                            minW: item.widget_type.min_width,
                            minH: item.widget_type.min_height,
                            maxW: item.widget_type.max_width,
                            maxH: item.widget_type.max_height,
                            noMove: !isEditable,
                            noResize: !isEditable,
                        }}
                        editable={isEditable}
<<<<<<< HEAD
                        isSubGrid={item.widgetType.isSubGrid}
                        parentId={item.parentId}
                        {GridStackLib}
                        {selectedLoadedWidgetItemConfig}
                        on:deleteSubGrid={handleDeleteSubGrid}
                        on:addWidgetInSubGrid={handleAddWidgetInSubGrid}
                        on:editSubGrid={handleEditSubGrid}
                        on:delete={handleDelete}
=======
                        on:add={handleAddWidget}
                        on:delete={handleDeleteWidget}
>>>>>>> aoh-web/develop
                        on:edit={handleEdit}
                        on:select={handleSelect}>
                        {#if selected_loaded_widget_item && selected_loaded_widget_item.id === item.id}
                            <!-- What's rendered when widget is selected -->
                            <svelte:component
                                this={item.component.main.default}
                                config={selected_loaded_widget_item.config} />
                        {:else if item.config}
                            <!-- What's rendered when there's a config -->
<<<<<<< HEAD
                            <svelte:component
                                this={item.widgetType.component.default}
                                id={item.widgetId}
                                config={{ ...item.widgetConfig, id: item.widgetId }} />
                        {:else}
                            <!-- What's rendered when there's no config-->
                            <svelte:component
                                this={item.widgetType.component.default}
                                id={item.widgetId}
                                config={item.widgetType.configComponent.configDefaults} />
=======
                            <svelte:component this={item.component.main.default} config={item.config} />
                        {:else}
                            <!-- What's rendered when there's no config-->
                            <svelte:component
                                this={item.component.main.default}
                                config={item.component.config.configDefaults} />
>>>>>>> aoh-web/develop
                        {/if}
                    </WidgetItem>
                {/each}
            </WidgetGrid>
        </div>
        <!-- Per-widget Configuration Panel -->
<<<<<<< HEAD
        {#if isConfigOpen === true && selectedLoadedWidgetItemConfig}
            <div
                id="configuration-panel"
                on:introend={resizeGrid}
                on:outroend={resizeGrid}
                use:widgetItemConfigAction
                class:hidden={!isConfigOpen}
                class:block={isConfigOpen}
                class="absolute z-30 w-1/5 flex-grow space-y-5 rounded-3xl border
                    border-selected-stroke bg-surface-gradient p-4 text-on-background shadow-glow shadow-selected-stroke">
                <div class="sticky top-0">
                    <div class="flex justify-between text-center text-sm">
                        Configure Widget
                        <Button
                            variant={ButtonVariant.EMPTY}
                            iconDefinition={faXmark}
                            on:click={() => {
                                isConfigOpen = false;
                                selectedLoadedWidgetItemConfig.widgetItemRef.setSelected(false);
                                selectedLoadedWidgetItemConfig = undefined;
                            }} />
                    </div>
                    <div class="p-2">
                        {#if selectedLoadedWidgetItemConfig}
                            <svelte:component
                                this={selectedLoadedWidgetItemConfig.widgetType.configComponent.default}
                                bind:config={selectedLoadedWidgetItemConfig.widgetConfig} />
                        {:else}
                            <div class="text-inherit">No widget selected</div>
                        {/if}
                    </div>
                </div>
            </div>
=======
        {#if isConfigOpen === true && selected_loaded_widget_item}
            <WidgetItemConfig
                bind:isOpen={isConfigOpen}
                init={widget_item_config_action}
                animationEndCallback={resizeGrid}
                {selected_loaded_widget_item}
                shared_configs={selected_widget_item_shared_configs}
                onClose={() => {
                    isConfigOpen = false;
                    selected_loaded_widget_item.svelte_component_ref.setSelected(false);
                    selected_loaded_widget_item = undefined;
                }}>
                <svelte:component
                    this={selected_loaded_widget_item.component.config.default}
                    bind:config={selected_loaded_widget_item.config} />
            </WidgetItemConfig>
>>>>>>> aoh-web/develop
        {/if}
    </div>
</div>

<Modal title={confirmation_modal[modal_type].title} isOpen={is_confirm_modal_open}>
    <div class="flex h-full flex-col items-center justify-center">
        <div class="flex grow items-center justify-center p-16 text-center text-sm">
            {confirmation_modal[modal_type].content}
        </div>
        <div class="flex h-fit w-full justify-end gap-1 border-t border-t-outline p-2">
            <Button
                setClass="rounded border-outline border p-1 px-2 text-sm
        hover:brightness-150 transition-color duration-200 ease-in-out {confirmation_modal[modal_type].showCancel
                    ? ''
                    : 'hidden'}"
                on:click={async () => {
                    // Close modal
                    is_confirm_modal_open = false;
                    await confirmation_modal[modal_type].reject();
                    await tick();
                }}>
                <span>{confirmation_modal[modal_type].cancelText}</span>
            </Button>
            <Button
                on:click={async () => {
                    // Close modal whilst executing callback
                    is_confirm_modal_open = false;
                    await confirmation_modal[modal_type].accept();
                    await tick();
                }}
                setClass="rounded border-outline border p-1 px-3 text-sm bg-error
        text-on-error hover:brightness-150 transition-color duration-200 ease-in-out">
                <span>{confirmation_modal[modal_type].confirmText}</span>
            </Button>
        </div>
    </div>
</Modal>
