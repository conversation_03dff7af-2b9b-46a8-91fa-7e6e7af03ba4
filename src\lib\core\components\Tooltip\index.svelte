<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    import { computePosition, flip, offset as offs, shift } from '@floating-ui/dom';
    import { onMount } from 'svelte';
    export let parent: HTMLElement = undefined;
    export let offset = 6;
    export let padded = true;

    let tooltipElement: HTMLDivElement;
    let tooltipArrow: HTMLDivElement;
    let isVisible = false;

    onMount(() => {
        if (!parent && tooltipElement?.parentElement) {
            parent = tooltipElement.parentElement;

            parent.addEventListener('pointerover', _event => {
                showTooltip();
            });
            parent.addEventListener('pointerout', _event => {
                hideTooltip();
            });
            parent.addEventListener('focus', _event => {
                showTooltip();
            });
            parent.addEventListener('blur', _event => {
                hideTooltip();
            });

            positionTooltip();
        }
    });

    function positionTooltip() {
        if (!parent || !tooltipElement) return;
        computePosition(parent, tooltipElement, {
            placement: 'bottom-start',
            middleware: [offs(offset), flip(), shift()],
        }).then(({ x, y }) => {
            Object.assign(tooltipElement.style, {
                left: `${x}px`,
                top: `${y}px`,
            });
        });
    }

    function showTooltip() {
        isVisible = true;
        positionTooltip();
    }

    function hideTooltip() {
        isVisible = false;
    }
</script>

<div
    bind:this={tooltipElement}
    on:pointerover
    on:pointerout
    on:focus
    on:blur
    class:pointer-events-none={!isVisible}
    class:opacity-0={!isVisible}
    class:opacity-100={isVisible}
    class:p-4={padded}
    class="absolute -left-full -top-full z-50 max-w-72 rounded-md bg-surface-1
    text-center text-xs text-on-surface-1 shadow-light transition-opacity">
    <slot />
    <div bind:this={tooltipArrow} />
</div>
