<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { type ComponentType } from 'svelte/internal';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    export interface WidgetComponent {
        id?: string;
        componentDisplay?: ComponentType;
        extraProps?: Record<string, any>;
        children?: WidgetComponent[];
        checkVisible?: string;
    }
</script>

<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { Accordion, AccordionItem } from 'flowbite-svelte';
    import WidgetItem from './WidgetItem.svelte';
    export let config: any;
    // export let configElementWidth: string = 'w-2/5';
    export let propertyFields: WidgetComponent[];
    export let thresholdFields: WidgetComponent[] = null;
    let configElement: HTMLElement;

    onMount(() => {
        // if (configElement) {
        //     let configurationPanel = configElement.parentElement.parentElement.parentElement;
        //     configurationPanel.classList.remove('w-1/5');
        //     configurationPanel.classList.add(configElementWidth);
        // }
    });

    onDestroy(() => {
        // if (configElement) {
        //     let configurationPanel = configElement.parentElement.parentElement.parentElement;
        //     configurationPanel.classList.remove(configElementWidth);
        //     configurationPanel.classList.add('w-1/5');
        // }
    });
</script>

<div bind:this={configElement}>
    <Accordion>
        <AccordionItem open>
            <span slot="header">Properties</span>
            <WidgetItem bind:config widgetFields={propertyFields} />
        </AccordionItem>
        {#if thresholdFields}
            <AccordionItem open>
                <span slot="header">Thresholds</span>
                <WidgetItem bind:config widgetFields={thresholdFields} />
            </AccordionItem>
        {/if}
    </Accordion>
</div>
