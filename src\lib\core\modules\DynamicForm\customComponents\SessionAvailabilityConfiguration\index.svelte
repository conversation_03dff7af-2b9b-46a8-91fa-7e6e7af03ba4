<script lang="ts">
    import { onMount } from 'svelte';

    export let form;
    export let fields;

    let listClinics = [];
    let listRooms = [];

    let oldServiceType = '';
    let times = 0;

    $: if (oldServiceType !== form.ServiceType) {
        if (times > 0) {
            form.Clinics = '';
            form.Room = '';
        }
        times++;
        oldServiceType = form.ServiceType;
    }

    // const initialize = async () => {
    //     const resClinic = await fetch(`/api/soc/cfg-ngemr-clinic`, {
    //         method: 'GET',
    //     });

    //     if (resClinic.ok) {
    //         listClinics = await resClinic.json();
    //     } else {
    //         console.error('Failed to fetch clinics');
    //     }

    //     const resRoom = await fetch(`/api/soc/cfg-room-sch-mapping`, {
    //         method: 'GET',
    //     });

    //     if (resRoom.ok) {
    //         listRooms = await resRoom.json();
    //     } else {
    //         console.error('Failed to fetch rooms');
    //     }
    // };

    onMount(() => {
        // initialize();
        console.log('SessionAvailabilityConfiguration component mounted');
    });
</script>
