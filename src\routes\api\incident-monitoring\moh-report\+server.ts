import { locations } from '$lib/stores/Locations';
import { StatusCodes } from 'http-status-codes';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

const IncidentMonitoringURL = `${get(locations).incident_monitoring}`;

export const GET: RequestHandler = async ({ locals, request, url, params }) => {
    let message: string;
    let status: StatusCodes;
    const page = url.searchParams.get('page') || 1;
    const limit = url.searchParams.get('limit') || 9999;

    const res = await fetch(`${IncidentMonitoringURL}/incident/moh-reports?page=${page}&limit=${limit}`, {
        method: 'GET',
        headers: {
            'content-type': 'application/json',
            'Authorization': 'Bearer ' + locals.user.access_token,
        },
    });

    const resp = await res.json();

    status = res.status;
    message = res.statusText;

    const data = resp.data ?? [];
    const metadata = resp?.metadata || {};

    const responseBody = {
        message,
        data,
        metadata,
    };

    return new Response(JSON.stringify(responseBody), {
        status,
    });
};

export const POST: RequestHandler = async ({ locals, request, url, params }) => {
    let message: string;
    let status: StatusCodes;
    const requestData: any = await request.json();
    const res = await fetch(`${IncidentMonitoringURL}/incident/moh-reports`, {
        method: 'POST',
        body: JSON.stringify(requestData),
        headers: {
            'content-type': 'application/json',
            'Authorization': 'Bearer ' + locals.user.access_token,
        },
    })
    const response = await res.json();

    return new Response(JSON.stringify(response), {
        status,
    });


   
}
