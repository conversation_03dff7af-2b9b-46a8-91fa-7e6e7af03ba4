<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export enum Candle_Stick_Label_Enum {
        Primary = 0,
        Secondary = 1,
        Threshold = 2,
    }
    export interface CandleStickConfig {
        title?: string;
        dataSource?: string;
        threshold?: number;
        labels: string[];
        colors: string[];
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'Candlestick';

    export const configDefaults: CandleStickConfig = {
        threshold: 0,
        dataSource: 'aoh_charts_candle_stick_sample',
        title: DEFAULT_TITLE,
        labels: ['Available', 'In Repair', 'Min. Quantity'],
        colors: ['#58D4EC', '#EFC12B', '#DE3730'],
    };
</script>

<script lang="ts">
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';
    import { dataSource } from '$lib/core/core';

    export let config: CandleStickConfig;

    let expectedFormat = [
        { name: 'quantity_1', type: 'Int' },
        { name: 'quantity_2', type: 'Int' },
        { name: 'threshold', type: 'Int' },
    ];

    // List of data source names (valid GraphQL query roots)
    let validQueries: Array<DropdownItem> = [];

    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => {
                return field?.name === format.name && format.type === field?.type?.ofType?.name;
            });
        });

        if (isQueryValid) validQueries.push({ label: query.name, value: query.name });
    });

    if (!validQueries.length) config.dataSource = '';

    const textClass = 'scroll text-on-surface';
    const inputClass =
        textClass +
        ' p-1 bg-transparent border border-outline transition-all duration-200 ease-in-out rounded focus:border-2 focus:border-primary font-light';
</script>

<div class="text-on-surface flex flex-col gap-2 backdrop-blur">
    <TextField
        placeholder={'Title'}
        bind:value={config.title}
        setClass="{inputClass} h-10 text-xs w-full"
        setLabelClass="text-primary" />

    <section class="">
        <header class="flex w-full items-center justify-between gap-2 text-xs">Data Sources</header>

        <div class="my-2 flex h-fit w-full items-center justify-between gap-2">
            <Dropdown
                title={'Data Source'}
                bind:value={config.dataSource}
                options={validQueries}
                setClass="{inputClass} h-10 text-xs w-full"
                setLabelClass="text-primary" />
        </div>
    </section>

    <header class="flex w-full items-center justify-between gap-2 text-xs">Legends</header>
    <section class="flex items-center gap-2">
        <TextField
            placeholder={'Primary'}
            bind:value={config.labels[Candle_Stick_Label_Enum.Primary]}
            setClass="{inputClass} h-10 text-xs grow"
            setLabelClass="text-primary" />

        <input
            type="color"
            class="h-10 w-10 rounded border-outline bg-transparent"
            bind:value={config.colors[Candle_Stick_Label_Enum.Primary]} />
    </section>

    <section class="flex items-center gap-2">
        <TextField
            placeholder={'Secondary'}
            bind:value={config.labels[Candle_Stick_Label_Enum.Secondary]}
            setClass="{inputClass} h-10 text-xs grow"
            setLabelClass="text-primary" />
        <input
            type="color"
            class="h-10 w-10 rounded border-outline bg-transparent"
            bind:value={config.colors[Candle_Stick_Label_Enum.Secondary]} />
    </section>

    <section class="flex items-center gap-2">
        <TextField
            placeholder={'Threshold'}
            bind:value={config.labels[Candle_Stick_Label_Enum.Threshold]}
            setClass="{inputClass} h-10 text-xs grow"
            setLabelClass="text-primary" />
        <input
            type="color"
            class="h-10 w-10 rounded border-outline bg-transparent"
            bind:value={config.colors[Candle_Stick_Label_Enum.Threshold]} />
    </section>
</div>
