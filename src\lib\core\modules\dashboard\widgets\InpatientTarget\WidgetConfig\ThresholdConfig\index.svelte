<script lang="ts" context="module">
    export interface ThresholdItemValid {
        isValid?: boolean;
        errorMessage: string;
    }

    export interface ThresholdItem {
        titleThresholdItem?: string;
        inpatientTarget?: number;
        targetUntilTime?: string;
        displayLabel?: string;
        isValidItem?: ThresholdItemValid;
    }

    export interface ThresholdConfig {
        isEnableThreshold?: boolean;
        thresholdConfigItems: ThresholdItem[];
    }
</script>

<script lang="ts">
    import { Label, Input, Button, Helper } from 'flowbite-svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';

    export let buttonfaPlus = faPlus;
    export let buttonfaMinus = faMinus;

    export const configThresholdItem: ThresholdItem = {
        titleThresholdItem: 'InpatientTarget',
        inpatientTarget: 20,
        targetUntilTime: '23:59',
        displayLabel: '23:59',
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
    };

    const THRESHOLD_CONFIG_ITEMS: Array<ThresholdItem> = [configThresholdItem];

    export let configThresholdConfigs: ThresholdConfig = {
        isEnableThreshold: true,
        thresholdConfigItems: THRESHOLD_CONFIG_ITEMS,
    };

    function getThresholdData() {
        return configThresholdConfigs.thresholdConfigItems;
    }

    function updateThresholdConfigData(data) {
        configThresholdConfigs.thresholdConfigItems = data;
    }

    function addThresholdConfigItem(thresholdConfig) {
        let array = thresholdConfig.thresholdConfigItems;
        if (!array.some(x => x.isValidItem.isValid === false)) {
            const lastItem = array[array.length - 1];
            const arrClone = {
                titleThresholdItem: lastItem.titleThresholdItem,
                inpatientTarget: lastItem.inpatientTarget,
                targetUntilTime: lastItem.targetUntilTime,
                displayLabel: lastItem.displayLabel,
                isValidItem: {
                    isValid: true,
                    errorMessage: '',
                },
            };
            array = [...array, arrClone];
            updateThresholdConfigData(array);
        }
    }

    function removeThresholdConfigItem(index) {
        let array = getThresholdData();
        if (array.length > 1) {
            array = array.slice(0, index).concat(array.slice(index + 1));
            updateThresholdConfigData(array);
        }
    }

    function checkValueOfThreshold(index: number) {
        const array = getThresholdData();
        if (array.length > 0) {
            let itemCurrent = array[index];
            let itemCurrentInpatientTarget = Number.parseInt(itemCurrent.inpatientTarget.toString());
            itemCurrent.isValidItem.isValid = true;
            itemCurrent.isValidItem.errorMessage = '';
            //Inpatient Target Validate
            if (itemCurrentInpatientTarget < 1 || itemCurrentInpatientTarget > 100) {
                itemCurrent.isValidItem.isValid = false;
                itemCurrent.isValidItem.errorMessage = `Current inpatient target value must be between 1 and 100`;
            }
            if (index > 0) {
                let itempPrevious = array[index - 1];
                if (!itemCurrent || !itempPrevious) {
                    return;
                }
                let itempPreviousinpatientTarget = Number.parseInt(itempPrevious.inpatientTarget.toString());
                if (itempPreviousinpatientTarget > itemCurrentInpatientTarget) {
                    itemCurrent.isValidItem.isValid = false;
                    itemCurrent.isValidItem.errorMessage = `Current threshold value must be greater previous threshold`;
                }
                //Time Validate
                if (itemCurrent.targetUntilTime < itempPrevious.targetUntilTime) {
                    itemCurrent.isValidItem.isValid = false;
                    itemCurrent.isValidItem.errorMessage = `Current until time must be later than previous until time`;
                }
            }
        }
    }

    function updateDataWhenThresholdChanged() {
        let array = getThresholdData();
        for (let i = 0; i < array.length; i++) {
            checkValueOfThreshold(i);
        }
    }

    $: if (configThresholdConfigs.thresholdConfigItems) {
        updateDataWhenThresholdChanged();
    }

    let pattern = '^[0-9]\\d*$';
    const onKeyDown = event => {
        if (pattern) {
            const newPattern = new RegExp(pattern);
            if (event.ctrlKey || event.altKey || typeof event.key !== 'string' || event.key.length !== 1) {
                return;
            }
            if (!newPattern.test(event.key)) {
                event.preventDefault();
            }
        }
    };
</script>

<div class="text-white dark:text-white">
    <div class="flex h-12">
        <Button
            pill={true}
            class={configThresholdConfigs.isEnableThreshold ? 'w-12 bg-gray-500' : 'hidden'}
            on:click={() => addThresholdConfigItem(configThresholdConfigs)}>{@html icon(buttonfaPlus).html}</Button>
        <span class="ml-2 self-center dark:text-white text-sm font-medium text-white">Add Threshold</span>
    </div>

    <div class="{configThresholdConfigs.isEnableThreshold ? '' : 'hidden'} ">
        <section class="flex flex-row flex-wrap items-center gap-2 py-2">
            <div class="w-[28%] self-center">
                <Label>Threshold Title</Label>
            </div>
            <div class="w-[20%] self-center break-all">
                <Label>Inpatient Target</Label>
            </div>
            <div class="w-[20%] self-center break-all">
                <Label>Until Time</Label>
            </div>
            <div class="w-[20%] self-center break-all">
                <Label>Display Label</Label>
            </div>
        </section>
        {#each configThresholdConfigs.thresholdConfigItems as item, index}
            <section class="flex flex-row flex-wrap items-center gap-2 py-2">
                <div class="w-[28%] self-center">
                    <Input placeholder="Threshold Title" bind:value={item.titleThresholdItem} />
                </div>
                <div class="w-[20%] self-center">
                    <Input
                        placeholder="Inpatient Target"
                        type="number"
                        bind:value={item.inpatientTarget}
                        on:keydown={onKeyDown}
                        min="1"
                        max="100" />
                </div>
                <div class="w-[20%] self-center">
                    {#if configThresholdConfigs.thresholdConfigItems.length - 1 == index}
                        <Input placeholder="Until Time" type="time" bind:value={item.targetUntilTime} disabled />
                    {:else}
                        <Input placeholder="Until Time" type="time" bind:value={item.targetUntilTime} />
                    {/if}
                </div>
                <div class="w-[20%] self-center">
                    <Input placeholder="Display Label" bind:value={item.displayLabel} />
                </div>
                <div class="self-center">
                    {#if configThresholdConfigs.thresholdConfigItems.length - 1 !== index}
                        <Button
                            class="h-12 w-12 bg-gray-500"
                            pill={true}
                            on:click={() => removeThresholdConfigItem(index)}>{@html icon(buttonfaMinus).html}</Button>
                    {/if}
                </div>
            </section>
            {#if item.isValidItem.isValid == false}
                <Helper class="text-sm font-medium" color="red">
                    <span>{item.isValidItem.errorMessage}</span>
                </Helper>
            {/if}
        {/each}
    </div>
</div>
