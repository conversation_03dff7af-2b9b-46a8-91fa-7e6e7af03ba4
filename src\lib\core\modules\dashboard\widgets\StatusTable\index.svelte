<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import dayjs from 'dayjs';
    import gql from 'graphql-tag';

    // Plugin to allow usage of durations
    dayjs.extend(dayjs.duration);

    export const widgetTitle = 'Status Table';
    export const widgetIcon = faUser;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetEnabled = true;
    export const widgetMinWidth = 4;
    export const widgetMinHeight = 8;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import type { StatusConfig } from '$lib/core/modules/dashboard/widgets/StatusTable/WidgetConfig/index.svelte';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { faUser } from '@fortawesome/free-solid-svg-icons';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';

    export let config: StatusConfig;

    type StatusMap = Map<string, { count: number }>;

    function getTotal(statusMap: StatusMap): number {
        let total = 0;
        statusMap.forEach(value => {
            total += value.count;
        });
        return total;
    }

    let subscriptions: Subscription[] = [];
    let categoryMap: Map<string, StatusMap> = new Map();

    let categories: { id: string }[] = [];
    let statuses: { id: string }[] = [];

    let headers: Set<string> = new Set();
    let data;

    const loadData = result => {
        data = [];
        categoryMap = new Map();

        categories.forEach(category => {
            let map: StatusMap = new Map();
            statuses.forEach(status => {
                map.set(status.id, { count: 0 });
                headers.add(status.id);
            });
            categoryMap.set(category.id, map);
        });

        if (result.data) {
            data = result.data[config.dataSource];

            data.forEach(d => {
                if (!d.category) return;
                let category = d.category;
                let status = d.status_type;

                //If the map doesnt have this status key, add an entry into the map
                if (!categoryMap.has(category)) {
                    //Create a new category entry
                    let map: StatusMap = new Map<string, { count: number }>();
                    categoryMap.set(category, map);
                }

                //Update the status count.
                let statusMap = categoryMap.get(category);

                if (!statusMap.has(status)) categoryMap.get(category).set(status, { count: 1 });
                else statusMap.get(status).count += 1;

                headers.add(status);
            });

            //Trigger reactivity
            categoryMap = categoryMap;
            headers = headers;
        }
    };

    const loadStatuses = result => {
        if (!result?.data) return;
        statuses = result.data[config.statusTypes];

        categoryMap.forEach((statusMap, category) => {
            statuses.forEach(s => {
                if (!statusMap.has(s.id)) statusMap.set(s.id, { count: 0 });
            });
        });

        categoryMap = categoryMap;
    };

    const loadCategories = result => {
        if (!result?.data) return;

        categories = result.data[config.categoryTypes];
        categories.forEach(c => {
            if (!categoryMap.has(c.id)) categoryMap.set(c.id, new Map());
        });

        categoryMap = categoryMap;
    };

    function replaceUnderscoresWithSpaces(text: string): string {
        return text?.replaceAll('_', ' ').toLowerCase();
    }

    const FetchStatusTableDocument = gql`
        query {
            ${config.dataSource} (order_by: {status_type: asc}) {
                name
                status_type
                category
            }
        }
    `;

    const FetchStatusesEnumDocument = gql`
        query {
            ${config.statusTypes} {
                id
            }
        }
    `;

    const FetchCategoryEnumDocument = gql`
        query {
            ${config.categoryTypes} {
                id
            }
        }
    `;

    const ObserveStatusTableDocument = gql`
        subscription {
            ${config.dataSource} (order_by: {status_type: asc}) {
                name
                status_type
                category
            }
        }
    `;

    const ObserveStatusesEnumDocument = gql`
        subscription {
            ${config.statusTypes} {
                id
            }
        }
    `;

    const ObserveCategoryEnumDocument = gql`
        subscription {
            ${config.categoryTypes} {
                id
            }
        }
    `;

    const client = get(gqlClientStore);
    client.query(FetchStatusTableDocument, {}).toPromise().then(loadData);
    client.query(FetchCategoryEnumDocument, {}).toPromise().then(loadCategories);
    client.query(FetchStatusesEnumDocument, {}).toPromise().then(loadStatuses);

    onMount(() => {
        categoryMap = new Map();

        subscriptions.push(wPipe(client.subscription(ObserveStatusTableDocument, {}), wSubscribe(loadData)));
        subscriptions.push(wPipe(client.subscription(ObserveCategoryEnumDocument, {}), wSubscribe(loadCategories)));
        subscriptions.push(wPipe(client.subscription(ObserveStatusesEnumDocument, {}), wSubscribe(loadStatuses)));

        categoryMap = categoryMap;
    });

    onDestroy(() => {
        subscriptions.forEach(subscription => {
            subscription?.unsubscribe();
        });
    });

    let boundingElem: HTMLElement;
    let childElems: HTMLElement[] = [];
    let hasOverflow: boolean = false;

    $: if (boundingElem?.childNodes) {
        const parentRect = boundingElem?.parentElement.parentElement.getBoundingClientRect();

        hasOverflow = childElems.some(child => {
            let childRect = child.getBoundingClientRect();
            return childRect.bottom > parentRect.bottom;
        });
    }
</script>

<div class="h-full max-h-[95%] w-full py-1 text-on-background" bind:this={boundingElem}>
    <!-- Title -->
    <header class="flex h-8 items-center justify-between gap-4 px-4 py-2">
        <p class="bg-transparent">{config.title}</p>
        {#if hasOverflow}
            <span class="ml-auto text-xs text-error"
                >Data is too huge for widget canvas, some content may be hidden</span>
        {/if}
    </header>

    <!-- Content-->
    <div class="flex w-full grow gap-2">
        <section class="max-h-full w-3/4">
            <header class="my-1 flex h-8 w-full items-center rounded bg-surface-1 font-semibold">
                <span class="h-full w-[8rem] px-2 text-center text-sm" />
                <span class="w-[6rem] px-2 text-center uppercase">Total</span>
                <div class="grid w-full grid-flow-col gap-2">
                    {#each statuses as status}
                        <p class="text-center">{status.id}</p>
                    {/each}
                </div>
            </header>

            <div class="flex h-full w-full flex-col gap-2 px-2">
                {#each [...categoryMap] as [category, statusMap], y}
                    {@const total = getTotal(statusMap)}
                    <div bind:this={childElems[y]} class="bg-surface-container-high flex max-h-16 w-full rounded">
                        <span
                            class="flex h-full w-[8rem] items-center gap-2 px-2 py-1 text-center text-sm font-light capitalize"
                            >{replaceUnderscoresWithSpaces(category)}
                        </span>
                        <span
                            class="bg-primary-container my-auto flex h-3/4 w-[4rem] items-center justify-center rounded text-center text-2xl font-semibold"
                            >{total}</span>
                        <div class="grid w-full grid-flow-col gap-2">
                            {#each [...statusMap] as [status, data], y}
                                <span class="flex items-center justify-center text-2xl font-semibold"
                                    >{data ? data.count : data}</span>
                            {/each}
                        </div>
                    </div>
                {/each}
            </div>
        </section>

        <!-- Status Table-->
        <div class="h-full w-1/4 overflow-y-auto">
            <table class="w-full table-fixed text-xs">
                <thead>
                    <tr class="w-full text-xs font-light">
                        <td class="px-2 text-start">Name</td>
                        <td class="w-fit text-center">Status</td>
                    </tr>
                </thead>
                {#if data}
                    <tbody>
                        {#each data as d, y}
                            <tr class="h-8 {y % 2 ? 'bg-surface-container-high' : ''} text-sm">
                                <td class="px-2 text-start">{d.name}</td>
                                <td class="text-center">{d.status_type}</td>
                            </tr>
                        {/each}
                    </tbody>
                {/if}
            </table>
        </div>
    </div>
</div>
