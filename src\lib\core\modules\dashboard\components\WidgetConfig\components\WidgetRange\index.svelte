<script lang="ts">
    import { Label, Range } from 'flowbite-svelte';
    export let title: string;
    export let min = 0;
    export let max = 10;
    export let step = 1;
    export let value;
    export let customClass = 'mb-6';
</script>

<div class={customClass}>
    <div class="relative mb-4">
        {#if title}<Label class="mb-2">{title}</Label>{/if}
        <Range {min} {max} {step} bind:value />
        <span class="absolute -bottom-6 start-0 text-sm text-gray-500 dark:text-gray-400">Min</span>
        {#each { length: 9 } as _, i}
            <span
                class="absolute -bottom-6 -translate-x-1/2 text-sm text-gray-500 rtl:translate-x-1/2 dark:text-gray-400"
                style="inset-inline-start: {i + 1}0%;">{i + 1}</span>
        {/each}
        <span class="absolute -bottom-6 end-0 text-sm text-gray-500 dark:text-gray-400">Max</span>
    </div>
</div>
