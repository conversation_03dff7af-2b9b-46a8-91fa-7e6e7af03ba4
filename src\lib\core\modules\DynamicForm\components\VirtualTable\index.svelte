<script>
    import { Checkbox } from 'flowbite-svelte';
    import { createEventDispatcher } from 'svelte';
    import { faPencil } from '@fortawesome/free-solid-svg-icons';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { CaretDownSolid, CaretUpSolid } from 'flowbite-svelte-icons';

    export let columns = [];
    export let rows = [];
    export let rowHeight = 53;
    export let height = 550;
    export let buffer = 5;
    export let keyField = 'id';
    export let selectedRows = new Set();
    export let sortBySelected = false;
    export let sortDirection;
    // For parent to get info about visible range, etc
    const dispatch = createEventDispatcher();

    let scrollTop = 0;

    // Convert selectedRows to Set if it's passed as an array
    $: if (Array.isArray(selectedRows)) {
        selectedRows = new Set(selectedRows);
    }

    $: totalRows = rows.length;
    $: start = Math.max(0, Math.floor(scrollTop / rowHeight) - buffer);
    $: end = Math.min(totalRows, Math.ceil((scrollTop + height) / rowHeight) + buffer);
    $: visibleRows = rows.slice(start, end);

    $: paddingTop = start * rowHeight;
    $: paddingBottom = (totalRows - end) * rowHeight;

    function handleScroll(e) {
        scrollTop = e.target.scrollTop;
        dispatch('scroll', { scrollTop, start, end });
    }
    const toggleSelection = itemId => {
        if (selectedRows.has(itemId)) {
            selectedRows.delete(itemId);
        } else {
            selectedRows.add(itemId);
        }
        // Reassign to trigger reactivity
        selectedRows = new Set(selectedRows);
    };
</script>

<div
    class="h-[550px] w-full overflow-x-auto overflow-y-auto border border-gray-600 shadow-md sm:rounded-lg"
    style="height:{height}px;"
    on:scroll={handleScroll}>
    <table class=" vtable w-full text-left text-sm text-gray-500 dark:text-gray-400">
        <thead
            class="border-b border-gray-600 bg-white text-xs capitalize text-gray-700 dark:bg-gray-800 dark:text-gray-400">
            <tr>
                <th class=" border-l border-gray-600 !p-4 px-6 py-3 {rows.length === 0 ? 'min-w-10' : 'w-10'}">
                    <div class="flex flex-row items-center gap-2 text-base text-on-background">
                        <div class="flex min-w-[175px] items-center gap-2">
                            <button
                                type="button"
                                class="m-0 cursor-pointer select-none border-none bg-transparent p-0"
                                on:click={() => {
                                    dispatch('selectAllClicked');
                                }}
                                aria-label="Select All">
                                Select All [{@html icon(faPencil).html}] &nbsp;
                            </button>
                            <Checkbox
                                checked={rows &&
                                    selectedRows &&
                                    rows.length > 0 &&
                                    rows.every(row => selectedRows.has(row[keyField || 'id']))}
                                on:click={() => {
                                    const allSelected = rows.every(row => selectedRows.has(row[keyField || 'id']));
                                    if (allSelected) {
                                        rows.forEach(row => selectedRows.delete(row[keyField || 'id']));
                                    } else {
                                        rows.forEach(row => selectedRows.add(row[keyField || 'id']));
                                    }
                                    selectedRows = new Set(selectedRows);
                                }} />
                            {#if sortBySelected}
                                {#if sortDirection === 1}<CaretUpSolid />{/if}
                                {#if sortDirection === -1}<CaretDownSolid />{/if}
                            {/if}
                        </div>
                    </div>
                </th>
                {#each columns as column}
                    <th class="relative border-l border-gray-600 px-6 py-3 capitalize">
                        <slot name="header" {column}>
                            <div class="gap-2 pr-2 text-base text-on-background">
                                {column}
                            </div>
                        </slot>
                    </th>
                {/each}
            </tr>
        </thead>
        <tbody>
            {#if paddingTop > 0}
                <tr style="height: {paddingTop}px;">
                    <td colspan={columns.length} style="padding:0; border:none;" />
                </tr>
            {/if}

            {#each visibleRows as row, rIdx (start + rIdx)}
                <tr
                    class="border-b bg-white last:border-b-0 odd:bg-white even:bg-gray-50 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 odd:dark:bg-gray-800 even:dark:bg-gray-700 dark:hover:bg-gray-600">
                    <td
                        class="min-w-[180px] whitespace-nowrap border-b border-l border-gray-600 !p-4 px-6 py-4 text-center font-medium text-gray-900 dark:text-white">
                        <input
                            type="checkbox"
                            checked={selectedRows.has(row[keyField || 'id'])}
                            on:change={() => toggleSelection(row[keyField || 'id'])}
                            value={row[keyField || 'id']} />
                    </td>

                    {#each columns as column}
                        <td
                            class="whitespace-nowrap border-b border-l border-gray-600 px-6 py-4 font-medium text-gray-900 dark:text-white">
                            <slot name="cell" {row} {column} value={row[column]}>
                                {row[column] || '-'}
                            </slot>
                        </td>
                    {/each}
                </tr>
            {/each}

            {#if paddingBottom > 0}
                <tr style="height: {paddingBottom}px;">
                    <td colspan={columns.length} style="padding:0; border:none;" />
                </tr>
            {/if}
        </tbody>
    </table>
</div>

<style>
    .vtable thead {
        position: sticky;
        top: 0;
        z-index: 2;
    }
</style>
