<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faArrowsLeftRightToLine, faCircleLeft, faCircleRight, faPalette } from '@fortawesome/free-solid-svg-icons';
    import { Button } from 'flowbite-svelte';
    import lodash from 'lodash';
    import { createEventDispatcher } from 'svelte';

    const uniqueId = lodash.uniqueId;

    export let classes = '';
    export let activeID = '';
    export let showLeft = true;
    export let showRight = true;
    export let height = 0;

    const dispatch = createEventDispatcher();
    const containerID = uniqueId();
    let resizing = false;
    // pos is cursor position when right click occur
    let pos = { x: 0, y: 0 };
    // menu is dimension (height and width) of context menu
    let menu = { h: 0, w: 0 };
    // browser/window dimension (height and width)
    let browser = { h: 0, w: 0 };
    // showMenu is state of context-menu visibility
    export let showMenu = false;

    $: if (activeID !== '' && activeID !== containerID) {
        showMenu = false;
    }

    const rightClickContextMenu = (
        e: MouseEvent & {
            currentTarget: EventTarget & HTMLButtonElement;
        }
    ) => {
        showMenu = true;
        browser = {
            w: window.innerWidth,
            h: window.innerHeight,
        };
        pos = {
            x: e.clientX,
            y: e.clientY,
        };
        // If bottom part of context menu will be displayed
        // after right-click, then change the position of the
        // context menu. This position is controlled by `top` and `left`
        // at inline style.
        // Instead of context menu is displayed from top left of cursor position
        // when right-click occur, it will be displayed from bottom left.
        if (browser.h - pos.y < menu.h) pos.y = pos.y - menu.h;
        if (browser.w - pos.x < menu.w) pos.x = pos.x - menu.w;

        dispatch('show', containerID);
    };

    const getContextMenuDimension = node => {
        // This function will get context menu dimension
        // when navigation is shown => showMenu = true
        let height = node.offsetHeight;
        let width = node.offsetWidth;
        menu = {
            h: height,
            w: width,
        };
    };

    const onPageClick = (
        e: MouseEvent & {
            currentTarget: EventTarget & Window;
        }
    ) => {
        // To make context menu disappear when
        // mouse is clicked outside context menu
        showMenu = false;
    };

    const outsideContextMenu = () => {
        showMenu = false;
    };

    const onAction = (actionName: string) => {
        dispatch('action', actionName);
        if (actionName === 'resize') {
            resizing = !resizing;
        }
    };
</script>

<button
    id={containerID}
    class={`${classes} relative z-10 ${resizing && 'cursor-default'}`}
    style="height: {height}px;"
    on:contextmenu|preventDefault={rightClickContextMenu}>
    <slot />
    <nav
        use:getContextMenuDimension
        class="absolute left-5 top-5 w-48 rounded-lg bg-gray-600 bg-opacity-10 bg-clip-padding backdrop-blur-sm backdrop-filter transition-all duration-300 {!showMenu &&
            'hidden'}">
        <div class="navbar" id="navbar">
            <ul class="list-none">
                <li class="border-b border-b-gray-800 {resizing && 'bg-gray-500'}">
                    <Button class="flex w-full justify-start gap-3" on:click={() => onAction('resize')}>
                        {@html icon(faArrowsLeftRightToLine).html}
                        <span>Resize</span>
                    </Button>
                </li>
                <li class="border-b border-b-gray-800 {!showLeft && 'hidden'}">
                    <Button class="flex w-full justify-start gap-3" on:click={() => onAction('move-left')}>
                        {@html icon(faCircleLeft).html}
                        <span>Move Left</span>
                    </Button>
                </li>
                <li class="border-b border-b-gray-800 {!showRight && 'hidden'}">
                    <Button class="flex w-full justify-start gap-3" on:click={() => onAction('move-right')}>
                        {@html icon(faCircleRight).html}
                        <span>Move Right</span>
                    </Button>
                </li>
                <li class="border-b border-b-gray-800 {!showRight && 'hidden'}">
                    <Button class="flex w-full justify-start gap-3" on:click={() => onAction('edit-style')}>
                        {@html icon(faPalette).html}
                        <span>Edit Style</span>
                    </Button>
                </li>
            </ul>
        </div>
    </nav>
</button>

<svelte:window on:click={onPageClick} />
