<script lang="ts">
    import { ColumnDataType, INV_STYLES, type ColumnName } from '$lib/shared/constants/general';
    import InvTable from '$lib/shared/components/table/Table.svelte';
    import { Button, Heading, Select } from 'flowbite-svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faAdd, faRefresh, faSave, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
    import { v4 as uuidv4 } from 'uuid';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import { categoryColorValues } from '../../schema';
    import Search from '$lib/shared/components/search/Search.svelte';
    import { ColumnType } from '$lib/shared/enum/search';
    import { hasAccess } from '$lib/shared/util/validation';
    import { SCOPE } from '$lib/shared/enum/general';

    export let abilities = [];
    export let data;
    export let onSave;
    export let onDelete;

    let tableData = [];
    let newRecords = new Set();
    let searchText = '';
    let selectedRow = null;
    let openModal = false;
    let onlyOkBtn = false;
    let modalMessage = '';
    let modalTitle = '';

    const initTableDate = () => {
        tableData = data.map(item => ({
            id: item.id,
            category: item.name,
            description: item.description,
            severity_colour_code: item.severity_colour_code,
        }));
    };

    initTableDate();

    let updateList = [];

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    const defaultPosition: UpdatePosition = { id: '', columnName: '', isEditing: false };
    let updatePosition: UpdatePosition = structuredClone(defaultPosition);

    const handleAppendNewRow = () => {
        const newId = uuidv4();
        tableData = [
            ...tableData,
            {
                id: newId,
                category: null,
                description: null,
                severity_colour_code: 'red',
                isNew: true,
            },
        ];
        newRecords.add(newId);
    };

    const onReset = () => {
        location?.reload();
    };

    const handleDeleteRow = async () => {
        let result;
        if (!newRecords.has(selectedRow)) {
            result = await onDelete(selectedRow);
        }
        if (result == true || newRecords.has(selectedRow)) {
            tableData = tableData.filter(item => item.id !== selectedRow);
            updateList = updateList.filter(item => item.id !== selectedRow);
            selectedRow = null;
        }
    };

    const columnNames: ColumnName[] = [
        {
            key: 'category',
            value: 'Incident Category',
            editable: true,
            type: ColumnDataType.String,
            searchType: ColumnType.String,
        },
        {
            key: 'description',
            value: 'Incident Category Description',
            editable: true,
            type: ColumnDataType.String,
            searchType: ColumnType.String,
        },
        {
            key: 'severity_colour_code',
            value: 'Severity Colour Code',
            editable: true,
            customStyle: true,
            searchType: ColumnType.String,
        },
    ];
    const searchColumns = columnNames.map(i => ({ name: i.key, label: i.value, type: i.searchType }));

    const handleChangeColor = (category, color) => {
        const idx = updateList.findIndex(e => e.id === category.id);
        if (idx === -1) {
            const newItem = structuredClone(category);
            newItem['severity_colour_code'] = color;
            updateList = [...updateList, newItem];
        } else {
            updateList[idx]['severity_colour_code'] = color;
        }
    };
    $: filteredData = [...tableData];

    const checkDeleteRow = () => {
        if (!selectedRow) {
            modalTitle = 'Notification';
            modalMessage = 'Please select a record!';
            onlyOkBtn = true;
            openModal = true;
        } else {
            modalTitle = 'Confirmation';
            modalMessage = 'Are you sure you want to delete the selected record(s)?<br />Click Yes to confirm.';
            onlyOkBtn = false;
            openModal = true;
        }
    };
</script>

<div class={INV_STYLES.fullTable}>
    <div class={INV_STYLES.header}>
        <Heading tag="h5">Incident CAT Configuration</Heading>
    </div>
    <div>
        <div class="m-4">
            <div class="flex items-center gap-2">
                <div class="relative">
                    <Search
                        bind:searchText
                        columns={searchColumns}
                        data={tableData}
                        on:apply={e => {
                            filteredData = e.detail ? e.detail.filteredData : tableData;
                        }} />
                </div>
                {#if hasAccess(abilities, SCOPE.UPDATE) || hasAccess(abilities, SCOPE.CREATE)}
                    <Button size="md" color="light" on:click={() => onSave(tableData, updateList, newRecords)}>
                        <span class="mr-2">{@html icon(faSave).html}</span>
                        <span class="text-nowrap">Save</span>
                    </Button>
                {/if}
                {#if hasAccess(abilities, SCOPE.CREATE)}
                    <Button size="md" color="light" on:click={() => handleAppendNewRow()}>
                        <span class="mr-2">{@html icon(faAdd).html}</span>
                        <span class="text-nowrap">New</span>
                    </Button>
                {/if}
                {#if hasAccess(abilities, SCOPE.DELETE)}
                    <Button size="md" color="light" on:click={() => checkDeleteRow()}>
                        <span class="mr-2">{@html icon(faTrashAlt).html}</span>
                        <span class="text-nowrap">Delete</span>
                    </Button>
                {/if}
                <Button size="md" color="light" on:click={() => onReset()}>
                    <span class="mr-2">{@html icon(faRefresh).html}</span>
                    <span class="text-nowrap">Refresh</span>
                </Button>
            </div>
        </div>
        <InvTable
            data={filteredData}
            {columnNames}
            bind:updateList
            bind:updatePosition
            bind:searchText
            bind:selectedRow
            allowSelected={true}>
            <div slot="row-custom" class="h-14" let:itemData>
                <div class="items flex h-full items-center px-4">
                    <div class="w-full rounded px-2 py-1">
                        <Select
                            items={categoryColorValues}
                            on:change={event => {
                                handleChangeColor(itemData, event.target.value);
                            }}
                            value={itemData?.severity_colour_code || 'red'} />
                    </div>
                </div>
            </div>
        </InvTable>
    </div>
</div>
<Notification
    bind:isOpen={openModal}
    {modalTitle}
    {modalMessage}
    {onlyOkBtn}
    confirmAction={onlyOkBtn ? null : handleDeleteRow} />
