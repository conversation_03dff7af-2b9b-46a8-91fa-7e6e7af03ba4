name: Web - Main CI - FPT

env:
  app_name: hcc3-web-fpt
  mytag: latest

on:
  workflow_dispatch:

concurrency:
  group: ci-hcc3-web-${{ github.ref }}
  cancel-in-progress: true

jobs:
  validate-branch:
    runs-on: ubuntu-latest
    outputs:
      run_ci: ${{ steps.check-branch.outputs.run_ci }}
    steps:
      - name: Check if branch name matches sprint pattern
        id: check-branch
        run: |
          echo "GITHUB_REF_NAME=${GITHUB_REF_NAME}"
          if [[ "$GITHUB_REF_NAME" =~ ^release/sprint-[0-9]+-fpt(-internal)?$ ]]; then
            echo "✅ Branch matches expected pattern"
            echo "run_ci=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Branch does not match pattern. Skipping workflow."
            echo "run_ci=false" >> $GITHUB_OUTPUT
          fi

  build-and-publish-image:
    needs: validate-branch
    if: needs.validate-branch.outputs.run_ci == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Generate CURRENT_DATE based on timestamp
        run: echo CURRENT_DATE=$(date +%s) >> $GITHUB_ENV

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          path: ./SRC

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Ensure ECR repository exists
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          aws ecr describe-repositories --repository-names ${{ env.app_name }} || \
          aws ecr create-repository --repository-name ${{ env.app_name }}

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ env.mytag }}
        run: |
          cd ./SRC
          docker build . -f Dockerfile -t $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG \
            --build-arg PORT=5173 \
            --build-arg IAM_CLIENT_ID=aoh_web \
            --build-arg IAM_CLIENT_SECRET=bvtNqH700tLb4q1WFtqOHM2aSaiSvckE \
            --build-arg PUBLIC_STATIC_BUILD_VERSION=${{ env.CURRENT_DATE }} \
            --build-arg PUBLIC_STATIC_OVERRIDE_HOME=0 \
            --build-arg PUBLIC_HASURA_COOKIE_MODE=1 \
            --build-arg IAM_URL=http://iams-keycloak:8080/realms/AOH \
            --build-arg GRAPHQL_URL=http://hcc3-hasura.*************.nip.io/v1/graphql \
            --build-arg PUBLIC_SECURE_MODE=0 \
            --build-arg PUBLIC_REPLAY_MODE=0 \
            --build-arg BATCHJOB_URL=http://batchjob:5000/v1 \
            --build-arg SOP_URL=http://sop:5002 \
            --build-arg AUDITLOG_URL=http://audittrail:8085/api \
            --build-arg INVENTORY_URL=http://inventory:1323 \
            --build-arg TAG_URL=http://tag:5006 \
            --build-arg DASH_URL=http://dash:5005 \
            --build-arg COMMON_URL=http://common:5009 \
            --build-arg DATA_AGG_URL=http://data-agg:5003 \
            --build-arg ORIGIN=http://hcc3-web.*************.nip.io \
            --build-arg PUBLIC_DOMAIN=*************.nip.io \
            --build-arg KEYCLOAK_AUTH_URL=http://iams-keycloak.*************.nip.io/realms/AOH/protocol/openid-connect/userinfo \
            --build-arg IAMS_AAS_URL=http://iams-aas.*************.nip.io \
            --build-arg WORKFLOW_ENGINE_URL=http://wfm.*************.nip.io \
            --build-arg INCIDENT_MONITORING_URL=http://incident:5010 \
            --build-arg SOC_URL=http://soc:5011
          docker push $ECR_REGISTRY/${{ env.app_name }}:$IMAGE_TAG
          
      - name: Post success message to Slack
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"✅ Build succeeded on *${{ github.repository }}* for branch \`${{ github.ref_name }}\`.\
            <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>\"}" \
            ${{ secrets.SLACK_WEBHOOK }}
      - name: Post failure message to Slack
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data "{\"text\":\"❌ Build failed on *${{ github.repository }}* for branch \`${{ github.ref_name }}\`.\
          <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>\"}" \
          ${{ secrets.SLACK_WEBHOOK }}
