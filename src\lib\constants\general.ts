import { VIEW_CHART_BY_ENUM } from '$lib/enum/general';

export const VIEW_CHART_BY = [
    {
        label: 'Day',
        value: VIEW_CHART_BY_ENUM.DAY,
    },
    {
        label: 'Week',
        value: VIEW_CHART_BY_ENUM.WEEK,
    },
    {
        label: 'Month',
        value: VIEW_CHART_BY_ENUM.MONTH,
    },
    {
        label: 'Quarter',
        value: VIEW_CHART_BY_ENUM.QUARTER,
    },
    {
        label: 'Year',
        value: VIEW_CHART_BY_ENUM.YEAR,
    },
];

export const FORMAT_DATE = 'DD/MM/YYYY';
export const FORMAT_MONTH = 'MM/YYYY';
export const FORMAT_YEAR = 'YYYY';
export const FORMAT_DATE_TIME = 'DD/MM/YYYY H:mm';

export const LIMIT_OPTIONS = [
    { value: 5, name: '5 rows' },
    { value: 10, name: '10 rows' },
    { value: 20, name: '20 rows' },
    { value: 50, name: '50 rows' },
    { value: 100, name: '100 rows' },
];
