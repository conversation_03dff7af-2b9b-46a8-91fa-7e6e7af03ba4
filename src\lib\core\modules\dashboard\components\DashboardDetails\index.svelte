<script lang="ts" context="module">
    import TextFieldv2 from '$lib/core/components/TextField/v2/index.svelte';
    import { logger } from '$lib/stores/Logger';
    import ChipSelector, {
        type ChipCreateEvent,
        type ChipForDisplay,
        type ChipUpdateEvent,
    } from '$lib/core/components/ChipSelector/index.svelte';
    import { get } from 'svelte/store';
    import { createEventDispatcher } from 'svelte';
    import { locations } from '$lib/stores/Locations';
    import Button from '$lib/core/components/Button/index.svelte';
    import {
        type InsertTagResponseBody,
        type UpdateDashboardTagResponseBody,
        type UpdateTagRequestApiBody,
    } from '$routes/api/dash/tag/+server';
    import { type UpdateDashboardDetailsResponseBody } from '$routes/api/dash/details/[id]/+server';
    import { DashboardStore } from '$lib/core/modules/dashboard/module';
    import { type InsertDashboardDetailsResponseBody } from '$routes/api/dash/details/+server';

    const src = new URL(import.meta.url).pathname;
    const [componentName] = src.split('/').slice(-2, -1);
    const log = logger.child({ src, componentName });

    const NAME_MAX_LENGTH = 80;
    const DESCRIPTION_MAX_LENGTH = 300;

    export type DashboardDetails = {
        id: string;
        name: string;
        description: string;
        tags: Array<Tag>;
        occ_lock?: number;
    };

    type ComponentEvents = {
        create: DashboardDetailsDetail;
        update: DashboardDetailsDetail;
    };

    export type DashboardDetailsDetail = DashboardDetails;

    export type DashboardDetailsCreatedEvent = CustomEvent<DashboardDetails>;
    export type DashboardDetailsUpdatedEvent = CustomEvent<DashboardDetails>;
</script>

<script lang="ts">
    /**
     * The dashboard which this details editor will modify for update mode
     * */
    export let details: DashboardDetails;

    /**
     * Whether this details editor is for inserting or updating dashboards
     */
    export let mode: 'update' | 'insert';

    /**
     * Chips used for display
     */
    export let chips: Array<ChipForDisplay> = [];

    const dispatch = createEventDispatcher<ComponentEvents>();

    const chipUpdateHandler = async (e: ChipUpdateEvent) => {
        const checked_chips = e?.detail?.chips.filter(val => {
            return val.checked;
        });

        details.tags = checked_chips;

        if (e?.detail?.isChanged && mode === 'update') {
            let body: UpdateTagRequestApiBody = {
                dashboard_id: details.id,
                tags: checked_chips.map(c => c.id),
            };

            // API call to update tag links
            const response = await fetch(`${get(locations).dash_web}/tag`, {
                method: 'PUT',
                body: JSON.stringify(body),
            });

            const result = await response.json();

            if (result && result.data) {
                const data = result.data as UpdateDashboardTagResponseBody['data'];
                log.debug({ data }, 'result from updating tags.');
            } else {
                log.warn({ result }, 'Unexpected: No result returned from dashboard tag API');
            }

            DashboardStore.update(ds => {
                let dash = ds.dashboard_map.get(details.id);

                if (dash) {
                    dash.tags = details.tags;
                }

                ds.dashboard_map.set(details.id, dash);
                return ds;
            });
        }
    };

    let is_creating_chip = false;

    const chipCreateHandler = async (e: ChipCreateEvent) => {
        is_creating_chip = true;

        // API Call to create a new tag
        const response = await fetch(`${get(locations).dash_web}/tag`, {
            method: 'POST',
            body: JSON.stringify({
                text: e?.detail.text,
            }),
        });

        const result = await response.json();

        if (result && result.data) {
            const data = result.data as InsertTagResponseBody['data'];

            // Eager UI update before subscription callback
            if (data.text === e?.detail.text) {
                chips.push({
                    id: data.id,
                    text: data.text,
                    checked: false,
                    filterHit: false,
                    occ_lock: data.occ_lock,
                });

                chips = chips;
            }

            log.debug({ result }, 'result from creating tag');

            DashboardStore.update(ds => {
                ds.tag_map.set(data.id, data);
                return ds;
            });
        } else {
            log.warn({ result }, 'unexpected: no result returned from dashboard tag API');
        }

        is_creating_chip = false;
    };

    const updateDashboardDetails = async (feedback?: (result?: UpdateDashboardDetailsResponseBody) => void) => {
        // API Call to update dashboard details
        const response = await fetch(`${get(locations).dash_web}/details/${details.id}`, {
            method: 'PATCH',
            body: JSON.stringify({
                name: details.name,
                description: details.description,
                occ_lock: details.occ_lock,
            }),
        });

        const result: UpdateDashboardDetailsResponseBody = await response.json();

        if (result && result.data) {
            const data = result.data;

            // Update model
            if (data.name === details.name && data.description === details.description) {
                details.occ_lock = data.occ_lock;
                log.debug({ result }, 'Result from updating dashboard details.');
                dispatch('update', details);
            } else {
                log.error({ result }), 'Dashboard details update do not match.';
            }
        } else if (result?.errors && result?.errors.length > 0) {
            feedback(result);

            log.error({ result }, 'Error');
        } else {
            log.warn({ result }, 'Unexpected: No result returned from dashboard details API');
        }
    };

    const dashboardNameChangeHandler = async (e: InputEvent) => {
        const input = e.target as HTMLInputElement;

        for (const [_, d] of $DashboardStore.dashboard_map) {
            if (d.name === details.name) {
                input.setCustomValidity('Name must be unique!');
                input.reportValidity();
                return;
            }
        }

        if (input.validity.valid && mode === 'update') {
            updateDashboardDetails(result => {
                if (result.message.includes('unique constraint') && result.message.includes('layout_name')) {
                    input.setCustomValidity('Name must be unique!');
                    input.reportValidity();
                }
            });
        } else {
            log.debug({ validity: input.validity }, 'invalid name change');
        }
    };

    const dashboardDescriptionChangeHandler = async (e: InputEvent) => {
        const input = e.target as HTMLInputElement;

        if (input.validity.valid && mode === 'update') {
            updateDashboardDetails();
        } else {
            log.debug({ validity: input.validity }, 'invalid description change');
        }
    };

    const newDashboardSubmitHandler = async (e: MouseEvent) => {
        const input = e.target as HTMLInputElement;

        const body: CreateDashboardRequest = {
            name: details.name,
            description: details.description,
            tags: details.tags,
        };

        if (input.validity.valid) {
            // API Call to create a new dashboard
            const response = await fetch(`${get(locations).dash_web}/details`, {
                method: 'POST',
                body: JSON.stringify(body),
            });

            const result: InsertDashboardDetailsResponseBody = await response.json();

            if (result && result.data) {
                const data = result.data;

                // Eager UI update before subscription callback
                // NOTE: NO MORE SUBSCRIPTIONS - UI state is patched through here - possible de-sync, find better
                // solution
                if (data.name !== details.name || data.description !== details.description) {
                    log.warn({ result }), 'Dashboard details update do not match.';
                }

                dispatch('create', {
                    id: data.id,
                    name: data.name,
                    description: data.description,
                    tags: details.tags,
                });
            } else if (result?.errors && result?.errors.length > 0) {
                if (result.message.includes('unique constraint') && result.message.includes('layout_name')) {
                    input.setCustomValidity('Name must be unique!');
                }

                log.error({ result }, 'Error');
            } else {
                log.warn({ result }, 'Unexpected: No result returned from dashboard details API');
            }
        } else {
            log.debug({ validity: input.validity }, 'Invalid input change');
        }
    };

    let chipsForFilter = [];

    $: if (chips) {
        chipsForFilter = [];

        chips.forEach(chip => {
            let filterChip: ChipForDisplay = {
                id: chip.id,
                text: chip.text,
                occ_lock: chip.occ_lock,
            };

            if (details?.tags) {
                for (const tag of details?.tags) {
                    if (tag.id === filterChip.id) {
                        filterChip.checked = true;
                        filterChip.filterHit = true;
                        break;
                    }
                }
            }

            chipsForFilter.push(filterChip);
        });

        chipsForFilter = chipsForFilter;
    }
</script>

<div class="flex w-[423px] flex-col gap-2">
    <TextFieldv2
        bind:value={details.name}
        on:change={dashboardNameChangeHandler}
        title="Name"
        required={true}
        maxlength={NAME_MAX_LENGTH} />
    <TextFieldv2
        bind:value={details.description}
        on:change={dashboardDescriptionChangeHandler}
        title="Description"
        required={true}
        maxlength={DESCRIPTION_MAX_LENGTH} />
    <ChipSelector
        bind:isCreatingChip={is_creating_chip}
        on:create={chipCreateHandler}
        createChipEnabled={true}
        label="label"
        chips={chipsForFilter}
        on:update={chipUpdateHandler} />

    {#if mode === 'insert'}
        <div class="flex h-fit w-full justify-end pt-4">
            <Button
                on:click={newDashboardSubmitHandler}
                setClass="rounded p-1 px-3 text-sm bg-primary
text-on-primary hover:brightness-110 active:brightness-125 transition-color duration-200 ease-in-out">
                Submit
            </Button>
        </div>
    {/if}
</div>
