<!--
@component
The target element must have overflow set to hidden and position set to relative for ripple to work
-->
<script lang="ts">
    export function ripple(event) {
        // Save target's original position and overflow values
        const target = event.target;

        // Set size and position of ripple
        const circle = document.createElement('div');
        const diameter = Math.max(target.clientWidth, target.clientHeight);
        const radius = diameter / 2;

        circle.style.width = circle.style.height = `${diameter}px`;
        // circle.style.left = `${event.clientX - (target.offsetLeft + radius)}px`;
        // circle.style.top = `${event.clientY - (target.offsetTop + radius)}px`;
        circle.style.left = `${event.offsetX - radius}px`;
        circle.style.top = `${event.offsetY - radius}px`;

        circle.classList.add('ripple');

        target.appendChild(circle);

        circle.addEventListener('animationend', function () {
            circle.remove();
        });
    }
</script>

<!-- Removing scoping - Ripple adds elements via J<PERSON>, scoping can't detect it -->
<svelte:head>
    <style>
        div.ripple {
            position: absolute;
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 600ms linear;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: 9999;
            pointer-events: none;
        }

        @keyframes ripple {
            from {
                transform: scale(0);
                opacity: 1;
            }
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</svelte:head>
