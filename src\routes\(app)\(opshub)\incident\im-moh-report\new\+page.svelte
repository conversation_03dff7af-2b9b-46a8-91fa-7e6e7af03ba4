<script lang="ts">
    import { title } from '$lib/core/components/PageTitle/index.svelte';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import { error } from '@sveltejs/kit';
    import { onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { ButtonAction } from '../../schema';
    import { updateDefaultResponseTracking } from '../../utils/file_utils';
    import MOHReportForm from '../components/MOHReportForm.svelte';
    import { callExternalApi } from '$routes/(app)/(opshub)/sop-dashboard/utils';

    $title = 'Edit Incident';

    export let data;

    let dataForm;
    let submitForm;

    // Modal config
    let popupModal = false;
    let modalTitle = 'Warning Message';
    let modalMessage = 'This field cannot be empty.';
    let onlyOkBtn = true;
    let confirmAction;

    const IncidentMonitoringURL = `${get(locations).incident_monitoring_web}`;
    const actionMessageMap = new Map();

    // const validateData = () => {
    //     const formError = getIncidentError(submitForm, data?.abilities);
    //     if (!formError.isError) {
    //         return true;
    //     }

    //     popupModal = true;
    //     modalTitle = formError.errorTitle;
    //     modalMessage = formError.errorMessage;
    //     onlyOkBtn = true;
    //     confirmAction = () => {};
    // };

    onMount(async () => {
        const incidentData = data.incidentData;
        const cfgParam = data.cfgParam;
        const initForm = {
            ID: incidentData.id,
            Institution_Name: cfgParam.key_value_json?.InstitutionName,
            Institution_Office: cfgParam.key_value_json?.InstitutionOffice,
            Incident_Number: incidentData.incident_number,
            Incident_Name: incidentData.name,
            Incident_Id: incidentData.id,
            Incident_Type_Id: incidentData.incident_type_id,
            Incident_At: new Date(incidentData.incident_at),
            Incident_Location_Id: incidentData.incident_location_id,
            Incident_Category_Id: incidentData.incident_category_id,
            Description: incidentData.description,
            Institution_Phone: cfgParam.key_value_json?.InstitutionPhoneNumber,
            Institution_Email: cfgParam.key_value_json?.InstitutionEmail,
        };
        dataForm = initForm;
    });
    async function triggerApprovalWorkflow(reportId: string, templatedId) {
        try {
            const sopUrl = get(locations).sop_web;
            const tenantId = get(user).claims.active_tenant.tenant_id;
            const templateName = data.templates.data.find(t => t.id === templateId)?.name;

            if (!templateName) {
                console.warn('Workflow template not found');
                return;
            }

            // Trigger the workflow
            const workflowData = await callExternalApi(`${sopUrl}/workflow/trigger/`, {
                method: 'POST',
                body: JSON.stringify({
                    template_id: templateId,
                    metadata: {
                        tenant_id: tenantId,
                        report_id: reportId,
                    },
                }),
            });

            // Activate the workflow
            await callExternalApi(`${sopUrl}/sys-wf-activation/`, {
                method: 'POST',
                body: JSON.stringify({
                    template_name: templateName,
                    template_id: templateId,
                    workflow_id: workflowData?.data?.data?.workflow_id,
                    is_manual_trigger: true,
                }),
            });

            console.log(`Workflow triggered successfully`);
        } catch (err) {
            console.error('Error triggering workflow:', err);
            // Don't throw error here as the main operation (updating report) was successful
        }
    }

    async function handleSendForApproval(payload: any) {
        try {
            const res = await fetch(`/api/incident-monitoring/moh-report`, {
                method: 'POST',
                body: JSON.stringify(payload),
            });

            const response = await res.json();

            if (res.status !== 200) {
                throw new Error(response.statusText || 'Failed to send for direct approval');
            }

            // Show success modal
            popupModal = true;
            modalTitle = 'Notification';
            modalMessage = 'Moh Report is created successfully!';
            onlyOkBtn = true;
            confirmAction = () => {
                location.href = `/incident/im-moh-report/list`;
            };

            // Workflow trigger
            await triggerApprovalWorkflow(response.id, '5dab966b-96aa-4221-9252-93f68e9d149f');
        } catch (err) {
            error(500, err);
        }
    }
</script>

<main class="mx-3">
    {#if dataForm}
        <MOHReportForm
            actionType="edit"
            initData={dataForm}
            incidentTypeList={data.incidentTypeList}
            incidentLocationList={data.incidentLocationList}
            incidentCategoryList={data.incidentCategoryList}
            bind:submitForm
            onSave={handleSendForApproval} />
    {/if}
</main>
<Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
