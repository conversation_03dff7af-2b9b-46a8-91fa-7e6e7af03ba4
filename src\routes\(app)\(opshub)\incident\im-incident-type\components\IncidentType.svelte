<script lang="ts">
    import { ColumnDataType, INV_STYLES, type ColumnName } from '$lib/shared/constants/general';
    import InvTable from '$lib/shared/components/table/Table.svelte';
    import { But<PERSON>, Heading } from 'flowbite-svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faAdd, faRefresh, faSave, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
    import { generateId } from '$lib/shared/util/customId';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import Search from '$lib/shared/components/search/Search.svelte';
    import { ColumnType } from '$lib/shared/enum/search';
    import { ArrowUpRightFromSquareOutline } from 'flowbite-svelte-icons';
    import SelectIncidentRole from './SelectIncidentRole.svelte';
    import { hasAccess } from '$lib/shared/util/validation';
    import { SCOPE } from '$lib/shared/enum/general';

    export let data;
    export let onSave;
    export let onDelete;
    export let iamRoles;
    export let abilities = [];

    let tableData = [];
    let newRecords = new Set();
    let searchText = '';
    let selectedRow = null;
    let openPreview = false;
    let openModal = false;
    let onlyOkBtn = false;
    let modalMessage = '';
    let modalTitle = '';

    const initTableDate = () => {
        tableData = data.map(item => ({
            id: item.id,
            type: item.name,
            description: item.description,
            roles: item?.roles?.map(role => role.role_id),
        }));
    };

    initTableDate();

    let updateList = [];

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    const defaultPosition: UpdatePosition = { id: '', columnName: '', isEditing: false };
    let updatePosition: UpdatePosition = structuredClone(defaultPosition);

    let selectIncidentRoleData = {};
    const mappedTypes = new Map(tableData?.map(obj => [obj.id, obj]));

    const handleUpdateRoles = (rowId, newTypes) => {
        const idx = updateList.findIndex(e => e.id === rowId);
        if (idx === -1) {
            const newItem = structuredClone(mappedTypes.get(rowId));
            newItem['roles'] = newTypes;
            updateList = [...updateList, newItem];
        } else {
            updateList[idx]['roles'] = newTypes;
        }
    };

    const handleOpenSelectRole = rowData => {
        const mappedUpdateList = new Map(updateList.map(obj => [obj.id, obj]));
        const typesMapped = mappedUpdateList.has(rowData.id)
            ? new Set(mappedUpdateList.get(rowData.id).roles)
            : new Set(rowData.roles);

        selectIncidentRoleData = { id: rowData.id, data: [] };
        for (const role of iamRoles) {
            const isSelected = typesMapped.has(role.id);
            selectIncidentRoleData['data'].push({ ...role, select: isSelected });
        }
        openPreview = true;
    };

    const handleAppendNewRow = () => {
        const newId = generateId();
        tableData = [
            ...tableData,
            {
                id: newId,
                type: null,
                description: null,
                roles: [],
                isNew: true,
            },
        ];
        newRecords.add(newId);
    };

    const handleDeleteRow = async () => {
        let result;
        if (!newRecords.has(selectedRow)) {
            result = await onDelete(selectedRow);
        }
        if (result == true || newRecords.has(selectedRow)) {
            tableData = tableData.filter(item => item.id !== selectedRow);
            updateList = updateList.filter(item => item.id !== selectedRow);
            selectedRow = null;
        }
    };

    const onReset = () => {
        location?.reload();
    };

    const columnNames: ColumnName[] = [
        {
            key: 'type',
            value: 'Incident Type',
            editable: true,
            type: ColumnDataType.String,
            searchType: ColumnType.String,
        },
        {
            key: 'description',
            value: 'Incident Type Description',
            editable: true,
            type: ColumnDataType.String,
            searchType: ColumnType.String,
        },
        { key: 'roles', value: 'Roles', editable: true, customStyle: true },
    ];
    const searchColumns = columnNames
        .filter(item => item.key !== 'roles')
        .map(i => ({ name: i.key, label: i.value, type: i.searchType }));
    $: filteredData = [...tableData];

    const checkDeleteRow = () => {
        if (!selectedRow) {
            modalTitle = 'Notification';
            modalMessage = 'Please select a record!';
            onlyOkBtn = true;
            openModal = true;
        } else {
            modalTitle = 'Confirmation';
            modalMessage = 'Are you sure you want to delete the selected record(s)?<br />Click Yes to confirm.';
            onlyOkBtn = false;
            openModal = true;
        }
    };
</script>

<div class={INV_STYLES.fullTable}>
    <div class={INV_STYLES.header}>
        <Heading tag="h5">Incident Type Configuration</Heading>
    </div>
    <div>
        <div class="m-4">
            <div class="flex items-center gap-2">
                <div class="relative min-w-[200px]">
                    <Search
                        bind:searchText
                        columns={searchColumns}
                        data={tableData}
                        on:apply={e => {
                            filteredData = e.detail ? e.detail.filteredData : tableData;
                        }} />
                </div>
                {#if hasAccess(abilities, SCOPE.UPDATE) || hasAccess(abilities, SCOPE.CREATE)}
                    <Button size="md" color="light" on:click={() => onSave(tableData, updateList, newRecords)}>
                        <span class="mr-2">{@html icon(faSave).html}</span>
                        <span class="text-nowrap">Save</span>
                    </Button>
                {/if}
                {#if hasAccess(abilities, SCOPE.CREATE)}
                    <Button size="md" color="light" on:click={() => handleAppendNewRow()}>
                        <span class="mr-2">{@html icon(faAdd).html}</span>
                        <span class="text-nowrap">New</span>
                    </Button>
                {/if}
                {#if hasAccess(abilities, SCOPE.DELETE)}
                    <Button size="md" color="light" on:click={() => checkDeleteRow()}>
                        <span class="mr-2">{@html icon(faTrashAlt).html}</span>
                        <span class="text-nowrap">Delete</span>
                    </Button>
                {/if}
                <Button size="md" color="light" on:click={() => onReset()}>
                    <span class="mr-2">{@html icon(faRefresh).html}</span>
                    <span class="text-nowrap">Refresh</span>
                </Button>
            </div>
        </div>
        <InvTable
            data={filteredData}
            {columnNames}
            hasSN={false}
            bind:updateList
            bind:updatePosition
            bind:searchText
            bind:selectedRow
            allowSelected={true}>
            <div slot="row-custom" class="h-14" let:itemData>
                <div class="items flex h-full items-center px-4">
                    <div class="rounded px-2 py-1">
                        <Button
                            outline
                            size="xs"
                            on:click={() => {
                                handleOpenSelectRole(itemData);
                            }}>
                            <ArrowUpRightFromSquareOutline />
                        </Button>
                    </div>
                </div>
            </div>
        </InvTable>
    </div>
</div>
{#if openPreview}
    <SelectIncidentRole data={selectIncidentRoleData} onSave={handleUpdateRoles} bind:openPreview />
{/if}
<Notification
    bind:isOpen={openModal}
    {modalTitle}
    {modalMessage}
    {onlyOkBtn}
    confirmAction={onlyOkBtn ? null : handleDeleteRow} />
