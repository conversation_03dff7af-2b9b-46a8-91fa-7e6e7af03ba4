export default function OveriewOfCurrentSituationForTelehealthConfiguration(form, fields) {
    for (let index = 0; index < fields.length; index++) {
        const element = fields[index];
        if (element?.field === 'VisitTypeGroup') {
            element.datasource = {
                ...element.datasource,
                viewFields: ['code', 'desc', 'visit_type_group', 'visit_type_group_display_name', 'service'],
                inArrayFilter: {
                    field: 'service',
                    value: ['AHS', 'SOC'],
                },
            };
        }
    }
}
