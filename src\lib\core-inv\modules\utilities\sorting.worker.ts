interface SortMessage {
    type: 'sort';
    data: {
        finalData: any[];
        sortKey: string;
        sortDirection: number;
        additionalData?: any;
    };
}

interface SortResult {
    type: 'sortComplete';
    data: any[];
}

const sortFn = (key: string, direction: number) => {
    return (a: any, b: any): number => {
        const aVal = a[key];
        const bVal = b[key];

        if (aVal == null && bVal == null) return 0;
        if (aVal == null) return -direction;
        if (bVal == null) return direction;

        const type = typeof aVal;
        if (type === 'boolean' || type === 'number') return (Number(aVal) - Number(bVal)) * direction;
        return String(aVal).localeCompare(String(bVal)) * direction;
    };
};

const sortSameUOMFn = (additionalData, direction: number) => {
    return (a: any, b: any): number => {
        const aVal = additionalData[a.id];
        const bVal = additionalData[b.id];
        if (!aVal || aVal < bVal) {
            return -direction;
        } else if (!bVal || aVal > bVal) {
            return direction;
        }
        return 0;
    };
};

const performSort = (message: SortMessage): SortResult => {
    const { finalData, additionalData, sortKey, sortDirection } = message.data;

    let sortedData;
    if (sortKey === 'include' && additionalData) {
        sortedData = finalData.slice().sort(sortSameUOMFn(additionalData, sortDirection));
    } else {
        sortedData = finalData.slice().sort(sortFn(sortKey, sortDirection));
    }

    return { type: 'sortComplete', data: sortedData };
};

self.addEventListener('message', (event: MessageEvent<SortMessage>) => {
    if (event.data?.type === 'sort') {
        const result = performSort(event.data);
        self.postMessage(result);
    }
});

export {};
