<script lang="ts">
    import { onMount } from 'svelte';

    import ExpectedPatient from '$lib/core-soc/modules/crud/ExpectedPatient.svelte';
    import { user } from '$lib/stores/Auth';
    import { get } from 'svelte/store';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';

    export let data;
    let user_id = '';
    onMount(() => {
        user_id = get(user)?.claims?.hasura_access?.['x-hasura-user-id'];
    });
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <div class="flex flex-col gap-20 p-3 text-on-surface-1">
        {#if user_id !== ''}
            <ExpectedPatient abilities={data.abilities} />
        {/if}
    </div>
</Guard>
