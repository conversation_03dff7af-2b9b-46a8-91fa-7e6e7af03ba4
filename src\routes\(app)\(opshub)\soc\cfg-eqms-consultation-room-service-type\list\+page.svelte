<script lang="ts">
    import { get } from 'svelte/store';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import { ColumnDataType, type ColumnNameCustom } from '$lib/shared/constants/general';
    import { v4 as uuidv4 } from 'uuid';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import StandardTable from '../../components/StandardTable.svelte';
    import { isLoading } from '$lib/stores/Loading';
    import { TriggerService } from '$lib/service/trigger';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';
    export let data;

    const SocURL = `${get(locations).soc_web}`;

    let popupModal = false;
    let modalTitle = '';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;
    let openUpdateResult = false;
    let tableData = Array.isArray(data.consultationRoomServiceTypes)
        ? data.consultationRoomServiceTypes.map(item => ({
              id: item.id,
              service_type: item.service_type,
          }))
        : [];
    let newRecords = new Set();
    let searchText = '';
    let selectedRow = null;
    let updateList = [];
    let standardTable;
    const triggerTableNames = 'cc3_hoc.cfg_eqms_consultation_room_service_type';

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    let updatePosition: UpdatePosition = { id: '', columnName: '', isEditing: false };

    const showSuccessMessage = msg => {
        popupModal = true;
        modalTitle = 'Notification';
        modalMessage = msg;
        onlyOkBtn = true;
        if (openUpdateResult) {
            confirmAction = () => {
                handleRefresh();
                standardTable.resetAllTableValue();
            };
        }
    };

    const handleCreateData = async (userClaims, newItems) => {
        await fetch(`${SocURL}/cfg-eqms-consultation-room-service-type`, {
            method: 'POST',
            body: JSON.stringify({
                data: newItems.map(item => ({
                    service_type: item.service_type,
                })),
            }),
        });
    };

    const handleUpdateData = async (userClaims, updateItems) => {
        await fetch(`${SocURL}/cfg-eqms-consultation-room-service-type`, {
            method: 'PATCH',
            body: JSON.stringify({
                data: updateItems.map(item => ({
                    id: item.id,
                    service_type: item.service_type,
                })),
            }),
        });
    };

    const handleSaveData = async () => {
        const userClaims = get(user).claims;
        let newItems = [],
            updateItems = [];
        updateList.forEach(row => (newRecords.has(row.id) ? newItems.push(row) : updateItems.push(row)));

        if (newItems.length) await handleCreateData(userClaims, newItems);
        if (updateItems.length) await handleUpdateData(userClaims, updateItems);
        openUpdateResult = true;
        showSuccessMessage('Configuration is saved successfully.');
        TriggerService.trigger(triggerTableNames);
    };

    const handleDeleteData = async typeId => {
        await fetch(`${SocURL}/cfg-eqms-consultation-room-service-type`, {
            method: 'DELETE',
            body: JSON.stringify({ data: [{ id: typeId }] }),
        });
    };

    const onDelete = async () => {
        if (selectedRow && !newRecords.has(selectedRow)) {
            await handleDeleteData(selectedRow);
        }
        tableData = tableData.filter(item => item.id !== selectedRow);
        updateList = updateList.filter(item => item.id !== selectedRow);
        selectedRow = null;
        showSuccessMessage('Successfully deleted configuration!');
    };

    const handleRefresh = async () => {
        isLoading.set(true);
        try {
            const res = await fetch(`${SocURL}/cfg-eqms-consultation-room-service-type?page=1&limit=9999`, {
                method: 'GET',
            });
            const response = await res.json();
            tableData = Array.isArray(response.data)
                ? response.data.map(item => ({
                      id: item.id,
                      service_type: item.service_type,
                  }))
                : [];
            updateList = [];
            selectedRow = null;
            newRecords = new Set();
        } catch (e) {
            console.error('Failed to refresh data:', e);
        } finally {
            isLoading.set(false);
        }
    };

    let newData;
    const handleAppendNewRow = () => {
        const newId = uuidv4();
        newData = {
            id: newId,
            service_type: '',
            isNew: true,
        };
        tableData = [...tableData, newData];
        newRecords.add(newId);
    };

    const columnNames: ColumnNameCustom[] = [
        {
            label: 'Service Type',
            name: 'service_type',
            editable: true,
            isRequired: true,
            type: ColumnDataType.String,
            isSearchable: true,
        },
    ];
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <StandardTable
        bind:this={standardTable}
        abilities={data.abilities}
        tableName="Consultation Room Service Type"
        {newRecords}
        data={tableData}
        initData={tableData}
        uniqueKey={'service_type'}
        {columnNames}
        {newData}
        bind:updateList
        bind:updatePosition
        bind:searchText
        bind:selectedRow
        allowSelected={true}
        on:add={() => handleAppendNewRow()}
        on:save={() => handleSaveData()}
        on:delete={() => onDelete()}
        on:refresh={() => handleRefresh()}
        defaultSortKey="service_type" />

    <Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
</Guard>
