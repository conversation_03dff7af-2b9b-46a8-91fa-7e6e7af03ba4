<script lang="ts">
    import { writable } from 'svelte/store';
    import { get } from 'svelte/store';
    import { locations } from '$lib/stores/Locations';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faSortUp,
        faSortDown,
        faPencilAlt,
        faAdd,
        faTrash,
        faRefresh,
        faSave,
        faSearch,
        faClose,
    } from '@fortawesome/free-solid-svg-icons';
    import {
        Table,
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Heading,
        Checkbox,
        Button,
        Toolbar,
        Dropdown,
        Input,
        MultiSelect,
        Select,
    } from 'flowbite-svelte';
    import { ColumnDataType, type ColumnNameCustom } from '$lib/shared/constants/general';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import Search from '$lib/shared/components/search/Search.svelte';
    import { isNullOrEmpty } from '../../../incident/utils/input_utils';
    import { ChevronDownOutline } from 'flowbite-svelte-icons';
    import { v4 as uuidv4 } from 'uuid';
    import { isLoading } from '$lib/stores/Loading';
    import { onMount } from 'svelte';
    import type { ColumnType } from '$lib/shared/enum/search';
    import { onApplyAdvancedSearch, removeAdvancedSearchLabel } from '$lib/shared/util/advancedSearch';
    import { TriggerService } from '$lib/service/trigger';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';
    import { hasAccess } from '$lib/shared/util/validation';
    interface Identifiable {
        id: string;
    }

    type UpdatePosition = {
        id: string;
        columnName: string;
        isEditing: boolean;
    };

    export let data;
    const triggerTableNames = 'cc3_hoc.rft_nursingou';
    const SocURL = `${get(locations).soc_web}`;
    const tableName = 'Nursing OU Configuration';
    const ASC: number = 1;
    const sortDirection = writable(ASC);
    const sortItems = writable([]);
    const checkAllMapped = writable({});
    const headCellStyle =
        'border border-b border-r border-gray-500 border-t-0 border-l-0 dark:text-white text-gray-900 last:border-r-0';
    const bodyCellStyle = 'text-nowrap border-b border-r border-gray-500 last:border-r-0';
    const inputStyle =
        'block w-full rounded-lg p-2.5 text-sm text-gray-900 ' +
        'border border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-700 ' +
        'focus:border-blue-500 focus:ring-blue-500 ' +
        'dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500';
    const dynamicColumns = (() => {
        if (!data?.rftNursingOuType) return [];
        // Separate remark/remarks columns
        const remarks = [];
        const others = [];
        data.rftNursingOuType.forEach(item => {
            const nameLower = item.name.trim().toLowerCase();
            const col = {
                label: item.name.trim(),
                name: item.name,
                editable: true,
                type: item.data_type,
                isRequired: false,
            };
            if (nameLower === 'remark' || nameLower === 'remarks') {
                remarks.push(col);
            } else {
                others.push(col);
            }
        });
        return [...others, ...remarks];
    })();
    const columnNames: ColumnNameCustom[] = [
        {
            label: 'Code',
            name: 'name',
            editable: true,
            type: ColumnDataType.String,
            isRequired: true,
            isSearchable: true,
        },
        {
            label: 'Description',
            name: 'description',
            editable: true,
            type: ColumnDataType.String,
            isRequired: false,
            isSearchable: true,
        },
        ...dynamicColumns,
    ];

    let selectAllColumnCheckboxRef = writable(false);
    let wardData = data?.rftNursingOu;
    let updateList = [];
    let newRecords = new Set();
    let searchText;
    let tableData = groupDataWithDynamicColumns(wardData, dynamicColumns);
    let originData = JSON.parse(JSON.stringify(tableData));
    let defaultSortKey: string = 'name';
    let hasSN = true;
    let selectedRow = undefined;
    let uniqueKey = 'name';
    let popupModal = false;
    let modalTitle = '';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;
    let isSearch = false;
    let isUpdateColumn = true;
    let filteredRecords: any[] = null;
    let customColumnShow: string[] = columnNames.map(item => item.name);
    let openUpdateResult = false;

    let isDisplayCategory;
    let previousStatus;
    let updatePosition: UpdatePosition = { id: '', columnName: '', isEditing: false };
    let searchColumnShow = '';
    let wardCode = new Set();
    let tableWrapperRef;
    let staticColumnName = columnNames?.filter(item => item.name === 'name' || item.name === 'description');
    let sortKey = writable(defaultSortKey || columnNames?.[0]?.name || '');
    let isCheckedAll = true;
    let subData = [];
    let isScrollable = false;
    let displayLabels: string[] = [];
    let applyAdvancedSearch;
    let advancedSearchVariables: Record<string, any> = {};
    let advancedSearchLabels: Record<string, string> = {};
    let displayRecordIds: string[];
    let advancedSearchRef;
    let searchColumns =
        columnNames
            ?.filter(col => col.isSearchable !== false)
            ?.map(item => {
                return {
                    name: item.name,
                    label: item.label,
                    type: item.type as unknown as ColumnType,
                    isUseDataDisplay: item.isUseDataDisplay ?? false,
                };
            }) ?? [];

    onMount(() => {
        checkSelectAll();
        updateSelectAllState();
    });

    const resetAllTableValue = (needResetColumn: boolean = true) => {
        tableData = JSON.parse(JSON.stringify(originData));
        popupModal = false;
        onlyOkBtn = false;
        updateList = [];
        newRecords = new Set();
        selectedRow = null;
        isDisplayCategory = previousStatus;
        isUpdateColumn = true;
        searchText = '';
        filteredRecords = null;
        displayLabels = [];
        openUpdateResult = false;
        sortDirection.set(ASC);
        sortKey.set(defaultSortKey || columnNames?.[0]?.name || '');
        advancedSearchVariables = {};
        advancedSearchLabels = {};
        displayRecordIds = undefined;
        searchColumnShow = '';
        wardCode = new Set();
        if (needResetColumn) {
            customColumnShow = columnNames.map(item => item.name);
        }
        checkSelectAll();
        updateSelectAllState();
    };

    const sortTable = (event: any, key: string) => {
        if (event.target.type === 'checkbox') {
            event.stopPropagation();
            return;
        }

        if ($sortKey === key) {
            sortDirection.update(val => -val);
        } else {
            sortKey.set(key);
            sortDirection.set(ASC);
        }
    };

    function stringToBoolean(value: string): boolean {
        return value.toLowerCase() === 'true';
    }

    const showSuccessMessage = msg => {
        popupModal = true;
        modalTitle = 'Notification';
        modalMessage = msg;
        onlyOkBtn = true;
        if (openUpdateResult) {
            confirmAction = () => {
                onRefresh(false);
            };
        }
    };

    const functionSort = (key: string, direction: number) => (a: any, b: any) => {
        let aVal = a[key]?.value;
        let bVal = b[key]?.value;

        if (typeof aVal === 'string' && typeof bVal === 'string') {
            if (aVal?.includes('%') && bVal?.includes('%')) {
                aVal = aVal.replace('%', '');
                bVal = bVal.replace('%', '');
            }
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }
        if (aVal === null || bVal === null) {
            return 0;
        }

        return aVal < bVal ? -direction : aVal > bVal ? direction : 0;
    };

    const addToUpdateList = item => {
        const idx = updateList.findIndex(e => e.id === item.id);
        if (idx === -1) {
            updateList = [...updateList, { ...structuredClone(item) }];
        }
    };

    const updateItem = <T extends Identifiable>(item: T, column: ColumnNameCustom) => {
        if (!column?.editable) return;
        addToUpdateList(item);
        updatePosition.isEditing = true;
        updatePosition.id = item.id;
        updatePosition.columnName = column.name;
    };

    const updateBooleanField = <T extends Identifiable>(event: any, item: T, field: string) => {
        let idx = updateList.findIndex(e => e.id === item.id);
        const isChecked = event.currentTarget.checked;

        if (idx === -1) {
            updateList = [
                ...updateList,
                {
                    ...(updateList.length > 0 ? updateList.find(e => e.id === item.id) ?? item : item),
                    [field]: {
                        value: isChecked,
                        wardId: item[field]?.wardId,
                    },
                },
            ];
            idx = updateList.findIndex(e => e.id === item.id);
        } else {
            updateList[idx][field] = {
                value: isChecked,
                wardId: item[field]?.wardId,
            };
        }

        tableData = tableData.map(u => {
            if (u.id === item.id) {
                u[field] = {
                    value: isChecked,
                    wardId: item[field]?.wardId,
                };
            }
            return u;
        });

        const mappedUpdateList = new Map(updateList.map(obj => [obj.id, obj]));
        isCheckedAll = $sortItems.every(item => {
            if (mappedUpdateList.has(item.id)) {
                return mappedUpdateList.get(item.id)[field]?.value;
            }
            return item[field]?.value;
        });

        checkAllMapped.set({
            ...$checkAllMapped,
            [field]: isCheckedAll,
        });
        checkAllMapped[field] = isUpdateColumn === true ? event.currentTarget.checked : isCheckedAll;
    };

    const updateField = <T extends Identifiable>(event: any, item: T, col: ColumnNameCustom) => {
        const { type, name } = col;
        updateList = updateList.map(u => {
            if (u.id === item.id) {
                const value = event.currentTarget.value;
                if (type === ColumnDataType.Number || type === ColumnDataType.SingleNumberSelect) {
                    // if empty, get default previous value
                    u[name].value = value ? Number(value) : null;
                }
                if (type === ColumnDataType.String || type === ColumnDataType.SingleStringSelect) {
                    u[name].value = value?.trim() || '';
                }
                if (type === ColumnDataType.Array) {
                    u[name].value = event.detail.map(e => e.value);
                }

                if (col.dependedByColumns && col.dependedByColumns.length > 0) {
                    col.dependedByColumns.forEach(depCol => {
                        const dependentValue = subData[depCol].find(d => d.key === value)?.value;
                        u[depCol].value = dependentValue || '';
                    });
                }
            }
            return u;
        });
    };

    function updateSelectAllState() {
        selectAllColumnCheckboxRef.set(customColumnShow.length === columnNames.length);
    }

    const isChange = (item): boolean => {
        const oldItem = $sortItems.find(i => i.id === item.id);
        return oldItem.value === item.value;
    };

    const isEdited = <T extends Identifiable>(list: T[], item: T, col: string): boolean => {
        const foundItem = list.find(e => e.id === item.id);
        const oldItem = originData.find(e => e.id === item.id) ?? {
            id: item.id,
            name: { value: '' },
            description: { value: '' },
            ...dynamicColumns.reduce((acc, column) => {
                acc[column.name] = { wardId: null, value: false };
                return acc;
            }, {}),
        };

        if (foundItem && oldItem) {
            if (foundItem[col] instanceof Object || oldItem[col] instanceof Object) {
                return foundItem[col]?.value !== oldItem[col]?.value;
            }
        }

        return false;
    };

    const isSelected = <T extends Identifiable>(
        position: UpdatePosition,
        item: T,
        column: ColumnNameCustom
    ): boolean => {
        return position.id === item.id && position.columnName === column.name;
    };

    const getValue = (list, item, col: string): string => {
        let foundItem = list?.find(e => e.id === item.id);
        const zeroString = value => {
            return value === 0 ? '0' : value === null ? '' : value === '' ? '\u00A0' : value;
        };
        if (foundItem) {
            return zeroString(foundItem[col].value);
        } else {
            foundItem = tableData.find(e => e.id === item.id);
            return zeroString(foundItem?.[col].value ? foundItem[col].value : item[col].value);
        }
    };

    const getBoolValue = (list, item, col: string): boolean => {
        let foundItem = list?.find(e => e.id === item.id);
        if (foundItem) {
            return foundItem[col].value;
        } else {
            foundItem = tableData.find(e => e.id === item.id);
            return foundItem?.[col].value ? foundItem[col].value : item[col].value;
        }
    };

    const getAllWardNames = (rowId: string): (string | null)[] => {
        const names = [];
        tableData.forEach(row => {
            if (row.id === rowId) {
                names.push({ name: row.name?.value || null });
            }
        });
        return names;
    };

    function groupDataWithDynamicColumns(wardData, dynamicColumns) {
        const groupedData = new Map();

        wardData.forEach(row => {
            const { name, id, type, value, description } = row;

            if (!groupedData.has(name)) {
                groupedData.set(name, {
                    id: uuidv4(),
                    name: {
                        value: name,
                    },
                    description: {
                        value: description,
                    },
                    ...dynamicColumns.reduce((acc, column) => {
                        acc[column.name] = { wardId: null, value: column.type === ColumnDataType.Boolean ? false : '' };
                        return acc;
                    }, {}),
                });
            }

            const groupedRow = groupedData.get(name);
            dynamicColumns.forEach(column => {
                if (type === column.name) {
                    groupedRow[column.name] = {
                        wardId: id,
                        value: column.type === ColumnDataType.Boolean ? stringToBoolean(value) : value,
                    };
                }
            });
        });

        return Array.from(groupedData.values());
    }

    const validateData = () => {
        const mergedMap = new Map();
        const duplicateValues = new Map(); // To track duplicate values and their occurrences
        let mergeData = [];
        let errorMessages = {};
        let emptyMessage = '';

        // Merge updateList into tableData
        updateList.forEach(item => {
            mergedMap.set(item.id, { ...item });
        });

        tableData.forEach(item => {
            if (mergedMap.has(item.id)) {
                mergeData.push(mergedMap.get(item.id));
            } else {
                mergeData.push(item);
            }
        });

        const requiredFields = columnNames.reduce((acc, col) => {
            if (col.isRequired) {
                acc.push(col.name);
            }
            return acc;
        }, []);

        // Check for required fields
        if (requiredFields) {
            for (const row of mergeData) {
                for (const field of requiredFields) {
                    if (isNullOrEmpty(row[field]?.value)) {
                        const fieldLabel = columnNames.find(i => i.name === field)?.label || field;
                        emptyMessage = `${fieldLabel} field cannot be empty.`;
                    }
                }
            }
        }

        // Validation rules
        for (let item of mergeData) {
            const compositeKey = Array.isArray(uniqueKey)
                ? uniqueKey.map(key => item[key]?.value).join('|')
                : item[uniqueKey]?.value;

            if (wardCode.has(compositeKey)) {
                // Track duplicate values
                if (!duplicateValues.has(compositeKey)) {
                    duplicateValues.set(compositeKey, []);
                }
                duplicateValues.get(compositeKey).push(item.id);
            } else {
                wardCode.add(compositeKey);
            }

            // Validation rules for specific ward types
            const isSurge = item['Surge']?.value === true;
            const isOverflow = item['Overflow']?.value === true;
            const isReserved = item['Reserved']?.value === true;
            const isMainTTSH = item['MainTTSH']?.value === true;
            const isPrivate = item['Private']?.value === true;
            const isHotWard = item['HotWard']?.value === true;

            const rowName = item.name?.value || 'Unknown';
            if (!errorMessages[rowName]) errorMessages[rowName] = [];

            if (!isPrivate || !isSurge || !isOverflow || !isReserved || !isHotWard) {
                // Rule: A ward cannot be both Surge and Overflow
                if (isSurge && isOverflow) {
                    errorMessages[rowName].push('- A ward cannot be both Surge and Overflow.');
                }

                // Rule: A ward cannot be both Surge and Reserved
                if (isSurge && isReserved) {
                    errorMessages[rowName].push('- A ward cannot be both Surge and Reserved.');
                }

                // Rule: A ward cannot be both Reserved and Overflow
                if (isReserved && isOverflow) {
                    errorMessages[rowName].push('- A ward cannot be both Reserved and Overflow.');
                }
            }

            // Rule: Main TTSH Ward cannot be selected as Surge Ward
            if (isMainTTSH && isSurge) {
                errorMessages[rowName].push('- Main TTSH Ward cannot be selected as Surge Ward.');
            }
        }

        errorMessages = Object.fromEntries(
            Object.entries(errorMessages).filter(([_, messages]) => messages.length > 0)
        );
        let errorMess = '';

        if (emptyMessage !== '') {
            errorMess = emptyMessage;
        } else if (duplicateValues.size > 0) {
            const duplicates = Array.from(duplicateValues.entries())
                .map(([value]) => `${value}`)
                .join(', ');

            errorMess = `The following names are duplicated: ${duplicates}. <br>Please ensure all names are unique.`;
        } else if (Object.keys(errorMessages).length > 0) {
            errorMess = Object.entries(errorMessages)
                .map(
                    ([row, messages]) => `<div class="text-left w-full">Row "${row}":<br>${messages.join('<br>')}</div>`
                )
                .join('<br><br>');
        }

        if (errorMess !== '') {
            popupModal = true;
            modalTitle = 'Validation Errors';
            modalMessage = errorMess;
            onlyOkBtn = true;
            confirmAction = () => {};
            isLoading.set(false);
            wardCode.clear();
            return false;
        }

        if (updateList.length === 0 && isDisplayCategory === previousStatus) {
            return false;
        }

        return true;
    };

    const onKeyUp = (event: KeyboardEventInit) => {
        if (event.name === 'Enter') {
            updatePosition.isEditing = false;
        }
    };

    const onFocusOut = item => {
        updatePosition.isEditing = false;
        const updatedItem = updateList.find(i => i.id === item.id);
        if (!isChange(updatedItem)) {
            updateList = updateList.filter(i => i.id != item.id);
        }

        if (updatedItem) {
            tableData = tableData.map(row => {
                if (row.id === updatedItem.id) {
                    return { ...row, ...updatedItem };
                }
                return row;
            });
        }
    };

    const onDelete = () => {
        if (selectedRow) {
            popupModal = true;
            modalTitle = 'Confirmation';
            modalMessage = `Are you sure you want to delete the selected record(s)?<br />Click Yes to confirm.`;
            confirmAction = () => handleDeleteData(getAllWardNames(selectedRow));
            onlyOkBtn = false;
        } else {
            modalTitle = 'Notification';
            popupModal = true;
            modalMessage = `Please selected one record.`;
            onlyOkBtn = true;
        }
    };

    const onRefresh = async (needResetColumn: boolean = true) => {
        isLoading.set(true);
        resetAllTableValue(needResetColumn);
        advancedSearchRef.resetSearchParams();
        try {
            const res = await fetch(`${SocURL}/rft-nursing-ou?page=1&limit=9999`, {
                method: 'GET',
            });
            const response = await res.json();
            let wardData = response.data;
            tableData = groupDataWithDynamicColumns(wardData, dynamicColumns);
            originData = JSON.parse(JSON.stringify(tableData));
            updateList = [];
            selectedRow = null;
            newRecords = new Set();
            checkSelectAll();
            updateSelectAllState();
        } catch (error) {
            console.error('Error during refresh:', error);
        } finally {
            isLoading.set(false);
        }
    };

    const onSave = () => {
        if (updateList.length === 0 && newRecords.size === 0) {
            return;
        }
        isLoading.set(true);
        if (!validateData()) {
            isLoading.set(false);
            return;
        }
        let newItems = [],
            updateItems = [];
        updateList.forEach(row => (newRecords.has(row.id) ? newItems.push(row) : updateItems.push(row)));

        if (newItems.length) handleCreateData(newItems);
        if (updateItems.length) handleUpdateData(updateItems);
        openUpdateResult = true;
        showSuccessMessage('Configuration is saved successfully!');
        isLoading.set(false);
        TriggerService.trigger(triggerTableNames);
    };

    const onCheckAll = (key = undefined) => {
        checkAllMapped[key] = !checkAllMapped[key];
        tableData = tableData.map(u => {
            const item = $sortItems.find(item => item.id === u.id);
            if (item) {
                if (key) {
                    const e = { currentTarget: { checked: checkAllMapped[key] } };
                    updateBooleanField(e, item, key);
                    u[key] = {
                        value: checkAllMapped[key],
                        wardId: u[key]?.wardId,
                    };
                }
            }
            return u;
        });
    };

    const transformItemToArray = item => {
        const result = [];

        Object.keys(item).forEach(key => {
            if (key !== 'id' && key !== 'name' && key !== 'description') {
                result.push({
                    name: item.name?.value || '',
                    id: item[key]?.wardId || null,
                    value: item[key]?.value?.toString() || '',
                    type: key,
                    description: item.description?.value || '',
                });
            }
        });

        return result;
    };

    const handleDeleteData = async names => {
        isLoading.set(true);
        openUpdateResult = true;
        try {
            const res = await fetch(`${SocURL}/rft-nursing-ou`, {
                method: 'DELETE',
                body: JSON.stringify({ data: names }),
            });
            if (!res.ok) throw new Error('Delete failed');
            showSuccessMessage('Successfully deleted configuration!');
        } catch (err) {
            showSuccessMessage('Failed to delete configuration!');
        } finally {
            isLoading.set(false);
        }
    };

    const handleUpdateData = async updateItems => {
        isLoading.set(true);
        try {
            const res = await fetch(`${SocURL}/rft-nursing-ou`, {
                method: 'PATCH',
                body: JSON.stringify({
                    data: updateItems.flatMap(transformItemToArray),
                }),
            });
            if (!res.ok) throw new Error('Update failed');
            showSuccessMessage('Successfully updated configuration!');
        } catch (err) {
            showSuccessMessage('Failed to update configuration!');
        } finally {
            isLoading.set(false);
        }
    };

    const handleCreateData = async newItems => {
        isLoading.set(true);
        try {
            const res = await fetch(`${SocURL}/rft-nursing-ou`, {
                method: 'POST',
                body: JSON.stringify({
                    data: newItems.flatMap(transformItemToArray),
                }),
            });
            if (!res.ok) throw new Error('Create failed');
            showSuccessMessage('Successfully created configuration!');
        } catch (err) {
            showSuccessMessage('Failed to create configuration!');
        } finally {
            isLoading.set(false);
        }
    };
    const handleAppendNewRow = () => {
        const newId = uuidv4();
        tableData = [
            ...tableData,
            {
                id: newId,
                name: { value: '' },
                description: { value: '' },
                ...dynamicColumns.reduce((acc, column) => {
                    acc[column.name] = { wardId: null, value: column.type === ColumnDataType.Boolean ? false : '' };
                    return acc;
                }, {}),
                isNew: true,
            },
        ];
        newRecords.add(newId);

        columnNames.forEach(column => {
            if (column.type === ColumnDataType.Boolean) {
                checkAllMapped.set({
                    ...$checkAllMapped,
                    [column.name]: false,
                });
            }
        });
    };

    function checkScroll() {
        if (tableWrapperRef) {
            isScrollable = tableWrapperRef.scrollHeight > (tableWrapperRef.clientHeight * 125) / 100;
        }
        if (isScrollable) {
            tableWrapperRef.scrollTo({ top: tableWrapperRef.scrollHeight, behavior: 'smooth' });
        }
    }

    function checkSelectAll() {
        columnNames.forEach(column => {
            if (column.type === ColumnDataType.Boolean) {
                const allChecked = tableData.every(row => row[column.name]?.value === true);
                checkAllMapped.set({
                    ...$checkAllMapped,
                    [column.name]: allChecked,
                });
            }
        });
    }

    function mapTableDataToList(tableData) {
        return tableData.map(row => {
            const mappedRow = {
                id: row.id,
                name: row.name?.value || '',
                description: row.description?.value || '',
            };

            Object.keys(row).forEach(key => {
                if (key !== 'id' && key !== 'name' && key !== 'description') {
                    mappedRow[key] = row[key]?.value || false;
                }
            });

            return mappedRow;
        });
    }

    $: {
        if (tableData?.length) {
            const headerKeys = columnNames?.map(n => n.name);
            const key = $sortKey;
            const direction = $sortDirection;
            const sorted = tableData.filter(d => {
                if (!searchText) return true;
                return headerKeys.some(key => {
                    if (!d[key].value || typeof d[key].value !== 'string') return false;
                    return d[key]?.value?.toLowerCase()?.includes(searchText.toLowerCase());
                });
            });

            if ((filteredRecords && !filteredRecords.length) || !tableData?.length) {
                $sortItems = [];
            } else {
                const temp =
                    filteredRecords && isSearch
                        ? tableData.filter(item => filteredRecords.some(filteredItem => filteredItem.id === item.id))
                        : tableData;
                $sortItems = searchText === '' ? temp : sorted;
            }
            $sortItems.sort(functionSort(key, direction));
        } else {
            sortItems.set([]);
        }
    }

    $: filteredColumnItems =
        searchColumnShow === ''
            ? columnNames
            : columnNames.filter(column => column.label.toLowerCase().indexOf(searchColumnShow?.toLowerCase()) !== -1);
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <div class="m-4 h-[calc(100%-12rem)]">
        <div
            class="w-full rounded-t-xl border border-gray-200 bg-gray-50 p-3 font-bold text-white dark:border-gray-600 dark:bg-gray-700">
            <Heading tag="h5">{tableName}</Heading>
        </div>
        <div
            class="h-[calc(100vh-160px)] overflow-y-auto rounded-b-xl px-2 pt-5 text-gray-700 dark:bg-gray-900 dark:text-gray-400">
            <Toolbar embedded>
                <div class="flex gap-4">
                    <div class="flex">
                        <Search
                            bind:searchText
                            columns={searchColumns}
                            bind:this={advancedSearchRef}
                            isNeedScroll={true}
                            data={mapTableDataToList(tableData)}
                            bind:applySearch={applyAdvancedSearch}
                            bind:isSearch
                            on:apply={e => {
                                if (!e?.detail) {
                                    return;
                                }
                                const newData = onApplyAdvancedSearch(e, {
                                    advancedSearchVariables,
                                    advancedSearchLabels,
                                    displayLabels,
                                    displayRecordIds,
                                    searchColumns,
                                });
                                advancedSearchVariables = newData.advancedSearchVariables;
                                advancedSearchLabels = newData.advancedSearchLabels;
                                displayLabels = newData.displayLabels;
                                filteredRecords = e.detail ? e.detail.filteredData : data;
                            }} />
                    </div>
                    {#if hasAccess(data.abilities, SCOPE.UPDATE) || hasAccess(data.abilities, SCOPE.CREATE)}
                        <Button size="md" color="light" on:click={onSave} class="dark:bg-gray-700">
                            <span class="mr-2">{@html icon(faSave).html}</span>
                            <span>Save</span>
                        </Button>
                    {/if}
                    {#if hasAccess(data.abilities, SCOPE.CREATE)}
                        <Button size="md" color="light" on:click={handleAppendNewRow} class="dark:bg-gray-700">
                            <span class="mr-2">{@html icon(faAdd).html}</span>
                            <span>New</span>
                        </Button>
                    {/if}
                    {#if hasAccess(data.abilities, SCOPE.DELETE)}
                        <Button size="md" color="light" on:click={onDelete} class="dark:bg-gray-700">
                            <span class="mr-2">{@html icon(faTrash).html}</span>
                            <span>Delete</span>
                        </Button>
                    {/if}
                    <Button size="md" color="light">
                        Show Column(s)
                        <ChevronDownOutline class="ms-2 h-5 w-6 text-white dark:text-white " />
                    </Button>
                    <Dropdown class="h-44 overflow-y-auto px-3 pb-3 text-sm">
                        <Input placeholder="Search" class="min-w-[200px]" bind:value={searchColumnShow}>
                            <div slot="left">
                                {@html icon(faSearch).html}
                            </div>
                        </Input>
                        <li class="rounded-sm p-2 hover:bg-gray-100 dark:hover:bg-gray-600">
                            <Checkbox
                                bind:checked={$selectAllColumnCheckboxRef}
                                on:change={e => {
                                    if (e.target.checked) {
                                        customColumnShow = columnNames.map(column => column.name);
                                    } else {
                                        customColumnShow = ['name', 'description'];
                                    }
                                    updateSelectAllState();
                                }}>
                                Select All
                            </Checkbox>
                        </li>
                        <!-- Individual Column Checkboxes -->
                        {#each filteredColumnItems as column (column.name)}
                            {#if column.name !== 'name' && column.name !== 'description'}
                                <li class="rounded-sm p-2 hover:bg-gray-100 dark:hover:bg-gray-600">
                                    <Checkbox
                                        checked={customColumnShow.includes(column.name)}
                                        on:change={e => {
                                            if (e.target.checked) {
                                                customColumnShow = [...customColumnShow, column.name];
                                            } else if (column.name !== 'name' && column.name !== 'description') {
                                                customColumnShow = customColumnShow.filter(
                                                    name => name !== column.name
                                                );
                                            }
                                            updateSelectAllState();
                                        }}
                                        data-column-name={column.name}>
                                        {column.label}
                                    </Checkbox>
                                </li>
                            {/if}
                        {/each}
                    </Dropdown>

                    <Button size="md" color="light" on:click={onRefresh} class="dark:bg-gray-700">
                        <span class="mr-2">{@html icon(faRefresh).html}</span>
                        <span>Refresh</span>
                    </Button>
                    <slot name="header-button" {selectedRow} {onRefresh} />
                </div>
            </Toolbar>
            {#if displayLabels.length > 1}
                <div class="mt-3">
                    {#each displayLabels as label, i}
                        <div class="mb-2 mr-2 inline-block rounded-md bg-gray-700 px-3 py-2 text-sm">
                            <span>{@html label}</span>
                            {#if i > 0}
                                <button
                                    class="pl-2 text-red-600"
                                    on:click={() =>
                                        removeAdvancedSearchLabel({
                                            index: i,
                                            advancedSearchVariables,
                                            advancedSearchLabels,
                                            applyAdvancedSearch,
                                        })}>{@html icon(faClose).html}</button>
                            {/if}
                        </div>
                    {/each}
                </div>
            {/if}
            <div
                bind:this={tableWrapperRef}
                class="mt-5 max-h-[calc(100%-80px)] overflow-y-auto border border-gray-500">
                <Table
                    striped
                    shadow
                    placeholder="Search by maker name"
                    style="border-collapse: separate; border-spacing: 0;"
                    divClass="">
                    <TableHead
                        defaultRow={false}
                        class="z-[1] bg-gray-50 text-base text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                        theadClass="sticky top-0 z-[-0]">
                        <TableBodyRow>
                            <TableHeadCell
                                class={`${headCellStyle} sticky left-0 w-20 bg-gray-50 dark:bg-gray-900`}
                                rowspan="2">
                                #
                            </TableHeadCell>
                            <TableHeadCell
                                rowspan="2"
                                on:click={e => sortTable(e, staticColumnName[0].name)}
                                class={`${headCellStyle} sticky left-[3.71rem] w-40 min-w-[10rem] cursor-pointer bg-gray-50 text-base font-medium dark:bg-gray-900`}>
                                <div class="flex items-center text-nowrap">
                                    {@html staticColumnName[0].label}
                                    {#if staticColumnName[0].isRequired}
                                        <span class="text-red-500">*</span>
                                    {/if}
                                    {#if staticColumnName[0].editable}
                                        <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>
                                    {/if}
                                    {#if staticColumnName[0].name === $sortKey && $sortDirection === ASC}
                                        <span class="ml-2">{@html icon(faSortUp).html}</span>
                                    {/if}
                                    {#if staticColumnName[0].name === $sortKey && $sortDirection === -ASC}
                                        <span class="ml-2">{@html icon(faSortDown).html}</span>
                                    {/if}
                                </div>
                            </TableHeadCell>
                            <TableHeadCell
                                rowspan="2"
                                on:click={e => sortTable(e, staticColumnName[1].name)}
                                class={`${headCellStyle} sticky left-[13.711rem] w-40 cursor-pointer bg-gray-50 text-base font-medium dark:bg-gray-900`}>
                                <div class="flex items-center text-nowrap">
                                    {@html staticColumnName[1].label}
                                    {#if staticColumnName[1].editable}
                                        <span class="ml-2 text-nowrap"> [{@html icon(faPencilAlt).html}]</span>
                                    {/if}

                                    {#if staticColumnName[1].name === $sortKey && $sortDirection === ASC}
                                        <span class="ml-2">{@html icon(faSortUp).html}</span>
                                    {/if}
                                    {#if staticColumnName[1].name === $sortKey && $sortDirection === -ASC}
                                        <span class="ml-2">{@html icon(faSortDown).html}</span>
                                    {/if}
                                    <span style="position: absolute; left:40vw; top:12px">Ward Type</span>
                                </div>
                            </TableHeadCell>
                            <TableHeadCell
                                colspan={dynamicColumns.length + 1}
                                class={`${headCellStyle} w-40 cursor-pointer bg-gray-50 text-center text-base font-medium dark:bg-gray-900`}>
                                <div class="min-h-6 text-nowrap" />
                            </TableHeadCell>
                        </TableBodyRow>
                        {#if columnNames?.length}
                            {#each columnNames as col}
                                {#if customColumnShow.includes(col.name)}
                                    {#if col.name !== 'name' && col.name !== 'description'}
                                        <TableHeadCell
                                            on:click={e => sortTable(e, col.name)}
                                            class={`${headCellStyle} w-80 cursor-pointer text-base font-medium`}>
                                            <div class="flex items-center text-nowrap">
                                                {@html col.label}
                                                {#if col.isRequired}
                                                    <span class="text-red-500">*</span>
                                                {/if}
                                                {#if col.editable}
                                                    <span class="ml-2 text-nowrap">
                                                        [{@html icon(faPencilAlt).html}]</span>
                                                {/if}
                                                {#if col.type === ColumnDataType.Boolean}
                                                    <Checkbox
                                                        class="ml-2"
                                                        on:change={() => {
                                                            isUpdateColumn = true;
                                                            onCheckAll(col.name);
                                                        }}
                                                        checked={$checkAllMapped[col.name]} />
                                                {/if}
                                                {#if col.name === $sortKey && $sortDirection === ASC}
                                                    <span class="ml-2">{@html icon(faSortUp).html}</span>
                                                {/if}
                                                {#if col.name === $sortKey && $sortDirection === -ASC}
                                                    <span class="ml-2">{@html icon(faSortDown).html}</span>
                                                {/if}
                                            </div>
                                        </TableHeadCell>
                                    {/if}
                                {/if}
                            {/each}
                        {/if}
                    </TableHead>
                    <TableBody>
                        {#if !$sortItems?.length}
                            <TableBodyRow>
                                <TableBodyCell class="border border-gray-600" colSpan={(columnNames?.length ?? 1) + 1}>
                                    <Heading tag="h6" class="p-1 text-center">No data</Heading>
                                </TableBodyCell>
                            </TableBodyRow>
                        {/if}
                        {#each $sortItems as item, i}
                            <TableBodyRow>
                                {#if hasSN}
                                    <TableBodyCell
                                        class={`
                                    ${bodyCellStyle} sticky left-0 z-10 bg-gray-50 dark:bg-gray-900 
                                    ${selectedRow === item.id && '!bg-gray-500'} `}>
                                        {i + 1}
                                    </TableBodyCell>
                                {/if}
                                {#each columnNames as col}
                                    {#if customColumnShow.includes(col.name)}
                                        <TableBodyCell
                                            class={`${bodyCellStyle} 
                                        ${col.customStyle ? '' : 'px-6 py-4'} 
                                        ${isSelected(updatePosition, item, col) ? ' dark:bg-gray-900' : ''} 
                                        ${selectedRow === item.id && '!bg-gray-500'} 
                                        ${
                                            (i + 1) % 2 === 0
                                                ? '!even:bg-gray-50 dark:even:bg-gray-700'
                                                : '!odd:bg-white dark:odd:bg-gray-800'
                                        } 
                                        ${
                                            col.name === 'name'
                                                ? 'sticky left-[3.71rem] z-10 bg-gray-50 dark:bg-gray-800 '
                                                : col.name === 'description'
                                                ? 'sticky left-[13.711rem] z-10 bg-gray-50 dark:bg-gray-700 '
                                                : ''
                                        }                                               
                                        ${customColumnShow.length === 1 ? 'gray-300 last:border-r' : ''}`}
                                            tdClass={`${isEdited(updateList, item, col.name) && `relative editing`}`}>
                                            {#if col.type === ColumnDataType.Boolean}
                                                <Checkbox
                                                    checked={getBoolValue(updateList, item, col.name)}
                                                    on:change={e => {
                                                        isUpdateColumn = false;
                                                        updateBooleanField(e, item, col.name);
                                                    }} />
                                            {:else if col.type === ColumnDataType.Array}
                                                <span on:click={() => checkScroll()}>
                                                    <MultiSelect
                                                        items={subData[col.name]}
                                                        value={item?.[col.name]}
                                                        on:change={event => {
                                                            updateItem(item, col);
                                                            updateField(event, item, col);
                                                        }}
                                                        class="bg-gray-800"
                                                        dropdownClass={`${
                                                            isScrollable ? 'absolute mb-80 -top-76 z-50' : ''
                                                        }`} />
                                                </span>
                                            {:else if (col.type === ColumnDataType.SingleNumberSelect || col.type === ColumnDataType.SingleStringSelect) && updatePosition.isEditing && updatePosition.id === item.id && updatePosition.columnName === col.name && col.editable}
                                                <Select
                                                    placeholder="Select Data {col.label}"
                                                    value={item[col.name]}
                                                    items={subData[col.name]}
                                                    on:change={event => {
                                                        updateItem(item, col);
                                                        updateField(event, item, col);
                                                    }}
                                                    class="bg-gray-800 " />
                                            {:else if updatePosition.isEditing && updatePosition.id === item.id && updatePosition.columnName === col.name && col.editable}
                                                <input
                                                    maxlength={col.type === ColumnDataType.Number
                                                        ? 5
                                                        : col.name.toLowerCase() === 'remarks'
                                                        ? 300
                                                        : 100}
                                                    on:change={event => updateField(event, item, col)}
                                                    on:keyup={event => onKeyUp(event)}
                                                    on:focusout={() => onFocusOut(item)}
                                                    value={getValue(updateList, item, col.name)}
                                                    type={col.type === ColumnDataType.Number ? 'number' : 'text'}
                                                    id={col.name}
                                                    name={col.name}
                                                    class={inputStyle}
                                                    placeholder={`Input ${col.label}`} />
                                            {:else if col.customStyle}
                                                <slot
                                                    name="row-custom"
                                                    rowData={getValue(updateList, item, col.name)}
                                                    colData={col}
                                                    itemData={item} />
                                            {:else}
                                                <button
                                                    type="button"
                                                    class={`max-w-xs whitespace-pre-line break-words ${
                                                        col.editable ? 'cursor-pointer' : 'cursor-text'
                                                    } min-h-5 w-full text-left`}
                                                    on:dblclick={() => updateItem(item, col)}
                                                    on:click={() => {
                                                        selectedRow = item.id;
                                                    }}>
                                                    {#if !item[col.name] && !getValue(updateList, item, col.name)}
                                                        <div>&nbsp;</div>
                                                    {:else}
                                                        <div>
                                                            {getValue(updateList, item, col.name)}
                                                        </div>
                                                    {/if}
                                                </button>
                                            {/if}
                                        </TableBodyCell>
                                    {/if}
                                {/each}
                            </TableBodyRow>
                        {/each}
                    </TableBody>
                </Table>
            </div>
        </div>
    </div>

    <Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
</Guard>

<style>
    :global(.editing)::after {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 10px 10px 0;
        border-color: transparent #c54343 transparent transparent;
        right: 0;
        top: 0;
        position: absolute;
    }
</style>
