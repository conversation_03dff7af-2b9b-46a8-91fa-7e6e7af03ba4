import { test } from '@playwright/test';
test('Textfield value', async ({ page }) => {
    // await page.goto('http://localhost:4173/test-pages/components');
    // // Fill [placeholder="Field"]
    // await page.locator('[placeholder="Field"]').fill('This is a textfield');
    // await expect(page.locator('[placeholder="Field"]')).toHaveValue('This is a textfield');
    // await page.close();
});

test('Textfield value 1', async ({ page }) => {
    // await page.goto('http://localhost:4173/test-pages/components');
    // // Fill [placeholder="Field"]
    // await page.locator('[placeholder="Field"]').fill('This is a textfield');
    // await expect(page.locator('[placeholder="Field"]')).toHaveValue('This is a textfield');
    // await page.close();
});
