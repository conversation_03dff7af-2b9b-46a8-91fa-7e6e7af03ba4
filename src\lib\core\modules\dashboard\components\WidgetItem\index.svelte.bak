<script lang="ts" context="module">
    export interface WidgetDetail {
        widgetId: string;
        widgetItem: HTMLElement;
        component: typeof self;
    }

    export interface GridStackHTMLElement extends HTMLElement {
        gridstack?: GridStack;
    }

    interface GridItemHTMLElementExt extends GridItemHTMLElement {
        broadcast?: boolean;
    }

    interface GridStackNodeExt extends GridStackNode {
        broadcast?: boolean;
        wId?: string;
        parentId?: string;
    }

    export const DEFAULT_WIDTH = 1;
    export const DEFAULT_HEIGHT = 1;
    export const DEFAULT_MIN_WIDTH = 1;
    export const DEFAULT_MIN_HEIGHT = 1;
    export const DEFAULT_MAX_WIDTH = 0;
    export const DEFAULT_MAX_HEIGHT = 0;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    type ComponentEvents = {
        select: WidgetDetail;
        edit: WidgetDetail;
        delete: WidgetDetail;
        saveWidget: WidgetDetail;
        addWidgetInSubGrid: WidgetDetail;
        editSubGrid: WidgetDetail;
        deleteSubGrid: WidgetDetail;
    };

    export type WidgetItemSelectEvent = CustomEvent<WidgetDetail>;
</script>

<!-- @component
    WidgetItem's are designed to be used in the widget system utilizing gridstack.js

    Props:
        - id: A unique identifier
        - title: The title to display for the widget
        - editable: Wether or not the edit button and delete buttons are visible - used to edit the configuration options for the widget;
        - options: The positioning and sizing of the WidgetItem { x: number, y: number, w: number, h: number }
        - onResize: A callback function to run whenever the WidgetItem is resized.
        - self: A reference to the component

    // TODO: Write documentation for exported functions when finalized

    Usage:
        ```tsx
        <WidgetGrid> <WidgetItem> <OtherComponents props={'Hi Mom'}/> </WidgetItem> </WidgetGrid>
        ```
 -->
<script lang="ts">
    import Button from '$lib/core/components/Button/index.svelte';
    import { isDirty } from '$lib/core/components/PageTitle/index.svelte';
    import { GRID_STACK_CONTEXT } from '$lib/core/modules/dashboard/components/WidgetGrid/index.svelte';
    import { logger } from '$lib/stores/Logger';
    import { faGear, faTrash, faPlus } from '@fortawesome/free-solid-svg-icons';
    import type { GridStack, GridStackNode, GridItemHTMLElement } from 'gridstack';
    import { createEventDispatcher, getContext, onMount, tick } from 'svelte';
    import type { FormEventHandler } from 'svelte/elements';
    import type { Writable } from 'svelte/store';
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { findWidgetById, filterWidgetByExcludeId, pushDataToWidget } from '$lib/core/modules/dashboard/utils/utils';
    import { loadedWidgetItemsStore } from '$lib/core/modules/dashboard/module';

    export let id: string;
    export let title: string;
    export let editable = false;
    export let options: GridStackNodeExt = undefined;
    export let isSubGrid: boolean = false;
    export let parentId: string;
    export let selectedLoadedWidgetItemConfig: LoadedWidgetItemConfig;
    let widgetItems: Array<LoadedWidgetItemConfig> = [];

    let selected = false;

    // Gridstack Types don't seem accurate
    let widgetItemElement: GridItemHTMLElementExt; //GristackWidget

    let subGridElement: GridStackHTMLElement;

    if (!getContext<Writable<GridStack>>(GRID_STACK_CONTEXT)) {
        throw new Error('no widget grid context found - widget Items can only be created inside widget grids');
    }

    // Get the GridStack this widget is in
    let gridstack = getContext<Writable<GridStack>>(GRID_STACK_CONTEXT);

    export let GridStackLib;

    const dispatch = createEventDispatcher<ComponentEvents>();

    export function setSelected(newSelected: boolean) {
        selected = newSelected;
    }

    export function getSelected() {
        return selected;
    }

    export function getGridstackNode() {
        return widgetItemElement.gridstackNode;
    }

    const selectWidget: FormEventHandler<HTMLDivElement> = (_e: Event) => {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };

        dispatch('select', detail);
    };

    const editWidget = (_e: MouseEvent) => {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };
        $isDirty = true;

        dispatch('edit', detail);
    };

    const deleteWidget = (_e: MouseEvent) => {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };

        dispatch('delete', detail);
    };

    function addWidgetInSubGrid() {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };

        dispatch('addWidgetInSubGrid', detail);
    }

    function editSubGrid() {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };
        $isDirty = true;

        dispatch('editSubGrid', detail);
    }

    function deleteSubGrid() {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };
        $isDirty = true;

        dispatch('deleteSubGrid', detail);
    }

    let mounted = false;

    function makeSubGrid() {
        widgetItemElement.broadcast = options.broadcast;
        let node: GridItemHTMLElement;
        let gridOrSubGrid = widgetItemElement.closest('.grid-stack').gridstack;
        options.wId = id;
        options.parentId = parentId;
        node = gridOrSubGrid.makeWidget(widgetItemElement, options);
        let ops = {
            ...$gridstack.opts.subGridOpts,
            column: options.w,
            children: [],
            disableOneColumnMode: true,
            cellHeight: Number($gridstack.opts.cellHeight) - 4,
        };
        createSubGrid(node.gridstackNode, ops);
        widgetItemElement.broadcast = true;
    }

    async function createSubGrid(node, ops) {
        node.subGridOpts = ops;

        node.subGrid = GridStackLib.GridStack.addGrid(subGridElement, ops);
        node.subGrid._autoColumn = true;

        //  Resize
        node.subGrid.on('resizestop', function (event, element) {
            resize(event, element);
        });

        node.subGrid.on('added removed change', function (event, element) {
            resize(event, element.el);
            let el = event['detail'][0]['el'];

            if (el) {
                if (el.broadcast) {
                    $isDirty = true;
                }
            }
        });
        node.subGrid.on('dropped', function (event: Event, previousWidget: GridStackNode, newWidget: GridStackNode) {
            let gs = newWidget.el.closest('.grid-stack').gridstack;
            gs.removeWidget(newWidget.el, false);
            let widgetItem = findWidgetById($loadedWidgetItemsStore, previousWidget.wId);
            widgetItem.widgetGridStackOptions = {
                ...widgetItem.widgetGridStackOptions,
                x: newWidget.x,
                y: newWidget.y,
                w: newWidget.w,
                h: newWidget.h,
            };
            $loadedWidgetItemsStore = filterWidgetByExcludeId($loadedWidgetItemsStore, previousWidget.wId);
            widgetItem.parentId = id;
            pushDataToWidget($loadedWidgetItemsStore, id, widgetItem);
            $loadedWidgetItemsStore = $loadedWidgetItemsStore;
        });

        node.subGrid.on('enable', function (event, element) {
            $isDirty = false;
        });

        window.addEventListener('resize', event => {
            resize(event);
        });

        resize();
    }

    export const resize = (_event?: Event, _element?: HTMLElement) => {
        browserBroadcaster.pub(Channel.AOH_WIDGET_RESIZED, {
            type: Channel.AOH_WIDGET_RESIZED,
            data: 'Gridstack resize stopped event.',
        });
    };

    function makeWidget() {
        // Options
        if (!options) {
            options = {};
        }

        if (options.x == undefined) {
            options.x = 0;
        }

        if (options.y == undefined) {
            options.y = 0;
        }

        if (!options.autoPosition) {
            options.autoPosition = false;
        }

        if (options.w == undefined) {
            options.w = DEFAULT_WIDTH;
        }

        if (options.h == undefined) {
            options.h = DEFAULT_HEIGHT;
        }

        if (options.minW == undefined) {
            options.minW = DEFAULT_MIN_WIDTH;
        }

        if (options.minH == undefined) {
            options.minH = DEFAULT_MIN_HEIGHT;
        }

        if (options.maxW == undefined) {
            options.maxW = DEFAULT_MAX_WIDTH;
        }

        if (options.maxH == undefined) {
            options.maxH = DEFAULT_MAX_HEIGHT;
        }

        // Store the broadcast flag for reading in the event handler - if we don't want it broadcast,
        // we won't process it in the handler - hacky workaround

        widgetItemElement.broadcast = options.broadcast;
        let gridOrSubGrid = widgetItemElement.closest('.grid-stack').gridstack;
        options.wId = id;
        options.parentId = parentId;
        gridOrSubGrid.makeWidget(widgetItemElement, options);
        // Only don't broadcast at the start?
        widgetItemElement.broadcast = true;
    }

    // We only want to update the noMove and noResize attributes
    // the x and y states in the 'options' should not be re-used to set the widget back to its former position, these
    // will be saved into the database when the user clicks save
    $: if (options && widgetItemElement) {
        updateGridItem();
    }

    function updateGridItem() {
        if (widgetItemElement)
            widgetItemElement.closest('.grid-stack')?.gridstack?.update(widgetItemElement, {
                noMove: options.noMove,
                noResize: options.noResize,
            });
    }

    $: if ($loadedWidgetItemsStore && mounted && isSubGrid) {
        getWidgetChilds();
    }

    function getWidgetChilds() {
        if (widgetItemElement) {
            let arr = findWidgetById($loadedWidgetItemsStore, id)?.widgetItems;
            widgetItems = arr ? arr : [];
        }
    }

    onMount(() => {
        // Wait for options to update the DOM before making widget
        tick().then(() => {
            if (isSubGrid) makeSubGrid();
            else makeWidget();
            mounted = true;
        });

        // client-side only onDestroy
        return () => {
            if (widgetItemElement) {
<<<<<<< HEAD
                widgetItemElement.closest('.grid-stack')?.gridstack?.removeWidget(widgetItemElement, false);
=======
                log.debug({ widgetItemElement }, 'About to destroy my widget item element');
                $gridstack.removeWidget(widgetItemElement, false);
>>>>>>> aoh-web/develop
            } else {
                logger.error({ widgetTitle: title }, 'Could not call gridstack.removeWidget');
            }
        };
    });
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->
{#if isSubGrid}
    <div
        bind:this={widgetItemElement}
        data-widgetContainer={'widget-' + id}
        class="grid-stack-item"
        class:ui-draggable-disabled={options.noMove}
        class:ui-draggable={!options.noMove}
        class:ui-resizable-disabled={options.noResize}
        class:ui-resizable={!options.noResize}>
        <div
            class="grid-stack-item-content sub-grid rounded-3xl border-[1px] border-outline bg-surface-gradient shadow-e-3">
            {#if editable}
                <div class="show-button invisible absolute right-0 z-20">
                    <Button
                        on:click={editSubGrid}
                        iconDefinition={faGear}
                        appendClass="@xs:w-48"
                        setColorClasses="bg-primary inline-flex text-on-primary"
                        ><span class="hidden @xs:block">EDIT WIDGET</span></Button>
                    <Button
                        on:click={addWidgetInSubGrid}
                        iconDefinition={faPlus}
                        appendClass="@xs:w-48"
                        setColorClasses="bg-secondary-btn inline-flex text-on-primary"
                        ><span class="hidden @xs:block">ADD WIDGET</span></Button>
                    <Button
                        on:click={deleteSubGrid}
                        iconDefinition={faTrash}
                        appendClass="@xs:w-48"
                        setColorClasses="bg-error inline-flex text-on-primary"
                        ><span class="hidden @xs:block">REMOVE</span></Button>
                </div>
            {/if}
            <slot />
            <div class="grid-stack" bind:this={subGridElement} data-grid={'grid-' + id}>
                {#each widgetItems as item (item.widgetId)}
                    <svelte:self
                        bind:this={item.widgetItemRef}
                        id={item.widgetId}
                        title={item.widgetType.title}
                        options={{ ...item.widgetGridStackOptions, noMove: !editable, noResize: !editable }}
                        {editable}
                        isSubGrid={item.widgetType.isSubGrid}
                        parentId={item.parentId}
                        {GridStackLib}
                        {selectedLoadedWidgetItemConfig}
                        on:deleteSubGrid
                        on:addWidgetInSubGrid
                        on:editSubGrid
                        on:delete
                        on:edit
                        on:select>
                        {#if selectedLoadedWidgetItemConfig && selectedLoadedWidgetItemConfig.widgetId === item.widgetId}
                            <!-- What's rendered when widget is selected -->
                            <svelte:component
                                this={item.widgetType.component.default}
                                config={selectedLoadedWidgetItemConfig.widgetConfig} />
                        {:else if item.widgetConfig}
                            <!-- What's rendered when there's a config -->
                            <svelte:component this={item.widgetType.component.default} config={item.widgetConfig} />
                        {:else}
                            <!-- What's rendered when there's no config-->
                            <svelte:component
                                this={item.widgetType.component.default}
                                config={item.widgetType.configComponent.configDefaults} />
                        {/if}
                    </svelte:self>
                {/each}
            </div>
        </div>
    </div>
{:else}
    <div
        on:click={selectWidget}
        data-widgetContainer={'widget-' + id}
        data-title={title}
        bind:this={widgetItemElement}
        class:ui-draggable-disabled={options.noMove}
        class:ui-draggable={!options.noMove}
        class:ui-resizable-disabled={options.noResize}
        class:ui-resizable={!options.noResize}
        class:cursor-grab={!options.noMove}
        class:active:cursor-grabbing={!options.noMove}
        class="grid-stack-item">
        <div
            class:shadow-glow={selected}
            class:border-selected-stroke={selected}
            class:shadow-selected-stroke={selected}
            class="grid-stack-item-content group rounded-3xl border-[1px] border-outline bg-surface-gradient shadow-e-3">
            <div class="group relative h-full w-full">
                <slot />
                {#if editable}
                    <div
                        class="invisible absolute left-0 top-0 z-10 flex h-full w-full flex-col items-center justify-center
                        gap-4 bg-white bg-opacity-50 p-4 @container group-hover:visible">
                        <Button
                            on:click={editWidget}
                            iconDefinition={faGear}
                            appendClass="@xs:w-48"
                            setColorClasses="bg-primary text-on-primary"
                            ><span class="hidden @xs:block">EDIT WIDGET</span></Button>
                        <Button
                            on:click={deleteWidget}
                            iconDefinition={faTrash}
                            appendClass="@xs:w-48"
                            setColorClasses="bg-error text-on-primary"
                            ><span class="hidden @xs:block">REMOVE</span></Button>
                    </div>
                {/if}
            </div>
        </div>
    </div>
{/if}

<style>
    @import './index.css';
    .sub-grid:hover > .show-button {
        visibility: visible;
    }
</style>
