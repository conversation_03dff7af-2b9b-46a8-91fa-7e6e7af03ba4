import { expect, test } from '@playwright/experimental-ct-svelte';

import But<PERSON>, { ButtonVariant } from './index.svelte';

// TODO: find path via current route and use env vars
// Find all dynamic routes
// let componentTests = import.meta.glob('/src/lib/**/test.svelte');

// let paths = [];
// let currentPath = $page.url.pathname.split('/')[1]; // this is just the path this index.svelte is in

// for (let path in componentTests) {
// let testPath = path.replace('/src/lib', '').replace('/test.svelte', '');
// paths.push(testPath);
// }

test('test to check if button is clickable', async ({ mount }) => {
    let clicked = false;
    const bttnComponent = await mount(Button, {
        props: { variant: ButtonVariant.FLAT },
        slots: {},
        on: { click: () => (clicked = true) },
    });

    await bttnComponent.click();
    expect(clicked).toBeTruthy();
});

test('test to check if button is not clickable when disabled', async ({ mount }) => {
    const bttnComponent = await mount(Button, {
        props: { variant: ButtonVariant.FLAT, disabled: true },
    });

    await expect(bttnComponent).toBeDisabled();
});
