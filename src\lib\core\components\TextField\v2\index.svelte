<script lang="ts" context="module">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCircleExclamation } from '@fortawesome/free-solid-svg-icons';
    import { logger } from '$lib/stores/Logger';
    import { onMount } from 'svelte';
    import Tooltip from '$lib/core/components/Tooltip/index.svelte';
    import type { FormEventHandler } from 'svelte/elements';

    const src = new URL(import.meta.url).pathname;
    const [componentName] = src.split('/').slice(-2, -1);
    const log = logger.child({ src, componentName });
</script>

<script lang="ts">
    export let title = 'Text Input';
    export let placeholder = undefined;
    export let required: boolean = false;
    export let maxlength: number = undefined;
    export let pattern: string = '^[^\\s].+'; // default to disallow starting with a space

    export let value: string = '';

    let inputElement: HTMLInputElement;
    let inputValid: boolean;
    let validationMessage: string;

    const inputHandler: FormEventHandler<HTMLInputElement> = (e: Event) => {
        // Reset away any server side invalidity on input update, let user of this component invalidate it
        // with server feedback
        if (inputElement?.validity.customError) {
            inputElement?.setCustomValidity('');
        }

        updateValidationDisplay();
    };

    function updateValidationDisplay() {
        inputValid = inputValid = inputElement?.validity?.valid;
        validationMessage = inputElement?.validationMessage;
    }

    onMount(() => {
        updateValidationDisplay();
    });
</script>

<div class="border-b border-mono-3 px-4 py-2 has-[:focus]:border-primary has-[:invalid]:border-error">
    <label class="flex flex-col text-xs leading-4 tracking-wide text-subheader">
        {title}
        <div class="flex items-center justify-between gap-1">
            <input
                bind:this={inputElement}
                on:input={inputHandler}
                on:invalid={updateValidationDisplay}
                on:change
                on:blur
                bind:value
                {required}
                {maxlength}
                {pattern}
                {placeholder}
                type="text"
                class="w-full bg-transparent pr-2 text-base font-normal leading-6
                tracking-[0.5px] text-body caret-body focus:border-primary focus:outline-none" />
            {#if !inputValid && validationMessage}
                <!-- Error Symbol -->
                <div class="relative">
                    <div class="absolute left-[3px] top-[6px] z-10 h-[10px] w-[10px] bg-on-primary" />
                    <div class="absolute z-20 text-base text-error">
                        {@html icon(faCircleExclamation).html}
                    </div>
                    <Tooltip offset={10}
                        ><div class="min-w-48">
                            {validationMessage}
                        </div></Tooltip>
                    &nbsp;
                </div>
            {/if}
        </div>
    </label>
</div>
