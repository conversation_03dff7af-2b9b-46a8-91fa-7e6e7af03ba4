import { locations } from '$lib/stores/Locations';
import { StatusCodes } from 'http-status-codes';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

const IncidentMonitoringURL = `${get(locations).incident_monitoring}`;

export const GET: RequestHandler = async ({ locals, request, url, params }) => {
    let message: string;
    let status: StatusCodes;
    const res = await fetch(`${IncidentMonitoringURL}/incident/moh-reports/${params.id}`, {
        method: 'GET',
        headers: {
            'content-type': 'application/json',
            'Authorization': 'Bearer ' + locals.user.access_token,
        },
    }).then(response => {
        status = response.status;
        message = response.statusText;
        return response.json();
    });
    const responseBody = {
        message,
        data: res,
    };

    return new Response(JSON.stringify(responseBody), {
        status,
    });
};

export const PATCH: RequestHandler = async ({ locals, request, url, params }) => {
    let message: string;
    let status: StatusCodes;
    const requestData: any = await request.json();
    const res = await fetch(`${IncidentMonitoringURL}/incident/moh-reports/${params.id}`, {
        method: 'PATCH',
        body: JSON.stringify(requestData),
        headers: {
            'content-type': 'application/json',
            'Authorization': 'Bearer ' + locals.user.access_token,
        },
    }).then(response => {
        return response;
    });
    status = res.status;
    message = res.statusText;
    const responseBody = {
        message,
    };
    return new Response(JSON.stringify(responseBody), {
        status,
    });
};
