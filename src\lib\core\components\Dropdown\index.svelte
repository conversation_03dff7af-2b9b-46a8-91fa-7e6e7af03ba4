<script context="module" lang="ts">
    export type DropdownItem = {
        label: string;
        value?: any;
        itemStyle?: string;
        icon?: IconDefinition;
        iconStyle?: string;
        itemIcon?: IconDefinition;
        itemIconStyle?: string;
        htmlString?: string;
        action?: (opts?: any) => any;
    };
</script>

<script lang="ts">
    import { icon, type IconDefinition } from '@fortawesome/fontawesome-svg-core';
    import { faCaretDown, faSearch } from '@fortawesome/free-solid-svg-icons';
    import { fly } from 'svelte/transition';

    import { logger } from '$lib/stores/Logger';
    import { clickOutside } from '$lib/utils';
    import { createEventDispatcher } from 'svelte';

    export let setClass = 'border-b-outline bg-surface-gradient';
    export let appendClass = '';
    export let menuClass = '';
    export let appendMenuClass = '';
    export let itemClass = '';
    export let appendItemClass = '';
    export let arrowIcon = faCaretDown;
    export let iconClass = 'text-on-background';
    export let title: string = '';
    export let value: any = null;
    export let labelClass = '';
    export let options: Array<DropdownItem>;
    export let selectedIndex = 0;
    export let placeholder = '-';
    export let label: string = '-';
    export let optionsDefaultLabel = '-';
    export let previousValue: unknown = null;
    export let onChange: () => void;

    export let setSearchbarClass: string =
        'grow bg-surface-1 h-8 w-full flex items-center gap-1 rounded border border-outline p-1 px-2 text-on-surface';
    export let setDisabled: boolean = false;
    export let enableSearch: boolean = false;

    // This is added to allow us to append extra details to data-testid's, for convenience of testing
    export let metaTestInfo = '';

    let isOpen = false;
    let searchText: string = '';
    const dispatch = createEventDispatcher();
    let menuElem: HTMLElement;

    /* -------------------------------------------------------------------------- */
    /*                                   HELPERS                                  */
    /* -------------------------------------------------------------------------- */

    export function setOption(index) {
        previousValue = value;
        label = options[index]?.label;
        value = options[index]?.value;
    }
    export function clearOption() {
        previousValue = value;
        label = placeholder;
        value = null;
    }

    function initialize() {
        // Initialize currently selected value
        if (options?.length) {
            let found = false;
            options.forEach((option, y) => {
                if (option.value === value) {
                    found = true;
                    label = option.label;
                    selectedIndex = y;
                }
            });

            if (found === false && options[0]) {
                previousValue = value;
                value = options[0].value;
                label = options[0].label;
                selectedIndex = 0;
            }
        } else {
            label = optionsDefaultLabel;
        }
    }

    // Select an option either by index or label, and pass in optional 'actionOpts' as arugment to the callback func
    export function select(opts?: { index?: number; label?: string; actionOpts?: any }) {
        if (options.length <= 0) {
            logger.warn({ opts }, 'Dropdown component: No options to select.');
            return;
        }

        if (opts?.label) {
            let found = false;
            for (let i = 0; i < options.length; i++) {
                let optElement = options[i];

                if (optElement.label === opts.label) {
                    selectedIndex = i;
                    found = true;
                    break;
                }
            }

            if (!found) {
                logger.warn({ opts }, 'Dropdown component: Unable to find option based on label.');
            }
        }

        if (typeof opts?.index === 'number') {
            // Select the last element
            if (opts.index === -1) {
                opts.index = options.length - 1;
            }

            if (opts.index > options.length - 1) {
                logger.warn({ opts }, 'Dropdown component: Attempted to select outside of options range.');
                return;
            }

            // Keep track of which has been selected
            selectedIndex = opts.index;
        }

        // Hide the list (close it) in-case it was open
        isOpen = false;

        let selectedOption = options[selectedIndex];

        // Set the displayed label
        label = selectedOption.label ? selectedOption.label : 'LABEL_MISSING';

        // Set the new value to whatever the option's stored value is
        if (selectedOption.value) {
            previousValue = value;
            value = selectedOption.value;
        }

        // Perform the callback action if it has one
        if (selectedOption.action) {
            selectedOption.action(opts.actionOpts);
        }
    }

    /* -------------------------------------------------------------------------- */
    /*                      LIFECYCLE AND REACTIVE STATEMENTS                     */
    /* -------------------------------------------------------------------------- */
    $: if (!isOpen) searchText = '';
    $: if (options.length && value) initialize();
</script>

<!-- svelte-ignore a11y-click-events-have-key-events -->
<div
    tabindex="0"
    role="listbox"
    data-testid={$$props['data-testid']}
    class="flex {setClass
        ? setClass
        : '' +
          appendClass} group relative min-h-[20px] cursor-pointer items-center justify-between gap-4 border-b border-b-outline"
    on:click={() => {
        if (options && !setDisabled) isOpen = !isOpen;
    }}
    use:clickOutside
    on:outclick={() => {
        if (options) isOpen = false;
    }}>
    {#if title}
        <p
            class:transition-all={isOpen}
            class:duration-75={isOpen}
            class:ease-in-out={isOpen}
            class="absolute -top-1 left-2 text-3xs">
            {title}
        </p>
    {/if}

    <p class="px-2 {title ? 'pt-4' : ''} truncate py-2 {labelClass} {setDisabled ? 'opacity-50' : ''}">
        {label ? label : '-'}
    </p>
    <p class="px-2 py-1 text-base {iconClass} {setDisabled ? 'opacity-50' : 'group-hover:scale-125'}">
        {@html icon(arrowIcon).html}
    </p>
    {#if isOpen && options}
        <div
            transition:fly|global={{ y: -10, duration: 100 }}
            bind:this={menuElem}
            data-testid={$$props['data-testid'] + '-' + metaTestInfo}
            class="absolute z-50 max-h-64 {menuClass
                ? menuClass
                : 'left-0 top-12 overflow-y-auto bg-surface-1 shadow-e-24'} {appendMenuClass}">
            {#if enableSearch}
                <header class="sticky top-0 {setSearchbarClass}">
                    <!-- svelte-ignore a11y-autofocus -->
                    <input
                        disabled={setDisabled}
                        class="w-full bg-transparent bg-opacity-50 p-2 focus:outline-none"
                        bind:value={searchText}
                        autofocus={true} />
                    {@html icon(faSearch).html}
                </header>
            {/if}
            {#each options.filter(item => {
                if (enableSearch) return item.label?.toLocaleLowerCase().includes(searchText?.toLocaleLowerCase());
                return item;
            }) as option, index}
                <!-- svelte-ignore a11y-click-events-have-key-events -->
                <div
                    role="option"
                    tabindex="0"
                    aria-selected={selectedIndex === index}
                    class="flex items-center justify-between gap-4 p-2 text-xs text-inherit {itemClass
                        ? itemClass
                        : 'max-w-96 truncate '} {appendItemClass} \
                        {selectedIndex === index
                        ? 'bg-surface-1 font-bold text-on-surface-1'
                        : 'text-on-surface-1 hover:bg-surface-4'}"
                    on:click={e => {
                        label = option.label;
                        selectedIndex = index;

                        value = option?.value;

                        isOpen = false;
                        if (onChange) {
                            onChange();
                        }

                        dispatch('update');

                        if (option.action) option.action();

                        e.stopPropagation();
                    }}>
                    <p class={option.itemStyle ? option.itemStyle : ''}>{option.label}</p>
                    {#if option.icon}
                        <i class={option.iconStyle ? option.iconStyle : ''}>{@html icon(option.icon).html}</i>
                    {/if}
                </div>
            {/each}
        </div>
    {/if}
</div>
