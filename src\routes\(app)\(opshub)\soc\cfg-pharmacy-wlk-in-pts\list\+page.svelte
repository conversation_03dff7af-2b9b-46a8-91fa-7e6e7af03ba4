<script lang="ts">
    import { onMount } from 'svelte';

    import WalkInPatients from '$lib/core-soc/modules/crud/WalkInPatients.svelte';
    import { user } from '$lib/stores/Auth';
    import { get } from 'svelte/store';
    import { SCOPE } from '$lib/shared/enum/general';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    export let data;

    let user_id = '';
    let subData;
    const getSubData = async () => {
        const pharmacyResponse = await fetch('/api/soc/cfg-ngemr-pharmacy');
        let pharmacyData;
        let pharmacyWlkInPtsData;
        if (pharmacyResponse.status === 200) {
            pharmacyData = await pharmacyResponse.json();
        }
        const pharmacyWlkInPtsResponse = await fetch('/api/soc/cfg-pharmacy-wlk-in-pts');
        if (pharmacyWlkInPtsResponse.status === 200) {
            pharmacyWlkInPtsData = await pharmacyWlkInPtsResponse.json();
        }
        const usedPharmacyCodes = pharmacyWlkInPtsData.data?.map(item => item?.pharmacy_code);
        const finalData = pharmacyData.data.filter(item => !usedPharmacyCodes.includes(item.code));
        subData = {
            pharmacy_desc: finalData?.map(item => ({ value: item.desc, name: item.desc, ref: item.code })),
            pharmacy_code: finalData?.map(item => ({ value: item.code, name: item.code, ref: item.desc })),
        };
    };
    onMount(async () => {
        user_id = get(user)?.claims?.hasura_access?.['x-hasura-user-id'];
        await getSubData();
    });
    const onSaveSuccess = async (result: CustomEvent<boolean>) => {
        await getSubData();
    };
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <div class="flex flex-col gap-20 p-3 text-on-surface-1">
        {#if user_id !== ''}
            <WalkInPatients {subData} on:saveSuccess={onSaveSuccess} abilities={data.abilities} />
        {/if}
    </div>
</Guard>
