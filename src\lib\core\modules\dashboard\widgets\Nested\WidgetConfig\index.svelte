<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface SubGridConfig {
        title?: string;
        bgColor?: string;
        showFilteringLabel: boolean;
        showGear: boolean;
        cfg_param: string;
        cfg_param_name: string;
        cfg_param_label: string;
        label_use_filter_datasource: boolean;
        filterLabelAlignment: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
    export const isSubGrid = true;

    const DEFAULT_BGCOLOR = '#242424';

    export const configDefaults: SubGridConfig = {
        title: WIDGET_GROUP_TITLE,
        bgColor: DEFAULT_BGCOLOR,
        showFilteringLabel: false,
        showGear: false,
        cfg_param: '',
        cfg_param_label: '',
        label_use_filter_datasource: false,
        filterLabelAlignment: 'end',
    };
</script>

<script lang="ts">
    import { WIDGET_GROUP_TITLE } from '$lib/core/modules/dashboard/widgets/utils';
    import { Accordion, AccordionItem, Button, Input, Label, Select, Toggle } from 'flowbite-svelte';
    import type { CfgParameter } from '$lib/shared/types/cfgParameter';
    import { onMount } from 'svelte';

    const alignItems = [
        { value: 'start', name: 'left' },
        { value: 'center', name: 'center' },
        { value: 'end', name: 'right' },
    ];

    export let config: SubGridConfig;
    export let updateChildrenConfig;
    export let id;

    let cfgParameters: CfgParameter[] = [];

    onMount(async () => {
        getCfgParameters();
    });

    const getCfgParameters = async () => {
        try {
            const resCfg = await fetch(
                `/api/CfgParameterService?category=filtering_label&category=filtering_config&category=BackendParameter`
            );
            const { res } = await resCfg.json();
            const data: CfgParameter[] = res.data;
            cfgParameters = data;
        } catch (error) {
            console.log(error);
        }
    };

    const getFilterLabelAlignment = (event: any) => {
        config.filterLabelAlignment = event.currentTarget.value;
    };

    const onChangeDatasource = (event: any) => {
        config.cfg_param = event.target.value;
        config.cfg_param_name = cfgParameters.find(e => e.id === event.target.value)?.name;

        updateChildrenConfig(id, {
            cfg_param: event.target.value,
            cfg_param_name: config.cfg_param_name,
        });
    };
</script>

<div class="flex flex-col gap-2">
    <Accordion>
        <AccordionItem open>
            <span slot="header">Properties</span>
            <div>
                <Label for="title" class="mb-2">Title</Label>
                <Input type="text" id="title" placeholder="Input Title" bind:value={config.title} />
            </div>

            <div class="mt-2 flex items-center gap-4">
                <Label for="bgColor">Background Color</Label>
                <Input id="bgColor" type="color" class="h-10 w-10 min-w-[2.5rem] p-0" bind:value={config.bgColor} />
            </div>
        </AccordionItem>
        <AccordionItem>
            <span slot="header">Filtering</span>

            <div class="my-3">
                <Toggle color="blue" bind:checked={config.showGear}>Show Filtering</Toggle>
            </div>
            <Label>
                Filter Data Source
                <Select
                    class="mt-2 capitalize"
                    items={cfgParameters
                        .filter(e => e.category === 'filtering_config')
                        .map(f => ({
                            name: f.name,
                            value: f.id,
                        }))}
                    bind:value={config.cfg_param}
                    on:change={onChangeDatasource} />
            </Label>
            <div class="my-3">
                <Toggle color="blue" bind:checked={config.showFilteringLabel}>Show Filtering Label</Toggle>
            </div>
            {#if config.showFilteringLabel}
                <div class="my-3">
                    <Toggle
                        disabled={!config.showGear}
                        class={`${!config.showGear && '!text-gray-600'}`}
                        color="blue"
                        bind:checked={config.label_use_filter_datasource}>Use the same as Filter Data Source</Toggle>
                </div>
                {#if !config.label_use_filter_datasource}
                    <Label>
                        Label Data Source
                        <Select
                            class="mt-2 capitalize"
                            items={cfgParameters
                                .filter(e => e.category === 'filtering_label')
                                .map(f => ({
                                    name: f.name,
                                    value: f.id,
                                }))}
                            bind:value={config.cfg_param_label} />
                    </Label>
                {/if}
                <div>
                    <Label for="tableContentAlignment" class="my-2">Filtering Label Alignment</Label>
                    <div class="flex items-center">
                        {#each alignItems as item}
                            <Button
                                class="mr-2 h-10 w-20 capitalize dark:focus-within:ring-gray-900 {item.value !==
                                    config.filterLabelAlignment && 'hover:bg-blue-700 dark:bg-gray-700'}"
                                color={item.value === config.filterLabelAlignment ? 'blue' : 'none'}
                                value={item.value}
                                on:click={e => getFilterLabelAlignment(e)}
                                >{item.name}
                            </Button>
                        {/each}
                    </div>
                </div>
            {/if}
        </AccordionItem>
    </Accordion>
</div>
