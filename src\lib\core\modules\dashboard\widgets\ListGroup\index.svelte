<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { faTableList } from '@fortawesome/free-solid-svg-icons';

    export const widgetTitle = 'List Group';
    export const widgetIcon = faTableList;
    export const widgetMinWidth = 4;
    export const widgetMinHeight = 3;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 12;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { onDestroy, onMount } from 'svelte';
    import { type Subscription } from 'wonka';
    import { type TableListConfig } from './WidgetConfig/index.svelte';
    import type { ThresholdConfig } from '$lib/core/components/ThresholdConfig/index.svelte';
    import { Channel, browserBroadcaster } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { COMPARISON_OPERATORS_HASH } from '$lib/core/modules/dashboard/widgets/utils';

    export let config: TableListConfig;

    let subscription: Subscription;
    let containerElement: HTMLElement;
    let titleElement: HTMLElement;
    let element: HTMLElement;
    let mounted = false;
    let idWidgetElement: string = '';

    let mockData = [
        {
            name: 'patients',
            label: 'Patients',
            numerator: 10,
            denominator: -99,
        },
        {
            name: 'waiting',
            label: 'Waiting',
            numerator: 99,
            denominator: 100,
        },
        {
            name: 'beingServed',
            label: 'Being Served',
            numerator: 100,
            denominator: 100,
        },
        {
            name: 'psas',
            label: 'PSAs',
            numerator: 20,
            denominator: 50,
        },
        {
            name: 'nurses',
            label: 'Nurses',
            numerator: 50,
            denominator: 100,
        },
        {
            name: 'doctors',
            label: 'Doctors',
            numerator: 30,
            denominator: 99,
        },
    ];

    const rowClass = `flex flex-row flex-nowrap whitespace-nowrap justify-between items-center`;
    const dataClass = 'overflow-hidden whitespace-nowrap';

    function setColor(thresholdConfig: ThresholdConfig, value: number, rowElement: HTMLElement) {
        if (!rowElement) return;
        let thresholdConfigArr = thresholdConfig.thresholdConfigItems;
        let name = thresholdConfig.name;
        let isManual = config?.listGroup?.find(el => el.name == name)?.isManual;
        let configNumerator = config.listGroup?.find(el => el.name == name).numerator;
        let denominator = config.listGroup?.find(el => el.name == name).denominator;
        let dataSourceNumerator = mockData?.find(el => el.name == name).numerator;
        rowElement.style.color = '#fff';

        if (thresholdConfigArr) {
            if (
                ((isManual && (configNumerator < 0 || denominator < 0)) ||
                    (isManual == false && dataSourceNumerator < 0) ||
                    denominator < 0) &&
                !(config.isShowDenominator == false && configNumerator > 0)
            ) {
                rowElement.style.color = '#777c80';
                return;
            }
            if (thresholdConfig.isEnableThreshold) {
                if (!thresholdConfigArr.find(x => x.isValidItem.isValid == false)) {
                    for (let i = 0; i < thresholdConfigArr.length; i++) {
                        const comparatorValue = thresholdConfigArr[i].comparatorThreshold;
                        const thresholdValue = Number.parseInt(thresholdConfigArr[i].valueOfThreshold.toString());
                        if (COMPARISON_OPERATORS_HASH[comparatorValue](value, thresholdValue)) {
                            rowElement.style.color = thresholdConfigArr[i].textColor;
                            break;
                        }
                    }
                }
            }
        }
    }

    function setMaxTextSize(element: HTMLElement) {
        if (!element) {
            return;
        }
        let maxTextSize = 0;
        maxTextSize = element.clientHeight / mockData.length;
        if (config.textSize > maxTextSize) {
            element.style.fontSize = maxTextSize.toString() + 'px';
        } else {
            element.style.fontSize = config.textSize.toString() + 'px';
        }
    }
    function updateStyle() {
        if (config.thresholdConfigList) {
            for (let i = 0; i < config.thresholdConfigList.length; i++) {
                let valueNumerator = config.listGroup[i].isManual
                    ? config.listGroup[i].numerator
                    : mockData[i].numerator;
                setColor(
                    config.thresholdConfigList[i],
                    valueNumerator,
                    document.getElementById(config.thresholdConfigList[i].name + `-${idWidgetElement}`)
                );
            }
        }
    }

    function getFields(input, field) {
        var output = [];
        for (var i = 0; i < input.length; ++i) output.push(input[i][field]);
        return output;
    }

    function setConfigValue() {
        let keyArr = getFields(mockData, 'name');
        //Set value for data list
        if (config.listGroup == null)
            config.listGroup = mockData.map(item => ({
                name: item.name,
                label: item.label,
                numerator: item.numerator,
                denominator: item.denominator,
                isManual: false,
            }));
        else {
            config.listGroup = config.listGroup.filter(item => keyArr.includes(item.name));
        }

        //Set value for threshold list
        if (config.thresholdConfigList == null) {
            const amberThreshold = {
                titleThresholdItem: 'Amber Threshold',
                comparatorThreshold: '<',
                valueOfThreshold: 70,
                textColor: '#FFFFFF',
                bgColor: '#000000',
                isValidItem: {
                    isValid: true,
                    errorMessage: '',
                },
            };

            const redThreshold = {
                titleThresholdItem: 'Red Threshold',
                comparatorThreshold: '<',
                valueOfThreshold: 100,
                textColor: '#FFFFFF',
                bgColor: '#000000',
                isValidItem: {
                    isValid: true,
                    errorMessage: '',
                },
            };

            let configThresholdConfigs: ThresholdConfig;
            config.thresholdConfigList = [];
            for (let i = 0; i < mockData.length; i++) {
                configThresholdConfigs = {
                    name: mockData[i].name,
                    isEnableThreshold: false,
                    thresholdConfigItems: [amberThreshold, redThreshold],
                };
                config.thresholdConfigList.push(JSON.parse(JSON.stringify(configThresholdConfigs)));
            }
        } else {
            config.thresholdConfigList = config.thresholdConfigList.filter(item => keyArr.includes(item.name));
        }
    }

    $: if (mounted && (config || containerElement.clientHeight || containerElement.clientWidth || config.textSize)) {
        updateStyle();
        setMaxTextSize(containerElement);
    }

    onMount(async () => {
        idWidgetElement = element.closest('.grid-stack-item')?.getAttribute('data-widgetContainer');
        for (let i = 0; i < containerElement.children.length; i++) {
            const element = containerElement.children[i];
            element.id += '-' + idWidgetElement;
        }
        setConfigValue();
        updateStyle();
        setMaxTextSize(containerElement);
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            updateStyle();
            setMaxTextSize(containerElement);
        });
        mounted = true;
    });

    onDestroy(async () => {
        subscription?.unsubscribe();
    });
</script>

<div bind:this={element} class="flex h-full w-full flex-col overflow-hidden p-4 text-on-background">
    <header
        bind:this={titleElement}
        class="flex h-12 min-h-6 items-start justify-between gap-3 text-sm {config?.title.length > 0 ? '' : 'hidden'}">
        <p class="h-full bg-transparent text-base">{config?.title}</p>
    </header>
    <div bind:this={containerElement} class="h-full divide-y overflow-hidden font-medium">
        {#each mockData as dataItem, i (dataItem.name)}
            {@const itemListGroup = config?.listGroup?.find(el => el.name == dataItem.name)}
            <div
                id={dataItem.name}
                style="height: {(1 / mockData.length) * 100}%;"
                class="{rowClass} 
                {(itemListGroup?.isManual && (itemListGroup?.numerator < 0 || itemListGroup?.denominator < 0)) ||
                (!itemListGroup?.isManual && (dataItem.numerator < 0 || itemListGroup?.denominator < 0))
                    ? config.isShowDenominator == false && (itemListGroup?.numerator > 0 || dataItem.numerator > 0)
                        ? ''
                        : 'text-[#777c80]'
                    : ''}">
                <span>{dataItem.label}</span>
                {#if itemListGroup?.isManual}
                    {#if itemListGroup?.numerator > 0 && !config.isShowDenominator}
                        <span class={dataClass}>{itemListGroup?.numerator}</span>
                    {:else if itemListGroup?.numerator < 0 || itemListGroup?.denominator < 0}
                        <span class={dataClass}>-{config.isShowDenominator ? '/-' : ''}</span>
                    {:else}
                        <span class={dataClass}
                            >{itemListGroup?.numerator}{config.isShowDenominator
                                ? '/' + itemListGroup?.denominator
                                : ''}</span>
                    {/if}
                {:else if dataItem.numerator < 0 || itemListGroup?.denominator < 0}
                    {#if dataItem.numerator > 0 && !config.isShowDenominator}
                        <span class={dataClass}>{dataItem.numerator}</span>
                    {:else}
                        <span class={dataClass}>-{config.isShowDenominator ? '/-' : ''}</span>
                    {/if}
                {:else}
                    <span class={dataClass}
                        >{dataItem.numerator}{config.isShowDenominator ? '/' + itemListGroup?.denominator : ''}</span>
                {/if}
            </div>
        {/each}
    </div>
</div>
