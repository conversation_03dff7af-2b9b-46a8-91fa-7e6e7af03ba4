import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
import { checkPermissions } from '$lib/shared/util/acl';
import type { PageServerLoad } from './$types';
export const load: PageServerLoad = async ({ locals, params, fetch }) => {
    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const access_token = locals.user.access_token;
    let permissionNeedToCheck = [
        {
            scopeId: SCOPE.READ,
            resourceId: RESOURCE.INVENTORY_CONSUMABLE_STOCK_THRESHOLD,
        },
        {
            scopeId: SCOPE.UPDATE,
            resourceId: RESOURCE.INVENTORY_CONSUMABLE_STOCK_THRESHOLD,
        },
        {
            scopeId: SCOPE.TOGGLE,
            resourceId: RESOURCE.INVENTORY_CONSUMABLE_STOCK_THRESHOLD,
        },
    ];
    const abilities = await checkPermissions({
        token: access_token,
        tenantId,
        userId,
        permissions: permissionNeedToCheck,
    });
    return {
        abilities: abilities,
    };
};
