<!-- @component
    This component displays the date, day, and time.

    The use offset flag can be configured to use either client machine time, or calculate the difference from server time,
    and update based on that.

    Props:
        - useOffset: boolean = true - if true, use the difference between server time and client time to calculate the time.
        - syncInterval: number =  1 hour - the interval in milliseconds to check the server time for offset calculation.Admin
        - period: number = 1 second - how often to update the display time (default 1 second)
        - allowedLocalDriftMilliseconds: number = period + 2 seconds - drift between update period (if difference is too large, client time might have changed), trigger server time fetch to update offset
        - allowedServerDriftMilliseconds: number = 5 seconds - threshold difference to display drift feedback (see `feedbackDrift`) 
        - feedbackDrift: boolean = true - if true, give visual feedback if the client time is beyond `allowedServerDriftMilliseconds` difference from the server time.

    Usage: 
        ```tsx
        <DateTimeDisplay useOffset={true} updateInterval={dayjs.duration({ hours: 1 }).asMilliseconds()} />
        ```
    
    Note this component is currently very simple, the time system must be expanded to allow multiple timezones
    - It should be able to scale with the number of users
    - It should be configurable to use server time and not just client time (should it?)
 -->
<script lang="ts" context="module">
    import dayjs from 'dayjs';
    import duration from 'dayjs/plugin/duration.js';
    import { onDestroy } from 'svelte';

    import { browser } from '$app/environment';
    import { logger } from '$lib/stores/Logger';

    dayjs.extend(duration);

    const timeEndpoint = '/api/info/time';

    let initialServerTime: dayjs.Dayjs;
    let offset: number;

    async function getServerOffsetTime() {
        try {
            initialServerTime = dayjs(await (await fetch(timeEndpoint)).json());
            offset = dayjs().diff(initialServerTime);
        } catch {
            logger.error('Error retrieving server time.');
        }
    }

    if (browser) {
        getServerOffsetTime();
    }
</script>

<script lang="ts">
    export let syncInterval: number = dayjs.duration({ hours: 1 }).asMilliseconds();
    export let period: number = dayjs.duration({ seconds: 1 }).asMilliseconds();
    export let useOffset = false;
    export let allowedLocalDriftMilliseconds: number = period + dayjs.duration({ seconds: 2 }).asMilliseconds();
    export let allowedServerDriftMilliseconds: number = dayjs.duration({ seconds: 5 }).asMilliseconds();
    export let feedbackDrift = true;

    let syncThresholdCrossed = false;

    let date: string;
    let day: string;
    let time: string;
    let prevTime = dayjs();

    function updateTime() {
        let now = dayjs();

        if (useOffset) {
            if (offset) {
                syncThresholdCrossed = Math.abs(offset) > allowedServerDriftMilliseconds;

                let diff = Math.abs(now.diff(prevTime));

                if (diff > allowedLocalDriftMilliseconds) {
                    getServerOffsetTime();
                }

                now = dayjs().subtract(offset, 'ms');
            }
        }

        if (now) {
            date = now.format('DD/MM/YYYY');
            day = now.format('ddd').toLocaleUpperCase();
            time = now.format('HH:mm:ss');
        } else {
            date = ' - - / - - / - - - -';
            day = '- - -';
            time = ' - - : - - : - - ';
        }

        prevTime = dayjs();
    }

    updateTime();

    const updateTimeInterval = setInterval(updateTime, period);
    let getServerOffsetInterval: NodeJS.Timer;

    if (useOffset) {
        getServerOffsetInterval = setInterval(getServerOffsetTime, syncInterval);
    }

    onDestroy(() => {
        clearInterval(updateTimeInterval);

        if (getServerOffsetInterval) clearInterval(getServerOffsetInterval);
    });
</script>

<div class="flex flex-row space-x-6 whitespace-nowrap text-xl text-on-background ">
    <div>{date}</div>
    <div>{day}</div>
    <div class:text-red-500={feedbackDrift && syncThresholdCrossed} class="font-bold">{time}</div>
</div>
