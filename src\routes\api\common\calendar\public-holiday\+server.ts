import { locations } from '$lib/stores/Locations';
import { StatusCodes } from 'http-status-codes';
import { get } from 'svelte/store';
import type { RequestHandler } from './$types';

const CommonURL = `${get(locations).common}`;

export const POST: RequestHandler = async ({ locals, request }) => {
    let message: string;
    let status: StatusCodes;

    const requestData: any = await request.formData();

    const res = await fetch(`${CommonURL}/calendar/public-holiday`, {
        method: 'POST',
        body: requestData,
        headers: {
            Authorization: 'Bearer ' + locals.user.access_token,
        },
    });

    status = res.status;
    message = await res.text();

    return new Response(
        JSON.stringify({
            message,
        }),
        {
            status,
        }
    );
};
