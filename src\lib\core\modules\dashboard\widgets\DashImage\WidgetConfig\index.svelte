<script lang="ts" context="module">
    export interface DashImageConfig {
        id: string;
    }
    export const configDefaults: DashImageConfig = {
        id: '',
    };
</script>

<script lang="ts">
    export let config: DashImageConfig;
    import WidgetConfig from '$lib/core/modules/dashboard/components/WidgetConfig/index.svelte';
    import WidgetConfigSelect from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigSelect/index.svelte';
    import { onMount } from 'svelte';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { get } from 'svelte/store';
    import gql from 'graphql-tag';

    const configElementWidth = 'w-1/5';

    let propertyFields;

    const updateFromResult = result => {
        propertyFields= [
            {
                id: 'id',
                componentDisplay: WidgetConfigSelect,
                extraProps: {
                    title: 'Group',
                    items: result.data.cc3_hoc_comm_dash_image_group.map(i => ({
                        name: i.name,
                        value: i.id,
                    }))
                },
            },
        ];
    };

    onMount(() => {
        let FetchImageGroup = gql`
            query FetchImageGroup {
                cc3_hoc_comm_dash_image_group(where: {is_deleted: {_neq: true}}) {
                    id
                    name
                    description
                }
            }
        `;
        const client = get(gqlClientStore);
        client.query(FetchImageGroup, {}).toPromise().then(updateFromResult);
    })
</script>

{#if propertyFields}
    <WidgetConfig bind:config {propertyFields} {configElementWidth} />
{/if}