<script lang="ts">
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import Tooltip from '$lib/core/components/Tooltip/index.svelte';
    import { locations } from '$lib/stores/Locations';
    import { titleCase } from 'change-case-all';
    import { get } from 'svelte/store';

    export let role: Array<string>;
    export let username: string;

    $: formattedRoles =
        role?.filter(name => name !== 'tenant-user')?.map(name => (name === 'tenant-admin' ? 'Tenant Admin' : name)) ||
        [];
</script>

<div class="flex items-center justify-center whitespace-nowrap px-2 text-lg">
    <!-- Placeholder until we have design/system for user profile -->
    <div class="flex h-8 w-8 items-center justify-center rounded-full bg-surface-1 pb-1">
        {username[0].toUpperCase()}
    </div>
    <Tooltip offset={0} padded={false}>
        <div class="flex h-full w-full flex-col text-left">
            <div class="small-scroll mb-[4px] max-h-[calc(100vh-150px)] gap-1 overflow-y-auto p-4 text-xs">
                {#if username}
                    <div class="mb-[2px] flex">
                        <div class=" min-w-[46px] pr-2">Name:</div>
                        <div class="font-light">{titleCase(username)}</div>
                    </div>
                {/if}
                {#if formattedRoles?.length}
                    <div class="flex">
                        <div class="min-w-[46px] pr-2">Role:</div>
                        <div class="max-w-[calc(100%-46px)] pr-2 font-light">
                            <ul class="max-w-full {formattedRoles.length > 1 ? 'list-disc-style' : ''}">
                                {#each formattedRoles as name}
                                    <li class="relative mb-[2px] max-w-full">
                                        <div class="max-w-full truncate" title={name}>
                                            {name}
                                        </div>
                                    </li>
                                {/each}
                            </ul>
                        </div>
                    </div>
                {/if}
            </div>
            <Button
                variant={ButtonVariant.EMPTY}
                on:click={() => {
                    window.location.href = get(locations).ROUTE_API_LOGOUT;
                }}
                appendClass="w-full h-full bg-surface-2 text-on-surface-4 p-4 rounded-b-md">
                Logout
            </Button>
        </div>
    </Tooltip>
</div>

<style>
    .list-disc-style li::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 4px;
        margin-right: 0.5em;
        background-color: currentColor;
        border-radius: 50%;
        position: absolute;
        left: -10px;
        top: 7px;
    }
</style>
