<script lang="ts">
    import { Toggle } from 'flowbite-svelte';
    export let title: string;
    export let checked;
    export let required = false;
    export let customClass = 'mb-4';
    export let disabled = false;
    export let onChange: () => void;
</script>

<div class={customClass}>
    <Toggle
        on:change={() => {
            if (onChange) {
                onChange();
            }
        }}
        bind:checked
        class={`${disabled ? '!text-gray-600' : ''}`}
        {required}
        {disabled}
        color="blue">{title}</Toggle>
</div>
