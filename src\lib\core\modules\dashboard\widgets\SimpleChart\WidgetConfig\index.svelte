<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    type Nullable<T> = T | null;
    type LabelPosition = 'top' | 'inside' | 'hover';
    type DisplayType = 'line' | 'bar' | 'area' | 'stackedBar';

    export interface SeriesOption {
        name: NonNullable<string>;
        displayName: string;
        displayType: DisplayType;
        stack: string;
        prefix: string;
        suffix: string;
        label: {
            position: LabelPosition;
            color: string;
        };
        itemStyle: {
            color: string;
        };
        isRightYaxis: boolean;
    }

    export interface XAxisOption {
        name: string;
        show: boolean;
        axisLine: {
            symbol: Array<string> | null;
        };
    }

    export interface YAxisOption {
        name: string;
        show: boolean;
        axisLine: {
            show: boolean;
            symbol: Array<string> | null;
        };
        splitLine: {
            show: boolean;
        };
        min: number | undefined;
        max: number | undefined;
    }

    export interface ToolboxOption {
        show: boolean;
    }

    export interface SimpleChart {
        title?: string;
        widgetName?: string;
        xAxis: XAxisOption;
        yAxis: YAxisOption[];
        dataSource?: string;
        showHorizontalLines?: boolean;
        toolbox?: ToolboxOption;
        series: SeriesOption[];
        showGear: boolean;
        cfg_param: string;
        positionLegend: 'none' | 'top' | 'right';
        showFilteringLabel: boolean;
        filterLabelAlignment: string;
        col_Series: string;
        col_default_series_type_left: DisplayType;
        col_default_series_type_right: DisplayType;
        col_default_right_Yaxis: string;
        col_default_stack_group: string;
        list_stacks?: string;
        list_prefix_series?: string;
        col_default_prefix_series?: string;
        list_suffix_series?: string;
        col_default_suffix_series?: string;
        col_default_empty_series_type?: string;
        col_DateTime: string;
        col_Value: string;
        showArrowAxis?: boolean;
        label_use_filter_datasource?: boolean;
        cfg_param_label?: string;
        useParentFilter?: boolean;
        showBoundaryGap?: boolean;
        showLegendScroll?: boolean;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'Simple Chart';

    export const configDefaults: SimpleChart = {
        dataSource: '',
        title: DEFAULT_TITLE,
        widgetName: '',
        xAxis: {
            name: '',
            show: true,
            axisLine: {
                symbol: null,
            },
        },
        yAxis: [
            {
                name: '',
                show: true,
                axisLine: {
                    show: true,
                    symbol: null,
                },
                splitLine: {
                    show: true,
                },
                min: undefined,
                max: undefined,
            },
            {
                name: '',
                show: false,
                axisLine: {
                    show: true,
                    symbol: null,
                },
                splitLine: {
                    show: true,
                },
                min: undefined,
                max: undefined,
            },
        ],
        showHorizontalLines: false,
        toolbox: {
            show: false,
        },
        series: [],
        showGear: false,
        cfg_param: '',
        positionLegend: 'top',
        showArrowAxis: false,
        showFilteringLabel: false,
        filterLabelAlignment: 'right',
        col_Series: '',
        col_default_series_type_left: 'bar',
        col_default_series_type_right: 'bar',
        col_default_right_Yaxis: '',
        col_default_stack_group: '',
        list_stacks: '',
        col_DateTime: '',
        col_Value: '',
        showBoundaryGap: true,
        showLegendScroll: false,
    };
    export const MMM_DD_hh_mm: string = 'MMM-DD hh:mm';
    export const MMM_DD: string = 'MMM-DD';
    export const DD: string = 'DD';
</script>

<script lang="ts">
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { Input, Select } from 'flowbite-svelte';
    import { AccordionItem, Accordion } from 'flowbite-svelte';
    import WidgetConfigItem from '$lib/core/modules/dashboard/components/WidgetConfig/WidgetItem.svelte';
    import WidgetConfigItemNested from '$lib/core/modules/dashboard/components/WidgetConfig/WidgetItemNested.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';
    import WidgetConfigSelect from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigSelect/index.svelte';
    import { onDestroy, onMount } from 'svelte';

    export let config: SimpleChart;

    // List of data source names (valid GraphQL query roots)
    let configElement: HTMLElement;
    export let datasources: Array<DropdownItem> = [];
    const supportedCharts = [
        {
            name: '',
            value: '',
        },
        {
            name: 'Line Chart',
            value: 'line',
        },
        {
            name: 'Bar Chart',
            value: 'bar',
        },
        {
            name: 'Area Chart',
            value: 'area',
        },
        {
            name: 'Stacked Bar',
            value: 'stackedBar',
        },
    ];

    const positionsLegend = ['None', 'Top', 'Right'];

    function createSelectList(configValue: string | undefined): Array<{ name: string; value: string }> {
        const baseList = [{ name: '', value: '' }];
        if (!configValue) return baseList;
        return [
            ...baseList,
            ...configValue.split(',').map(item => ({
                name: item.trim(),
                value: item.trim(),
            })),
        ];
    }

    function validateSeriesProperty(
        series: SeriesOption[],
        validList: Array<{ name: string; value: string }>,
        property: keyof Pick<SeriesOption, 'stack' | 'prefix' | 'suffix'>
    ) {
        series?.forEach(seriesItem => {
            if (seriesItem[property] && !validList.some(item => item.value === seriesItem[property])) {
                seriesItem[property] = '';
            }
        });
    }

    let stackGroup: Array<{ name: string; value: string }> = [];
    $: {
        stackGroup = createSelectList(config.list_stacks);
    }

    let prefixList: Array<{ name: string; value: string }> = [];
    $: {
        prefixList = createSelectList(config.list_prefix_series);
        validateSeriesProperty(config?.series, prefixList, 'prefix');
    }

    let suffixList: Array<{ name: string; value: string }> = [];
    $: {
        suffixList = createSelectList(config.list_suffix_series);
        validateSeriesProperty(config?.series, suffixList, 'suffix');
    }

    const labelPositions = [
        {
            name: 'Top',
            value: 'top',
        },
        {
            name: 'Inside',
            value: 'inside',
        },
        {
            name: 'Hover',
            value: 'hover',
        },
        {
            name: 'Inside Bottom',
            value: 'insideBottom',
        },
    ];
    let propertyFieldsFirst = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: datasources,
                setClass: 'border-b border-b-outline bg-transparent mb-4',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
            },
        },
        {
            id: 'widgetName',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Name',
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'xAxis.name',
                    path: ['xAxis', 'name'],
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'X-axis Title',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'list_stacks',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'List Stack',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'col_default_stack_group',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Default Stack Group',
                        checked: config.col_default_stack_group,
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'xAxis.show',
                    path: ['xAxis', 'show'],
                    componentDisplay: WidgetToggle,
                    extraProps: {
                        title: 'Show X-axis',
                        checked: config.xAxis?.show,
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'yAxis[0].name',
                    path: ['yAxis', 0, 'name'],
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Left Y-axis Title',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'col_default_series_type_left',
                    componentDisplay: WidgetConfigSelect,
                    extraProps: {
                        title: 'Default Series Type (Left Y-axis)',
                        items: supportedCharts,
                        selected: config.col_default_series_type_left,
                        customClass: 'w-[220px] flex flex-col mb-4',
                    },
                },
                {
                    id: 'yAxis[0].show',
                    path: ['yAxis', 0, 'show'],
                    componentDisplay: WidgetToggle,
                    extraProps: {
                        title: 'Show Left Y-axis',
                        checked: config.yAxis?.[0]?.show,
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'yAxis[1].name',
                    path: ['yAxis', 1, 'name'],
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Right Y-axis Title',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'col_default_series_type_right',
                    componentDisplay: WidgetConfigSelect,
                    extraProps: {
                        title: 'Default Series Type (Right Y-axis)',
                        items: supportedCharts,
                        selected: config.col_default_series_type_right,
                        customClass: 'w-[220px] flex flex-col mb-4',
                    },
                },
                {
                    id: 'col_default_right_Yaxis',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Default Right Y-axis Series',
                        checked: config.col_default_right_Yaxis,
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'yAxis[1].show',
                    path: ['yAxis', 1, 'show'],
                    componentDisplay: WidgetToggle,
                    extraProps: {
                        title: 'Show Right Y-axis',
                        checked: config.yAxis?.[1]?.show,
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'col_Series',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Chart Series',
                        checked: config.col_Series,
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'col_DateTime',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'X-axis (DateTime)',
                        checked: config.col_DateTime,
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'col_Value',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Y-axis (Value)',
                        checked: config.col_Value,
                        customClass: 'w-[220px] mb-4',
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'yAxis[0].min',
                    path: ['yAxis', 0, 'min'],
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Min Left Y-axis',
                        checked: config.yAxis?.[0]?.min,
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'yAxis[0].max',
                    path: ['yAxis', 0, 'max'],
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Max Left Y-axis',
                        checked: config.yAxis?.[0]?.max,
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'yAxis[1].min',
                    path: ['yAxis', 1, 'min'],
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Min Right Y-axis',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'yAxis[1].max',
                    path: ['yAxis', 1, 'max'],
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Max Right Y-axis',
                        customClass: 'w-[220px] mb-4',
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'list_prefix_series',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Prefix',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'col_default_prefix_series',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Default Series For Prefix',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'list_suffix_series',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Suffix',
                        customClass: 'w-[220px] mb-4',
                    },
                },
                {
                    id: 'col_default_suffix_series',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Default Series For Suffix',
                        customClass: 'w-[220px] mb-4',
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'col_default_empty_series_type',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Default Empty Series Type',
                        customClass: 'w-[220px] mb-4',
                    },
                },
            ],
        },
        {
            id: 'toolbox.show',
            path: ['toolbox', 'show'],
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Toolbox',
                checked: config.toolbox?.show,
            },
        },
        {
            id: 'showHorizontalLines',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Horizontal Lines',
                checked: config.showHorizontalLines,
            },
        },
        {
            id: 'showArrowAxis',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Arrowhead',
                checked: config.showArrowAxis,
            },
        },
        {
            id: 'showBoundaryGap',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Boundary Gap',
                checked: config?.showBoundaryGap ?? true,
            },
        },
        {
            id: 'showLegendScroll',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Legend Scroll',
                checked: config?.showLegendScroll ?? false,
            },
        },
        {
            id: 'positionLegend',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Legend Position',
                labels: positionsLegend,
            },
        },
    ];
    const ALIGNMENT: string[] = ['Left', 'Center', 'Right'];
    let filteringFields = [];

    const getFilteringCFG = async () => {
        try {
            const resCfg = await fetch(`/api/CfgParameterService?category=filtering_config&category=filtering_label`);
            const { res } = await resCfg.json();
            const listCfgParametes = res.data;
            if (listCfgParametes) {
                filteringFields = [
                    {
                        id: 'showGear',
                        componentDisplay: WidgetToggle,
                        extraProps: {
                            title: 'Show Filtering',
                            checked: config.showGear,
                        },
                        onChange: () => {
                            if (config.showGear === false) {
                                config.label_use_filter_datasource = false;
                            }
                        },
                    },
                    {
                        id: 'cfg_param',
                        componentDisplay: Dropdown,
                        extraProps: {
                            title: 'Filter Data Source',
                            options: listCfgParametes
                                .filter(e => e.category === 'filtering_config')
                                .map(item => ({
                                    label: item.title || item.name,
                                    value: item.id,
                                })),
                            setClass: 'border-b border-b-outline bg-transparent mb-4',
                            enableSearch: true,
                            appendMenuClass: 'w-full',
                        },
                    },
                    {
                        id: 'showFilteringLabel',
                        componentDisplay: WidgetToggle,
                        extraProps: {
                            title: 'Show Filtering Label',
                            checked: config.showFilteringLabel,
                        },
                        onChange: () => {
                            if (config.showFilteringLabel === false) {
                                config.label_use_filter_datasource = false;
                            }
                        },
                    },
                    {
                        id: 'label_use_filter_datasource',
                        componentDisplay: WidgetToggle,
                        extraProps: {
                            title: 'Use the same as Filter Data Source',
                            checked: config.label_use_filter_datasource,
                        },
                        checkVisible: 'showFilteringLabel',
                        disabled: configData => !configData.showGear,
                    },
                    {
                        id: 'cfg_param_label',
                        componentDisplay: Dropdown,
                        extraProps: {
                            title: 'Label Data Source',
                            options: listCfgParametes
                                .filter(e => e.category === 'filtering_label')
                                .map(item => ({
                                    label: item.title || item.name,
                                    value: item.id,
                                })),
                            setClass: 'border-b border-b-outline bg-transparent mb-4',
                            enableSearch: true,
                            appendMenuClass: 'w-full',
                        },
                        checkVisible: configData =>
                            configData.showFilteringLabel && !configData.label_use_filter_datasource,
                    },
                    {
                        id: 'filterLabelAlignment',
                        componentDisplay: WidgetGroupButton,
                        extraProps: {
                            title: 'Filtering Label Alignment',
                            labels: ALIGNMENT,
                        },
                        checkVisible: 'showFilteringLabel',
                    },
                ];
            }
        } catch (error) {
            console.log(error);
        }
    };

    onMount(() => {
        getFilteringCFG();
        if (configElement) {
            let configurationPanel =
                configElement.parentElement.parentElement.parentElement.parentElement.parentElement;
            configurationPanel.classList.remove('w-1/5');
            configurationPanel.classList.add(
                'overflow-x-auto',
                'h-[calc(100%-4rem)]',
                'overflow-y-hidden',
                '!w-[1250px]'
            );
        }
    });

    onDestroy(() => {
        if (configElement) {
            let configurationPanel =
                configElement.parentElement.parentElement.parentElement.parentElement.parentElement;
            configurationPanel.classList.remove(
                'overflow-x-auto',
                'h-[calc(100%-4rem)]',
                'overflow-y-hidden',
                '!w-[1250px]'
            );
            configurationPanel.classList.add('w-1/5');
        }
    });
</script>

<div bind:this={configElement} class="text-on-surface flex flex-col gap-3 overflow-x-hidden">
    <Accordion>
        <AccordionItem>
            <span slot="header">Properties</span>
            <WidgetConfigItemNested bind:config bind:widgetFields={propertyFieldsFirst} />
        </AccordionItem>
        <AccordionItem>
            <span slot="header">Filtering</span>
            <WidgetConfigItem bind:config widgetFields={filteringFields} />
        </AccordionItem>
        <AccordionItem>
            <span slot="header">Series Chart Config</span>
            {#if Array.isArray(config?.series) && config.series.length > 0}
                <div
                    class="mb-2 grid gap-x-2 text-sm text-white"
                    style="grid-template-columns: 13% 11% 11% 11% 9% 9% 9% 7% 7% 8%;">
                    <div>Chart Series Data</div>
                    <div>Custom Name</div>
                    <div>Type</div>
                    <div>Stack Name</div>
                    <div>Prefix</div>
                    <div>Suffix</div>
                    <div>Position</div>
                    <div>Fill Color</div>
                    <div>Text Color</div>
                    <div>Right Y-axis</div>
                </div>
                {#each config?.series as itemSeries, i (itemSeries.name)}
                    <div
                        class="mb-2 grid items-center gap-x-2"
                        style="grid-template-columns: 13% 11% 11% 11% 9% 9% 9% 7% 7% 8%;">
                        <div>
                            <Input bind:value={itemSeries.name} disabled={true} type="text" />
                        </div>
                        <div>
                            <Input bind:value={itemSeries.displayName} type="text" />
                        </div>
                        <div>
                            <Select bind:value={itemSeries.displayType} items={supportedCharts} />
                        </div>
                        <div>
                            <Select bind:value={itemSeries.stack} items={stackGroup} />
                        </div>
                        <div>
                            <Select bind:value={itemSeries.prefix} items={prefixList} />
                        </div>
                        <div>
                            <Select bind:value={itemSeries.suffix} items={suffixList} />
                        </div>
                        <div>
                            <Select bind:value={itemSeries.label.position} items={labelPositions} />
                        </div>
                        <div>
                            <Input
                                bind:value={itemSeries.itemStyle.color}
                                type="color"
                                class="h-10 w-10 min-w-[2.5rem] p-0" />
                        </div>
                        <div>
                            <Input
                                bind:value={itemSeries.label.color}
                                type="color"
                                class="h-10 w-10 min-w-[2.5rem] p-0" />
                        </div>
                        <div>
                            <WidgetToggle bind:checked={itemSeries.isRightYaxis} title="" customClass="" />
                        </div>
                    </div>
                {/each}
            {/if}
        </AccordionItem>
    </Accordion>
</div>
