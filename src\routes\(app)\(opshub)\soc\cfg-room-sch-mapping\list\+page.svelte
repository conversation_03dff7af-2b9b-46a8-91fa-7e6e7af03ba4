<script lang="ts">
    import { get } from 'svelte/store';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import { ColumnDataType, type ColumnNameCustom } from '$lib/shared/constants/general';
    import { v4 as uuidv4 } from 'uuid';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import StandardTable from '../../components/StandardTable.svelte';
    import { isLoading } from '$lib/stores/Loading';
    import { TriggerService } from '$lib/service/trigger';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';
    export let data;

    const SocURL = `${get(locations).soc_web}`;

    const FieldNames = {
        ScheduleName: 'schedule_name',
        SessionType: 'session_type',
        RoomName: 'room_name',
        Year: 'year',
        Month: 'month',
        ClinicCode: 'clinic_code',
    };

    let popupModal = false;
    let modalTitle = '';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;
    let openUpdateResult = false;
    let extraData: any = {};
    let isDependentField = true;
    let standardTable;

    extraData = data?.extraData || {};

    let tableData = Array.isArray(data.roomSchMapping)
        ? data.roomSchMapping.map(item => {
              const sessionType = data?.extraData?.sessionTypeData?.find(st => st.category === item.session_type);
              const sessionTypeNameDisplay = sessionType ? sessionType.category : item.session_type;

              const scheduleName = data?.extraData?.roomSchedulingData?.find(
                  st => st.schedule_id === item.schedule_name
              );
              const scheduleNameDisplay = scheduleName ? scheduleName.schedule_name : item.schedule_name;

              const roomNameDisplay = scheduleName ? scheduleName.room_name : item.room_name;

              return {
                  id: item.id,
                  clinic_code: item.clinic_code,
                  year: item.year,
                  month: item.month,
                  day: item.day,
                  session_type: item.session_type,
                  session_type_display: sessionTypeNameDisplay,
                  room_name: item.room_name,
                  room_name_display: roomNameDisplay,
                  schedule_name: item.schedule_name,
                  schedule_name_display: scheduleNameDisplay,
                  no_of_rooms: item.no_of_rooms,
              };
          })
        : [];

    const subData = {
        year: [2024, 2025, 2026, 2027, 2028, 2029, 2030].map(year => ({
            value: year,
            name: year.toString(),
        })),

        month: Array.from({ length: 12 }, (_, i) => i + 1).map(month => ({
            value: month,
            name: month.toString().padStart(2, '0'),
        })),

        day: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day, index) => ({
            value: day,
            name: day,
        })),
        session_type: data?.extraData.sessionTypeData.map(item => ({ value: item.category, name: item.category })),
        schedule_name: data?.extraData.roomSchedulingData.map(item => ({
            value: item.schedule_id,
            name: item.schedule_name,
        })),
        room_name: data?.extraData.roomSchedulingData.map(item => ({ key: item.schedule_id, value: item.room_id })),
        clinic_code: data?.extraData.clinicRoomFileData.map(item => ({
            value: item.clinic_code,
            name: item.clinic_code,
        })),
    };

    function getClinicCodeByConditions(data, { room_name, month, year }) {
        const monthMap = {
            '1': 'jan',
            '2': 'feb',
            '3': 'mar',
            '4': 'apr',
            '5': 'may',
            '6': 'jun',
            '7': 'jul',
            '8': 'aug',
            '9': 'sep',
            '10': 'oct',
            '11': 'nov',
            '12': 'dec',
        };

        const monthKey = monthMap[month];

        if (!monthKey) return null;

        const found = data.find(
            item => item.room_code === room_name && item.year === Number(year) && item[monthKey] === 'Y'
        );

        return found ? found.clinic_code : null;
    }

    let newRecords = new Set();
    let searchText = '';
    let selectedRow = null;
    let updateList = [];

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    let updatePosition: UpdatePosition = { id: '', columnName: '', isEditing: false };
    let triggerTableNames = 'cc3_hoc.cfg_room_sch_mapping';

    const showSuccessMessage = msg => {
        popupModal = true;
        modalTitle = 'Notification';
        modalMessage = msg;
        onlyOkBtn = true;
        if (openUpdateResult) {
            confirmAction = () => {
                handleRefresh();
                standardTable.resetAllTableValue();
            };
        }
    };

    const handleCreateData = async (userClaims, newItems) => {
        await fetch(`${SocURL}/cfg-room-sch-mapping`, {
            method: 'POST',
            body: JSON.stringify({
                data: newItems.map(item => ({
                    clinic_code: item.clinic_code,
                    year: +item.year,
                    month: +item.month,
                    day: item.day,
                    session_type: item.session_type,
                    room_name: item.room_name,
                    schedule_name: item.schedule_name,
                    no_of_rooms: item.no_of_rooms,
                })),
            }),
        });
    };

    const handleUpdateData = async (userClaims, updateItems) => {
        await fetch(`${SocURL}/cfg-room-sch-mapping`, {
            method: 'PATCH',
            body: JSON.stringify({
                data: updateItems.map(item => ({
                    id: item.id,
                    clinic_code: item.clinic_code,
                    year: +item.year,
                    month: +item.month,
                    day: item.day,
                    session_type: item.session_type,
                    room_name: item.room_name,
                    schedule_name: item.schedule_name,
                    no_of_rooms: item.no_of_rooms,
                })),
            }),
        });
    };

    const handleSaveData = async () => {
        const userClaims = get(user).claims;
        let newItems = [],
            updateItems = [];
        updateList.forEach(row => (newRecords.has(row.id) ? newItems.push(row) : updateItems.push(row)));

        if (newItems.length) await handleCreateData(userClaims, newItems);
        if (updateItems.length) await handleUpdateData(userClaims, updateItems);
        openUpdateResult = true;
        showSuccessMessage('Configuration is saved successfully.');
        TriggerService.trigger(triggerTableNames);
    };

    const handleDeleteData = async typeId => {
        await fetch(`${SocURL}/cfg-room-sch-mapping`, {
            method: 'DELETE',
            body: JSON.stringify({ data: [{ id: typeId }] }),
        });
    };

    const onDelete = async () => {
        if (selectedRow && !newRecords.has(selectedRow)) {
            await handleDeleteData(selectedRow);
        }
        tableData = tableData.filter(item => item.id !== selectedRow);
        updateList = updateList.filter(item => item.id !== selectedRow);
        selectedRow = null;
        showSuccessMessage('Successfully deleted configuration!');
    };

    let newData;
    const handleAppendNewRow = () => {
        const newId = uuidv4();
        newData = {
            id: newId,
            clinic_code: '',
            year: null,
            month: null,
            day: null,
            session_type: null,
            session_type_display: null,
            room_name: null,
            room_name_display: null,
            schedule_name: null,
            schedule_name_display: null,
            no_of_rooms: null,
            isNew: true,
        };
        tableData = [newData, ...tableData];
        newRecords.add(newId);
    };

    const handleRefresh = async () => {
        isLoading.set(true);
        try {
            const res = await fetch(`${SocURL}/cfg-room-sch-mapping?page=1&limit=9999`, {
                method: 'GET',
            });
            const response = await res.json();
            tableData = Array.isArray(response.data)
                ? response.data.map(item => {
                      const sessionType = data?.extraData?.sessionTypeData?.find(
                          st => st.category === item.session_type
                      );
                      const sessionTypeNameDisplay = sessionType ? sessionType.category : item.session_type;

                      const scheduleName = data?.extraData?.roomSchedulingData?.find(
                          st => st.schedule_id === item.schedule_name
                      );
                      const scheduleNameDisplay = scheduleName ? scheduleName.schedule_name : item.schedule_name;

                      const roomNameDisplay = scheduleName ? scheduleName.room_name : item.room_name;

                      return {
                          id: item.id,
                          clinic_code: item.clinic_code,
                          year: item.year,
                          month: item.month,
                          day: item.day,
                          session_type: item.session_type,
                          session_type_display: sessionTypeNameDisplay,
                          room_name: item.room_name,
                          room_name_display: roomNameDisplay,
                          schedule_name: item.schedule_name,
                          schedule_name_display: scheduleNameDisplay,
                          no_of_rooms: item.no_of_rooms,
                      };
                  })
                : [];
            updateList = [];
            selectedRow = null;
            newRecords = new Set();
        } catch (error) {
            console.error('Error during refresh:', error);
        } finally {
            isLoading.set(false);
        }
    };

    const columnNames: ColumnNameCustom[] = [
        { label: 'YYYY', name: 'year', editable: true, type: ColumnDataType.SingleNumberSelect, isRequired: true },
        { label: 'MM', name: 'month', editable: true, type: ColumnDataType.SingleNumberSelect, isRequired: true },
        { label: 'Day', name: 'day', editable: true, type: ColumnDataType.SingleStringSelect, isRequired: true },
        {
            label: 'Session Type',
            name: 'session_type',
            editable: true,
            type: ColumnDataType.SingleStringSelect,
            isUseDataDisplay: true,
            isRequired: true,
        },
        {
            label: 'Clinic Code',
            name: 'clinic_code',
            type: ColumnDataType.String,
            dependedByColumns: ['room_name', 'year', 'month'],
        },
        { label: 'Room Name', name: 'room_name', type: ColumnDataType.String, isUseDataDisplay: true },
        {
            label: 'Name of Schedule',
            name: 'schedule_name',
            editable: true,
            type: ColumnDataType.SingleStringSelect,
            dependedByColumns: ['room_name'],
            isUseDataDisplay: true,
        },
        { label: 'Number of Rooms', name: 'no_of_rooms', editable: true, type: ColumnDataType.Number },
    ];
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <StandardTable
        bind:this={standardTable}
        abilities={data.abilities}
        tableName="Room-Schedule Mapping"
        {newRecords}
        uniqueKey={['day', 'month', 'year', 'session_type_display']}
        data={tableData}
        initData={tableData}
        {newData}
        {subData}
        {extraData}
        customFuntion={getClinicCodeByConditions}
        {FieldNames}
        {isDependentField}
        {columnNames}
        bind:updateList
        bind:updatePosition
        bind:searchText
        bind:selectedRow
        allowSelected={true}
        hasSN={false}
        on:add={() => handleAppendNewRow()}
        on:save={() => handleSaveData()}
        on:delete={() => onDelete()}
        on:refresh={() => handleRefresh()} />

    <Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
</Guard>
