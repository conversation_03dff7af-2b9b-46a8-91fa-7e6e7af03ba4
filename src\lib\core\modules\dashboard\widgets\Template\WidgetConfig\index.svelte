<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface WidgetConfiguration {
        dataSource?: string;
        title?: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'aoh_charts_indicator_sample_aggregate';
    const DEFAULT_TITLE = 'Template Widget Title';

    export const configDefaults: WidgetConfiguration = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
    };

    export const expectedFormat = [{ name: 'number', type: 'numeric' }];
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';

    export let config: WidgetConfiguration;

    // List of data source names (valid GraphQL query roots)
    const validQueries: Array<DropdownItem> = [];

    // Look for aggregate > sum queries with expected format
    // aggregate > sum follows Hasura's aggregate sum schema
    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => {
                if (field?.name !== 'aggregate') {
                    return false;
                }

                let aggregateFields = field?.type?.fields;

                return aggregateFields.some(aField => {
                    if (aField?.name !== 'sum') {
                        return false;
                    }

                    let sumFields = aField?.type?.fields;

                    return sumFields.some(sField => sField?.name === format.name && sField?.type?.name === format.type);
                });
            });
        });

        if (isQueryValid) {
            validQueries.push({ label: query.name, value: query.name });
        }
    });
</script>

<div class="flex flex-col gap-2">
    <div>
        <Dropdown
            title="Data Source"
            options={validQueries}
            setClass="border-b border-b-outline bg-transparent"
            bind:value={config.dataSource} />
    </div>
    <div>
        <TextField
            placeholder="Title"
            bind:value={config.title}
            setClass="w-full p-1 bg-transparent border border-outline transition-all duration-200 \
            ease-in-out rounded focus:border-2 focus:border-primary font-light h-10"
            setLabelClass="text-primary text-xs" />
    </div>
</div>
