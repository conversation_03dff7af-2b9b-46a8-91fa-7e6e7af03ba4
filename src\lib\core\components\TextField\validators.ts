function requiredValidator() {
    return function required(value) {
        return (value.value !== undefined && value.value !== null && value.value !== '') || 'This field is required';
    };
}

function rangeValidator() {
    return function range(value) {
        if (value.compareOperator === '>') {
            return (
                parseInt(value.value) > parseInt(value.compareValue) ||
                value.compareValue === undefined ||
                'This field is not in range'
            );
        } else if (value.compareOperator === '<') {
            return (
                parseInt(value.value) < parseInt(value.compareValue) ||
                value.compareValue === undefined ||
                'This field is not in range'
            );
        } else {
            return value.value !== undefined || 'This field is not in range';
        }
    };
}

export { rangeValidator, requiredValidator };
