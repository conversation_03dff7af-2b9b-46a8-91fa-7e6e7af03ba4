import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
import { checkPermissions } from '$lib/shared/util/acl';
import { logger } from '$lib/stores/Logger';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

const log = logger.child({ src: new URL(import.meta.url).pathname });
export const load: PageServerLoad = async ({ parent, params, fetch, locals }) => {
    const surgicalServiceRes = await fetch(`/api/ot/cfg-ngemr-surgical-service`);
    const roomsRes = await fetch(`/api/soc/mst-ngemr-rooms`);
    const casePriorityRes = await fetch(`/api/ot/dummy-data?func-name=f_case_priority_for_ot_room`);

    if (surgicalServiceRes.status !== 200) {
        log.error({ status: surgicalServiceRes.status });
        return error(404);
    }
    if (roomsRes.status !== 200) {
        log.error({ status: roomsRes.status });
        return error(404);
    }
    if (casePriorityRes.status !== 200) {
        log.error({ status: casePriorityRes.status });
        return error(404);
    }

    const surgicalService = await surgicalServiceRes?.json();
    const roomsService = await roomsRes?.json();
    const casePriorityService = await casePriorityRes?.json();

    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const token = locals.user.access_token;

    let permissions = [
        { scopeId: SCOPE.READ, resourceId: RESOURCE.OT_ROOM_CONFIGURATION },
        { scopeId: SCOPE.CREATE, resourceId: RESOURCE.OT_ROOM_CONFIGURATION },
        { scopeId: SCOPE.UPDATE, resourceId: RESOURCE.OT_ROOM_CONFIGURATION },
        { scopeId: SCOPE.DELETE, resourceId: RESOURCE.OT_ROOM_CONFIGURATION },
    ];
    const abilities = await checkPermissions({ token, tenantId, userId, permissions });

    return {
        surgical: surgicalService?.data || [],
        rooms: roomsService?.data || [],
        casePriority: casePriorityService?.data || [],
        abilities,
    };
};
