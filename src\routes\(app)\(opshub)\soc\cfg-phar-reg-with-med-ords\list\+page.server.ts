import { logger } from '$lib/stores/Logger';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { checkPermissions } from '$lib/shared/util/acl';
import { RESOURCE } from '$lib/shared/enum/general';
import { SCOPE } from '$lib/shared/enum/general';

const log = logger.child({ src: new URL(import.meta.url).pathname });

export const load: PageServerLoad = async ({ parent, params, fetch, locals }) => {
  
    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const access_token = locals.user.access_token;
    let permissionNeedToCheck = [
        {
            scopeId: SCOPE.READ,
            resourceId: RESOURCE.SOC_NUMBER_OF_PATIENTS_WITH_MEDICATION_ORDER,
        },
        {
            scopeId: SCOPE.CREATE,
            resourceId: RESOURCE.SOC_NUMBER_OF_PATIENTS_WITH_MEDICATION_ORDER,
        },
        {
            scopeId: SCOPE.UPDATE,
            resourceId: RESOURCE.SOC_NUMBER_OF_PATIENTS_WITH_MEDICATION_ORDER,
        },
        {
            scopeId: SCOPE.DELETE,
            resourceId: RESOURCE.SOC_NUMBER_OF_PATIENTS_WITH_MEDICATION_ORDER,
        },
        {
            scopeId: SCOPE.TOGGLE,
            resourceId: RESOURCE.SOC_NUMBER_OF_PATIENTS_WITH_MEDICATION_ORDER,
        },
    ];
    const abilities = await checkPermissions({
        token: access_token,
        tenantId,
        userId,
        permissions: permissionNeedToCheck,
    });
    return { abilities };
};
