<script lang="ts" context="module">
    export interface WidgetTransferToConfig {
        title?: string;
        titleAlign?: string;
        titleFontSize?: number;
        titleArrowDirection?: string;
        titlePosition?: string;
        indicatorValue?: number;
        indicatorFontSize?: number;
        indicatorBgColor?: string;
        indicatorTextColor?: string;
        isEnableBackground: boolean;
        uom: string;
        pastXUom?: number;
    }
    export const configDefaults: WidgetTransferToConfig = {
        title: 'Throughput',
        titleAlign: 'center',
        titleFontSize: 12,
        titleArrowDirection: 'up right',
        titlePosition: 'top',
        indicatorValue: 10,
        indicatorFontSize: 20,
        indicatorBgColor: '#000000',
        indicatorTextColor: '#FFFFFF',
        isEnableBackground: true,
        uom: 'hour',
        pastXUom: 22, //Transfer Actualisation date time DO33
    };
</script>

<script lang="ts">
    export let config: WidgetTransferToConfig;
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import WidgetConfig from '$lib/core/modules/dashboard/components/WidgetConfig/index.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';

    let datasources: Array<DropdownItem> = [];
    let configElementWidth = 'w-[27%]';

    const ALIGNMENT = ['Left', 'Center', 'Right'];
    const ARROW_DIRECTIONS = ['None', 'Up Right', 'Down Right'];
    const LIST_UOMS: string[] = ['Hour', 'Minute', 'Day'];
    const TITLE_POSITION: string[] = ['Top', 'Left', 'Right', 'Bottom'];

    const propertyFields = [
        {
            id: 'dataSource',
            componentDisplay: Dropdown,
            extraProps: {
                title: 'Data Source',
                options: datasources,
                setClass: 'border-b border-b-outline bg-transparent mb-4',
                enableSearch: true,
                appendMenuClass: 'w-full',
                required: true,
            },
        },
        {
            id: 'title',
            componentDisplay: WidgetConfigText,
            extraProps: {
                title: 'Title',
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full',
            },
            children: [
                {
                    id: 'titleAlign',
                    componentDisplay: WidgetGroupButton,
                    extraProps: {
                        title: 'Title Alignment',
                        labels: ALIGNMENT,
                        customClass: 'w-2/3 mb-4',
                    },
                },
                {
                    id: 'titleFontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Title Font Size',
                        min: 0,
                        pattern: '^[0-9]\\d*$',
                        customClass: 'w-1/3 mb-4',
                    },
                },
            ],
        },
        {
            id: 'titlePosition',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Title Position',
                labels: TITLE_POSITION,
            },
        },
        {
            id: 'titleArrowDirection',
            componentDisplay: WidgetGroupButton,
            extraProps: {
                title: 'Arrow Direction',
                labels: ARROW_DIRECTIONS,
            },
        },
        {
            id: 'isEnableBackground',
            componentDisplay: WidgetToggle,
            extraProps: {
                title: 'Show Indicator Background',
                checked: config.isEnableBackground,
            },
        },
        {
            extraProps: {
                customClass: 'flex w-full flex-row items-end gap-2',
            },
            children: [
                {
                    id: 'indicatorFontSize',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Indicator Font Size',
                        min: 0,
                        pattern: '^[0-9]\\d*$',
                        customClass: 'w-1/3 mb-4',
                    },
                },
                {
                    id: 'indicatorBgColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Bg Color',
                        type: 'color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
                {
                    id: 'indicatorTextColor',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        title: 'Text Color',
                        type: 'color',
                        inputClass: 'mx-auto h-10 w-10 p-0',
                    },
                },
            ],
        },
        {
            extraProps: {
                customClass: 'flex w-full',
            },
            children: [
                {
                    id: 'uom',
                    componentDisplay: WidgetGroupButton,
                    extraProps: {
                        title: 'UOM',
                        labels: LIST_UOMS,
                        customClass: 'w-2/3',
                    },
                },
                {
                    id: 'pastXUom',
                    componentDisplay: WidgetConfigText,
                    extraProps: {
                        type: 'number',
                        title: 'Throughput Time',
                        min: 1,
                        pattern: '^[0-9]\\d*$',
                        customClass: 'w-1/3',
                        excludeZeroFirst: true,
                    },
                },
            ],
        },
    ];
</script>

<WidgetConfig bind:config {propertyFields} {configElementWidth} />
