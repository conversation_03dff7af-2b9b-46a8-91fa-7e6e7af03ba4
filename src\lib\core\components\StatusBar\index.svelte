<script lang="ts" context="module">
    let StatusBarElement;

    export function setStatusBarDisplay(display: 'none' | 'flex') {
        if (StatusBarElement) StatusBarElement.style.display = display;
    }
</script>

<script lang="ts">
    import DisplayClock from '$lib/core/components/DisplayClock/index.svelte';
    import Status from '$lib/core/components/StatusBar/Status/index.svelte';
    import UserInfo from '$lib/core/components/UserInfo/index.svelte';

    export let role: Array<string>;
    export let username: string;
    export let dateTime: ISO8601Date;
    export let isOutOfSync = false;
    export let isAnchor = false;
</script>

<div
    bind:this={StatusBarElement}
    data-testid="status-bar"
    class="sticky left-0 top-0 z-30 flex h-[60px] flex-initial flex-row items-center justify-between space-x-6
     bg-background px-4 py-2 text-on-background">
    <div class="flex">
        <!-- Remove page title -->
        <!-- <PageTitle class="whitespace-nowrap text-lg text-on-background" /> -->
        <Status {isAnchor} />
    </div>
    <div class="block flex flex-row gap-3 max-lg:hidden">
        <DisplayClock {dateTime} showOutOfSyncIndication={true} {isOutOfSync} />
        <UserInfo {role} {username} />
    </div>
</div>
