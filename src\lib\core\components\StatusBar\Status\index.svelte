<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { onDestroy, onMount } from 'svelte';
    import { writable } from 'svelte/store';

    export const componentName = new URL(import.meta.url).pathname;
    export const StatusAnchor = writable<Element>();
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    export let isAnchor = false;

    let statusElement: HTMLDivElement;
    let isRender = false;
    onMount(() => {
        if (isAnchor) {
            $StatusAnchor = statusElement;
        } else {
            $StatusAnchor.appendChild(statusElement);
            isRender = true;
        }
    });

    onDestroy(() => {
        if ($StatusAnchor?.firstChild) {
            $StatusAnchor.removeChild($StatusAnchor.firstChild);
        }
    });
</script>

<div class="h-full w-full overflow-hidden {isRender ? 'visible' : 'invisible'}" bind:this={statusElement}>
    <slot />
</div>
