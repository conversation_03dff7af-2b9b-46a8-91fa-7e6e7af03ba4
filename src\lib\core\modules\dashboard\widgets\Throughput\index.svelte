<script lang="ts" context="module">
    import { faExchange } from '@fortawesome/free-solid-svg-icons';
    export const widgetTitle = 'Throughput Indicator';
    export const widgetIcon = faExchange;
    export const widgetMinWidth = 2;
    export const widgetMinHeight = 2;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 12;
    export const widgetLimit = Number.POSITIVE_INFINITY;
    export const widgetCategory = 'Resource Management';
    export const componentName = new URL(import.meta.url).pathname;
</script>

<script lang="ts">
    import type { WidgetTransferToConfig } from './WidgetConfig/index.svelte';
    import { onMount, afterUpdate } from 'svelte';
    import { Channel, browserBroadcaster } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    export let config: WidgetTransferToConfig;
    let titleElement: HTMLElement;
    let indicatorElement: HTMLElement;
    let throughputElement: HTMLElement;

    //  Note: Refer to https://www.toptal.com/designers/htmlarrows/arrows/ for more arrows. The arrows below are what are currently used in DO Hospital Flow:
    // &#8625;
    // &#8627;
    let arrowDirections = '&#8627;';

    const styleAlign = {
        left: 'justify-start',
        right: 'justify-end',
        center: 'justify-center',
    };

    $: pastXUomValue =
        parseInt(config.pastXUom.toString()) === 1 || parseInt(config.pastXUom.toString()) == 0 ? '' : config.pastXUom;

    $: configTitleFrontSize = config.titleFontSize;
    $: if (titleElement && configTitleFrontSize) {
        titleElement.style.fontSize = `${configTitleFrontSize}px`;
    }

    $: configIndicatorFontSize = config.indicatorFontSize;
    $: if (indicatorElement && configIndicatorFontSize) {
        indicatorElement.style.fontSize = `${configIndicatorFontSize}px`;
    }

    $: configIndicatorTextColor = config.indicatorTextColor;
    $: if (indicatorElement && configIndicatorTextColor) {
        indicatorElement.style.color = `${configIndicatorTextColor}`;
    }

    $: configIndicatorBgColor = config.indicatorBgColor;
    $: if (indicatorElement && configIndicatorBgColor) {
        indicatorElement.style.backgroundColor = `${config.isEnableBackground ? `${configIndicatorBgColor}` : ''}`;
    }

    $: arrowDirections =
        config.titleArrowDirection === 'down right'
            ? '&#8627;&nbsp;'
            : config.titleArrowDirection === 'up right'
              ? '&#8625;&nbsp;'
              : '';

    const titleElementDefaultClass = 'text-on-background w-full items-center flex';
    $: titleElementClass = `${titleElementDefaultClass}  ${styleAlign[config.titleAlign]}`;

    const resizeWidgetItems = (widgetItem: HTMLElement) => {
        if (!widgetItem || !throughputElement) return;
        let minSize =
            Math.min(...[throughputElement.clientWidth, throughputElement.clientHeight].filter(m => m > 0)) - 20;
        widgetItem.style.height = minSize * 0.5 + 'px';
        widgetItem.style.width = minSize * 0.8 + 'px';
    };

    const initialDataAndStyle = () => {
        resizeWidgetItems(indicatorElement);
    };

    onMount(async () => {
        initialDataAndStyle();
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _ => {
            initialDataAndStyle();
        });
    });

    afterUpdate(() => {
        initialDataAndStyle();
    });
</script>

<div
    class={'h-full w-full flex p-2 ' +
        (config.titlePosition === 'top' || config.titlePosition === 'bottom' ? 'flex-col' : '')}
    bind:this={throughputElement}>
    <!-- Title -->
    <div
        class={`${titleElementClass} ` +
            (config.titlePosition === 'left' || config.titlePosition === 'top' ? ' order-1 ' : ' order-2 ')
            + (config.title =='' && config.titleArrowDirection == 'none' ? '' : 'p-1')}
        bind:this={titleElement}>
        <span class="w-full text-{config.titleAlign}">{@html arrowDirections}{config.title}</span>
    </div>

    <!-- Indicator -->
    <div
        class={'flex h-full w-full text-on-background items-center justify-center' +
            (config.titlePosition === 'left' || config.titlePosition === 'top' ? ' order-2 ' : ' order-1 ')}>
        <div
            bind:this={indicatorElement}
            class="align-middle flex items-center h-1/2 w-full rounded-md px-2 justify-center">
            <span>{config.indicatorValue}/{pastXUomValue}{config.uom.charAt(0)}</span>
        </div>
    </div>
</div>
