<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
<<<<<<< HEAD
    import { faChartPie, faTriangleExclamation } from '@fortawesome/free-solid-svg-icons';
=======
    import { faTriangleExclamation } from '@fortawesome/free-solid-svg-icons';
    import { faChartPie } from '@fortawesome/free-solid-svg-icons';
>>>>>>> aoh-web/release/v2.2.0
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import ProgressBar from './ProgressBar/index.svelte';
    import { ResolveThemeProperty, ResolveThemePropertyRoot } from '$lib/core/modules/theme';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import gql from 'graphql-tag';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import { gqlClientStore } from '$lib/stores/Clients';
    import type { EntityCountConfig } from './WidgetConfig/index.svelte';
    import chroma from 'chroma-js';
    import * as pixi from 'pixi.js';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);

    export const widgetTitle = 'Pie Chart with Percentage Bar';
    export const widgetIcon = faChartPie;
<<<<<<< HEAD
    export const widgetHeight = 3;
    export const widgetWidth = 3;
=======
    export const widgetWidth = 6;
    export const widgetHeight = 4;
    export const widgetMinWidth = 4;
    export const widgetMinHeight = 3;
>>>>>>> aoh-web/release/v2.2.0
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = Number.POSITIVE_INFINITY;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { data } from 'jquery';

    let pieChartEl: HTMLElement;
    let wrapper: HTMLElement;
    let subscriptions: Subscription[] = [];
    const MAX_ROWS = 3;

    export let config: EntityCountConfig;
    const colorScale = chroma.scale(['#f9fdff', '#ead791', '#b0cafc']);

    const NON_CURRENT_KEY_INDEX = 1;
    const BACKGROUND_COLOR = ResolveThemePropertyRoot('surface-3');
    const FONT_FAMILY = ResolveThemePropertyRoot('sans');
    const SERIES_DELIMITER_LINE_COLOR = 'white';

    let total: number = 0;
    let labels: string[] = [];
    let chartData: { name: string; value: number }[] = [];
    let selectOptions: DropdownItem[] = [];
    let totalFromLastCount = 0;
    let colorMap: Map<string, string> = new Map();

    let isInit = false;
    let label = '';
    let rows: Array<unknown> = [];
    let app: pixi.Application<pixi.Renderer> = new pixi.Application();
    let pieChart = new pixi.Graphics();
    let seriesLines = new pixi.Graphics();
    let chartLabels: Array<pixi.Text> = [];

    function load(data: any[]) {
        // Reset all values before mapping
        total = 0;
        chartData = [];
        labels = [];
        selectOptions = [];
        colorMap = new Map();
        rows = [];

        data.forEach((d, y) => {
            if (d[config?.group] === config?.groupKeys[NON_CURRENT_KEY_INDEX]) {
                if (!isInit) {
                    totalFromLastCount = d[config.count];
                    label = d[config.label];
                }

                selectOptions.push({
                    label: d[config.label],
                    value: d[config.count],
                });
            } else {
                labels.push(d[config.label]);
                chartData.push({ name: d[config.label], value: d[config.count] });

                //Calculate the total count
                total += d[config.count];
            }
        });

        rows = chartData;
        selectOptions = selectOptions;
        labels = labels;
    }

    async function initialize() {
        await app.init({
            antialias: true,
            backgroundAlpha: 0,
            resizeTo: wrapper,
            autoDensity: true,
            resolution: window.devicePixelRatio || 1,
        });
        pieChartEl.appendChild(app.canvas);

        updatePixiChart();

        window.addEventListener('resize', () => {
            updatePixiChart();
        });

        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            updatePixiChart();
        });
    }

    function updatePixiChart() {
        chartLabels = [];
        pieChart.clear();
        seriesLines.clear();
        app.stage.removeChildren();

        if (app.resize) {
            app.resize();
        }

        const X_CENTER = pieChartEl.clientWidth / 2;
        const Y_CENTER = pieChartEl.clientHeight / 2;
        const BOUND_SIZE = Math.min(pieChartEl.clientWidth, pieChartEl.clientHeight);
        const BASE_RADIUS = BOUND_SIZE * 0.35;
        const PIE_THICKNESS = BASE_RADIUS * 0.33;
        const SERIES_ARC_RADIUS = BASE_RADIUS - PIE_THICKNESS / 1.4;
        const SERIES_TEXT_RADIUS = BASE_RADIUS * 1.3;
        const EDGE_P1_MUL = 0.725;
        const EDGE_P2_MUL = 1.35;

        // Pie Chart Background
        let pieChartBackground = pieChart.circle(X_CENTER, Y_CENTER, BASE_RADIUS).fill(BACKGROUND_COLOR);

        app.stage.addChild(pieChartBackground);

        let startAngle = -Math.PI / 2;

        chartData.forEach((v, i) => {
            // Pie Chart Percentages
            const sweepAngle = (v.value / total) * Math.PI * 2;
            const endAngle = startAngle + sweepAngle;

            const seriesArc = new pixi.GraphicsPath().arc(X_CENTER, Y_CENTER, SERIES_ARC_RADIUS, startAngle, endAngle);

            pieChart = pieChart.path(seriesArc);

            pieChart.stroke({
                width: PIE_THICKNESS,
                color: colorScale(i / (chartData.length - 1)).num(),
            });

            // Pie Chart Line Delimiters
            let edgeP1 = new pixi.Point(
                X_CENTER + SERIES_ARC_RADIUS * EDGE_P1_MUL * Math.cos(endAngle),
                Y_CENTER + SERIES_ARC_RADIUS * EDGE_P1_MUL * Math.sin(endAngle)
            );

            let edgeP2 = new pixi.Point(
                X_CENTER + SERIES_ARC_RADIUS * EDGE_P2_MUL * Math.cos(endAngle),
                Y_CENTER + SERIES_ARC_RADIUS * EDGE_P2_MUL * Math.sin(endAngle)
            );

            const seriesEdge = new pixi.GraphicsPath().moveTo(edgeP1.x, edgeP1.y);
            seriesEdge.lineTo(edgeP2.x, edgeP2.y);

            seriesLines.path(seriesEdge);

            seriesLines.stroke({
                width: PIE_THICKNESS / 18,
                color: SERIES_DELIMITER_LINE_COLOR,
            });

            // Series Text
            let textPoint = new pixi.Point(
                X_CENTER + SERIES_TEXT_RADIUS * Math.cos(startAngle + sweepAngle / 2.5),
                Y_CENTER + SERIES_TEXT_RADIUS * Math.sin(startAngle + sweepAngle / 2.5)
            );

            let chartLabelText = new pixi.Text({
                text: v.name,
                style: {
                    fontSize: '10px',
                    fill: 'white',
                    align: 'justify',
                    fontWeight: '300',
                    fontFamily: FONT_FAMILY,
                },
            });

            chartLabelText.x = textPoint.x - chartLabelText.width / 2;
            chartLabelText.y = textPoint.y - chartLabelText.height / 2;
            chartLabelText.resolution = 1.25;

            chartLabels.push(chartLabelText);

            startAngle = endAngle;
        });

        // Pie Chart Center Text
        const totalCount = new pixi.Text({
            text: total,
            style: {
                fontSize: PIE_THICKNESS * 1.25,
                fill: ResolveThemePropertyRoot('on-surface-1'),
                align: 'center',
                fontWeight: '300',
                fontFamily: FONT_FAMILY,
            },
        });

        totalCount.x = X_CENTER - totalCount.width / 2;
        totalCount.y = Y_CENTER - totalCount.height / 2;

        app.stage.addChild(pieChart);
        app.stage.addChild(seriesLines);
        app.stage.addChild(totalCount);
        chartLabels.forEach(v => {
            app.stage.addChild(v);
        });
    }

    //Construct the query parameters
    function parameters(): string {
        return config?.order ? `(order_by: {${config.order}: asc})` : '';
    }

    const FetchDataDocument = gql`
        query {
            ${config?.dataSource}${parameters()} {
                ${config?.label}
                ${config?.count}
                ${config?.group}
            }
        }
    `;

    const ObserveDataDocument = gql`
        subscription {
            ${config?.dataSource}${parameters()} {
                ${config?.label}
                ${config?.count}
                ${config?.group}
            }
        }
    `;

    const client = get(gqlClientStore);
    client
        .query(FetchDataDocument, {})
        .toPromise()
        .then(result => {
            load(result.data[config?.dataSource]);
            if (app) {
                updatePixiChart();
            } else {
                logger.warn('Possible timing issue: attempted to update pixi chart when app was not initialized.');
            }
        });

    onMount(async () => {
        if (config?.dataSource?.length) {
            subscriptions.push(
                wPipe(
                    client.subscription(ObserveDataDocument, {}),
                    wSubscribe(result => {
                        load(result.data[config?.dataSource]);
                        if (app) {
                            updatePixiChart();
                        } else {
                            logger.warn(
                                'Possible timing issue: attempted to update pixi chart when app was not initialized.'
                            );
                        }
                    })
                )
            );
        }

        await initialize();
    });

    onDestroy(() => {
        subscriptions.forEach(s => {
            s?.unsubscribe();
        });
    });
</script>

<div class="flex h-full w-full flex-col justify-between text-sm text-on-background">
    <header class="flex basis-[2rem] items-center gap-3 p-4 text-base">
        <p class="grow bg-transparent">{config?.title}</p>
        <p class="text-sm">
            CURRENT TOTAL
            <span
                class="ml-2 rounded-full bg-primary px-3 py-1 text-lg font-bold leading-5 tracking-[0.15%] text-on-primary"
                >{total}</span>
        </p>
    </header>

    <section class="flex h-full w-full gap-4 p-4">
        <div class="relative h-full w-2/5" bind:this={wrapper}>
            <div class="absolute" bind:this={pieChartEl} />
        </div>
        <div class="flex h-full w-3/5 flex-col justify-center">
            {#each chartData.slice(0, MAX_ROWS) as data, i}
                <ProgressBar
                    label={data.name}
                    color={colorScale(i / (chartData.length - 1)).hex()}
                    totalCount={total}
                    count={data.value} />
            {/each}

            {#if rows?.length > 3}
                <article class="rounded px-2 text-end text-2xs text-amber-400">
                    <span class="mr-1 font-semibold"> {@html icon(faTriangleExclamation).html} WARNING</span> Some values
                    may be truncated.
                </article>
            {/if}
        </div>
    </section>

    <footer class="flex basis-[2rem] items-center justify-end gap-1 border-t border-t-outline p-4 text-sm">
        <span class="flex items-center gap-2 text-xs">
            TOTAL FROM LAST
            <Dropdown
                options={selectOptions}
                setClass="bg-transparent w-[7rem] h-4"
                {label}
                bind:value={totalFromLastCount}
                menuClass="max-h-[150px] h-fit w-full overflow-y-auto scroll bottom-5 bg-secondary-container" />
            <span
                class="ml-2 rounded-full bg-primary px-3 py-1 text-lg font-bold leading-5 tracking-[0.15%] text-on-primary"
                >{totalFromLastCount}</span>
        </span>
    </footer>
</div>
