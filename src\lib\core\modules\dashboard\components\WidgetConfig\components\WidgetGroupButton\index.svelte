<script lang="ts">
    import { Label, Button } from 'flowbite-svelte';
    import { onMount } from 'svelte';
    export let title: string;
    export let value;
    export let customClass = 'mb-4';
    export let labels: string[];
    export let isMultiple = false;
    export let isTimeFormat = false;

    const TIME_ORDER = ['day', 'hr', 'min', 'sec'];

    const updateConfigValue = (label: string) => {
        if (typeof value === 'boolean') {
            value = !value;
            return;
        }

        const lowerLabel = label.toLowerCase();

        if (isMultiple) {
            const hasLabel = value.includes(lowerLabel);

            if (hasLabel) {
                if (isTimeFormat) {
                    const hasDay = value.includes('day');
                    const hasHr = value.includes('hr');
                    const hasMin = value.includes('min');
                    const hasSec = value.includes('sec');

                    if (lowerLabel === 'hr' && hasDay && hasMin) {
                        value = value.filter((item: string) => item !== 'day');
                    } else if (lowerLabel === 'min' && hasHr && hasSec) {
                        value = value.filter((item: string) => item !== 'sec');
                    }
                }

                value = value.filter((item: string) => item !== lowerLabel);
                return;
            }
            value.push(lowerLabel);

            if (isTimeFormat) {
                value = value.map(v => v.toLowerCase());
                value.sort((a, b) => TIME_ORDER.indexOf(a) - TIME_ORDER.indexOf(b));

                const first = TIME_ORDER.indexOf(value[0]);
                const last = TIME_ORDER.indexOf(value[value.length - 1]);
                value = TIME_ORDER.slice(first, last + 1);
            } else {
                value = value;
            }
        } else {
            value = lowerLabel;
        }
    };

    onMount(() => {
        if (isMultiple && !value) {
            value = [];
        }
    });

    const getValueByLabel = (label: string) => {
        let value = label;
        switch (label) {
            case '>=':
                value = '&#8805;';
                break;
            case '<=':
                value = '&#8804;';
                break;
            default:
                value = label;
                break;
        }
        return value;
    };
</script>

<div class={customClass}>
    {#if title}<Label class="mb-2 block">{title}</Label>{/if}
    <div class="flex flex-wrap gap-2">
        {#each labels as label}
            <Button
                color={value === label.toLowerCase() || (isMultiple && value?.includes(label.toLowerCase()))
                    ? 'blue'
                    : 'light'}
                class="h-10 w-20"
                on:click={() => updateConfigValue(label)}>{@html getValueByLabel(label)}</Button>
        {/each}
    </div>
</div>
