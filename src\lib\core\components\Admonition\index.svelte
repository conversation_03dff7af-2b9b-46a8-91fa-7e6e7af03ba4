<script lang="ts" context="module">
    export enum Admonition_Type_Enum {
        Note = 'Note',
        Info = 'Info',
        Warning = 'Warning',
        Error = 'Error',
    }
</script>

<script lang="ts">
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faCircleInfo,
        faTriangleExclamation,
        faXmarkCircle,
        type IconDefinition,
    } from '@fortawesome/free-solid-svg-icons';

    type AdmonitionType = {
        icon: IconDefinition;
        style: string;
    };

    export let type: Admonition_Type_Enum = Admonition_Type_Enum.Note;
    export let message: string = '';
    export let title: string = 'Untitled';

    function setStyle(): AdmonitionType {
        switch (type) {
            case Admonition_Type_Enum.Warning:
                return {
                    icon: faTriangleExclamation,
                    style: 'border-l border-l-warning bg-opacity bg-yellow-500',
                };

            case Admonition_Type_Enum.Error:
                return {
                    icon: faXmarkCircle,
                    style: 'border-l border-l-on-error-container bg-opacity-50 bg-error-container ',
                };
                break;

            default:
            case Admonition_Type_Enum.Note:
                return {
                    icon: faCircleInfo,
                    style: 'border-l border-l-outline bg-surface-container-highest',
                };
        }
    }
</script>

<div class="rounded border-l-3 p-2 pl-2 text-2xs {setStyle().style}">
    <header class="flex items-center gap-2 font-semibold uppercase brightness-150">
        {@html icon(setStyle().icon).html}
        {title}
    </header>

    <article class="brightness-150">{@html `<p>${message}</p>`}</article>
</div>
