<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
<<<<<<< HEAD
    import { faUserCircle } from '@fortawesome/free-solid-svg-icons';
    import dayjs, { duration } from 'dayjs';
=======
    import { faSquare1 } from '@fortawesome/pro-duotone-svg-icons';
    import dayjs from 'dayjs';
>>>>>>> aoh-web/develop
    import gql from 'graphql-tag';

    // Plugin to allow usage of durations
    dayjs.extend(dayjs.duration);

    export const widgetTitle = 'Indicator';
<<<<<<< HEAD
    export const widgetIcon = faUserCircle;
    export const widgetWidth = 2;
    export const widgetHeight = 2;
    export const widgetMinWidth = 2;
    export const widgetMinHeight = 2;
    export const widgetMaxWidth = 8;
    export const widgetMaxHeight = 8;
=======
    export const widgetIcon = faSquare1;
>>>>>>> aoh-web/develop
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetEnabled = true;
    export const widgetMinWidth = 5;
    export const widgetMinHeight = 5;
    export const widgetMaxWidth = 5;
    export const widgetMaxHeight = 5;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import { type IndicatorConfig } from './WidgetConfig/index.svelte';

    export let config: IndicatorConfig;

    const FetchIndicatorWidgetDocument = gql`
        query FetchIndicatorWidget {
            ${config.dataSource} {
                aggregate {
                    sum {
                        number
                    }
                }
            }
        }
    `;

    const ObserveIndicatorWidgetDocument = gql`
        subscription ObserveIndicatorWidget {
            ${config.dataSource} {
                aggregate {
                    sum {
                        number
                    }
                }
            }
        }
    `;

    function setFontSize(
        element: HTMLElement,
        sizer: (viewport_min_length: number) => string,
        referenceElement?: HTMLElement
    ) {
        if (!referenceElement) {
            referenceElement = element;
        }

        let vmin = 0;

        vmin = Math.min(referenceElement?.clientWidth, referenceElement?.clientHeight);
        if (element) element.style.fontSize = sizer(vmin);
    }

    let subscription: Subscription;
    let number: number;
    let indicatorElement: HTMLElement;
    let titleElement: HTMLElement;
    let statusElement: HTMLElement;
    const bbsymbol: unique symbol = Symbol();

    const updateFromResult = result => {
        number = result?.data[config?.dataSource]?.aggregate?.sum?.number;
    };

    const client = get(gqlClientStore);
    client.query(FetchIndicatorWidgetDocument, {}).toPromise().then(updateFromResult);

    onMount(async () => {
        updateTextSizes();
        browserBroadcaster.sub(
            Channel.AOH_WIDGET_RESIZED,
            _m => {
                updateTextSizes();
            },
            bbsymbol
        );

        subscription = wPipe(client.subscription(ObserveIndicatorWidgetDocument, {}), wSubscribe(updateFromResult));
    });

    function updateTextSizes() {
        setFontSize(indicatorElement, viewport_min_length => viewport_min_length / 2 + 'px');
        setFontSize(
            statusElement,
            viewport_min_length => {
                const SCALE = 14;
                const INPUT = viewport_min_length / SCALE;
                const MIN = 14;
                const MAX = 42;
                const INPUT_BOUNDED = Math.min(Math.max(MIN, INPUT), MAX);
                const normalized = (INPUT_BOUNDED - MIN) / (MAX - MIN);
                const size = MIN + Math.sqrt(1 - Math.pow(normalized - 1, 2)) * (MAX - MIN);

                return size + 'px';
            },
            indicatorElement
        );
    }

    onDestroy(async () => {
        subscription?.unsubscribe();
        browserBroadcaster.unsub(Channel.AOH_WIDGET_RESIZED, bbsymbol);
    });
</script>

<div class="flex h-full w-full flex-col items-center justify-center p-4">
    <header
        bind:this={titleElement}
        class="flex w-full basis-4 items-center justify-start text-wrap text-base font-medium
        leading-tight text-on-background">
        {config.title}
    </header>
    <div
        bind:this={indicatorElement}
        class="flex w-full basis-4/5 items-center justify-center text-center font-semibold text-on-background"
        style="color:{number < config.lowThreshold
            ? config.lowColor
            : number < config.mediumThreshold
            ? config.mediumColor
            : config.highColor};">
        {number === undefined ? '?' : number}
    </div>
    <div
        class:visible={config.showThreshold}
        class:invisible={!config.showThreshold}
        bind:this={statusElement}
        class="basis-1/5 font-semibold text-on-background"
        style="color:{number < config.lowThreshold
            ? config.lowColor
            : number < config.mediumThreshold
            ? config.mediumColor
            : config.highColor};">
        {number < config.lowThreshold
            ? config.lowStatusText
            : number < config.mediumThreshold
            ? config.mediumStatusText
            : config.highStatusText}
    </div>
</div>
