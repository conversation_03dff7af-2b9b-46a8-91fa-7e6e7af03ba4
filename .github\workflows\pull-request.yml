name: Web - Pre-merge Checks

env:
    app_name: hcc3-web

on:
    workflow_dispatch:
    pull_request:
        branches:
            - develop
            - release/*
        paths:
            - '**'
            - '!.github/workflows/**'
            - '!docusaurus/**'

jobs:
    unit-tests:
        name: Run Unit Tests & Publish Badge
        runs-on: ubuntu-latest
        env:
            GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        permissions: # Job-level permissions configuration starts here
            contents: write # 'write' access to repository contents
            pull-requests: write # 'write' access to pull requests
        steps:
            - name: Checkout
              uses: actions/checkout@v4
              with:
                  ref: ${{ github.event.pull_request.head.ref }}
            - name: Set up NodeJS
              uses: actions/setup-node@v4
              with:
                  node-version: 18.12
                  cache: 'npm'
                  registry-url: https://npm.pkg.github.com/
            - name: Install Dependencies
              run: |
                  npm set //npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}
                  npm set registry=https://registry.npmjs.org
                  npm config list -l
                  npm ci
                  npm run clean
                  npm run generate
            - name: Run Unit Tests
              run: |
                  npm run test:unit
            #- name: Generate Coverage Badge
            #  id: gen-cov-badge
            #  run: |
            #      npm run coverage-badge
            #      mkdir -p ./badges && cp ./coverage/badges.svg ./badges/coverage.svg
            #      git diff --quiet HEAD || echo "NEW_BADGE=true" >> $GITHUB_OUTPUT
            # - name: Commit Updated Coverage Badge
            #  if: ${{ steps.gen-cov-badge.outputs.NEW_BADGE == 'true' }}
            #  run: |
            #      git config user.name "GitHub Actions [Bot]"
            #      git config user.email "<>"
            #      git add badges
            #      git commit -m "[Bot] Updated Coverage Badge"
            #      git push
    build-container:
        name: Try Build of Container
        runs-on: ubuntu-latest
        needs:
            - unit-tests
        steps:
            - name: '☁️ Checkout code'
              uses: actions/checkout@v4
              with:
                  path: ./SRC
            - name: '📦 ${{ env.app_name }} - Perform a test build of the container image'
              run: |
                  cd ./SRC

                  # PORT is placeholder to be defined at runtime
                  export PUBLIC_STATIC_BUILD_VERSION=next
                  export IMAGE_TAG=test
                  export PORT=3000

                  export GITHUB_ACCESS_TOKEN=${{ secrets.PAT }}

                  export PUBLIC_SECURE_MODE=0
                  export PUBLIC_REPLAY_MODE=0
                  export DISABLE_LOGIN=0

                  export IAM_URL=
                  export GRAPHQL_URL=
                  export IMAGE_TAG=

                  docker-compose -f docker-compose.yml build web
