<script lang="ts">
    import { get } from 'svelte/store';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import { ColumnDataType, type ColumnNameCustom } from '$lib/shared/constants/general';
    import { v4 as uuidv4 } from 'uuid';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import StandardTable from '../../components/StandardTable.svelte';
    import { CFG_PARAMETER_NAMES, SCOPE } from '$lib/shared/enum/general';
    import { isLoading } from '$lib/stores/Loading';
    import { TriggerService } from '$lib/service/trigger';
    import Guard from '$lib/shared/components/guard/Guard.svelte';

    export let data;

    const SocURL = `${get(locations).soc_web}`;

    let popupModal = false;
    let modalTitle = '';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;
    let openUpdateResult = false;
    let standardTable;
    let tableData = Array.isArray(data.apptStatus)
        ? data.apptStatus.map(item => ({
              id: item.id,
              code: item.code,
              desc: item.desc,
              category: item.category,
              remarks: item.remarks,
              is_filter_display: item.is_filter_display,
          }))
        : [];
    let newRecords = new Set();
    let searchText = '';
    let selectedRow = null;
    let updateList = [];
    const triggerTableNames = 'cc3_hoc.cfg_ngemr_appt_status';

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    let updatePosition: UpdatePosition = { id: '', columnName: '', isEditing: false };

    const showSuccessMessage = msg => {
        popupModal = true;
        modalTitle = 'Notification';
        modalMessage = msg;
        onlyOkBtn = true;
        if (openUpdateResult) {
            confirmAction = () => {
                handleRefresh();
                standardTable.resetAllTableValue();
            };
        }
    };

    const handleCreateData = async (userClaims, newItems) => {
        await fetch(`${SocURL}/cfg-ngemr-appt-status`, {
            method: 'POST',
            body: JSON.stringify({
                data: newItems.map(item => ({
                    code: item.code,
                    desc: item.desc,
                    category: item.category,
                    remarks: item.remarks,
                    is_filter_display: item.is_filter_display,
                })),
            }),
        });
    };

    const handleUpdateData = async (userClaims, updateItems) => {
        await fetch(`${SocURL}/cfg-ngemr-appt-status`, {
            method: 'PATCH',
            body: JSON.stringify({
                data: updateItems.map(item => ({
                    id: item.id,
                    code: item.code,
                    desc: item.desc,
                    category: item.category,
                    remarks: item.remarks,
                    is_filter_display: item.is_filter_display,
                })),
            }),
        });
    };

    const handleSaveData = async () => {
        const userClaims = get(user).claims;
        let newItems = [],
            updateItems = [];
        updateList.forEach(row => (newRecords.has(row.id) ? newItems.push(row) : updateItems.push(row)));

        if (newItems.length) await handleCreateData(userClaims, newItems);
        if (updateItems.length) await handleUpdateData(userClaims, updateItems);
        openUpdateResult = true;
        showSuccessMessage('Configuration is saved successfully.');
        TriggerService.trigger(triggerTableNames);
    };

    const handleDeleteData = async typeId => {
        await fetch(`${SocURL}/cfg-ngemr-appt-status`, {
            method: 'DELETE',
            body: JSON.stringify({ data: [{ id: typeId }] }),
        });
    };

    const onDelete = async () => {
        if (selectedRow && !newRecords.has(selectedRow)) {
            await handleDeleteData(selectedRow);
        }
        tableData = tableData.filter(item => item.id !== selectedRow);
        updateList = updateList.filter(item => item.id !== selectedRow);
        selectedRow = null;
        showSuccessMessage('Successfully deleted configuration!');
    };

    const handleRefresh = async () => {
        isLoading.set(true);
        try {
            var data = await fetch(`${SocURL}/cfg-ngemr-appt-status?page=1&limit=9999`, {
                method: 'Get',
            });
            const response = await data.json();
            tableData = response.data.map(item => ({
                id: item.id,
                code: item.code,
                desc: item.desc,
                category: item.category,
                remarks: item.remarks,
                is_filter_display: item.is_filter_display,
            }));
        } catch (error) {
            console.error('Error during refresh:', error);
        } finally {
            isLoading.set(false);
        }
    };

    let newData;
    const handleAppendNewRow = () => {
        const newId = uuidv4();
        newData = {
            id: newId,
            code: '',
            desc: '',
            category: '',
            remarks: '',
            is_filter_display: false,
            isNew: true,
        };
        tableData = [...tableData, newData];
        newRecords.add(newId);
    };

    const columnNames: ColumnNameCustom[] = [
        {
            label: 'Code',
            name: 'code',
            editable: true,
            type: ColumnDataType.String,
            isRequired: true,
            isNotEditableAfterSave: true,
        },
        {
            label: 'Description',
            name: 'desc',
            editable: true,
            type: ColumnDataType.String,
            isRequired: true,
        },
        {
            label: 'Category',
            name: 'category',
            editable: true,
            type: ColumnDataType.String,
        },
        {
            label: 'Filter Display',
            name: 'is_filter_display',
            editable: true,
            type: ColumnDataType.Boolean,
        },
        {
            label: 'Remarks',
            name: 'remarks',
            editable: true,
            type: ColumnDataType.String,
        },
    ];
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <StandardTable
        abilities={data.abilities}
        bind:this={standardTable}
        tableName="Appointment Status"
        {newRecords}
        configName={CFG_PARAMETER_NAMES.APPOINTMENT_STATUS_ENABLEMENT}
        data={tableData}
        initData={tableData}
        {columnNames}
        {newData}
        defaultSortKey="code"
        bind:updateList
        bind:updatePosition
        bind:searchText
        bind:selectedRow
        allowSelected={true}
        on:add={() => handleAppendNewRow()}
        on:save={() => handleSaveData()}
        on:delete={() => onDelete()}
        on:refresh={() => handleRefresh()} />

    <Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
</Guard>
