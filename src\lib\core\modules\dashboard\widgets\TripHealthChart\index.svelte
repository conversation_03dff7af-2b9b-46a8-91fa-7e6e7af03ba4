<script lang="ts" context="module">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import SituationAwarenessPictureViewer from '$lib/demo/trip/components/SituationAwarenessPictureViewer/index.svelte';
    import { logger } from '$lib/stores/Logger';
    import { faMap } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import { onDestroy, onMount } from 'svelte';
    import type { Config } from './WidgetConfig/index.svelte';
    import type { _ } from 'ajv';

    dayjs.extend(isToday);

    export const widgetCategory = 'Trip Management';
    export const widgetTitle = 'Trip Health Picture';
    export const widgetIcon = faMap;
    export const widgetLimit = 999;
    export const widgetEnabled = true;
    export const widgetMinWidth = 12;
    export const widgetMinHeight = 6;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    export let config: Config = {
        title: 'Trip Health Picture',
    };

    const bbsymbol: unique symbol = Symbol();

    let resize: () => void;

    onMount(() => {
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, () => {
            if (resize) resize();
        });
    });

    onDestroy(() => {
        browserBroadcaster.unsub(Channel.AOH_WIDGET_RESIZED, bbsymbol);
    });
</script>

<SituationAwarenessPictureViewer bind:resize>
    <div class="absolute left-4 top-4 text-on-background">{config.title}</div>
</SituationAwarenessPictureViewer>
