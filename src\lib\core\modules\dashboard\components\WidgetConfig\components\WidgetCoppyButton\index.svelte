<script lang="ts">
    import { Label, Button } from 'flowbite-svelte';
    import { faClone } from '@fortawesome/free-solid-svg-icons';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    export let title: string;
    export let value: string;
    export let customClass = 'mb-4 w-1/2';

    async function copyToClipboard(val: string) {
        if (!val) return;

        const text = `{{${String(val)}}}`;
        if (window.isSecureContext && navigator.clipboard) {
            try {
                await navigator.clipboard.writeText(text);
            } catch (err) {
                console.error('Failed to write to clipboard:', err);
            }
        } else {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);
            textarea.focus();
            textarea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Fallback copy failed:', err);
            } finally {
                document.body.removeChild(textarea);
            }
        }
    }
</script>

<div class={customClass}>
    {#if title}<Label class="mb-2 block">{title}</Label>{/if}
    <Button
        color="light"
        class="flex h-10 w-full justify-between truncate bg-gray-500 "
        on:click={() => copyToClipboard(value)}
        ><p class="w-4/5 truncate text-left">{value ? `{{${value}}}` : ''}</p>
        <span class="ml-2">{@html icon(faClone).html}</span></Button>
</div>
