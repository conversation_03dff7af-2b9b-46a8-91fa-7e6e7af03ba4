<script lang="ts">
    import { onMount } from 'svelte';

    import NumberOfPatients from '$lib/core-soc/modules/crud/NumberOfPatients.svelte';
    import { user } from '$lib/stores/Auth';
    import { get } from 'svelte/store';
    import { SCOPE } from '$lib/shared/enum/general';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    export let data;
    let user_id = '';
    let subData;
    const getSubData = async () => {
        const specialtyResponse = await fetch('/api/soc/cfg-ngemr-specialty');
        let specialtyData;
        let numberOfPtsData;
        if (specialtyResponse.status === 200) {
            specialtyData = await specialtyResponse.json();
        }
        const numberOfPtsResponse = await fetch('/api/soc/cfg-phar-reg-with-med-ords');
        if (numberOfPtsResponse.status === 200) {
            numberOfPtsData = await numberOfPtsResponse.json();
        }
        const usedSpecialtyCodes = numberOfPtsData.data?.map(item => item?.specialty_code);
        const finalData = specialtyData.data.filter(item => !usedSpecialtyCodes.includes(item.code));
        subData = {
            specialty_desc: finalData?.map(item => ({ value: item.desc, name: item.desc, ref: item.code })),
            specialty_code: finalData?.map(item => ({ value: item.code, name: item.code, ref: item.desc })),
        };
    };
    onMount(async () => {
        user_id = get(user)?.claims?.hasura_access?.['x-hasura-user-id'];
        await getSubData();
    });
    const onSaveSuccess = async (result: CustomEvent<boolean>) => {
        await getSubData();
    };
</script>

<Guard abilities={data.abilities} scopeId={SCOPE.READ}>
    <div class="flex flex-col gap-20 p-3 text-on-surface-1">
        {#if user_id !== ''}
            <NumberOfPatients on:saveSuccess={onSaveSuccess} {subData} abilities={data.abilities} />
        {/if}
    </div>
</Guard>
