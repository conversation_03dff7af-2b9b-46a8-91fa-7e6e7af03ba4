import { getHiddenFieldValue, setHiddenFieldValue } from '$lib/shared/util/customFunction';
import SOCViewTypeHandler from './SOCViewTypeHandler';

export default function SessionAvailabilityConfiguration(form, fields) {
    const oldViewTypeData = getHiddenFieldValue('oldViewType_SessionAvailabilityConfiguration');
    const viewTypeData = {
        ViewType: form.ViewType,
        StartDate: form.StartDate,
        EndDate: form.EndDate,
        NumberOfDays: form.NumberOfDays,
    };
    if (oldViewTypeData !== JSON.stringify(viewTypeData)) {
        SOCViewTypeHandler(form, fields);
        setHiddenFieldValue('oldViewType_SessionAvailabilityConfiguration', JSON.stringify(viewTypeData));
    }

    const oldServiceType = getHiddenFieldValue('oldServiceType_SessionAvailabilityConfiguration');
    if (oldServiceType !== form.ServiceType) {
        setHiddenFieldValue('oldServiceType_SessionAvailabilityConfiguration', form.ServiceType);
        form.Clinics = '';
        form.Room = '';
    }

    for (let index = 0; index < fields.length; index++) {
        const element = fields[index];
        if (element?.field === 'Clinics') {
            element.datasource = {
                ...element.datasource,
                fields: ['code', 'desc', 'category', 'service'],
                viewFields: ['code', 'desc', 'category'],
                customArrayFilter: {
                    field: 'service',
                    value:
                        !form.ServiceType || form.ServiceType === ''
                            ? []
                            : form.ServiceType.split(',').map(item => item.trim()),
                },
            };
        }

        if (element?.field === 'Room') {
            element.datasource = {
                ...element.datasource,
                fields: ['room_code', 'room_desc', 'service'],
                viewFields: ['room_code', 'room_desc'],
                roomFilter: {
                    field: 'service',
                    value: form.ServiceType,
                },
            };
        }
    }
}
