<script lang="ts">
    import { createEventDispatcher, onMount } from 'svelte';
    import { Button, Heading, Toggle } from 'flowbite-svelte';
    import Search from '$lib/shared/components/search/Search.svelte';
    import Table from './Table.svelte';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import Loading from '$lib/shared/components/loading/Loading.svelte';
    import { generateId } from '$lib/shared/util/customId';
    import { parseJSON } from '$lib/shared/util/dataConvert';
    import { POPUP_MESSAGES } from '$lib/shared/util/popupMessage';
    import { displayMessage, hasAccess } from '$lib/shared/util/validation';
    import { ColumnDataType, DataInputType, INV_STYLES, type ColumnName } from '$lib/shared/constants/general';
    import { TriggerService } from '$lib/service/trigger';
    import { onApplyAdvancedSearch } from '$lib/shared/util/advancedSearch';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faRefresh, faSave } from '@fortawesome/free-solid-svg-icons';
    import Select from './Select.svelte';
    import qs from 'query-string';
    import { ColumnType } from '$lib/shared/enum/search';
    import { SCOPE } from '$lib/shared/enum/general';

    export let figureName = '';
    export let baseURL;
    export let columnNames: ColumnName[] = [];
    export let searchColumns = null;
    export let defaultSortKey = '';
    export let toggleConfigName: string = null;
    export let toggleBars = [];
    export let triggerTableNames = null;

    export let validateAllData = null;
    export let validateNewData = null;
    export let subData = null;
    export let abilities = [];

    let tableData;
    let searchData;

    let popupModal = false;
    let modalTitle = 'Notification';
    let modalMessage = '';
    let onlyOkBtn = true;
    let confirmAction;
    let applyAdvancedSearch;
    let advancedSearchVariables: Record<string, any> = {};
    let advancedSearchLabels: Record<string, string> = {};
    let displayLabels: string[] = [];
    let resetSearchParams;
    let selectedDay = 'mon';
    let selectedTime = '12:30';
    let isCalculateAtTimePoint = false;
    let isCalculateAtTimePointChanged = false;

    let dayOptions = [
        { name: 'Monday', value: 'mon' },
        { name: 'Tuesday', value: 'tue' },
        { name: 'Wednesday', value: 'wed' },
        { name: 'Thursday', value: 'thu' },
        { name: 'Friday', value: 'fri' },
        { name: 'Saturday', value: 'sat' },
        { name: 'Sunday', value: 'sun' },
    ];
    let timeOptions = Array.from({ length: 48 }, (_, i) => {
        const hours = Math.floor(i / 2);
        const minutes = i % 2 === 0 ? 0 : 30;
        return {
            name: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`,
            value: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`,
        };
    });
    // for adv search
    $: if (!searchColumns && columnNames?.length) {
        searchColumns = columnNames?.map(col => ({ label: col.value, type: col.type, name: col.key }));
    }
    let searchText = '';
    let displayRecordIds = [];

    let newRecords = new Set();
    let selectedRow = null;
    let isDelete = false;
    let isLoading = false;
    let isRefresh = false;

    let updateList = [];

    let cfgParameter = null;
    const dispatch = createEventDispatcher<{ saveSuccess: boolean }>();

    type UpdatePosition = { id: string; columnName: string; isEditing: boolean };
    const defaultPosition: UpdatePosition = { id: '', columnName: '', isEditing: false };
    let updatePosition: UpdatePosition = structuredClone(defaultPosition);
    $: filterConfig = {
        page: '1',
        limit: '9999',
        day_of_week: selectedDay,
        time_point: selectedTime,
    };

    $: if (filterConfig) {
        getData();
    }
    const refreshAndClearSearch = () => {
        onRefresh?.();
        resetSearchParams?.();
    };
    const getData = async () => {
        const response = await fetch(`${baseURL}?${qs.stringify(filterConfig)}`);
        if (response.status === 200) {
            const json = await response.json();
            tableData = json?.data || [];
        } else {
            tableData = [];
        }

        if (tableData?.length) {
            displayRecordIds = tableData?.map(item => item.id);
            searchData = structuredClone(tableData);
            isCalculateAtTimePoint = tableData[0]['is_calculated_at_time_point'];
            if (subData) {
                searchData = searchData.map(row => {
                    let newRow = { ...row };
                    Object.entries(subData).forEach(([key, data]) => {
                        let text = '';
                        let vals = newRow[key];
                        if (vals) {
                            if (typeof vals === 'string') vals = vals.split(',');
                            vals.forEach(val => {
                                const row = data?.find(e => e.value === val);
                                text += row?.name || '';
                            });
                        }
                        newRow[key] = text;
                    });
                    return newRow;
                });
            }
        }
    };

    const loadCfgParameter = async () => {
        if (!toggleConfigName) return;

        try {
            let response = await fetch(`/api/CfgParameterService/${toggleConfigName}`, { method: 'GET' });
            let json = await response.json();
            cfgParameter = json.cfgParam;
            if (cfgParameter?.id) {
                const cfg = parseJSON(cfgParameter?.key_value_json);
                if (cfg) {
                    for (const [key, value] of Object.entries(cfg)) {
                        const idx = toggleBars?.findIndex(t => t.key === key);
                        if (idx !== -1) toggleBars[idx].value = value;
                    }
                }
            }
        } catch (e) {
            console.error('Fail to get cfg parameter', e);
        }
    };

    const mergeData = (tableData, updateList) => {
        const mergedMap = new Map();
        let mergeData = [];

        updateList.forEach(item => {
            mergedMap.set(item.id, { ...item });
        });

        tableData.forEach(item => {
            if (mergedMap.has(item.id)) {
                mergeData.push(mergedMap.get(item.id));
            } else {
                mergeData.push(item);
            }
        });
        return mergeData;
    };

    const showMessage = (message, isRefresh = false, title = null) => {
        popupModal = true;
        modalTitle = title ? title : 'Notification';
        modalMessage = message;
        onlyOkBtn = true;
        confirmAction = isRefresh ? async () => await onRefresh() : () => {};
    };

    const mapDataToSave = data => {
        return data?.map(item => {
            let newItem = { ...item };
            columnNames.forEach(col => {
                const colName = col.key;
                const colType = col.type;
                const colInputType = col.inputType;
                newItem[colName] = item[colName];

                // Specific case for List of Specialty
                if (colName === 'alt_tgt_days') {
                    newItem[colName] = item[colName] || item[colName] === 0 ? item[colName] + '' : null;
                }

                if (!newItem[colName]) {
                    if (colType === ColumnDataType.Boolean) newItem[colName] = false;
                    if (colType === ColumnDataType.String) {
                        newItem[colName] = '';
                        if (colInputType === DataInputType.MultiSelect) newItem[colName] = null;
                    }
                }
            });
            return newItem;
        });
    };

    const handleSaveData = async (data, isUpdated = false) => {
        const method = isUpdated ? 'PATCH' : 'POST';

        const res = await fetch(
            `${baseURL}?${qs.stringify({
                ...filterConfig,
                is_calculated_at_time_point: isCalculateAtTimePoint,
            })}`,
            {
                method,
                body: JSON.stringify({ data: mapDataToSave(data) }),
            }
        );
        if (res.status === 200) {
            const response = await res.json();
            console.log({ response });

            return response.res;
        }
        return false;
    };

    const saveData = async (tableData, updateList, newRecords) => {
        let validateMsg = '';

        if (!!validateNewData) {
            validateMsg = validateNewData(updateList);
            if (validateMsg) {
                showMessage(validateMsg);
                return;
            }
        }

        if (!!validateAllData) {
            const finalData = mergeData(tableData, updateList);
            validateMsg = validateAllData(finalData);
            if (validateMsg) {
                showMessage(validateMsg);
                return;
            }
        }

        let newTypes = [];
        let updateTypes = [];

        const tableSet = new Set(tableData.map(item => JSON.stringify(Object.entries(item).sort())));
        const newUpdateList = updateList?.filter(item => !tableSet.has(JSON.stringify(Object.entries(item).sort())));

        for (const row of newUpdateList) {
            if (newRecords.has(row.id)) {
                newTypes.push(row);
            } else {
                updateTypes.push(row);
            }
        }
        let isCreateOK = newTypes.length ? await handleSaveData(newTypes) : null;
        let isUpdateOK =
            updateTypes.length || isCalculateAtTimePointChanged ? await handleSaveData(updateTypes, true) : null;
        dispatch('saveSuccess', true);

        const { key, message } = displayMessage(isCreateOK, isUpdateOK);
        if (message) showMessage(message, true);
        if (!!toggleConfigName && key.includes('true')) TriggerService.trigger(triggerTableNames);
    };

    const onSave = async () => {
        isLoading = true;
        await saveData(tableData, updateList, newRecords);
        isLoading = false;
    };

    const deleteRows = async typeId => {
        const res = await fetch(baseURL, {
            method: 'DELETE',
            body: JSON.stringify({ data: [{ ID: typeId }] }),
        });
        if (res.status === 200) {
            const response = await res.json();
            return response.res;
        }

        return false;
    };

    const handleDeleteRow = async () => {
        isLoading = true;
        let result;
        if (!newRecords.has(selectedRow)) {
            result = await deleteRows(selectedRow);
            if (result) showMessage(POPUP_MESSAGES.deleteSuccess, true);
            else showMessage(POPUP_MESSAGES.deleteFail);
        }
        if (result == true || newRecords.has(selectedRow)) {
            tableData = tableData.filter(item => item.id !== selectedRow);
            updateList = updateList.filter(item => item.id !== selectedRow);
            selectedRow = null;
        }
        isLoading = false;
    };

    const onRefresh = async () => {
        isLoading = true;
        isRefresh = true;
        selectedRow = null;
        searchText = '';
        updateList = [];
        updatePosition = structuredClone(defaultPosition);
        await getData();
        isLoading = false;
    };

    onMount(async () => {
        isLoading = true;
        await getData();
        isLoading = false;
    });
    $: if (searchText !== null) {
        displayRecordIds = tableData
            ?.filter(record =>
                searchColumns
                    .filter(column => column.type === ColumnType.String || column.type === ColumnType.Number)
                    .some(column => {
                        return (record[column.name] + '')?.toLowerCase().includes(searchText.toLowerCase().trim());
                    })
            )
            .map(r => r.id);
    }
</script>

<div class={INV_STYLES.fullTable}>
    <div class={INV_STYLES.header}>
        <Heading tag="h5">{figureName}</Heading>
    </div>
    <div class="mx-2 my-4 flex gap-2">
        <Search
            bind:searchText
            bind:resetSearchParams
            columns={searchColumns}
            data={searchData}
            bind:applySearch={applyAdvancedSearch}
            on:apply={e => {
                if (!e?.detail) {
                    return;
                }
                const newData = onApplyAdvancedSearch(e, {
                    advancedSearchVariables,
                    advancedSearchLabels,
                    displayLabels,
                    displayRecordIds,
                    searchColumns,
                });
                advancedSearchVariables = newData.advancedSearchVariables;
                advancedSearchLabels = newData.advancedSearchLabels;
                displayLabels = newData.displayLabels;
                displayRecordIds = newData.displayRecordIds;
            }} />

        <div class="ml-3 flex items-center gap-3">
            <label class="min-w-[100px]">Refresh Day</label>
            <div class="min-w-[120px]">
                <Select
                    bind:value={selectedDay}
                    items={dayOptions}
                    isSearchable={false}
                    on:change={async event => {
                        selectedDay = event.detail.value;
                    }} />
            </div>
        </div>
        <div class="flex items-center gap-3">
            <Select
                bind:value={selectedTime}
                items={timeOptions}
                isSearchable={false}
                on:change={async event => {
                    selectedTime = event.detail.value;
                }} />
        </div>

        {#if hasAccess(abilities, SCOPE.TOGGLE)}
            <Toggle
                color="blue"
                bind:checked={isCalculateAtTimePoint}
                on:change={() => {
                    // Custom logic when user changes the toggle
                    // For example, you can call a function or set a flag here
                    isCalculateAtTimePointChanged = true;
                }}>
                Calculate at Refresh Point
            </Toggle>
        {/if}
        {#if hasAccess(abilities, SCOPE.UPDATE)}
            <Button size="md" color="light" class="dark:bg-gray-700" on:click={onSave}>
                <span class="mr-2">{@html icon(faSave).html}</span>
                <span class="text-nowrap">Save</span>
            </Button>
        {/if}

        <Button size="md" color="light" class="dark:bg-gray-700" on:click={refreshAndClearSearch}>
            <span class="mr-2">{@html icon(faRefresh).html}</span>
            <span class="text-nowrap">Refresh</span>
        </Button>
    </div>
    <div>
        {#if tableData}
            <Table
                data={tableData}
                {abilities}
                {columnNames}
                {displayRecordIds}
                {defaultSortKey}
                bind:isRefresh
                bind:updateList
                bind:updatePosition
                bind:selectedRow
                allowSelected={true} />
        {/if}
    </div>
</div>
<Notification
    bind:isOpen={isDelete}
    modalTitle="Confirmation"
    modalMessage={POPUP_MESSAGES.deleteConfirm}
    confirmAction={handleDeleteRow} />
<Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
<Loading bind:isOpen={isLoading} />
