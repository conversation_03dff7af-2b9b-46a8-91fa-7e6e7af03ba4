<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import Button from '$lib/core/components/Button/index.svelte';

    type HeaderGroup = {
        header: string;
        attributes: Array<ColumnData>;
    };

    export interface ResourceStatusByLocationConfig {
        title?: string;
        dataSource?: string;
        label?: string;
        category: string;
        groups: Array<HeaderGroup>;
        limit: number;
    }

    export interface ColumnData {
        label: string;
        key: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    export const configDefaults: ResourceStatusByLocationConfig = {
        dataSource: 'aoh_charts_active_trips',
        title: 'Active Trips',
        label: 'name',
        category: '',
        groups: [
            {
                header: '',
                attributes: [
                    {
                        label: 'School',
                        key: 'school',
                    },
                    {
                        label: 'In-Charge',
                        key: 'in_charge',
                    },
                    {
                        label: 'Start Date',
                        key: 'start_date',
                    },
                ],
            },
        ],
        limit: 8,
    };
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import {
        faArrowRight,
        faArrowTurnDown,
        faCaretDown,
        faMinus,
        faDatabase,
        faPlus,
        faSliders,
        faXmark,
        faXmarkCircle,
    } from '@fortawesome/free-solid-svg-icons';
    import { BUTTON_CLASS, DROPDOWN_CLASS, INPUT_CLASS, LABEL_CLASS, type Query } from '../../utils';

    import { slide } from 'svelte/transition';

    export let config: ResourceStatusByLocationConfig;

    let currentDatasource: Query = undefined;

    function updateDatasources() {
        dataSources = [];

        $dataSource.__schema?.types?.forEach(query => {
            if (config?.dataSource === query.name) currentDatasource = query;

            dataSources.push({
                label: query.name,
                value: query.name,
                action: () => {
                    currentDatasource = query;
                },
            });
        });

        dataSources = [
            {
                label: 'None',
                value: undefined,
                action: () => {
                    currentDatasource = undefined;
                },
            },
            ...dataSources,
        ];

        if (!dataSources.length) config.dataSource = '';
    }

    // List of data source names (valid GraphQL query roots)
    let dataSources: Array<DropdownItem> = [];
    let labelOptions: Array<DropdownItem> = [];

    updateDatasources();

    function reset() {
        labelOptions = [];
    }

    function update(query: Query) {
        if (!query) return;

        reset();

        query?.fields?.forEach(f => {
            if (f?.type?.ofType?.name === 'Int') {
            }
            labelOptions.push({
                label: f.name,
                value: f.name,
            });
        });

        labelOptions = [{ label: 'None', value: undefined }, ...labelOptions];

        labelOptions = labelOptions;
    }

    $: update(currentDatasource);
</script>

<div class="text-on-surface flex max-h-[600px] w-full flex-col gap-3 overflow-y-auto">
    <TextField placeholder={'Title'} bind:value={config.title} setClass={INPUT_CLASS} setLabelClass={LABEL_CLASS} />

    <!-- Datasource column-->
    <section class="flex flex-col gap-2">
        <header class="flex w-full items-center gap-2 text-xs">
            Data Source
            {@html icon(faDatabase).html}
        </header>

        <Dropdown
            title={'Data Source'}
            bind:value={config.dataSource}
            enableSearch={true}
            options={dataSources}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />
    </section>

    <div
        class="w-full {currentDatasource
            ? ''
            : 'pointer-events-none brightness-50'} flex flex-col gap-2 transition-colors duration-200 ease-in-out">
        <p class="py-2 text-center">{@html icon(faCaretDown).html}</p>

        <header class="flex w-full items-center gap-2 border-b-2 border-outline py-1 text-xs">
            Properties
            {@html icon(faSliders).html}
        </header>

        <Dropdown
            title={'Id'}
            bind:value={config.label}
            enableSearch={true}
            options={labelOptions}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />

        <Dropdown
            title={'Category'}
            bind:value={config.category}
            enableSearch={true}
            options={labelOptions}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />

        <!-- Configure Groups -->
        {#if config?.groups}
            <div class="mt-1 flex w-full items-center gap-2 pl-2 text-xs">
                Header Groups ({Object.entries(config.groups).length})
                <Button
                    on:click={() => {
                        config.groups.push({
                            header: '',
                            attributes: [],
                        });

                        config.groups = config.groups;
                    }}
                    setClass="ml-auto {BUTTON_CLASS}">{@html icon(faPlus).html}</Button>
            </div>

            {#each config?.groups as headerGroup, index}
                <section class="text-on-surface mt-2 pl-4 text-sm" transition:slide={{ axis: 'y' }}>
                    <div class="">
                        <header class="flex w-full items-center gap-2">
                            <p class="h-full">{@html icon(faArrowTurnDown).html}</p>
                            <input bind:value={headerGroup.header} class="{INPUT_CLASS} grow focus:outline-none" />

                            <Button
                                setClass="hover:brightness-150 duration-200 transitions-color ease-in-out"
                                on:click={() => {
                                    config.groups.splice(index, 1);
                                    config.groups = config.groups;
                                }}>{@html icon(faXmarkCircle).html}</Button>
                        </header>

                        <div class="ml-4 mt-1 flex flex-col">
                            {#each headerGroup.attributes as attribute, y}
                                <span
                                    class="my-1 flex w-full items-center justify-between gap-2 px-2"
                                    transition:slide={{ axis: 'y' }}>
                                    <Dropdown
                                        placeholder="Attribute"
                                        options={labelOptions}
                                        bind:value={attribute.key}
                                        appendMenuClass="w-full max-h-[150px]"
                                        setClass={`text-xs h-8 w-2/5`}
                                        setLabelClass={LABEL_CLASS + 'text-xs'} />

                                    <p class="text-xs">{@html icon(faArrowRight).html}</p>

                                    <input bind:value={attribute.label} class={`${INPUT_CLASS} h-8 w-2/5 pl-2`} />

                                    <Button
                                        setClass={BUTTON_CLASS}
                                        on:click={() => {
                                            headerGroup.attributes.splice(index, 1);
                                            headerGroup.attributes = headerGroup.attributes;
                                        }}>{@html icon(faXmark).html}</Button>
                                </span>
                            {/each}

                            <Button
                                on:click={() => {
                                    headerGroup.attributes.push({
                                        label: `HEADER GROUP ${headerGroup.attributes.length} `,
                                        key: '',
                                    });
                                    headerGroup.attributes = headerGroup.attributes;
                                }}
                                setClass="w-fit px-1 ml-auto text-end text-2xs {BUTTON_CLASS}">
                                ...Add an attribute.
                            </Button>
                        </div>
                    </div>
                </section>
            {/each}
        {/if}
    </div>
</div>
