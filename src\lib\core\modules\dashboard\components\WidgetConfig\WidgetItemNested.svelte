<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import { type ComponentType } from 'svelte/internal';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    export interface WidgetComponent {
        id?: string;
        path?: any[];
        componentDisplay?: ComponentType;
        extraProps?: Record<string, any>;
        children?: WidgetComponent[];
        checkVisible?: string;
    }
</script>

<script lang="ts">
    import WidgetConfigItemNestedBind from './WidgetItemNestedBind.svelte';

    export let config: any;
    export let widgetFields: WidgetComponent[];
</script>

{#each widgetFields as item}
    {#if !item.checkVisible || config[item.checkVisible]}
        {#if !item.children}
            {#if !item.path}
                <svelte:component
                    this={item.componentDisplay}
                    {...item.extraProps}
                    bind:value={config[item.id]}
                    bind:checked={config[item.id]} />
            {:else}
                <WidgetConfigItemNestedBind
                    bind:configNested={config}
                    path={item.path}
                    componentDisplay={item.componentDisplay}
                    extraProps={item.extraProps} />
            {/if}
        {:else}
            <div class={item.extraProps?.customClass}>
                <svelte:self widgetFields={item.children} bind:config />
            </div>
        {/if}
    {/if}
{/each}
