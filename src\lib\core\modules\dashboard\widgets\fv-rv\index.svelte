<script lang="ts" context="module">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { logger } from '$lib/stores/Logger';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import type { FvRvConfig } from './WidgetConfig/index.svelte';
    import type { EChartsOption, EChartsType } from 'echarts';
    import { queryStore, subscriptionStore } from '@urql/svelte';
    import {
        GetClinicsDocument,
        type GetClinicsQuery,
        GetFilteringByUserIdDocument,
        type GetFilteringByUserIdQuery,
        GetFvRvAttendanceStreamingDocument,
        type GetFvRvAttendanceStreamingSubscription,
        GetSpecialtiesByGroupsDocument,
        type GetSpecialtiesByGroupsQuery,
        GetPatientTypesDocument,
        type GetPatientTypesQuery,
        type Cc3_Soc_Filtering,
        type Cc3_Soc_Fv_Rv_Attendances,
        type Cc3_Soc_Clinics,
        type Cc3_Soc_Specialties,
        type Cc3_Soc_Patient_Types,
        type Cc3_Soc_Visit_Type_Groups,
        type GetVisitTypeGroupsQuery,
        GetVisitTypeGroupsDocument,
    } from '$generated-types';
    import weekOfYear from 'dayjs/plugin/weekOfYear';
    import quarterOfYear from 'dayjs/plugin/quarterOfYear';
    import lodash from 'lodash';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faEye, faFilter, faGear } from '@fortawesome/free-solid-svg-icons';

    const groupBy = lodash.groupBy;
    const sumBy = lodash.sumBy;
    
    dayjs.extend(isToday);
    dayjs.extend(weekOfYear);
    dayjs.extend(quarterOfYear);

    export const widgetIcon = faEye;
    export const widgetCategory = 'Specialist Outpatient Clinics';
    export const widgetLimit = 999;
    export const widgetTitle = 'First Visit: Re-Visit';
    export const widgetEnabled = true;
    export const widgetMinWidth = 12;
    export const widgetMinHeight = 6;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { onMount } from 'svelte';
    import {
        Button,
        Heading,
        Modal,
        Table,
        TableBody,
        TableBodyCell,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Tooltip,
    } from 'flowbite-svelte';
    import Filtering from '$lib/core-soc/modules/filtering/Filtering.svelte';
    import { VIEW_CHART_BY_ENUM } from '$lib/shared/enum/general';
    import { user } from '$lib/stores/Auth';

    export let config: FvRvConfig = {
        title: 'First Visit : Re-Visit',
        resolution: 1000,
        showLabel: false,
    };

    const client = get(gqlClientStore);
    let user_id = '';

    let filteringOpen = false;

    const labelOption = {
        show: config.showLabel,
        position: 'insideBottom',
        distance: 15,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 16,
        rich: {
            name: {},
        },
    };

    let chartContainer: HTMLElement;
    let chart: EChartsType;
    const defaultOption: EChartsOption = {
        title: {
            text: config.title,
            subtext: `ratio 1:${config.resolution}`,
            textStyle: {
                color: '#FFF',
            },
        },
        grid: {
            containLabel: true,
            bottom: '3%',
            right: '10%',
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
            },
        },
        toolbox: {
            show: true,
            feature: {
                saveAsImage: { show: true },
                dataView: { show: true, readOnly: false },
                dataZoom: { show: true },
            },
        },
        xAxis: {
            type: 'category',
            data: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value} W',
            },
            axisPointer: {
                snap: true,
            },
        },
        legend: {
            data: ['FV:RV', 'FV Attendance', 'RV Attendance', 'FV No Show', 'RV No Show'],
            textStyle: {
                color: '#FFF',
            },
            right: '0%',
            bottom: '30%',
            orient: 'vertical',
        },
        series: [
            {
                name: 'FV:RV',
                type: 'line',
                smooth: true,
                data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120],
            },
            {
                name: 'FV Attendance',
                data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120],
                barGap: 0,
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
            {
                name: 'RV Attendance',
                data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120],
                barGap: 0,
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
            {
                name: 'FV No Show',
                data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120],
                barGap: 0,
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
            {
                name: 'RV No Show',
                data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120],
                barGap: 0,
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
        ],
    };
    let option = structuredClone(defaultOption);

    const defaultFiltering: Cc3_Soc_Filtering = {
        clinic_ids: [],
        id: undefined,
        specialty_ids: [],
        patient_type_ids: [],
        visit_type_group_ids: [],
        user_id: undefined,
        view_chart_by: VIEW_CHART_BY_ENUM.MONTH,
        view_date_from: dayjs().add(-1, 'months'),
        view_date_to: dayjs(),
    };

    let filtering: Cc3_Soc_Filtering;
    let visitTypeGroups: Cc3_Soc_Visit_Type_Groups[] = [];
    let clinics: Cc3_Soc_Clinics[] = [];
    let specialties: Cc3_Soc_Specialties[] = [];
    let patientTypes: Cc3_Soc_Patient_Types[] = [];

    let fvRvData: {
        [key: string]: Cc3_Soc_Fv_Rv_Attendances[];
    }; // or Record<string, Cc3_Soc_Fv_Rv_Attendance[]>

    const getFilteringConfig = () => {
        let filteringConfig = queryStore<GetFilteringByUserIdQuery>({
            client,
            query: GetFilteringByUserIdDocument,
            variables: { user_id },
        });

        const unsubscriber = filteringConfig.subscribe(res => {
            if (!res.fetching && !res.error) {
                filtering = res.data.cc3_soc_filtering?.[0];
                unsubscriber();
            } else if (!res.fetching && res.error) {
                filtering = structuredClone(defaultFiltering);
                unsubscriber();
            }
        });
    };

    const getVisitTypeGroups = () => {
        const query = queryStore<GetVisitTypeGroupsQuery>({
            client,
            query: GetVisitTypeGroupsDocument,
        });
        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                visitTypeGroups = res.data.cc3_soc_visit_type_groups;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                visitTypeGroups = [];
                unsubscriber();
            }
        });
    };

    const getClinics = () => {
        const query = queryStore<GetClinicsQuery>({
            client,
            query: GetClinicsDocument,
        });
        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                clinics = res.data.cc3_soc_clinics;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                clinics = [];
                unsubscriber();
            }
        });
    };

    const getSpecialties = () => {
        let query = queryStore<GetSpecialtiesByGroupsQuery>({
            client,
            query: GetSpecialtiesByGroupsDocument,
            variables: { groups: ['SOC', 'AHS'] },
        });

        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                specialties = res.data.cc3_soc_specialties;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                specialties = [];
                unsubscriber();
            }
        });
    };

    const getPatientTypes = () => {
        const query = queryStore<GetPatientTypesQuery>({
            client,
            query: GetPatientTypesDocument,
        });
        const unsubscriber = query.subscribe(res => {
            if (!res.fetching && !res.error) {
                patientTypes = res.data.cc3_soc_patient_types;
                unsubscriber();
            } else if (!res.fetching && res.error) {
                patientTypes = [];
                unsubscriber();
            }
        });
    };

    const updateXAxis = () => {
        option.xAxis = {
            type: 'category',
            data: Object.keys(fvRvData),
            axisLabel: {
                margin: 10,
            },
            axisPointer: {
                type: 'shadow',
            },
        };
    };

    const updateYAxis = () => {
        option.yAxis = [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}',
                    inside: false,
                },
                axisPointer: {
                    snap: true,
                },
            },
        ];
    };

    const updateSeries = () => {
        option.series = [
            {
                name: 'FV Attendance',
                yAxisIndex: 0,
                data: Object.values(fvRvData).map(d => {
                    return {
                        value: sumBy(d, 'fv_attendance') / config.resolution,
                        itemStyle: { color: '#91cc75' },
                    };
                }),
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
            {
                name: 'RV Attendance',
                yAxisIndex: 0,
                data: Object.values(fvRvData).map(d => {
                    return {
                        value: sumBy(d, 'rv_attendance') / config.resolution,
                        itemStyle: { color: '#fac858' },
                    };
                }),
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
            {
                name: 'FV No Show',
                yAxisIndex: 0,
                data: Object.values(fvRvData).map(d => {
                    return {
                        value: sumBy(d, 'fv_no_show') / config.resolution,
                        itemStyle: { color: '#ee6666' },
                    };
                }),
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
            {
                name: 'RV No Show',
                yAxisIndex: 0,
                data: Object.values(fvRvData).map(d => {
                    return {
                        value: sumBy(d, 'rv_no_show') / config.resolution,
                        itemStyle: { color: '#73c0de' },
                    };
                }),
                type: 'bar',
                label: labelOption as any,
                emphasis: {
                    focus: 'series',
                },
            },
            {
                name: 'FV:RV',
                type: 'line',
                smooth: true,
                yAxisIndex: 0,
                data: Object.values(fvRvData).map(d => {
                    return {
                        value: (sumBy(d, 'fv_attendance') / sumBy(d, 'rv_attendance')).toFixed(3),
                        itemStyle: { color: '#9a60b4' },
                    };
                }),
                lineStyle: { color: '#5470c6', width: 4 },
            },
        ];
    };

    const mergeData = (data: Cc3_Soc_Fv_Rv_Attendances[]) => {
        data.sort((a, b) => {
            return new Date(a.day).getTime() - new Date(b.day).getTime();
        });

        switch (filtering.view_chart_by) {
            case VIEW_CHART_BY_ENUM.DAY:
                fvRvData = groupBy(data, item => {
                    return dayjs(item.day).format('DD-MM-YYYY');
                });
                break;
            case VIEW_CHART_BY_ENUM.WEEK:
                fvRvData = groupBy(data, item => {
                    return `${dayjs(item.day).week()}-${dayjs(item.day).year()}`;
                });
                break;
            case VIEW_CHART_BY_ENUM.MONTH:
                fvRvData = groupBy(data, item => {
                    return dayjs(item.day).format('MM-YYYY');
                });
                break;
            case VIEW_CHART_BY_ENUM.QUARTER:
                fvRvData = groupBy(data, item => {
                    return `${dayjs(item.day).quarter()}-${dayjs(item.day).year()}`;
                });
                break;
            case VIEW_CHART_BY_ENUM.YEAR:
                fvRvData = groupBy(data, item => {
                    return dayjs(item.day).format('YYYY');
                });
                break;
            default:
                fvRvData = groupBy(data, item => {
                    return dayjs(item.day).format('MM-YYYY');
                });
                break;
        }
    };

    const updateDataOption = async (data: Cc3_Soc_Fv_Rv_Attendances[]) => {
        mergeData(data);
        updateXAxis();
        updateYAxis();
        updateSeries();
        if (option && chart) {
            chart.dispose();
            const echarts = await import('echarts');
            chart = echarts.init(chartContainer);
            chart.clear();
            chart.setOption({
                ...chart.getOption(),
                ...option,
            });
        }
    };

    let fvRv;
    $: if (filtering)
        fvRv = subscriptionStore<GetFvRvAttendanceStreamingSubscription>({
            client,
            query: GetFvRvAttendanceStreamingDocument,
            variables: {
                fromDate: filtering.view_date_from,
                toDate: filtering.view_date_to,
                clinic_ids: filtering.clinic_ids,
                specialty_ids: filtering.specialty_ids,
                patient_type_ids: filtering.patient_type_ids,
            },
        });

    $: if ($fvRv?.data?.cc3_soc_fv_rv_attendances?.length > 0 && filtering) {
        updateDataOption($fvRv.data.cc3_soc_fv_rv_attendances);
    }

    const onGearClick = () => {
        filteringOpen = true;
    };

    onMount(async () => {
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, () => {
            if (chart) {
                chart.resize();
            }
        });

        const echarts = await import('echarts');

        if (chartContainer) chart = echarts.init(chartContainer);
        if (chart) {
            chart.setOption(defaultOption);
        }

        window.addEventListener('resize', () => {
            if (chart) {
                chart.resize();
            }
        });
        user_id = get(user).claims.hasura_access['x-hasura-user-id']
        if (user_id) {
            getFilteringConfig();
            getClinics();
            getSpecialties();
            getPatientTypes();
            getVisitTypeGroups;
        }
    });
</script>

<div class="flex h-full flex-col justify-between gap-3 p-3 text-on-surface-1">
    <div class="flex items-center justify-between">
        <Heading tag="h5">Total Appointments</Heading>
        <Button size="md" on:click={onGearClick}>
            <span>
                {@html icon(faGear).html}
            </span>
            <Tooltip>
                {`Filter configuration`}</Tooltip>
        </Button>
    </div>
    <div class="grid grid-cols-12">
        <div class="col-span-10 max-h-[260px] flex-1 overflow-auto rounded-lg">
            <Table striped={true}>
                <TableHead>
                    <TableHeadCell>{filtering?.view_chart_by}</TableHeadCell>
                    <TableHeadCell>No. of FV Attendance</TableHeadCell>
                    <TableHeadCell>No. Of RV Attendance</TableHeadCell>
                    <TableHeadCell>No. Of FV No Show</TableHeadCell>
                    <TableHeadCell>No. Of RV No Show</TableHeadCell>
                    <TableHeadCell>RV:FV Attendance</TableHeadCell>
                </TableHead>
                <TableBody>
                    {#if fvRvData}
                        {#each Object.keys(fvRvData) as key}
                            <TableBodyRow>
                                <TableBodyCell>
                                    {key}
                                </TableBodyCell>
                                <TableBodyCell>
                                    {sumBy(fvRvData[key], 'fv_attendance').toLocaleString()}
                                </TableBodyCell>
                                <TableBodyCell>
                                    {sumBy(fvRvData[key], 'rv_attendance').toLocaleString()}
                                </TableBodyCell>
                                <TableBodyCell>
                                    {sumBy(fvRvData[key], 'fv_no_show').toLocaleString()}
                                </TableBodyCell>
                                <TableBodyCell>
                                    {sumBy(fvRvData[key], 'rv_no_show').toLocaleString()}
                                </TableBodyCell>
                                <TableBodyCell>
                                    {`${(sumBy(fvRvData[key], 'rv_attendance') / sumBy(fvRvData[key], 'fv_attendance')).toFixed(0)}`}
                                    <!-- {(
                                        sumBy(fvRvData[key], 'fv_attendance') / sumBy(fvRvData[key], 'rv_attendance')
                                    ).toLocaleString()} -->
                                </TableBodyCell>
                            </TableBodyRow>
                        {/each}
                    {/if}
                </TableBody>
            </Table>
        </div>
        <div class="col-span-2 flex flex-col gap-2 px-2">
            {#if specialties?.length && clinics?.length && patientTypes?.length && visitTypeGroups?.length && filtering}
                <div class="flex">
                    <div class="flex h-10 flex-col items-center justify-center bg-gray-800 p-3">
                        <span>
                            {@html icon(faFilter).html}
                        </span>
                    </div>
                    <span
                        class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                        >{`${dayjs(filtering.view_date_from).format('MMM YYYY')} to ${dayjs(
                            filtering.view_date_to
                        ).format('MMM YYYY')}`}</span>
                    <Tooltip>
                        {`${dayjs(filtering.view_date_from).format('DD/MM/YYYY')} to ${dayjs(
                            filtering.view_date_to
                        ).format('DD/MM/YYYY')}`}</Tooltip>
                </div>

                <div class="flex">
                    <div class="flex h-10 flex-col items-center justify-center bg-gray-800 p-3">
                        <span>
                            {@html icon(faFilter).html}
                        </span>
                    </div>
                    <span
                        class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                        >{`${filtering.visit_type_group_ids
                            ?.slice(0, 1)
                            ?.map(id => visitTypeGroups?.find(v => v.id === id)?.name)
                            ?.filter(name => name)
                            ?.join(', ')} ...`}</span>
                    <Tooltip>
                        {filtering.visit_type_group_ids
                            ?.map(id => visitTypeGroups?.find(v => v.id === id)?.name)
                            ?.filter(name => name)
                            ?.join(', ')}
                    </Tooltip>
                </div>

                <div class="flex">
                    <div class="flex h-10 flex-col items-center justify-center bg-gray-800 p-3">
                        <span>
                            {@html icon(faFilter).html}
                        </span>
                    </div>
                    <span
                        class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                        >{`${filtering.clinic_ids
                            ?.slice(0, 1)
                            ?.map(id => clinics.find(s => s.id === id)?.description)
                            ?.filter(des => des)
                            ?.join(', ')} ...`}</span>
                    <Tooltip>
                        {filtering.clinic_ids
                            ?.map(id => clinics.find(c => c.id === id)?.description)
                            ?.filter(des => des)
                            ?.join(', ')}</Tooltip>
                </div>

                <div class="flex">
                    <div class="flex h-10 flex-col items-center justify-center bg-gray-800 p-3">
                        <span>
                            {@html icon(faFilter).html}
                        </span>
                    </div>
                    <span
                        class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                        >{`${filtering.specialty_ids
                            .slice(0, 1)
                            .map(id => specialties.find(s => s.id === id)?.description)
                            ?.filter(des => des)
                            .join(', ')} ...`}</span>
                    <Tooltip>
                        {filtering.specialty_ids
                            ?.map(id => specialties.find(s => s.id === id)?.description)
                            ?.filter(des => des)
                            ?.join(', ')}
                    </Tooltip>
                </div>

                <div class="flex">
                    <div class="flex h-10 flex-col items-center justify-center bg-gray-800 p-3">
                        <span>
                            {@html icon(faFilter).html}
                        </span>
                    </div>
                    <span
                        class="my-auto flex h-10 w-full flex-col items-center justify-center overflow-hidden bg-gray-500 p-3 text-center"
                        >{`${filtering.patient_type_ids
                            ?.slice(0, 1)
                            ?.map(id => patientTypes.find(p => p.id === id)?.description)
                            ?.filter(des => des)
                            ?.join(', ')} ...`}</span>
                    <Tooltip>
                        {filtering.patient_type_ids
                            ?.map(id => patientTypes.find(p => s.id === id)?.description)
                            ?.filter(des => des)
                            ?.join(', ')}</Tooltip>
                </div>
            {/if}
        </div>
    </div>

    <div id="fv-rv-chart" class="mt-5 flex h-full w-full flex-1 flex-col">
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <div on:click data-tag="chart-container" class="h-full w-full" bind:this={chartContainer} />
    </div>
</div>

<Modal title="Filter Configuration for Appointments" bind:open={filteringOpen} size="lg">
    <Filtering
        userId={user_id}
        on:onSave={result => {
            filtering = result.detail;
        }} />
</Modal>
