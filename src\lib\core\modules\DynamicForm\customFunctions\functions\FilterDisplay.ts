export default function FilterDisplay(form, fields, fieldList) {
    for (let index = 0; index < fields.length; index++) {
        const element = fields[index];

        let checkField = element?.field;

        if (checkField) {
            if (fieldList?.includes(checkField)) {
                element.datasource = {
                    ...element.datasource,
                    viewFields: ['code', 'desc', 'category'],
                    filter: {
                        field: 'is_filter_display',
                        value: true,
                    },
                };
            }
        } else {
            checkField = element?.subFields;
            if (!checkField?.length) continue;

            for (let i = 0; i < checkField.length; i++) {
                const element = checkField[i];

                if (fieldList?.includes(element?.field)) {
                    element.datasource = {
                        ...element.datasource,
                        viewFields: ['code', 'desc', 'category'],
                        filter: {
                            field: 'is_filter_display',
                            value: true,
                        },
                    };
                }
            }
        }
    }
}
