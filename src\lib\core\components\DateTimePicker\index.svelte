<script lang="ts">
    import Button from '$lib/core/components/Button/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCalendarDay } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import CustomParseFormat from 'dayjs/plugin/customParseFormat';
    import LocalizedFormat from 'dayjs/plugin/localizedFormat';

    import { onMount } from 'svelte';

    dayjs.extend(CustomParseFormat);
    dayjs.extend(LocalizedFormat);

    export let value: string = undefined; // DOM datetime-local format 'YYYY-MM-DD[T]HH:mm'
    export let label = '';
    export let setClass =
        'border-b border-b-outline text-xs xl:text-sm xl:h-12 h-10 w-[12rem] border-b px-2 text-on-surface-2';
    export let labelClass = '';
    export let buttonIcon = faCalendarDay;
    export let format = 'L LT ';
    export let disabled: boolean = false;
    export let step: number = 60;
    export let min: string = undefined;
    export let max: string = undefined;
    export let overrideDisplayValue: ISO8601Date = undefined;
    export let customValidation: (event: Event & { currentTarget: HTMLInputElement }) => void = undefined;

    let elem: HTMLInputElement;

    onMount(() => {
        if (elem && customValidation) {
            elem.addEventListener('change', customValidation);
        }
    });
</script>

<div
    class:opacity-50={disabled}
    class:pointer-events-none={disabled}
    class="flex min-w-[150px] {setClass} flex-col justify-center">
    <span class="w-full text-3xs {labelClass}">{label}</span>
    <p class="flex items-center justify-between gap-2">
        {overrideDisplayValue
            ? dayjs(overrideDisplayValue).format(format)
            : dayjs(value).isValid()
            ? dayjs(value).format(format)
            : '--/--/---- --:--:--'}
        <Button
            on:click={() => {
                elem.showPicker();
            }}
            setDisabled={disabled}
            setClass="hover:text-surface-variant">{@html icon(buttonIcon).html}</Button>
    </p>
    <input
        {disabled}
        type="datetime-local"
        class="invisible absolute border"
        {step}
        bind:this={elem}
        bind:value
        on:input
        on:change
        on:focusin
        on:focus
        on:focusout
        {min}
        {max} />
</div>
