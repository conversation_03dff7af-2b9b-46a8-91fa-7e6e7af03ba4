<script lang="ts" context="module">
    export interface WidgetDetail {
        widgetId: string;
        widgetItem: HTMLElement;
        component: typeof self;
    }

    export const DEFAULT_WIDTH = 1;
    export const DEFAULT_HEIGHT = 1;
    export const DEFAULT_MIN_WIDTH = 1;
    export const DEFAULT_MIN_HEIGHT = 1;
    export const DEFAULT_MAX_WIDTH = 0;
    export const DEFAULT_MAX_HEIGHT = 0;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    type ComponentEvents = {
        select: WidgetDetail;
        edit: WidgetDetail;
        delete: WidgetDetail;
        triggerTables: any;
        openAddList: WidgetDetail;
        deleteSubGrid: WidgetDetail;
        iamsSync: any;
    };

    export type WidgetItemSelectEvent = CustomEvent<WidgetDetail>;
</script>

<!-- @component
    WidgetItem's are designed to be used in the widget system utilizing gridstack.js

    Props:
        - id: A unique identifier
        - title: The title to display for the widget
        - editable: Wether or not the edit button and delete buttons are visible - used to edit the configuration options for the widget;
        - options: The positioning and sizing of the WidgetItem { x: number, y: number, w: number, h: number }
        - onResize: A callback function to run whenever the WidgetItem is resized.
        - self: A reference to the component

    // TODO: Write documentation for exported functions when finalized

    Usage:
        ```tsx
        <WidgetGrid> <WidgetItem> <OtherComponents props={'Hi Mom'}/> </WidgetItem> </WidgetGrid>
        ```
 -->
<script lang="ts">
    import Button from '$lib/core/components/Button/index.svelte';
    import { isDirty } from '$lib/core/components/PageTitle/index.svelte';
    import { GRID_STACK_CONTEXT } from '$lib/core/modules/dashboard/components/WidgetGrid/index.svelte';
    import { logger } from '$lib/stores/Logger';
    import { faGear, faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
    import type { GridStack, GridStackNode } from 'gridstack';
    import { createEventDispatcher, getContext, onDestroy, onMount, setContext, tick } from 'svelte';
    import type { FormEventHandler } from 'svelte/elements';
    import type { Writable } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import { gqlClientStore } from '$lib/stores/Clients';
    import gql from 'graphql-tag';
    import { get } from 'svelte/store';
    import { user } from '$lib/stores/Auth';
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { locations } from '$lib/stores/Locations';
    import { DATA_SOURCE } from '$lib/shared/enum/general';
    import * as _ from 'lodash';
    import { deepSort } from '../../utils/utils';

    const dispatch = createEventDispatcher<ComponentEvents>();
    const IncidentMonitoringURL = `${get(locations).incident_monitoring_web}`;

    export function sendDataToDashboard(payload?: any) {
        dispatch('triggerTables', payload ?? {});
    }

    export let id: string;
    export let title: string;
    export let editable = false;
    export let options: GridStackNode & { broadcast?: boolean } = undefined;
    export let isSubGrid = false;
    export let parent_grid_id: string;
    export let fullyInitialized = false;
    export let draggingWidgetData = null;
    export let draggingRootWidgetData = null;
    export let column = null;
    export let updateWidgetFiltering;
    export let dataSourceId: string;
    export let cleanup_close_widget_item_config: () => void;

    let parentGrid: GridStack;
    let selected = false;
    let sysDatasource = {
        defaultData: 0,
        sendDataToDashboard,
    };
    let subscriptions: Subscription[] = [];

    interface HTMLElementGridStack extends HTMLElement {
        gridstackNode: GridStackNode;
    }

    // Gridstack Types don't seem accurate
    let widgetItemElement: (HTMLElement | HTMLElementGridStack) & { broadcast?: boolean }; //GristackWidget

    if (!getContext<Writable<GridStack>>(GRID_STACK_CONTEXT)) {
        throw new Error('no widget grid context found - widget Items can only be created inside widget grids');
    }

    // Get the GridStack this widget is in
    let gridStack = getContext<Writable<GridStack>>(GRID_STACK_CONTEXT);

    export function setSelected(newSelected: boolean) {
        selected = newSelected;
    }

    export function getSelected() {
        return selected;
    }

    export function getGridstackNode() {
        return (widgetItemElement as HTMLElementGridStack).gridstackNode;
    }

    function areDataSourcesEqual(current, newData) {
        if (!current && !newData) return true;
        if (!current || !newData) return false;

        const currentClone = _.cloneDeep(current);
        const newDataClone = _.cloneDeep(newData);

        delete currentClone.sendDataToDashboard;
        delete newDataClone.sendDataToDashboard;

        const sortedCurrentJson = deepSort(currentClone?.json_values);
        const sortedNewJson = deepSort(newDataClone?.json_values);

        return _.isEqual(sortedNewJson, sortedCurrentJson) && currentClone.user_id === newDataClone.user_id;
    }

    const selectWidget: FormEventHandler<HTMLDivElement> = (_e: Event) => {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };

        dispatch('select', detail);
    };

    const editWidget = (_e: MouseEvent) => {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };
        $isDirty = true;

        dispatch('edit', detail);
    };

    const deleteWidget = (_e: MouseEvent) => {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };

        dispatch('delete', detail);
    };

    const deleteSubGrid = (_e: MouseEvent) => {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };

        dispatch('deleteSubGrid', detail);
    };

    function openAddList() {
        let detail: WidgetDetail = {
            widgetId: id,
            widgetItem: widgetItemElement,
            component: self,
        };

        dispatch('openAddList', detail);
    }

    let mounted = false;
    let initialized = false;

    const transformOptions = (options: GridStackNode) => ({
        ...(options || {}),
        x: options.x ?? 0,
        y: options.y ?? 0,
        autoPosition: options.autoPosition ?? false,
        w: options.w ?? DEFAULT_WIDTH,
        h: options.h ?? DEFAULT_HEIGHT,
        minW: options.minW ?? DEFAULT_MIN_WIDTH,
        minH: options.minH ?? DEFAULT_MIN_HEIGHT,
        maxW: options.maxW ?? DEFAULT_MAX_WIDTH,
        maxH: options.maxH ?? DEFAULT_MAX_HEIGHT,
    });

    // Add component level 1 (doesn't have parent_grid_id) to dashboard grid
    $: if (!parent_grid_id && $gridStack && mounted && !initialized) {
        (async () => {
            if (!parent_grid_id) {
                parentGrid = $gridStack;
                initialized = true;
            }
            options = transformOptions(options);

            // Wait for options to update the DOM before making widget
            await tick();

            if (widgetItemElement) {
                // Store the broadcast flag for reading in the event handler - if we don't want it broadcast,
                // we won't process it in the handler - hacky workaround
                widgetItemElement.broadcast = options.broadcast;

                const subgrid = parentGrid.makeWidget(widgetItemElement, {
                    ...options,
                });

                if (isSubGrid) {
                    const grid = subgrid.gridstackNode?.subGrid;

                    if (!grid) {
                        return;
                    }

                    grid.on('added removed change', function (event, element) {
                        resize(event, element);
                        if ((editable && event.type === 'change') || event.type === 'added') {
                            // KIV: Unverified assumption that detail is always there...
                            let el = event['detail'][0]['el'];

                            if (el && el.broadcast) {
                                $isDirty = true;
                            }
                        }

                        if ((editable && event.type === 'change') || event.type === 'removed') {
                            const parentCols = (grid.el.closest('.grid-stack-item') as HTMLElementGridStack)
                                ?.gridstackNode?.w;

                            const requiredCols = getMaxColumn(grid?.engine?.nodes);
                            if (requiredCols !== grid.getColumn() && parentCols < requiredCols) {
                                grid.column(requiredCols, 'none');
                            } else if (requiredCols < grid.getColumn() && requiredCols < parentCols) {
                                grid.column(parentCols, 'none');
                            }
                            if (grid.getColumn() === parentCols) {
                                grid.el.closest('.grid-stack-item-content')?.classList.add('!overflow-x-hidden');
                            }
                        }
                    });

                    grid.on('added', (e, items) => {
                        for (const item of items) {
                            if (draggingWidgetData && item.id === draggingWidgetData.id) {
                                if (draggingWidgetData.w + draggingWidgetData.x > grid.getColumn()) {
                                    grid.column(draggingWidgetData.w + draggingWidgetData.x, 'none');
                                    grid.update(item.el, { w: draggingWidgetData.w });
                                    draggingWidgetData = {};

                                    grid.el.closest('.grid-stack-item-content')?.classList.remove('!overflow-x-hidden');
                                }
                                try {
                                    updateWidgetFiltering(id, item.id);
                                } catch (e) {
                                    log.error(e);
                                }
                            }
                        }
                    });

                    grid.on('dragstart', (e, el) => {
                        cleanup_close_widget_item_config?.();
                        const node = el?.gridstackNode;
                        if (node) {
                            draggingWidgetData = {
                                id: node.id,
                                w: node.w,
                                h: node.h,
                                x: 0,
                                y: 0,
                            };
                        }
                    });

                    grid.on('drag', (e, el) => {
                        const node = el?.gridstackNode;
                        if (node) {
                            draggingRootWidgetData = {
                                id: node.id,
                                w: node.w,
                                h: node.h,
                                x: node.x,
                                y: node.y,
                            };
                        }
                    });

                    setTimeout(() => {
                        if (column > options.w) {
                            grid.column(column, 'none');
                            grid.el.closest('.grid-stack-item-content')?.classList.remove('!overflow-x-hidden');
                        }
                    }, 200);
                }

                // Only don't broadcast at the start?
                widgetItemElement.broadcast = true;
            } else {
                log.warn({ title, widgetItemElement }, 'Possible missing widget (skipped due to element ref missing)');
            }
        })();
    }

    function getMaxColumn(nodes) {
        return Math.max(...nodes.map(n => (n.x ?? 0) + (n.w ?? 1)), 6);
    }

    // Add component level 2 (has parent_grid_id) to subgrid
    $: if (fullyInitialized && parent_grid_id && mounted && !initialized && $gridStack) {
        (async () => {
            await tick();
            parentGrid = $gridStack?.engine?.nodes?.find(n => n.id === parent_grid_id)?.subGrid;
            if (parentGrid) {
                options = transformOptions(options);

                // Wait for options to update the DOM before making widget
                await tick();
                if (widgetItemElement) {
                    widgetItemElement.broadcast = options.broadcast;

                    await tick();
                    initialized = true;

                    if (options.x + options.w > parentGrid.getColumn()) {
                        parentGrid.column(options.x + options.w, 'none');
                        parentGrid.el.closest('.grid-stack-item-content')?.classList.remove('!overflow-x-hidden');
                    }

                    parentGrid.addWidget(widgetItemElement, {
                        ...options,
                    });

                    // Only don't broadcast at the start?
                    widgetItemElement.broadcast = true;
                } else {
                    log.warn(
                        { title, widgetItemElement },
                        'Possible missing widget (skipped due to element ref missing)'
                    );
                }
            }
        })();
    }

    const resize = (_event?: Event, _element?: HTMLElement) => {
        browserBroadcaster.pub(Channel.AOH_WIDGET_RESIZED, {
            type: Channel.AOH_WIDGET_RESIZED,
            data: 'Gridstack resize stopped event.',
        });
    };

    // We only want to update the noMove and noResize attributes
    // the x and y states in the 'options' should not be re-used to set the widget back to its former position, these
    // will be saved into the database when the user clicks save
    $: if (widgetItemElement) {
        parentGrid?.update(widgetItemElement, {
            noMove: !editable,
            noResize: !editable,
        });
    }

    async function patchIncidentDepartment() {
        try {
            const response = await fetch(`${IncidentMonitoringURL}/incident-department/iams-sync`, {
                method: 'PATCH',
            });
            if (response.status !== 200) {
                const data = await response.json();
            }
            return true;
        } catch (err) {
            console.log(err);
        }
    }

    onMount(() => {
        if (dataSourceId === DATA_SOURCE.LE04_RESPONSE_TRACKING) {
            dispatch('iamsSync', {});
        }
        const user_id = get(user).claims.hasura_access['x-hasura-user-id'];
        mounted = true;
        const client = get(gqlClientStore);
        const ObserveBi = gql`
        subscription GetBiFlipboardSubscription {
            cc3_hoc_bi_flipboard(where: { 
                widget_id: { _eq: "${id}" }
                _or: [
                    { user_id: { _eq: "${user_id}" } }
                    { user_id: { _eq: "" } }
                ]
            }) {
                created_at
                created_by
                id
                json_values
                name
                remark
                tenant_id
                updated_at
                updated_by
                user_id
                widget_id
            }
        }`;
        subscriptions.push(
            wPipe(
                client.subscription(ObserveBi, {}),
                wSubscribe(result => {
                    if (result?.data?.cc3_hoc_bi_flipboard) {
                        const biData = result?.data?.cc3_hoc_bi_flipboard;
                        let newSysDatasource = biData.find(i => i.user_id === user_id && i.json_values);
                        if (!newSysDatasource) {
                            newSysDatasource = biData.find(i => i.user_id === '' && i.json_values);
                        }

                        // Only update if the data has actually changed
                        if (!newSysDatasource) {
                            sysDatasource = {
                                defaultData: 0,
                                sendDataToDashboard,
                            };
                        } else if (newSysDatasource && !areDataSourcesEqual(sysDatasource, newSysDatasource)) {
                            sysDatasource = {
                                ..._.cloneDeep(newSysDatasource),
                                sendDataToDashboard,
                            };
                        }
                    }
                })
            )
        );

        // client-side only onDestroy
        return () => {
            if (widgetItemElement) {
                parentGrid?.removeWidget(widgetItemElement, false);
            } else {
                logger.error({ widgetTitle: title }, 'Could not call gridstack.removeWidget');
            }
        };
    });

    onDestroy(() => {
        subscriptions.forEach(s => {
            s?.unsubscribe();
        });
    });
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->

<div
    on:click={selectWidget}
    on:keydown={() => {}}
    tabindex="0"
    role="button"
    data-widgetContainer={'widget-' + id}
    {id}
    data-title={title}
    bind:this={widgetItemElement}
    class:ui-draggable-disabled={options.noMove}
    class:ui-draggable={!options.noMove}
    class:ui-resizable-disabled={options.noResize}
    class:ui-resizable={!options.noResize}
    class:cursor-grab={!options.noMove}
    class:active:cursor-grabbing={!options.noMove}
    class:shadow-glow={selected && isSubGrid}
    class:border-selected-stroke={selected && isSubGrid}
    class:shadow-selected-stroke={selected && isSubGrid}
    class="grid-stack-item {isSubGrid
        ? 'group/grid flex flex-col overflow-hidden rounded-3xl border border-outline shadow-e-3'
        : 'not-subgrid'}">
    {#if isSubGrid}
        <div class="header-wrapper relative">
            {#if editable}
                <div
                    class="absolute right-0 top-0 z-[11] flex"
                    style="height: calc(var(--cell-height) * 1px - 2px); min-height: 39px;">
                    <Button
                        on:click={deleteSubGrid}
                        iconDefinition={faTrash}
                        appendClass="w-[48px]"
                        setColorClasses=" text-on-primary"><span class="block hidden" /></Button>
                    <Button
                        on:click={editWidget}
                        iconDefinition={faGear}
                        appendClass="w-[48px]"
                        setColorClasses=" text-on-primary"><span class="block hidden" /></Button>
                    <Button
                        on:click={openAddList}
                        iconDefinition={faPlus}
                        appendClass="w-[48px]"
                        setColorClasses=" text-on-primary"><span class="block hidden" /></Button>
                </div>
            {/if}
            <slot {sysDatasource} />
        </div>
    {/if}
    <div
        id="grid-item"
        class:shadow-glow={selected && !isSubGrid}
        class:border-selected-stroke={selected && !isSubGrid}
        class:shadow-selected-stroke={selected && !isSubGrid}
        class="grid-stack-item-content {isSubGrid
            ? '!overflow-x-hidden'
            : 'group rounded-3xl border border-outline bg-surface-gradient shadow-e-3'}">
        {#if !isSubGrid}
            <div class="relative h-full w-full">
                <slot {sysDatasource} />

                {#if editable}
                    <div
                        class="invisible absolute left-0 top-0 z-10 flex h-full w-full flex-col items-center justify-center
                    gap-4 bg-white bg-opacity-50 p-4 @container group-hover:visible">
                        <Button
                            on:click={editWidget}
                            iconDefinition={faGear}
                            appendClass="@xs:w-48"
                            setColorClasses="bg-primary text-on-primary"
                            ><span class="hidden @xs:block">EDIT WIDGET</span></Button>
                        <Button
                            on:click={deleteWidget}
                            iconDefinition={faTrash}
                            appendClass="@xs:w-48"
                            setColorClasses="bg-error text-on-primary"
                            ><span class="hidden @xs:block">REMOVE</span></Button>
                    </div>
                {/if}
            </div>
        {/if}
    </div>
</div>
