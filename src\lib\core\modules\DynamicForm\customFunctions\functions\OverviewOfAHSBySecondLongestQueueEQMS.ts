export default function OverviewOfAHSBySecondLongestQueueEQMS(form, fields) {
    form.PositionInQueue = '2';
    for (let index = 0; index < fields.length; index++) {
        const element = fields[index];
        if (element?.field === 'VisitTypeGroup') {
            element.datasource = {
                ...element.datasource,
                viewFields: ['code', 'desc', 'visit_type_group', 'visit_type_group_display_name'],
                filter: {
                    field: 'service',
                    value: 'AHS',
                },
            };
        }
        if (element?.field === 'Specialty') {
            element.datasource = {
                ...element.datasource,
                fields: ['code', 'desc', 'category', 'alt_tgt_days', 'service'],
                viewFields: ['code', 'desc', 'category', 'alt_tgt_days'],
                filter: {
                    field: 'service',
                    value: 'AHS',
                },
            };
        }
        if (element?.field === 'Clinics') {
            element.datasource = {
                ...element.datasource,
                fields: ['code', 'desc', 'category', 'service'],
                viewFields: ['code', 'desc', 'category'],
                customFilter: {
                    field: 'service',
                    value: 'AHS',
                },
            };
        }
        if (element?.field === 'ClassOfPatient') {
            element.datasource = {
                ...element.datasource,
                fields: ['code', 'desc', 'category', 'service'],
                viewFields: ['code', 'desc', 'category'],
                customFilter: {
                    field: 'service',
                    value: 'AHS',
                },
            };
        }
    }
}
