<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface NetflowConfig {
        title?: string;
        dataSource?: string;
        threshold?: number;
        thresholdColor?: string;

        label?: string;

        inflowTimestamp: string;
        inflowLabel: string;
        inflowLineColor?: string;

        outflowLabel: string;
        outflowTimestamp: string;
        outflowLineColor?: string;

        orderBy: string;
        numOfDays: number;

        showThreshold?: boolean;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    export const configDefaults: NetflowConfig = {
        threshold: 0,
        dataSource: 'aoh_charts_aoh_ims_netflow',
        title: 'Incidents on Trips',
        numOfDays: 5,
        inflowTimestamp: 'incidents_open',
        outflowTimestamp: 'incidents_closed',
        orderBy: 'order',
        inflowLabel: 'Open',
        outflowLabel: 'Closed',
        inflowLineColor: '#58D4EC',
        outflowLineColor: '#0056b3',
        thresholdColor: '#545454',
        label: 'label',
        showThreshold: false,
    };
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';
    import Toggle from '$lib/core/components/Toggle/index.svelte';
    import { DROPDOWN_CLASS, INPUT_CLASS, LABEL_CLASS, type Query } from '$lib/core/modules/dashboard/widgets/utils';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faArrowLeft, faArrowRight, faCaretDown, faDatabase, faSliders } from '@fortawesome/free-solid-svg-icons';

    export let config: NetflowConfig;
    let currentDatasource: Query = undefined;

    function updateDatasources() {
        datasources = [];

        datasources = $dataSource.__schema?.types.map(query => {
            if (config?.dataSource === query.name) currentDatasource = query;

            return {
                label: query.name,
                value: query.name,
                action: () => {
                    currentDatasource = query;
                },
            };
        });

        if (!datasources.length) config.dataSource = '';
    }

    // List of data source names (valid GraphQL query roots)
    let datasources: Array<DropdownItem> = [];
    let options: DropdownItem[] = [];
    let labelOptions: DropdownItem[] = [];

    function updateOptions(queries) {
        if (queries) {
            options = labelOptions = [];

            labelOptions = queries
                .map(q => {
                    if (q?.type?.ofType?.name === 'String') {
                        return {
                            label: q.name,
                            value: q.name,
                        };
                    }
                })
                .filter(option => {
                    //To filter undefined values
                    return Boolean(option);
                });

            options = queries
                .map(q => {
                    if (q?.type?.ofType?.name === 'Int') {
                        return {
                            label: q.name,
                            value: q.name,
                        };
                    }
                })
                .filter(option => {
                    //To filter undefined values
                    return Boolean(option);
                });
        }
    }

    $: updateOptions(currentDatasource?.fields);

    updateDatasources();
</script>

<div class="text-on-surface flex flex-col gap-3">
    <TextField placeholder={'Title'} bind:value={config.title} setClass={INPUT_CLASS} setLabelClass={LABEL_CLASS} />

    <!-- Datasource column-->
    <section class="flex flex-col gap-2">
        <header class="flex w-full items-center gap-2 text-xs">
            Data Source
            {@html icon(faDatabase).html}
        </header>

        <Dropdown
            title={'Data Source'}
            bind:value={config.dataSource}
            enableSearch={true}
            options={datasources}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />
    </section>

    <!-- Properties Column-->
    <div
        class="w-full {currentDatasource
            ? ''
            : 'pointer-events-none brightness-50'} flex flex-col gap-2 transition-colors duration-200 ease-in-out">
        <p class="py-2 text-center">{@html icon(faCaretDown).html}</p>

        <header class="flex w-full items-center gap-2 border-b-2 border-outline py-1 text-xs">
            Properties
            {@html icon(faSliders).html}
        </header>

        <Dropdown
            title={'Label'}
            options={labelOptions}
            bind:value={config.label}
            appendMenuClass="w-full"
            setClass={DROPDOWN_CLASS}
            setLabelClass={LABEL_CLASS} />

        <section class="flex items-center gap-2">
            <Dropdown
                title={'Order by'}
                {options}
                appendMenuClass="w-full"
                bind:value={config.orderBy}
                setClass={DROPDOWN_CLASS}
                setLabelClass={LABEL_CLASS} />
        </section>

        <section class="flex flex-col gap-1">
            <header class="my-1 flex w-full items-center gap-2 text-xs">
                Inflow
                {@html icon(faArrowLeft).html}
            </header>

            <section class="flex items-center gap-2">
                <TextField
                    placeholder={'Label'}
                    bind:value={config.inflowLabel}
                    setClass="{INPUT_CLASS} grow"
                    setLabelClass={LABEL_CLASS} />

                <input
                    type="color"
                    class="h-10 w-10 rounded border-outline bg-transparent"
                    bind:value={config.inflowLineColor} />
            </section>

            <Dropdown
                title={'Inflow'}
                {options}
                appendMenuClass="w-full"
                bind:value={config.inflowTimestamp}
                setClass={DROPDOWN_CLASS}
                setLabelClass={LABEL_CLASS} />
        </section>

        <section class="flex flex-col gap-1">
            <header class="my-1 flex w-full items-center gap-2 text-xs">
                Outflow
                {@html icon(faArrowRight).html}
            </header>

            <section class="flex items-center gap-2">
                <TextField
                    placeholder={'Label'}
                    bind:value={config.outflowLabel}
                    setClass="{INPUT_CLASS} grow"
                    setLabelClass={LABEL_CLASS} />

                <input
                    type="color"
                    class="h-10 w-10 rounded border-outline bg-transparent"
                    bind:value={config.outflowLineColor} />
            </section>

            <Dropdown
                title={'Outflow'}
                {options}
                appendMenuClass="w-full"
                setClass={DROPDOWN_CLASS}
                bind:value={config.outflowTimestamp}
                setLabelClass={LABEL_CLASS} />
        </section>

        <section class="flex flex-col gap-2">
            <header class="my-1 flex w-full items-center justify-between gap-2 text-xs">Threshold</header>
            <section class="flex items-center gap-2">
                <TextField
                    type="number"
                    placeholder={'Threshold'}
                    bind:value={config.threshold}
                    setClass="{INPUT_CLASS} grow"
                    setLabelClass={LABEL_CLASS} />

                <input
                    type="color"
                    class="h-10 w-10 rounded border-outline bg-transparent"
                    bind:value={config.thresholdColor} />
            </section>
            <div>
                <Toggle labelText="Show Threshold" bind:checked={config.showThreshold} />
            </div>
        </section>
    </div>
</div>
