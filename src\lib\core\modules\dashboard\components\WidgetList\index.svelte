<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    import DashboardPreview from '$lib/core/modules/dashboard/components/DashboardPreview/index.svelte';
    import Accordion from '$lib/core/components/Accordion/index.svelte';
    import Button, { ButtonVariant } from '$lib/core/components/Button/index.svelte';
    import Modal from '$lib/core/components/Modal/index.svelte';
    import Textfield from '$lib/core/components/TextField/index.svelte';
    import { findIconDefinition, icon, parse } from '@fortawesome/fontawesome-svg-core';
    import { faBug, faMagnifyingGlass } from '@fortawesome/free-solid-svg-icons';
    import { onMount } from 'svelte';
    import { v4 as uuidv4 } from 'uuid';
    import type { GridStackOptions } from 'gridstack';
    import { DashboardStore } from '$lib/core/modules/dashboard/module';

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });
</script>

<script lang="ts">
    export let isOpen = false;
    export let widgets: Array<WidgetType> = [];
    export let widgetValidationAction: Function;
    export let onAdd: Function;
    export let maxColumns = 24;
    export let maxRows = 0;

    const gridOptions: GridStackOptions = {
        acceptWidgets: false,
        alwaysShowResizeHandle: false,
        animate: false,
        auto: true,
        cellHeightThrottle: 100,
        column: maxColumns,
        row: maxRows,
        disableDrag: true,
        disableResize: true,
        float: true,
        margin: '0.25rem',
    };

    let searchValue = undefined;
    let selected_widget_type: WidgetType = undefined;
    let widget_to_preview: WidgetGridItem = undefined;
    let dashboard_preview_config: Array<Widget> = [];

    type CategoryDisplayName = string;

    let widgets_by_category_name_map: Map<CategoryDisplayName, Array<WidgetType>> = new Map();

    function map_widget_types_by_category(widgets: Array<WidgetType>) {
        widgets_by_category_name_map = new Map();
        let widgets_with_no_category: Array<WidgetType> = [];

        if (widgets.length) {
            //Create a category for widgets with unknown categories
            widgets.forEach(w => {
                if (!w.enabled) return;
                let category = $DashboardStore.category_map.get(w.category_id);

                if (!category) {
                    widgets_with_no_category.push(w);
                } else {
                    if (!widgets_by_category_name_map.get(category.name)) {
                        widgets_by_category_name_map.set(category.name, []);
                    }

                    widgets_by_category_name_map.get(category.name).push(w);
                }
            });
        }

        if (widgets_with_no_category.length) {
            widgets_by_category_name_map.set('[ Category Missing ]', widgets_with_no_category);
        }

        widgets_by_category_name_map = hideWidgetsByCategory(
            ['Specialist Outpatient Clinics', 'Inventory'],
            widgets_by_category_name_map
        );
    }

    function hideWidgetsByCategory(input: string[], widgets: Map<string, WidgetType[]>): Map<string, WidgetType[]> {
        const filteredWidgets = new Map<string, WidgetType[]>();

        widgets.forEach((widgetList, category) => {
            if (!input.includes(category)) {
                filteredWidgets.set(category, widgetList);
            }
        });

        return filteredWidgets;
    }

    onMount(() => {
        map_widget_types_by_category(widgets);
    });

    $: if (searchValue?.length) {
        if (searchValue !== '') {
            map_widget_types_by_category(
                widgets.filter(w => w.name && w.name.toLowerCase().includes(searchValue.toLowerCase()))
            );
        }
    } else map_widget_types_by_category(widgets);

    $: if (widgets) {
        map_widget_types_by_category(widgets);
    }
</script>

<Modal bind:isOpen title="Widget List" widthClass="w-[95%]" heightClass="h-[95%]">
    <div class="text-on-surface z-50 flex h-full w-full flex-col text-sm">
        <section class="flex h-[1px] grow divide-x-2 divide-outline">
            <!-- Selection Panel -->
            <div
                class="h-full min-w-[600px] overflow-y-auto px-4 py-2"
                data-testid="widget-edit-view-widget-list-panel">
                <span class="flex h-12 w-full items-center border-b-2 border-b-outline">
                    <p class="flex items-center px-2 text-on-surface-2">{@html icon(faMagnifyingGlass).html}</p>
                    <Textfield
                        bind:value={searchValue}
                        placeholder="Search"
                        setClass="grow font-base bg-transparent truncate text-on-surface-2"
                        appendLabelClass="text-on-surface-2" />
                </span>
                {#each [...widgets_by_category_name_map] as [category, widgetList]}
                    {#if widgetList.length > 0}
                        <Accordion
                            setClass="bg-transparent mt-2 text-on-surface-2"
                            headerClass="h-8 text-xs"
                            isOpen={true}
                            title={`${category} (${widgetList.length})`}>
                            <ul class="flex w-full flex-col gap-2">
                                {#each widgetList as w, _index (w.path)}
                                    <li use:widgetValidationAction={w} class="items-center justify-center">
                                        <Button
                                            data-subject="button"
                                            on:click={() => {
                                                // Set selected widget
                                                selected_widget_type = w;

                                                // Prepare widget grid item for preview
                                                widget_to_preview = {
                                                    id: uuidv4(),
                                                    row: 0,
                                                    column: 0,
                                                    width: selected_widget_type.max_width,
                                                    height: selected_widget_type.max_height,
                                                    config: null,
                                                    widget_type_id: selected_widget_type.id,
                                                    shared_config_id: null,
                                                    widget_type: selected_widget_type,
                                                    component: {
                                                        config: null,
                                                        main: null,
                                                    },
                                                    svelte_component_ref: null,
                                                };

                                                // Set for preview - reactivity will trigger Dashboard Preview
                                                dashboard_preview_config = [widget_to_preview];
                                            }}
                                            variant={ButtonVariant.WIDGET_ITEM}
                                            appendClass={selected_widget_type?.path === w.path
                                                ? 'border-2 border-selected-stroke'
                                                : undefined}>
                                            <!-- TODO: Avoid repetition -->
                                            {#if w.icon}
                                                {@const iconDef = findIconDefinition(parse.icon(w.icon))}
                                                {#if iconDef}
                                                    <div class="text-center text-lg font-extrabold leading-5">
                                                        {@html icon(iconDef).html}
                                                    </div>
                                                {:else}
                                                    <div class="text-center text-lg font-extrabold leading-5">
                                                        {@html icon(faBug).html}
                                                    </div>
                                                {/if}
                                            {:else}
                                                <div class="text-center text-lg font-extrabold leading-5">
                                                    {@html icon(faBug).html}
                                                </div>
                                            {/if}
                                            {w.name ? w.name : '[ Name Missing ]'}
                                            <span
                                                class="ml-auto text-end text-xs font-medium leading-[115%] text-error-85"
                                                id="error-msg"><!-- Error Message Replaced Here--></span>
                                            <span class="text-xs font-medium leading-[115%] text-on-background"
                                                >{w.min_width != null ? w.min_width : '[ ? ]'}x{w.min_height != null
                                                    ? w.min_height
                                                    : '[ ? ]'}</span>
                                        </Button>
                                    </li>
                                {/each}
                            </ul>
                        </Accordion>
                    {/if}
                {/each}
            </div>

            <!-- Preview Panel-->
            <div class="flex h-full w-full flex-col justify-between gap-2 p-4">
                <DashboardPreview showGrid={false} showBackground={false} dashboard_config={dashboard_preview_config} />

                <Button
                    on:click={() => {
                        if (onAdd) {
                            onAdd(widget_to_preview);
                        } else {
                            log.warn('unable to call onAdd from WidgetList');
                        }

                        isOpen = false;
                        selected_widget_type = undefined;
                        dashboard_preview_config = [];
                        searchValue = '';
                    }}
                    setDisabled={selected_widget_type === undefined}
                    setClass="disabled:brightness-50 w-full h-8 rounded text-sm bg-primary
                     text-on-primary hover:brightness-125 transition-all ease-in-out duration-150">
                    Add Widget
                </Button>
            </div>
        </section>
    </div>
</Modal>
