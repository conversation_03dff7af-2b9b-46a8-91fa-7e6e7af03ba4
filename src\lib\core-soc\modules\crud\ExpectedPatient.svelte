<script lang="ts">
    import { ColumnDataType, type ColumnName } from '$lib/shared/constants/general';
    import { POPUP_MESSAGES } from '$lib/shared/util/popupMessage';
    import { validateCharacterLength } from '$lib/shared/util/validation';
    import ExpectedPatientConfiguration from '../../components/ExpectedPatientConfiguration.svelte';
    export let abilities;
    const baseURL = '/api/soc/cfg-expt-pts-in-pharmacy';
    const figureName = 'Expected Patient in Pharmacy by Configuration';

    const columnNames: ColumnName[] = [
        {
            key: 'specialty_code',
            value: 'Specialty Code',
            editable: false,
            type: ColumnDataType.String,
            required: false,
        },
        {
            key: 'specialty_desc',
            value: 'Specialty',
            editable: false,
            type: ColumnDataType.String,
        },
        {
            key: 'avg_time_to_reach_pharmacy',
            value: 'Avg Time Taken to Reach Pharmacy (Mins)',
            editable: true,
            type: ColumnDataType.Number,
        },
        {
            key: 'avg_time_spent_at_pharmacy',
            value: 'Avg Patient Spent in Pharmacy Time (Mins)',
            editable: true,
            type: ColumnDataType.Number,
        },
    ];

    const validateNewData = (updateData): string | null => {
        if (!validateCharacterLength(updateData, columnNames)) {
            return POPUP_MESSAGES.lengthLimit;
        }
        for (const row of updateData) {
            if (row.avg_time_spent_at_pharmacy !== '' && Number(row.avg_time_spent_at_pharmacy) < 0) {
                return POPUP_MESSAGES.notNegative.replace('{field}', 'Avg Patient Spent in Pharmacy Time');
            }
            if (row.avg_time_to_reach_pharmacy !== '' && Number(row.avg_time_to_reach_pharmacy) < 0) {
                return POPUP_MESSAGES.notNegative.replace('{field}', 'Avg Time Taken to Reach Pharmacy');
            }
        }
        return null;
    };
</script>

<ExpectedPatientConfiguration
    {abilities}
    {baseURL}
    {figureName}
    {columnNames}
    {validateNewData}
    defaultSortKey="specialty_desc" />
