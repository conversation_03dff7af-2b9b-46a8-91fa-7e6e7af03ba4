<script lang="ts" context="module">
    //Components
    //Utils
    import { logger } from '$lib/stores/Logger';
    import { faTable, faSortUp, faSortDown, faGear, faSave, faRefresh } from '@fortawesome/free-solid-svg-icons';
    import isToday from 'dayjs/plugin/isToday';

    //svelte
    import { get, writable } from 'svelte/store';

    //Graphql
    import { gqlClientStore } from '$lib/stores/Clients';

    import { icon } from '@fortawesome/fontawesome-svg-core';

    import { type AdvancedTableConfig } from './WidgetConfig/index.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);

    export const widgetIcon = faTable;
    export const widgetCategory = 'Inventory';
    export const widgetLimit = 999;
    export const widgetTitle = 'Consumable Item vs Incident';
    export const widgetEnabled = true;
    export const widgetMinWidth = 5;
    export const widgetMinHeight = 5;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import {
        Heading,
        Table,
        TableBody,
        TableBodyRow,
        TableHead,
        TableHeadCell,
        Pagination,
        TableBodyCell,
        Button,
        Tooltip,
        Modal,
        P,
        Radio,
    } from 'flowbite-svelte';
    import { gql, mutationStore, subscriptionStore, type OperationResultStore, type Pausable } from '@urql/svelte';
    import { onMount } from 'svelte';
    import {
        GetCriticalSupplyConfigurationsStreamingDocument,
        type GetCriticalSupplyConfigurationsStreamingSubscription,
        type UpdateCriticalSupplyViewConfigurationMutation,
        UpdateCriticalSupplyViewConfigurationDocument,
    } from '$generated-types';
    import dayjs from 'dayjs';
    import toast from 'svelte-french-toast';
    import { user } from '$lib/stores/Auth';
    export let config: AdvancedTableConfig;

    let filteringOpen = false;
    let user_id = '';

    const ASC: number = 1;

    let page = 0;
    let limit = parseInt(config.numberOfRows, 10);
    let helper = { start: 1, end: 10, total: 100 };
    let borderStyle = '';

    enum ViewType {
        Group = 'group',
        Detail = 'detail',
    }
    const viewTypes: Record<ViewType, string> = {
        [ViewType.Group]: 'Group View',
        [ViewType.Detail]: 'Detail View',
    };

    // temp setup
    let viewConfig: ViewType = ViewType.Detail;
    let viewUpdated: boolean = false;

    const sortKey = writable('');
    const sortDirection = writable(ASC);
    const sortItems = writable([]);
    const sumItems = writable({});

    const client = get(gqlClientStore);
    let subscriptionSource;
    let subscriptionCount;

    const paginationNumberStyle = 'font-semibold text-gray-900 dark:text-white';
    const paginationButtonStyle = 'flex items-center gap-2 text-white text-gray-800';

    const buildFetchDocument = () => {
        return gql`
        subscription {${config.dataSource}(limit: ${config.numberOfRows}, offset: ${(page * limit).toFixed()}) {
                ${config.columns.map(e => e.name).join(' ')}
            }
        }
    `;
    };

    const buildFetchCountDocument = () => {
        return gql`
        subscription {${config.dataSource}_aggregate {
            aggregate {
                    count
                }
            }
        }
    `;
    };

    $: if (config.dataSource) {
        if (config?.columns?.length)
            subscriptionSource = subscriptionStore({
                client,
                query: buildFetchDocument(),
            });

        subscriptionCount = subscriptionStore({
            client,
            query: buildFetchCountDocument(),
        });
    }

    $: if ($subscriptionCount && $subscriptionCount.data && $subscriptionCount.data[`${config.dataSource}_aggregate`]) {
        const total = $subscriptionCount.data[`${config.dataSource}_aggregate`].aggregate.count;
        helper.total = total;
        helper.start = page * limit + 1;
        helper.end = page * limit + limit;
        if (helper.end > total) {
            helper.end = total;
        }
    }

    const refresh = () => {
        subscriptionSource = subscriptionStore({
            client,
            query: buildFetchDocument(),
        });
    };

    const previous = () => {
        if (page - 1 > -1) {
            page--;
        }
        refresh();
    };

    const next = () => {
        if (page + 1 < Math.ceil(helper.total / limit)) {
            page++;
        } else {
            if (config.autoPagination) page = 0;
        }
        refresh();
    };

    const sortTable = key => {
        if ($sortKey === key) {
            sortDirection.update(val => -val);
        } else {
            sortKey.set(key);
            sortDirection.set(ASC);
        }
    };

    $: if (config?.defaultSortingColumn) sortKey.set(config?.defaultSortingColumn);

    $: {
        const key = $sortKey;
        const direction = $sortDirection;
        const data = $subscriptionSource?.data?.[config.dataSource];
        if (data) {
            const sorted = data.sort((a, b) => {
                const aVal = a[key];
                const bVal = b[key];
                if (aVal < bVal) {
                    return -direction;
                } else if (aVal > bVal) {
                    return direction;
                }
                return 0;
            });
            sortItems.set(sorted);

            const sum = {};
            data.forEach(item => {
                for (const [key, value] of Object.entries(item)) {
                    const parsedValue = Number(value);
                    if (!isNaN(parsedValue)) {
                        if (!sum.hasOwnProperty(key)) sum[key] = 0;
                        sum[key] += parsedValue;
                    }
                }
            });
            sumItems.set(sum);
        }
    }

    $: if (config.gridVisible) {
        const thickness = config.borderThickness ? config.borderThickness : 1;
        if (thickness === 1) borderStyle = `border border-gray-600`;
        if (thickness === 2) borderStyle = `border-2 border-gray-600`;
        if (thickness === 3) borderStyle = `border-4 border-gray-600`;
        if (thickness === 4) borderStyle = `border-8 border-gray-600`;
    } else {
        borderStyle = '';
    }

    let interval;
    let autoPaginationRunning = false;
    $: if (config.autoPagination && $sortItems?.length && !autoPaginationRunning) {
        interval = setInterval(next, config.paginationPeriod * 1000 || 3000);
        autoPaginationRunning = true;
    } else if (!config.autoPagination && autoPaginationRunning) {
        if (interval) {
            clearInterval(interval);
            autoPaginationRunning = false;
        }
    }

    let criticalSupplyConfigurations: OperationResultStore<GetCriticalSupplyConfigurationsStreamingSubscription> &
        Pausable;

    $: if (user_id) {
        criticalSupplyConfigurations = subscriptionStore<GetCriticalSupplyConfigurationsStreamingSubscription>({
            client,
            query: GetCriticalSupplyConfigurationsStreamingDocument,
            variables: { user_id },
        });
    }

    $: if ($criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations?.length && !viewUpdated) {
        viewConfig = $criticalSupplyConfigurations.data.cc3_inventory_critical_supply_configurations[0]
            .view_type as ViewType;
        viewUpdated = true;
    }

    const onGearClick = () => {
        filteringOpen = true;
    };

    onMount(() => {
        user_id = get(user).claims.hasura_access['x-hasura-user-id'];
    });

    const onSave = () => {
        if (!$criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations?.length) {
            toast.error('Failed to save configuration not found.');
            filteringOpen = false;
            return;
        }
        const unsubscriber = mutationStore<UpdateCriticalSupplyViewConfigurationMutation>({
            client,
            query: UpdateCriticalSupplyViewConfigurationDocument,
            variables: {
                id: $criticalSupplyConfigurations.data.cc3_inventory_critical_supply_configurations[0].id,
                view_type: viewConfig,
            },
        }).subscribe(res => {
            if (!res.fetching && !res.error) {
                unsubscriber();
                filteringOpen = false;
            } else if (!res.fetching && res.error) {
                toast.error('Unable to save configuration successfully.');
                unsubscriber();
            }
        });
    };

    const onReset = () => {
        viewConfig =
            ($criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations?.[0]
                ?.view_type as ViewType) || ViewType.Detail;
    };

    const displayDate = date => {
        if (!date) return '-';
        return dayjs(date).format('YYYY-MM-DD');
    };
</script>

<div class="relative flex flex-col gap-2 p-3">
    <div class="group flex justify-between rounded-lg bg-gray-800">
        <Heading class="flex self-center p-3" tag="h5">{config?.title}</Heading>
        <Button size="md" on:click={onGearClick} class="opacity-0 transition-all duration-300 group-hover:opacity-100">
            <span>{@html icon(faGear).html}</span>
        </Button>
        <Tooltip class="z-10 transition-none duration-0">{`Filter configuration`}</Tooltip>
    </div>
    <div class="flex h-full w-full justify-end overflow-hidden" class:h-0={!$criticalSupplyConfigurations}>
        <P size="sm">
            <span>Compare Incident: </span>
            <span>
                {#if $criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations[0].is_enabled}
                    {$criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations[0].incidents.find(
                        e => e.is_reference
                    )?.incident.name || ''}
                {:else}
                    -
                {/if}
            </span>
        </P>
    </div>
    <div class="flex h-full w-full justify-end overflow-hidden" class:h-0={!$criticalSupplyConfigurations}>
        <P size="sm">
            <span>Compare Period: </span>
            <span>
                {#if $criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations[0].is_enabled}
                    {displayDate(
                        $criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations[0].incidents.find(
                            e => e.is_reference
                        )?.incident.start_date
                    )} to {displayDate(
                        $criticalSupplyConfigurations?.data?.cc3_inventory_critical_supply_configurations[0].incidents.find(
                            e => e.is_reference
                        )?.incident.end_date
                    )}
                {:else}
                    -
                {/if}
            </span>
        </P>
    </div>
    <div class="overflow-auto rounded-lg">
        <Table hoverable={true}>
            <TableHead class="capitalize">
                {#each config?.columns as column}
                    <TableHeadCell
                        on:click={() => sortTable(column.name)}
                        class={`cursor-pointer text-gray-900 dark:text-white ${borderStyle} text-${config.textHeaderAlignment}`}>
                        <span class="mr-1">{column.customName}</span>
                        {#if column.name === $sortKey}
                            <span>{@html icon($sortDirection === ASC ? faSortUp : faSortDown).html}</span>
                        {/if}
                    </TableHeadCell>
                {/each}
            </TableHead>
            <TableBody>
                {#if $sortItems?.length}
                    {#each $sortItems as item, index}
                        <TableBodyRow
                            class={`${
                                config?.stripedRows &&
                                'odd:bg-white even:bg-gray-50 odd:dark:bg-gray-800 even:dark:bg-gray-700'
                            }`}>
                            {#each config.columns as column}
                                <TableBodyCell
                                    rowSpan={config.mergeRowsOnSortedColumn &&
                                    column.name === $sortKey &&
                                    index + 1 < $sortItems.length &&
                                    $sortItems[index + 1][column.name] === item[column.name]
                                        ? $sortItems[$sortItems.length - 1][column.name] === item[column.name]
                                            ? $sortItems.length
                                            : $sortItems
                                                  .slice(index)
                                                  .findIndex(i => i[column.name] !== item[column.name])
                                        : 1}
                                    class={`text-wrap ${borderStyle} ${
                                        config.mergeRowsOnSortedColumn &&
                                        column.name === $sortKey &&
                                        index &&
                                        $sortItems[index - 1][column.name] === item[column.name] &&
                                        'hidden'
                                    } text-${config.textContentAlignment}`}>{item[column.name] || ''}</TableBodyCell>
                            {/each}
                        </TableBodyRow>
                    {/each}
                    {#if config?.sumHeaderVisible}
                        <Heading tag="h6" class="mt-4">Total</Heading>
                    {/if}
                    {#if config?.sumVisible}
                        <TableBodyRow class={`text-wrap text-lg`}>
                            {#each config.columns as column}
                                <TableBodyCell class={`text-wrap ${borderStyle} text-${config.textContentAlignment}`}
                                    >{$sumItems[column.name] || 'N/A'}</TableBodyCell>
                            {/each}
                        </TableBodyRow>
                    {/if}
                {/if}
                {#if !$sortItems?.length || !config.columns?.length}
                    <div>No data available</div>
                {/if}
            </TableBody>
        </Table>
    </div>
    {#if config?.paginationVisible}
        <div class="flex flex-col items-center justify-center gap-2">
            <div class="text-sm text-gray-700 dark:text-gray-400">
                Showing <span class={paginationNumberStyle}>{helper.start}</span> to
                <span class={paginationNumberStyle}>{helper.end}</span> of
                <span class={paginationNumberStyle}>{helper.total}</span> Rows
            </div>

            <Pagination table on:next={next} on:previous={previous}>
                <div slot="prev" class="{paginationButtonStyle} {page === 0 && 'cursor-not-allowed'}">Prev</div>
                <div
                    slot="next"
                    class="{paginationButtonStyle} {page === Math.ceil(helper.total / limit) - 1 &&
                        'cursor-not-allowed'}">
                    Next
                </div>
            </Pagination>
        </div>
    {/if}
</div>

<Modal size="sm" title="Group/Detail View Configuration" bind:open={filteringOpen}>
    <div class="flex justify-center gap-3 rounded-lg bg-gray-900 p-5">
        {#each Object.keys(viewTypes) as viewType}
            <Radio bind:group={viewConfig} value={viewType}>{viewTypes[viewType]}</Radio>
        {/each}
    </div>
    <div class="flex justify-center gap-3">
        <Button color="light" on:click={onSave} class="flex justify-center gap-2">
            <span>{@html icon(faSave).html}</span>
            <span>Save</span>
        </Button>
        <Button color="light" on:click={onReset} class="flex justify-center gap-2">
            <span>{@html icon(faRefresh).html}</span>
            <span>Reset</span>
        </Button>
    </div>
</Modal>
