<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    export interface IndicatorConfig {
        showGear?: boolean;
        filterLabelAlignment?: string;
        dataSource?: string;
        dataSourceName?: string;
        dataSourceType?: string;
        title?: string;
        bgColor?: string;
        left: string;
        right: string;
        dateFormat?: string;
        isValidDate?: boolean;
        isBorderTop: boolean;
        isBorderBottom: boolean;
        isBorderLeft: boolean;
        isBorderRight: boolean;
        borderWidth?: string;
        borderColor?: string;
        isShowDate: boolean;
        dateFontSize: string;
        dateFontColor: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'c3hoc_VW_SCDF_Amb_aggregate';
    const DEFAULT_TITLE = '<p style="text-align:center;">Label</p>';
    const DEFAULT_BG_COLOR = '';
    const DEFAULT_LEFT = 'none';
    const DEFAULT_RIGHT = 'none';
    const DEFAULT_FORMAT = 'YYYY/MM';
    const IS_VALID_DATE = true;
    const DEFAULT_BORDER_WIDTH = '0';
    const DEFAULT_BORDER_COLOR = '';
    const DEFAULT_IS_BORDER = false;
    const DEFAULT_IS_SHOW_DATE = true;
    const DEFAULT_FONT_SIZE = '14';
    const DEFAULT_FONT_COLOR = '#ffffff';

    export const configDefaults: IndicatorConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        bgColor: DEFAULT_BG_COLOR,
        left: DEFAULT_LEFT,
        right: DEFAULT_RIGHT,
        dateFormat: DEFAULT_FORMAT,
        isValidDate: IS_VALID_DATE,
        isBorderTop: DEFAULT_IS_BORDER,
        isBorderBottom: DEFAULT_IS_BORDER,
        isBorderLeft: DEFAULT_IS_BORDER,
        isBorderRight: DEFAULT_IS_BORDER,
        borderWidth: DEFAULT_BORDER_WIDTH,
        borderColor: DEFAULT_BORDER_COLOR,
        isShowDate: DEFAULT_IS_SHOW_DATE,
        dateFontSize: DEFAULT_FONT_SIZE,
        dateFontColor: DEFAULT_FONT_COLOR,
    };
</script>

<script lang="ts">
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import WidgetConfig from '$lib/core/modules/dashboard/components/WidgetConfig/index.svelte';
    import WidgetConfigText from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetConfigText/index.svelte';
    import Ckeditor from '$lib/core/modules/dashboard/components/Ckeditor/index.svelte';
    import WidgetButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetButton/index.svelte';
    import WidgetCoppyButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetCoppyButton/index.svelte';
    import WidgetToggle from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetToggle/index.svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';
    import { onMount } from 'svelte';
    export let config: IndicatorConfig;
    const datetimeFormatList: Array<String> = ['DD/MM', 'MMM YYYY', 'MM/DD/YY', 'DD MMM, YYYY'];
    const configElementWidth = ''; //'w-[29%]';
    $: config.isShowDate = config.left == 'date' || config.right == 'date' ? true : false;
    export let datasources: Array<DropdownItem> = [];

    interface ParameterItem {
        name: string;
        id: string | number;
    }
    let allDataSources = [];
    const ALIGNMENT: string[] = ['Left', 'Center', 'Right'];
    const getTypeAndNameDataSource = (id: string | number) => {
        const found = allDataSources.find(item => item.value === id);
        if (found) {
            config.dataSourceName = found.label;
            config.dataSourceType = found.type;
        }
        return null;
    };

    let formattedParameters: { label: string; value: string | number }[] = [];
    let propertyFields = [];
    const getCfgParameters = async () => {
        try {
            const resCfg = await fetch(`/api/CfgParameterService?category=filtering_config&category=filtering_label`);
            const { res } = await resCfg.json();
            const data = res.data;
            formattedParameters = data.map((item: ParameterItem) => ({
                label: item.name,
                value: item.id,
                type: 'cfg_parameter',
            }));
            allDataSources = [{ label: '', value: '', type: 'cfg_parameter' }, ...formattedParameters];
            if (formattedParameters) {
                propertyFields = [
                    {
                        extraProps: {
                            customClass: 'flex w-full items-end gap-2',
                        },
                        children: [
                            {
                                id: 'dataSource',
                                componentDisplay: Dropdown,
                                extraProps: {
                                    title: 'Data Source',
                                    options: allDataSources,
                                    setClass: 'border-b border-b-outline bg-transparent mb-4 w-1/2',
                                    enableSearch: true,
                                    appendMenuClass: 'w-full',
                                    required: true,
                                },
                                onChange: () => {
                                    if (config.dataSource) {
                                        getTypeAndNameDataSource(config.dataSource);
                                    }
                                },
                            },
                            {
                                id: 'dataSourceName',
                                componentDisplay: WidgetCoppyButton,
                            },
                        ],
                    },
                    {
                        id: 'showGear',
                        componentDisplay: WidgetToggle,
                        extraProps: {
                            title: 'Show Data Source Label',
                            checked: config.showGear,
                        },
                    },
                    {
                        id: 'filterLabelAlignment',
                        componentDisplay: WidgetGroupButton,
                        extraProps: {
                            title: 'Data Source Label Alignment',
                            labels: ALIGNMENT,
                        },
                    },
                    {
                        id: 'title',
                        componentDisplay: Ckeditor,
                        extraProps: {
                            title: 'Title',
                            customClass: 'mb-4 pr-2',
                        },
                    },
                    {
                        extraProps: {
                            customClass: 'flex flex-wrap w-full items-end gap-2',
                        },
                        children: [
                            {
                                id: 'left',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    title: 'Left',
                                    label: 'None',
                                },
                            },
                            {
                                id: 'left',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Inflow',
                                },
                            },
                            {
                                id: 'left',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Stock',
                                },
                            },
                            {
                                id: 'left',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Outflow',
                                },
                            },
                            {
                                id: 'left',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Date',
                                },
                            },
                        ],
                    },
                    {
                        extraProps: {
                            customClass: 'flex flex-wrap w-full items-end gap-2',
                        },
                        children: [
                            {
                                id: 'right',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    title: 'Right',
                                    label: 'None',
                                },
                            },
                            {
                                id: 'right',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Inflow',
                                },
                            },
                            {
                                id: 'right',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Stock',
                                },
                            },
                            {
                                id: 'right',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Outflow',
                                },
                            },
                            {
                                id: 'right',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Date',
                                },
                            },
                        ],
                    },
                    {
                        checkVisible: 'isShowDate',
                        extraProps: {
                            customClass: 'flex w-full items-end gap-2',
                        },
                        children: [
                            {
                                id: 'dateFontSize',
                                componentDisplay: WidgetConfigText,
                                extraProps: {
                                    title: 'Date Font Size',
                                    type: 'number',
                                    min: 0,
                                    customClass: 'mb-4 w-1/2',
                                    pattern: '^[0-9]\\d*$',
                                },
                            },
                            {
                                id: 'dateFontColor',
                                componentDisplay: WidgetConfigText,
                                extraProps: {
                                    type: 'color',
                                    inputClass: 'mx-auto h-10 w-10 p-0',
                                },
                            },
                        ],
                    },
                    {
                        checkVisible: 'isShowDate',
                        id: 'dateFormat',
                        componentDisplay: WidgetConfigText,
                        extraProps: {
                            title: 'Date Format',
                            placeholder: 'YYYY/MM',
                            pattern: '^[aszhmdyASZHMDY/\\-:s., ]*$',
                            toolTip: datetimeFormatList,
                            customClass: 'mb-4 pr-2',
                        },
                    },
                    {
                        extraProps: {
                            customClass: 'flex w-full items-end gap-2',
                        },
                        children: [
                            {
                                id: 'borderWidth',
                                componentDisplay: WidgetConfigText,
                                extraProps: {
                                    title: 'Border Size',
                                    type: 'number',
                                    min: 0,
                                    customClass: 'mb-4 w-1/2',
                                    pattern: '^[0-9]\\d*$',
                                },
                            },
                            {
                                id: 'borderColor',
                                componentDisplay: WidgetConfigText,
                                extraProps: {
                                    title: 'Border Color',
                                    type: 'color',
                                    inputClass: 'mx-auto h-10 w-10 p-0',
                                },
                            },
                            {
                                id: 'bgColor',
                                componentDisplay: WidgetConfigText,
                                extraProps: {
                                    title: 'Bg Color',
                                    type: 'color',
                                    inputClass: 'mx-auto h-10 w-10 p-0',
                                },
                            },
                        ],
                    },
                    {
                        extraProps: {
                            customClass: 'flex flex-wrap w-full items-end gap-2',
                        },
                        children: [
                            {
                                id: 'isBorderTop',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    title: 'Border Style',
                                    label: 'Top',
                                },
                            },
                            {
                                id: 'isBorderBottom',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Bottom',
                                },
                            },
                            {
                                id: 'isBorderLeft',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Left',
                                },
                            },
                            {
                                id: 'isBorderRight',
                                componentDisplay: WidgetButton,
                                extraProps: {
                                    label: 'Right',
                                },
                            },
                        ],
                    },
                ];
            }
        } catch (error) {
            console.log(error);
        }
    };

    onMount(async () => {
        await getCfgParameters();
    });
</script>

<WidgetConfig bind:config {propertyFields} {configElementWidth} />
