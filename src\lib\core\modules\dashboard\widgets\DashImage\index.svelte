<script lang="ts" context="module">
    import { faImage } from '@fortawesome/free-solid-svg-icons';
    import { onDestroy, onMount } from 'svelte';

    export const widgetTitle = 'Dash Image';
    export const widgetIcon = faImage;
    export const widgetLimit = 999;
    export const widgetCategory = 'Resource Management';
    export const widgetMinWidth = 2;
    export const widgetMinHeight = 2;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 12;
    export const componentName = new URL(import.meta.url).pathname;
</script>

<script lang="ts">
    import type { DashImageConfig } from './WidgetConfig/index.svelte';
    import { get } from 'svelte/store';
    import gql from 'graphql-tag';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import { gqlClientStore } from '$lib/stores/Clients';

    export let config: DashImageConfig;

    let subscription: Subscription;
    let name;
    let image;

    const updateData = result => {
        if (result?.data) {
            name = result.data.cc3_hoc_comm_dash_image_group_by_pk.name;
            image = result.data.cc3_hoc_comm_dash_image_group_by_pk.image;
        }
    };

    const loadData = () => {
        let FetchDashImageDocument = gql`
                subscription DashImageGroupSubscription {
                    cc3_hoc_comm_dash_image_group_by_pk(id: "${config.id}") {
                        image
                        name
                    }
                }
            `;
        const client = get(gqlClientStore);
        subscription = wPipe(client.subscription(FetchDashImageDocument, {}), wSubscribe(updateData));
    }

    onMount(async () => {
        loadData();
    })

    onDestroy(() => {
        subscription.unsubscribe();
    })

    $: {
        if (config.id) {
            if (subscription) subscription.unsubscribe();
            loadData();
        }
    }
</script>

{#if image}
    <img src="data:image/png;base64, {image}" alt={name} />
{/if}
