import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
import { checkPermissions } from '$lib/shared/util/acl';
import { locations } from '$lib/stores/Locations';
import { logger } from '$lib/stores/Logger';
import { error } from '@sveltejs/kit';
import { get } from 'svelte/store';
import type { PageServerLoad } from './$types';
// import { CfgParameterService } from '$lib/service/cfg_parameter';
// import { INVENTORY_URL } from '$env/static/private';

const log = logger.child({ src: new URL(import.meta.url).pathname });

export const load: PageServerLoad = async ({ locals, params, fetch, url, cookies }) => {
    const id = params.id
    const token = cookies.get('access_token') ?? "";
    const response = await fetch(`${get(locations).incident_monitoring_web}/moh-report/${id}`);
    if (response.status !== 200) {
        log.error({ status: response.status });
        return error(404);
    }
    const reportData = await response.json();
    const typeResponse = await fetch(`${get(locations).incident_monitoring_web}/type`);
    if (typeResponse.status !== 200) {
        log.error({ status: typeResponse.status });
        return error(404);
    }
    const incidentType = await typeResponse.json();
    const locationResponse = await fetch(`${get(locations).incident_monitoring_web}/rft-rtls-location`);
    if (locationResponse.status !== 200) {
        log.error({ status: locationResponse.status });
        return error(404);
    }
    const incidentLocation = await locationResponse.json();
    const categoryResponse = await fetch(`${get(locations).incident_monitoring_web}/category`);
    if (categoryResponse.status !== 200) {
        log.error({ status: categoryResponse.status });
        return error(404);
    }
    const incidentCategory = await categoryResponse.json();
    // const userId = locals.user.claims?.sub;
    // const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    // const access_token = locals.user.access_token;
    const page = url.searchParams.get('page') || 1;
    const size = url.searchParams.get('size') || 9999;
    // const cfgParam = await CfgParameterService.Find(token, INVENTORY_URL, 'InstitutionAndContactDetailsForMOHIncidentReport');
    const res = await fetch(`${get(locations).workflow_api}/v1/workflow_template?page=${page}&size=${size}`, {
        method: 'GET',
        headers: {
            'content-type': 'application/json',
            'Authorization': 'Bearer ' + locals.user.access_token,
        },
    });

    const templates = await res.json();
    return {
        reportData: {
          ...reportData.data
        },
        incidentTypeList: incidentType.data.data,
        incidentLocationList: incidentLocation.data.data,
        incidentCategoryList:  incidentCategory.data.data,
        // cfgParam,
        templates
      
    };
};
