<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';
    // import { faChartLine } from '@fortawesome/pro-duotone-svg-icons';
    import { faCircle, faChartLine } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import gql from 'graphql-tag';

    // Plugin to allow usage of durations
    dayjs.extend(dayjs.duration);

    export const widgetTitle = 'Supply vs Demand';
    export const widgetIcon = faChartLine;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetEnabled = true;
    export const widgetMinWidth = 10;
    export const widgetMinHeight = 7;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 24;

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
</script>

<script lang="ts">
    import { browserBroadcaster, Channel } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faSquare } from '@fortawesome/free-solid-svg-icons';
    import type { EChartsOption, EChartsType, TooltipComponentFormatterCallbackParams } from 'echarts';
    import { intersect } from 'mathjs';
    import { onDestroy, onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { pipe as wPipe, subscribe as wSubscribe, type Subscription } from 'wonka';
    import type { SupplyAndDemandConfig } from './WidgetConfig/index.svelte';

    export let config: SupplyAndDemandConfig;

    type SupplyAndDemand = {
        supply: number;
        demand: number;
        date_time: ISO8601Date;
    };

    type ChartDataPoint = {
        date_time: ISO8601Date;
        current_supply?: number;
        current_demand?: number;
        predicted_supply?: number;
        predicted_demand?: number;
        current_minimum?: number;
        predicted_minimum?: number;
        current_deficit?: number;
        predicted_deficit?: number;
    };

    function toPrecisionIfNecessary(value, dp) {
        return +parseFloat(value).toPrecision(dp);
    }

    //Chart neccessities
    let chartContainer: HTMLElement;
    let chart: EChartsType;
    let option: EChartsOption;
    let subscription: Subscription;

    const distanceMonths = dayjs.duration({ months: (config?.windowMonths ?? 0) / 2 });

    const startDate: ISO8601Date = dayjs().subtract(distanceMonths.asMonths(), 'month').toISOString();
    const endDate: ISO8601Date = dayjs().add(distanceMonths.asMonths(), 'month').toISOString();

    // eCharts options
    option = {
        textStyle: {
            fontFamily: 'Noto Sans',
        },
        grid: {
            top: '20',
            bottom: '60',
            left: '60',
            right: '25',
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                snap: false,
                lineStyle: {
                    type: 'solid',
                },
                z: 1,
            },
            backgroundColor: '#0e0f0f',
            textStyle: {
                color: '#FFFFFF',
            },
            borderColor: '#545454',
            formatter: (params: TooltipComponentFormatterCallbackParams) => {
                let supply = 'Supply: ';
                let demand = 'Demand: ';
                let supplyKey = '';
                let demandKey = '';
                let note = '';
                let supplySeries: any;
                let demandSeries: any;
                let color = '#FFFFF';

                if (!Array.isArray(params) || params.length <= 0) {
                    // params should be an array with all our seriers data, it's not just return blank
                    return '';
                }

                if (!params[0].value) {
                    // A value is expected here, otherwise there might be corrupted data
                    return '';
                }

                // Check if line is after today
                if (dayjs(params[0].value['date_time']).isAfter(dayjs())) {
                    supplyKey = 'predicted_supply';
                    demandKey = 'predicted_demand';
                    supplySeries = params.find(e => e.seriesName === 'Predicted Supply');
                    demandSeries = params.find(e => e.seriesName === 'Predicted Fulfillable Demand');
                } else {
                    supplyKey = 'current_supply';
                    demandKey = 'current_demand';
                    supplySeries = params.find(e => e.seriesName === 'Current Supply');
                    demandSeries = params.find(e => e.seriesName === 'Current Fulfillable Demand');
                }

                let supplyValue = supplySeries.value[supplyKey];
                let demandValue = demandSeries.value[demandKey];

                if (supplyValue === demandValue) {
                    supplyValue = demandValue = toPrecisionIfNecessary(supplyValue, 2);
                } else {
                    // Arbitrarily decided to round down supply and round up demand
                    // e.g. since demand of 3.1 would require 4 items to fulfil, we round up
                    // e.g. since supply of 3.5 wouldn't be able to fulfil 4, we round down
                    supplyValue = Math.floor(supplyValue);
                    demandValue = Math.ceil(demandValue);
                }

                let diff = supplyValue - demandValue;

                if (diff < 0) {
                    note = diff + ' <span style="font-size: 0.7rem">(deficit)</span>';
                    color = '#FB5405';
                } else if (diff > 0) {
                    note = diff + ' <span style="font-size: 0.7rem">(surplus)</span>';
                    color = '#05FB1E';
                } else {
                    note = diff + ' <span style="font-size: 0.7rem">(surplus)</span>';
                    color = '#E5DE00';
                }

                supply += supplyValue;
                demand += demandValue;

                return `<div style='padding-right: 25px'>
                            <div style="font-size: 1rem; color: ${color}">
                                ${note}
                            </div>
                            <div style="font-size: 0.75rem">
                                ${supply}
                            </div>
                            <div style="font-size: 0.75rem">
                                ${demand}
                            </div>
                            <div style="font-size: 0.6rem; opacity: 0.4">
                                ${dayjs(params[0].value['date_time']).format('D MMM YYYY')}
                            </div>
                        <div>`;
            },
        },
        xAxis: {
            type: 'time',
            axisLabel: {
                formatter: (value, _index) => {
                    // Only show year at the boundaries (December and January)
                    if (dayjs(value).month() === 0 || dayjs(value).month() === 11) {
                        return dayjs(value).format("MMM 'YY");
                    }

                    return dayjs(value).format('MMM');
                },
                margin: 20,
                fontSize: 14,
                color: '#AEAEAE',
            },
            axisTick: {
                show: false,
            },
            splitNumber: (config?.windowMonths ?? 0) / 2,
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                margin: 20,
                fontSize: 14,
                color: '#AEAEAE',
            },
            splitLine: {
                lineStyle: {
                    opacity: 0.1,
                },
            },
            interval: 2,
        },
        series: [
            {
                name: 'Current Supply',
                dimensions: ['date_time', 'current_supply'],
                type: 'line',
                symbol: 'circle',
                showSymbol: false,
                symbolSize: 7,
                z: 10,
                itemStyle: {
                    color: '#91DEDF',
                },
                lineStyle: {
                    color: '#91DEDF',
                },
                markLine: {
                    symbol: 'none',
                    label: {
                        show: true,
                        position: 'insideEndTop',
                    },
                    tooltip: {
                        show: false,
                    },
                    data: [
                        {
                            name: 'Medium risk threshold',
                            yAxis: config?.mediumRiskThreshold ?? 0,
                            label: {
                                formatter: params => {
                                    return params.name + ': ' + params.value;
                                },
                                color: '#EF8D32',
                                fontWeight: 400,
                            },
                            lineStyle: {
                                color: '#EF8D32',
                                type: [3, 4],
                            },
                        },
                        {
                            name: 'High risk threshold',
                            yAxis: config?.highRiskThreshold ?? 0,
                            label: {
                                formatter: params => {
                                    return params.name + ': ' + params.value;
                                },
                                color: '#FA2205',
                                fontWeight: 400,
                            },
                            lineStyle: {
                                color: '#FA2205',
                                type: [3, 4],
                            },
                        },
                        {
                            name: 'Now',
                            xAxis: dayjs().toISOString(),
                            label: {
                                show: false,
                                fontSize: 0,
                            },
                            lineStyle: {
                                type: [2, 2],
                                color: '#FFFFFF',
                                opacity: 0.35,
                            },
                        },
                    ],
                },
                emphasis: {
                    disabled: true,
                },
                triggerLineEvent: true,
            },
            {
                name: 'Current Fulfillable Demand',
                dimensions: ['date_time', 'current_minimum'],
                type: 'line',
                stack: 'Current Demand',
                symbol: 'none',
                itemStyle: {
                    color: '#91DEDF',
                },
                lineStyle: {
                    color: '#91DEDF',
                    width: 0,
                },
                areaStyle: {
                    opacity: 0.5,
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 1,
                        x2: 0,
                        y2: 0,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(145, 222, 223, 0.3)', // color at 0%
                            },
                            {
                                offset: 1,
                                color: 'rgba(145, 222, 223, 1)', // color at 100%
                            },
                        ],
                    },
                },
                emphasis: {
                    disabled: true,
                },
            },
            {
                name: 'Current Deficit',
                dimensions: ['date_time', 'current_deficit'],
                type: 'line',
                stack: 'Current Demand',
                symbol: 'none',
                itemStyle: {
                    color: '#91DEDF',
                },
                lineStyle: {
                    color: '#91DEDF',
                    width: 0,
                },
                areaStyle: {
                    color: '#FB2305',
                    opacity: 0.25,
                },
                emphasis: {
                    disabled: true,
                },
            },
            {
                name: 'Predicted Supply',
                dimensions: ['date_time', 'predicted_supply'],
                type: 'line',
                symbol: 'circle',
                showSymbol: false,
                symbolSize: 7,
                z: 10,
                itemStyle: {
                    color: '#91DEDF',
                },
                lineStyle: {
                    type: [5, 4],
                    color: '#91DEDF',
                    opacity: 0.5,
                },
                emphasis: {
                    disabled: true,
                },
            },
            {
                name: 'Predicted Fulfillable Demand',
                dimensions: ['date_time', 'predicted_minimum'],
                type: 'line',
                stack: 'Predicted Demand',
                symbol: 'none',
                itemStyle: {
                    color: '#91DEDF',
                },
                lineStyle: {
                    color: '#91DEDF',
                    opacity: 0.07,
                    width: 1,
                },
                areaStyle: {
                    color: '#91DEDF',
                    opacity: 0.07,
                },
                emphasis: {
                    disabled: true,
                },
            },
            {
                name: 'Predicted Deficit',
                dimensions: ['date_time', 'predicted_deficit'],
                type: 'line',
                stack: 'Predicted Demand',
                symbol: 'none',
                itemStyle: {
                    color: '#91DEDF',
                },
                lineStyle: {
                    color: '#91DEDF',
                    width: 0,
                },
                areaStyle: {
                    color: '#FB2305',
                    opacity: 0.25,
                },
                emphasis: {
                    disabled: true,
                },
            },
        ],
        animation: true,
    };

    // Populate chart with data whenever possible
    $: if (Boolean(option) && Boolean(chart) && Boolean(config)) {
        chart?.setOption(option);
    }

    const loadChartData = result => {
        let data: Array<SupplyAndDemand> = result?.data[config.dataSource];

        let source: Array<ChartDataPoint> = [];

        if (data && data.length > 1) {
            for (let index = 0; index < data.length; index++) {
                let datapoint = data[index];

                // Check for intersections - add these extra points in for the demand (overlay)
                // This is necessary to ensure all the deficit/red areas are covered up
                if (index > 0 && source.length > 0) {
                    let previousDataPointSource = source[source.length - 1];

                    let demandStartPoint = [
                        dayjs(previousDataPointSource.date_time).unix(),
                        previousDataPointSource.current_demand ?? previousDataPointSource.predicted_demand,
                    ];

                    let demandEndPoint = [dayjs(datapoint.date_time).unix(), datapoint.demand];

                    let supplyStartPoint = [
                        dayjs(previousDataPointSource.date_time).unix(),
                        previousDataPointSource.current_supply ?? previousDataPointSource.predicted_supply,
                    ];

                    let supplyEndPoint = [dayjs(datapoint.date_time).unix(), datapoint.supply];

                    // Note: This function extrapolates lines to infinity
                    // We must layer check if the intersection point is within the line segment
                    let intersectionPoint = intersect(
                        demandStartPoint,
                        demandEndPoint,
                        supplyStartPoint,
                        supplyEndPoint
                    );

                    if (intersectionPoint) {
                        let [x, y] = intersectionPoint;
                        let minX = demandStartPoint[0] < demandEndPoint[0] ? demandStartPoint[0] : demandEndPoint[0];
                        let maxX = demandStartPoint[0] >= demandEndPoint[0] ? demandStartPoint[0] : demandEndPoint[0];

                        let minY = demandStartPoint[1] < demandEndPoint[1] ? demandStartPoint[1] : demandEndPoint[1];
                        let maxY = demandStartPoint[1] >= demandEndPoint[1] ? demandStartPoint[1] : demandEndPoint[1];

                        // Check if intersection point is WITHIN the line segment
                        if (
                            minX <= (x as number) &&
                            (x as number) <= maxX &&
                            minY <= (y as number) &&
                            (y as number) <= maxY
                        ) {
                            if (dayjs.unix(x as number).isBefore(dayjs())) {
                                // Note at intersection deficit is always 0
                                source.push({
                                    date_time: dayjs.unix(x as number).toISOString(),
                                    current_demand: y as number,
                                    current_supply: y as number,
                                    current_minimum: y as number,
                                    current_deficit: 0,
                                });
                            } else {
                                // Note at intersection deficit is always 0
                                source.push({
                                    date_time: dayjs.unix(x as number).toISOString(),
                                    predicted_demand: y as number,
                                    predicted_supply: y as number,
                                    predicted_minimum: y as number,
                                    predicted_deficit: 0,
                                });
                            }
                        }
                    }
                }

                // Dates falling after today are predicted dates, before are 'current' dates
                if (dayjs(datapoint.date_time).isBefore(dayjs())) {
                    let minimum = datapoint.demand > datapoint.supply ? datapoint.supply : datapoint.demand;
                    let deficit = datapoint.demand - minimum;

                    source.push({
                        date_time: datapoint.date_time,
                        current_demand: datapoint.demand,
                        current_supply: datapoint.supply,
                        current_minimum: minimum,
                        current_deficit: deficit,
                    });
                } else {
                    // Add predicted data joined from the current data
                    // This logic assumes data is sorted (the subscription orders data by date_time)
                    if (index > 0) {
                        let previousDatapoint = data[index - 1];

                        // Calculate from the perspective of drawing the line from past to present day
                        if (dayjs(previousDatapoint.date_time).isBefore(dayjs())) {
                            // Linearly interpolate to current day
                            let x = dayjs();
                            let x0 = dayjs(previousDatapoint.date_time);
                            let x1 = dayjs(datapoint.date_time);
                            let denominator = x1.diff(x0);

                            let y0supply = previousDatapoint.supply;
                            let y1supply = datapoint.supply;
                            let ySupply = (y0supply * x1.diff(x) + y1supply * x.diff(x0)) / denominator;

                            let y0demand = previousDatapoint.demand;
                            let y1demand = datapoint.demand;
                            let yDemand = (y0demand * x1.diff(x) + y1demand * x.diff(x0)) / denominator;

                            // Add that interpolated data to both current and predicted supply and demand
                            // This causes the lines to join up at the current date, even though the data
                            // for it might not exist

                            let minimum = yDemand > ySupply ? ySupply : yDemand;
                            let deficit = yDemand - minimum;

                            // These points touch at the current date (today)
                            source.push({
                                date_time: x.toISOString(),
                                current_demand: yDemand,
                                current_supply: ySupply,
                                current_minimum: minimum,
                                current_deficit: deficit,
                            });

                            // These points touch at the current date (today)
                            source.push({
                                date_time: x.toISOString(),
                                predicted_demand: yDemand,
                                predicted_supply: ySupply,
                                predicted_minimum: minimum,
                                predicted_deficit: deficit,
                            });
                        }
                    }

                    let minimum = datapoint.demand > datapoint.supply ? datapoint.supply : datapoint.demand;
                    let deficit = datapoint.demand - minimum;

                    // Add predicted data
                    source.push({
                        date_time: datapoint.date_time,
                        predicted_demand: datapoint.demand,
                        predicted_supply: datapoint.supply,
                        predicted_minimum: minimum,
                        predicted_deficit: deficit,
                    });
                }
            }
        }

        // eCharts seems to render sequentially - order matters, anything in higher index with earlier date
        // does not get rendered
        source.sort((a, b) => dayjs(a.date_time).diff(b.date_time));

        // Update dataset - reactive statement will update option
        option.dataset = {
            source,
        };
    };

    const FetchDataDocument = gql`
        query SupplyAndDemandWidget($startDate: timestamptz, $endDate: timestamptz) {
            ${config.dataSource}(
                where: { date_time: { _gte: $startDate, _lte: $endDate } }
                order_by: { date_time: asc }
            ) {
                supply
                demand
                date_time
            }
        }
    `;

    const ObserveDataDocument = gql`
        subscription SupplyAndDemandWidget($startDate: timestamptz, $endDate: timestamptz) {
            ${config.dataSource}(
                where: { date_time: { _gte: $startDate, _lte: $endDate } }
                order_by: { date_time: asc }
            ) {
                supply
                demand
                date_time
            }
        }
    `;

    const client = get(gqlClientStore);
    client.query(FetchDataDocument, {}).toPromise().then(loadChartData);

    onMount(async () => {
        subscription = wPipe(
            client.subscription(ObserveDataDocument, {
                startDate,
                endDate,
            }),
            wSubscribe(loadChartData)
        );

        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _m => {
            if (chart) chart.resize();
        });

        const echarts = await import('echarts');

        chart = echarts.init(chartContainer);

        window.addEventListener('resize', () => {
            if (chart) chart.resize();
        });
    });

    onDestroy(async () => {
        subscription?.unsubscribe();
    });
</script>

<div class="h-full w-full font-inter text-white">
    <!-- Title -->
    <div class="h-[22.5%] w-full px-5 pt-4">
        <div class="text-center text-lg text-white/70">Supply vs Demand</div>
        <div class="flex justify-between">
            <div class="">
                <div class="text-left text-lg">
                    {config?.title}
                </div>
                <div class="opacity-50">
                    {dayjs(startDate).format('MMM YYYY')} - {dayjs(endDate).format('MMM YYYY')}
                </div>
            </div>
            <div class="grid grid-cols-2 text-xs">
                <div class="flex flex-row items-center justify-start gap-1">
                    <span class="text-[#91DEDF]">{@html icon(faSquare).html}</span> Current Demand
                </div>
                <div class="flex flex-row items-center justify-start gap-1">
                    <div class="inline-block w-4 border-t-2 border-solid border-[#91DEDF]" />
                    Current Availability
                </div>
                <div class="flex flex-row items-center justify-start gap-1">
                    <span class="text-[#91DEDF]/20">{@html icon(faSquare).html}</span> Predicted Demand
                </div>
                <div class="flex flex-row items-center justify-start gap-1">
                    <div class="inline-block w-4">
                        <div class="absolute w-8 -translate-x-2 scale-x-50 border-t-2 border-dashed border-[#91DEDF]" />
                    </div>
                    Predicted Availability
                </div>
            </div>
        </div>
    </div>
    <!--E-chart-->
    <div data-tag="chart-container" class="h-[77.5%] w-full bg-[#171717]" on:click bind:this={chartContainer} />
</div>
