<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface AdvancedTableConfig {
        title?: string;
        dataSource?: string;
        dateFormat: string;
        numberOfRows: string;
        columns: {
            customName: string;
            name: string;
        }[];
        defaultSortingColumn: string;
        textHeaderAlignment: string;
        textContentAlignment: string;
        gridVisible: boolean;
        paginationVisible: boolean;
        stripedRows: boolean;
        sumVisible: boolean;
        sumHeaderVisible: boolean;
        mergeRowsOnSortedColumn: boolean;
        autoPagination: boolean;
        paginationPeriod: number;
        borderThickness: number;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_TITLE = 'View Current Consumable Item vs Incident';

    export const configDefaults: AdvancedTableConfig = {
        dataSource: '',
        title: DEFAULT_TITLE,
        dateFormat: 'DD/MM/YYYY',
        numberOfRows: '10',
        columns: [],
        defaultSortingColumn: '',
        textHeaderAlignment: 'left',
        textContentAlignment: 'left',
        gridVisible: false,
        paginationVisible: true,
        stripedRows: false,
        sumVisible: false,
        sumHeaderVisible: false,
        mergeRowsOnSortedColumn: false,
        autoPagination: false,
        paginationPeriod: 3,
        borderThickness: 1,
    };
</script>

<script lang="ts">
    //Components
    import { dataSource } from '$lib/core/core';

    import { onMount } from 'svelte';
    import {
        Input,
        Label,
        Select,
        MultiSelect,
        Listgroup,
        ListgroupItem,
        Toggle,
        Range,
        Tooltip,
        Button,
    } from 'flowbite-svelte';

    export let config: AdvancedTableConfig = configDefaults;

    let listDataSource: {
        value: string;
        name: string;
        fields: string[];
    }[] = [];

    let listColumn = [];
    let sortItems = [];

    let multiSelectValues = [];

    const alignItems = [
        { value: 'left', name: 'left' },
        { value: 'center', name: 'center' },
        { value: 'right', name: 'right' },
    ];

    $: sortItems = config?.columns?.map(c => ({ value: c.name, name: c.customName })) || [];

    const editColumnLabel = (event: Event, currentValue: string) => {
        config.columns = config.columns.map(e => {
            if (e.name === currentValue) {
                e.customName = (event as any).currentTarget.value;
            }
            return e;
        });
    };

    const onMultiSelectChange = () => {
        if (!multiSelectValues.length) {
            config = { ...config, columns: [] };
            return;
        }

        const selectedColumns = multiSelectValues.map(value => {
            const name = listColumn.find(column => column.value === value).value;
            const foundColumn = config.columns.find(column => column.name === value);
            return {
                name,
                customName: foundColumn ? foundColumn.customName : name,
            };
        });

        config = { ...config, columns: selectedColumns };
    };

    const initListColumn = (dataSource: string) => {
        listColumn = [
            ...listDataSource
                .find(e => e.value === dataSource)
                .fields.map(f => ({
                    value: f,
                    name: f,
                })),
        ];
    };

    onMount(() => {
        const unsubscribe = dataSource.subscribe(res => {
            const aggregations = res.__schema.types
                .filter(t => t.name.includes('aggregate') && t.name.split('_aggregate')[1] === '')
                .map(t => t.name.split('_aggregate')[0]);
            listDataSource = res.__schema.types
                .filter(e => aggregations.includes(e.name.split('_aggregate')[0]) && !e.name.includes('_aggregate_'))
                .map(e => ({
                    value: e.name,
                    name: e.name,
                    fields: e.fields ? e.fields.map(e => e.name) : [],
                }));
            if (!config.dataSource) {
                config = {
                    ...config,
                    columns: [],
                    dataSource: listDataSource[0].value,
                };
            }
            initListColumn(config.dataSource);
            multiSelectValues = [...config.columns.map(e => e.name)];
        });

        return () => {
            unsubscribe();
        };
    });

    const onDataSourceChange = (event: Event) => {
        initListColumn((event as any).currentTarget.value);
        config = {
            ...config,
            dataSource: (event as any).currentTarget.value,
            columns: [],
        };
        multiSelectValues = [];
    };

    const onNumberOfRowChanged = (event: Event) => {
        if ((event as any).currentTarget.value === '') {
            (event as any).currentTarget.value = '1';
        }
        let value = parseInt((event as any).currentTarget.value, 10);
        if (value < 1) {
            value = 1;
            (event as any).currentTarget.value = '1';
        }
        config.numberOfRows = value.toFixed();
    };

    const getTableHeaderAlignment = (event: any) => {
        config.textHeaderAlignment = event.currentTarget.value;
    };

    const getTableContentAlignment = (event: any) => {
        config.textContentAlignment = event.currentTarget.value;
    };
</script>

<div class="flex h-[50%] flex-col gap-2 overflow-auto">
    <div>
        <Label for="title" class="mb-2">Title</Label>
        <Input type="text" id="title" placeholder="Input Title" bind:value={config.title} />
    </div>

    <Label>
        Data Source
        <Select
            placeholder="select Data source"
            class="mt-2"
            items={listDataSource}
            value={config.dataSource}
            on:change={onDataSourceChange} />
    </Label>

    <div>
        <Label class="mb-2">Columns</Label>
        <MultiSelect
            items={listColumn}
            bind:value={multiSelectValues}
            on:change={onMultiSelectChange}
            defaultClass="z-50" />
    </div>

    <Listgroup class="mt-2">
        <span class="text-wrap p-2">Column Customisation</span>
        {#each multiSelectValues as v}
            <ListgroupItem>
                <div class="flex gap-2 w-full">
                    <div class="flex flex-col justify-between gap-2">
                        <Label>Current Label</Label>
                        <Input class="mt-auto" readonly value={config.columns.find(e => e.name === v)?.name} />
                    </div>
                    <div class="flex flex-col justify-between">
                        <Label>Customised Label</Label>
                        <Input
                            class="mt-auto"
                            type="text"
                            value={config.columns.find(e => e.name === v)
                                ? config.columns.find(e => e.name === v).customName
                                : v}
                            on:change={event => editColumnLabel(event, v)} />
                    </div>
                </div>
            </ListgroupItem>
        {/each}
    </Listgroup>

    <Label>
        Default Sorting Column
        <Select class="mt-2" items={sortItems} bind:value={config.defaultSortingColumn} />
    </Label>

    <div>
        <Label for="numberOfRows" class="mb-2">Number of Rows</Label>
        <Input
            type="number"
            id="numberOfRows"
            placeholder="Input Number of Rows"
            value={config.numberOfRows}
            on:change={onNumberOfRowChanged} />
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.paginationVisible}>Show Pagination</Toggle>
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.autoPagination}>Pagination Period (Seconds)</Toggle>
        {#if config.autoPagination}
            <div class="mb-2 mt-1 flex">
                <Tooltip triggeredBy="#paginationPeriod" placement="top">seconds</Tooltip>
                <span class="mr-2">
                    <Range
                        id="paginationPeriod"
                        size="sm"
                        min="3"
                        max="15"
                        bind:value={config.paginationPeriod}
                        step="1" />
                </span>
                <span>{config.paginationPeriod || 'N/A'}</span>
            </div>
        {/if}
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.gridVisible}>Show Grid</Toggle>
        {#if config.gridVisible}
            <Tooltip triggeredBy="#gridThickness" placement="top">Grid thickness</Tooltip>
            <div class="mb-2 mt-1 flex">
                <span class="mr-2">
                    <Range id="gridThickness" size="sm" min="1" max="4" bind:value={config.borderThickness} step="1" />
                </span>

                <span>{config.borderThickness || 'N/A'}</span>
            </div>
        {/if}
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.stripedRows}>Striped Rows</Toggle>
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.sumHeaderVisible}>Show Total Header</Toggle>
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.sumVisible}>Show Total Table</Toggle>
    </div>
    <div>
        <Toggle color="blue" bind:checked={config.mergeRowsOnSortedColumn}>Merge Rows On Sorted Column</Toggle>
    </div>

    <div>
        <Label for="tableHeaderAlignment" class="mb-1">Table Header Alignment</Label>
        <div class="flex items-center">
            {#each alignItems as item}
                <Button
                    class="w-20 h-10 capitalize ml-2 dark:focus-within:ring-gray-900 {item.value !== config.textHeaderAlignment &&
                        'dark:bg-gray-700 hover:bg-blue-800'}"
                    color={item.value === config.textHeaderAlignment ? 'blue' : 'none'}
                    value={item.value}
                    on:click={e => getTableHeaderAlignment(e)}
                    >{item.name}
                </Button>
            {/each}
        </div>
    </div>

    <div>
        <Label for="tableContentAlignment" class="mb-1">Table Content Alignment</Label>
        <div class="flex items-center">
            {#each alignItems as item}
                <Button
                    class="w-20 h-10 capitalize ml-2 dark:focus-within:ring-gray-900 {item.value !==
                        config.textContentAlignment && 'dark:bg-gray-700 hover:bg-blue-700'}"
                    color={item.value === config.textContentAlignment ? 'blue' : 'none'}
                    value={item.value}
                    on:click={e => getTableContentAlignment(e)}
                    >{item.name}
                </Button>
            {/each}
        </div>
    </div>
    <div></div>
</div>
