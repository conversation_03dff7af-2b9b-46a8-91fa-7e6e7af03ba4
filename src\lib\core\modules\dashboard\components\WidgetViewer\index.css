.grid-stack .grid-stack-placeholder > .placeholder-content {
    background-color: var(--on-primary) !important;
    box-shadow: 0 0 4px var(--on-primary) !important;
    border-radius: 1.5rem;
    border-color: var(--outline);
    border-width: 1px;
}

@media (prefers-color-scheme: dark) {
    .grid-stack .grid-stack-placeholder > .placeholder-content {
        background-color: var(--on-primary) !important;
        box-shadow: 0 0 4px var(--on-primary) !important;
        border-radius: 1.5rem;
        border-color: var(--outline);
        border-width: 1px;
    }
}

.grid-stack.gs-24 > .grid-stack-item {
    width: 4.167%;
    min-width: 4.167%;
}
.grid-stack.gs-24 > .grid-stack-item {
    left: 0%;
}

.grid-stack.gs-24 > .grid-stack-item[gs-w='1'] {
    width: 4.167%;
    min-width: 4.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='1'] {
    left: 4.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='2'] {
    width: 8.333%;
    min-width: 8.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='2'] {
    left: 8.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='3'] {
    width: 12.5%;
    min-width: 12.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='3'] {
    left: 12.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='4'] {
    width: 16.667%;
    min-width: 16.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='4'] {
    left: 16.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='5'] {
    width: 20.833%;
    min-width: 20.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='5'] {
    left: 20.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='6'] {
    width: 25%;
    min-width: 25%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='6'] {
    left: 25%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='7'] {
    width: 29.167%;
    min-width: 29.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='7'] {
    left: 29.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='8'] {
    width: 33.333%;
    min-width: 33.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='8'] {
    left: 33.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='9'] {
    width: 37.5%;
    min-width: 37.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='9'] {
    left: 37.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='10'] {
    width: 41.667%;
    min-width: 41.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='10'] {
    left: 41.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='11'] {
    width: 45.833%;
    min-width: 45.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='11'] {
    left: 45.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='12'] {
    width: 50%;
    min-width: 50%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='12'] {
    left: 50%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='13'] {
    width: 54.167%;
    min-width: 54.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='13'] {
    left: 54.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='14'] {
    width: 58.333%;
    min-width: 58.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='14'] {
    left: 58.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='15'] {
    width: 62.5%;
    min-width: 62.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='15'] {
    left: 62.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='16'] {
    width: 66.667%;
    min-width: 66.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='16'] {
    left: 66.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='17'] {
    width: 70.833%;
    min-width: 70.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='17'] {
    left: 70.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='18'] {
    width: 75%;
    min-width: 75%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='18'] {
    left: 75%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='19'] {
    width: 79.167%;
    min-width: 79.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='19'] {
    left: 79.167%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='20'] {
    width: 83.333%;
    min-width: 83.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='20'] {
    left: 83.333%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='21'] {
    width: 87.5%;
    min-width: 87.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='21'] {
    left: 87.5%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='22'] {
    width: 91.667%;
    min-width: 91.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='22'] {
    left: 91.667%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='23'] {
    width: 95.833%;
    min-width: 95.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-x='23'] {
    left: 95.833%;
}
.grid-stack.gs-24 > .grid-stack-item[gs-w='24'] {
    width: 100% !important;
    min-width: 100%;
}

.ui-resizable-se {
    right: 0.5rem !important;
    bottom: 0.5rem !important;
}

.grid-display {
    background-image: url('/images/dashboard/grid-square.png');
    background-size: calc(4.167%);
    background-position: 0px 0px, 0px 0px;
}

.grid-display .grid-stack-sub-grid > .grid-stack-item-content {
    background-image: url('/images/dashboard/grid-square.png');
    background-size: calc(var(--cell-height) * 1px) calc(var(--cell-height) * 1px);
    background-position: 0px 0px, 0px 0px;
    background-attachment: local;
}
