<script lang="ts">
    import { v4 as uuidv4 } from 'uuid';

    export let bgColor = 'bg-primary';
    export let id;
    export let value;

    export let minlength;
    export let maxlength;

    //Validation patterns
    export let pattern;

    if (id == null) {
        id = uuidv4();
    }
</script>

<span class="relative {bgColor} flex" tabIndex={-1}>
    <input
        {id}
        {minlength}
        {maxlength}
        {pattern}
        type="password"
        disabled={$$props.disabled}
        bind:value
        class="peer w-full rounded border-2 border-on-surface/[0.12] bg-inherit p-1 pl-1 text-sm text-on-surface/[0.54] placeholder-transparent outline-none invalid:border-error focus:border-secondary"
        placeholder={$$props.placeholder} />

    <label
        for={id}
        class="absolute -top-2 left-2 h-fit w-fit rounded bg-inherit px-0.5 text-xs font-semibold text-on-surface/[0.54]  transition-all peer-placeholder-shown:right-10 peer-placeholder-shown:top-1 peer-placeholder-shown:text-sm peer-placeholder-shown:font-medium peer-placeholder-shown:text-gray-400 peer-focus:text-secondary"
        >{$$props.placeholder}</label>
</span>
