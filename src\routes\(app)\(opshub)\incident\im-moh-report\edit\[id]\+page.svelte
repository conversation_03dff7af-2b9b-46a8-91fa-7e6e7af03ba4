<script lang="ts">
    import { title } from '$lib/core/components/PageTitle/index.svelte';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import { callExternalApi } from '$routes/(app)/(opshub)/sop-dashboard/utils';
    import { error } from '@sveltejs/kit';
    import { onMount } from 'svelte';
    import { get } from 'svelte/store';
    import MOHReportEditForm from '../../components/MOHReportEditForm.svelte';

    $title = 'Edit Incident';

    export let data;

    let dataForm;
    let submitForm;

    // Modal config
    let popupModal = false;
    let modalTitle = 'Warning Message';
    let modalMessage = 'This field cannot be empty.';
    let onlyOkBtn = true;
    let confirmAction;

    const id = data.reportData.id;

    // Helper function to trigger approval workflows
    async function triggerApprovalWorkflow(reportId: string, templateId: string) {
        try {
            const sopUrl = get(locations).sop_web;
            const tenantId = get(user).claims.active_tenant.tenant_id;
            const templateName = data.templates.data.find(t => t.id === templateId)?.name;

            if (!templateName) {
                console.warn('Workflow template not found');
                return;
            }

            // Trigger the workflow
            const workflowData = await callExternalApi(`${sopUrl}/workflow/trigger/`, {
                method: 'POST',
                body: JSON.stringify({
                    template_id: templateId,
                    metadata: {
                        tenant_id: tenantId,
                        report_id: reportId,
                    },
                }),
            });

            // Activate the workflow
            await callExternalApi(`${sopUrl}/sys-wf-activation/`, {
                method: 'POST',
                body: JSON.stringify({
                    template_name: templateName,
                    template_id: templateId,
                    workflow_id: workflowData?.data?.data?.workflow_id,
                    is_manual_trigger: true,
                }),
            });

            console.log(`Workflow triggered successfully for`);
        } catch (err) {
            console.error('Error triggering workflow:', err);
            // Don't throw error here as the main operation (updating report) was successful
        }
    }

    onMount(async () => {
        const reportData = data.reportData;
        // const cfgParam = data.cfgParam;
        console.log({ reportData });
        const initForm = {
            ID: reportData.id,
            Institution_Name: reportData.institution_name,
            Institution_Office: reportData.institution_office,
            Incident_Type_Id: reportData.incident_type_id,
            Incident_At: new Date(reportData.incident_at),
            Incident_Location_Id: reportData.incident_location_id,
            Incident_Category_Id: reportData.incident_category_id,
            Risk_Assessment: reportData.risk_assessment,
            Reporter_Info: reportData.reporter_info,
            Description: reportData.description,
            Institution_Phone: reportData.institution_phone_no,
            Institution_Email: reportData.institution_email,
            Comments: reportData.comments,
            Report_Status: reportData.report_status,
            Last_Rejected_Level: reportData.last_rejected_level,
        };
        dataForm = initForm;
    });
    async function handleSaveMohReport(payload: any) {
        console.log('Saving MOH Report as draft:', payload);

        // Ensure the report stays in Draft status when saving as draft
        const updatedPayload = {
            ...payload,
            Report_Status: 'Draft', // Keep as draft
        };

        try {
            const res = await fetch(`/api/incident-monitoring/moh-report/${id}`, {
                method: 'PATCH',
                body: JSON.stringify(updatedPayload),
            });

            const response = await res.json();

            if (res.status === 200) {
                // Show success notification
                popupModal = true;
                modalTitle = 'Notification';
                modalMessage = 'MOH Report has been saved as draft successfully!';
                onlyOkBtn = true;
                confirmAction = () => {
                    location.href = `/incident/im-moh-report/list`;
                };

                // Note: No workflow trigger for draft saves
                console.log('Draft saved successfully');
            } else {
                throw new Error(response.statusText || 'Failed to save draft');
            }
        } catch (err) {
            console.error('Error saving draft:', err);
            popupModal = true;
            modalTitle = 'Error';
            modalMessage = 'Failed to save report as draft. Please try again.';
            onlyOkBtn = true;
            confirmAction = () => {};
        }
    }
    async function handleSendForApproval(payload: any, buttonType: string) {
        console.log('Sending for approval:', buttonType, payload);
        // Update the report status to indicate it's ready for approval
        const updatedPayload = {
            ...payload,
            Report_Status: 'Ready for Send', // Change status from Draft
            Last_Rejected_Level: null, // Reset rejection level
        };

        try {
            const res = await fetch(`/api/incident-monitoring/moh-report/${id}`, {
                method: 'PATCH',
                body: JSON.stringify(updatedPayload),
            });

            const response = await res.json();

            if (res.status === 200) {
                // Show success notification
                popupModal = true;
                modalTitle = 'Notification';
                modalMessage = 'MOH Report has been sent for approval successfully!';
                onlyOkBtn = true;
                confirmAction = () => {
                    location.href = `/incident/im-moh-report/list`;
                };

                // Trigger workflow for approval process
                await triggerApprovalWorkflow(response.id, '5dab966b-96aa-4221-9252-93f68e9d149f');
            } else {
                throw new Error(response.statusText || 'Failed to send for approval');
            }
        } catch (err) {
            console.error('Error sending for approval:', err);
            popupModal = true;
            modalTitle = 'Error';
            modalMessage = 'Failed to send report for approval. Please try again.';
            onlyOkBtn = true;
            confirmAction = () => {};
        }
    }
    async function handleSendForDirectApproval(payload: any, buttonType: string) {
        console.log('Sending for direct approval:', buttonType, payload);

        // Update the report status and set direct approval level
        const updatedPayload = {
            ...payload,
            Report_Status: 'Ready for Send',
            Last_Rejected_Level: null, // Reset rejection level
            Approval_Level: 2, // Direct approval level
        };

        try {
            const res = await fetch(`/api/incident-monitoring/moh-report/${id}`, {
                method: 'PATCH',
                body: JSON.stringify(updatedPayload),
            });

            const response = await res.json();

            if (res.status === 200) {
                // Show success notification
                popupModal = true;
                modalTitle = 'Notification';
                modalMessage = 'MOH Report has been sent for direct approval successfully!';
                onlyOkBtn = true;
                confirmAction = () => {
                    location.href = `/incident/im-moh-report/list`;
                };

                // Trigger workflow for direct approval process
                await triggerApprovalWorkflow(response.id, '5dab966b-96aa-4221-9252-93f68e9d149f');
            } else {
                throw new Error(response.statusText || 'Failed to send for direct approval');
            }
        } catch (err) {
            console.error('Error sending for direct approval:', err);
            popupModal = true;
            modalTitle = 'Error';
            modalMessage = 'Failed to send report for direct approval. Please try again.';
            onlyOkBtn = true;
            confirmAction = () => {};
        }
    }
    async function handleSendForCOOApproval(payload: any, buttonType: string) {
        console.log('Sending for COO approval:', buttonType, payload);
        // Update the report status and set COO approval level
        const updatedPayload = {
            ...payload,
            Report_Status: 'Ready for Send',
            Last_Rejected_Level: null, // Reset rejection level
            Approval_Level: 3, // COO approval level
        };

        try {
            const res = await fetch(`/api/incident-monitoring/moh-report/${id}`, {
                method: 'PATCH',
                body: JSON.stringify(updatedPayload),
            });

            const response = await res.json();

            if (res.status === 200) {
                // Show success notification
                popupModal = true;
                modalTitle = 'Notification';
                modalMessage = 'MOH Report has been sent for COO approval successfully!';
                onlyOkBtn = true;
                confirmAction = () => {
                    location.href = `/incident/im-moh-report/list`;
                };

                // Trigger workflow for COO approval process
                await triggerApprovalWorkflow(response.id, '5dab966b-96aa-4221-9252-93f68e9d149f');
            } else {
                throw new Error(response.statusText || 'Failed to send for COO approval');
            }
        } catch (err) {
            console.error('Error sending for COO approval:', err);
            popupModal = true;
            modalTitle = 'Error';
            modalMessage = 'Failed to send report for COO approval. Please try again.';
            onlyOkBtn = true;
            confirmAction = () => {};
        }
    }
</script>

<main class="mx-3">
    {#if dataForm}
        <MOHReportEditForm
            initData={dataForm}
            incidentTypeList={data.incidentTypeList}
            incidentLocationList={data.incidentLocationList}
            incidentCategoryList={data.incidentCategoryList}
            onSaveAsDraft={handleSaveMohReport}
            actionType="edit"
            onSendForApproval={handleSendForApproval}
            onSendForDirectApproval={handleSendForDirectApproval}
            onSendForCOOApproval={handleSendForCOOApproval}
            bind:submitForm />
    {/if}
</main>
<Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
