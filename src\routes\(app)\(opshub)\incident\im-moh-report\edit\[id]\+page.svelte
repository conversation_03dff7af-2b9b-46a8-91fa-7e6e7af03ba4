<script lang="ts">
    import { title } from '$lib/core/components/PageTitle/index.svelte';
    import Notification from '$lib/shared/components/notification/Notification.svelte';
    import { user } from '$lib/stores/Auth';
    import { locations } from '$lib/stores/Locations';
    import { error } from '@sveltejs/kit';
    import { onMount } from 'svelte';
    import { get } from 'svelte/store';
    import { superValidate } from 'sveltekit-superforms';
    import { zod } from 'sveltekit-superforms/adapters';
    import { v4 } from 'uuid';
    import { ButtonAction, INCIDENT_TABLES, incidentSchema, IncidentStatus } from '../../../schema';
    import {
        downloadIncidentReport,
        getIncidentCategory,
        getIncidentLocation,
        getIncidentType,
        updateDefaultResponseTracking,
    } from '../../../utils/file_utils';
    import { getIncidentError } from '../../../utils/input_utils';
    import MOHReportEditForm from '../../components/MOHReportEditForm.svelte';
    import { convertISOToUTC } from '$routes/(app)/(opshub)/audittrail/utils/function';
    import { TriggerService } from '$lib/service/trigger';
    import Guard from '$lib/shared/components/guard/Guard.svelte';
    import { SCOPE } from '$lib/shared/enum/general';
    import { callExternalApi } from '$routes/(app)/(opshub)/sop-dashboard/utils';

    $title = 'Edit Incident';

    export let data;

    let dataForm;
    let submitForm;

    // Modal config
    let popupModal = false;
    let modalTitle = 'Warning Message';
    let modalMessage = 'This field cannot be empty.';
    let onlyOkBtn = true;
    let confirmAction;

    const IncidentMonitoringURL = `${get(locations).incident_monitoring_web}`;
    const actionMessageMap = new Map();
    const id = data.reportData.id;

    // const validateData = () => {
    //     const formError = getIncidentError(submitForm, data?.abilities);
    //     if (!formError.isError) {
    //         return true;
    //     }

    //     popupModal = true;
    //     modalTitle = formError.errorTitle;
    //     modalMessage = formError.errorMessage;
    //     onlyOkBtn = true;
    //     confirmAction = () => {};
    // };

    onMount(async () => {
        const reportData = data.reportData;
        // const cfgParam = data.cfgParam;
        console.log({ reportData });
        const initForm = {
            ID: reportData.id,
            Institution_Name: reportData.institution_name,
            Institution_Office: reportData.institution_office,
            Incident_Type_Id: reportData.incident_type_id,
            Incident_At: new Date(reportData.incident_at),
            Incident_Location_Id: reportData.incident_location_id,
            Incident_Category_Id: reportData.incident_category_id,
            Risk_Assessment: reportData.risk_assessment,
            Reporter_Info: reportData.reporter_info,
            Description: reportData.description,
            Institution_Phone: reportData.institution_phone_no,
            Institution_Email: reportData.institution_email,
            Comments: reportData.comments,
            Report_Status: reportData.report_status,
            Last_Rejected_Level: reportData.last_rejected_level,
        };
        dataForm = initForm;
        console.log({ dataForm });
    });
    async function handleSaveMohReport(payload: any) {
        // if (!validateData()) {
        //     return;
        // }
        await fetch(`/api/incident-monitoring/moh-report/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(payload),
        }).then(async res => {
            const response = await res.json();
            if (res.status === 200) {
                popupModal = true;
                modalTitle = 'Notification';
                modalMessage = 'Moh Report is updated successfully!';
                onlyOkBtn = true;
                confirmAction = () => {
                    location.href = `/incident/im-moh-report/list`;
                };
                try {
                    const sopUrl = get(locations).sop_web;
                    const tenantId = get(user).claims.active_tenant.tenant_id;
                    const templateId = '5dab966b-96aa-4221-9252-93f68e9d149f';
                    const templateName = data.templates.data.find(t => t.id === templateId).name;
                    const workflowData = await callExternalApi(`${sopUrl}/workflow/trigger/`, {
                        method: 'POST',
                        body: JSON.stringify({
                            template_id: templateId,
                            metadata: {
                                tenant_id: tenantId,
                                report_id: response.id,
                            },
                        }),
                    });

                    await callExternalApi(`${sopUrl}/sys-wf-activation/`, {
                        method: 'POST',
                        body: JSON.stringify({
                            template_name: templateName,
                            template_id: templateId,
                            workflow_id: workflowData?.data?.data?.workflow_id,
                            is_manual_trigger: true,
                        }),
                    });
                } catch (err) {
                    error(400, err);
                }
            } else {
                error(400, response.statusText);
            }
        });
    }
    async function handleSendForApproval(payload: any) {}
    async function handleSendForDirectApproval(payload: any) {}
    async function handleSendForCOOApproval(payload: any) {}
</script>

<main class="mx-3">
    {#if dataForm}
        <MOHReportEditForm
            initData={dataForm}
            incidentTypeList={data.incidentTypeList}
            incidentLocationList={data.incidentLocationList}
            incidentCategoryList={data.incidentCategoryList}
            onSaveAsDraft={handleSaveMohReport}
            actionType="edit"
            onSendForApproval={handleSendForApproval}
            onSendForDirectApproval={handleSendForDirectApproval}
            onSendForCOOApproval={handleSendForCOOApproval}
            bind:submitForm />
    {/if}
</main>
<Notification bind:isOpen={popupModal} {modalTitle} {modalMessage} {onlyOkBtn} {confirmAction} />
