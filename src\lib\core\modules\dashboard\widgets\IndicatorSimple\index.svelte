<script lang="ts" context="module">
    import { getCurrentTimezoneOffset } from '../utils';
    import { logger } from '$lib/stores/Logger';
    import { faSquare, faCalendarDay, faGear } from '@fortawesome/free-solid-svg-icons';
    import dayjs from 'dayjs';
    import isToday from 'dayjs/plugin/isToday';
    import { onMount, afterUpdate } from 'svelte';
    import { Channel, browserBroadcaster } from '$lib/core/modules/browser_broadcaster/brower_broadcaster';
    import LocalizedFormat from 'dayjs/plugin/localizedFormat';
    import { JUSTIFY_iTEMS_OBJ } from '../utils';
    import Notification from '$lib/shared/components/notification/Notification.svelte';

    // Plugin to allow usage of durations
    dayjs.extend(isToday);
    dayjs.extend(LocalizedFormat);

    export const widgetIcon = faSquare;
    export const widgetCategory = 'Resource Management';
    export const widgetLimit = 999;
    export const widgetTitle = 'Simple Indicator';
    export const widgetEnabled = true;
    export const widgetMinWidth = 3;
    export const widgetMinHeight = 3;
    export const widgetMaxWidth = 24;
    export const widgetMaxHeight = 12;

    export let buttonIcon = faCalendarDay;
    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName, widgetTitle });
    const TEXT_DECORATION = {
        none: '',
        single: 'underline underline-offset-8 decoration-2',
        double: 'underline underline-offset-8 decoration-2 decoration-double',
    };
</script>

<script lang="ts">
    import {
        INDICATOR_VALUE_FORMAT,
        type IndicatorConfigItem,
        type IndicatorSimpleConfig,
    } from './WidgetConfig/index.svelte';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { get } from 'svelte/store';
    import { gqlClientStore } from '$lib/stores/Clients';
    import { user } from '$lib/stores/Auth';
    import type { UpdateCfgUserFilteringDto } from '$lib/shared/dto/cfg_user_filtering';
    import { queryStore, subscriptionStore, type OperationResultStore, type Pausable } from '@urql/svelte';
    import FormModal from '$lib/core/modules/DynamicForm/FormModal.svelte';
    import CfgParameterViewer from '$lib/shared/components/cfg-parameter/CfgParameterViewer.svelte';
    import { Button } from 'flowbite-svelte';
    import {
        GetCfgFilteringLabelDocument,
        GetCfgParameterByIdDocument,
        GetCfgUserFilteringByCfgParamUserIdDocument,
        GetCfgUserFilteringByCfgParamUserIdSubscriptionDocument,
        GetCfgUserFilteringLabelByCfgParamUserIdSubscriptionDocument,
        type GetCfgFilteringLabelSubscription,
        type GetCfgParameterByIdQuery,
        type GetCfgUserFilteringByCfgParamUserIdQuery,
        type GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription,
        type GetCfgUserFilteringLabelByCfgParamUserIdSubscriptionSubscription,
    } from '$generated-types';

    import { SOC_CFG_POPUP_INFO, SOC_CFG_POPUP_KEYS } from '$lib/shared/enum/general';
    import { getToggleStatus, mappingDataByKeys, standardizedFilterLabel } from '../../utils/utils';
    import { convertDateMonthQuarterYearToDateString } from '$lib/core/modules/DynamicForm/utils';

    export let config: IndicatorSimpleConfig;
    export let json_values;
    export let sendDataToDashboard;

    $: {
        function convertToCamelCase(input) {
            const parts = input ? input.split('_') : [];
            for (let i = 0; i < parts.length; i++) {
                parts[i] = parts[i].toLowerCase();
                if (parts[i].length > 0) {
                    parts[i] = parts[i][0].toUpperCase() + parts[i].slice(1);
                }
            }
            return parts.join('');
        }
        const indicatorValueKey = config.titleConfigItem.widgetName;
        if (json_values?.[indicatorValueKey] !== undefined && indicatorElement) {
            indicatorElement.style.color = json_values.IndicatorTxtColour;
            indicatorElement.style.backgroundColor = json_values.IndicatorBGColour;
        }
        indicatorValue = json_values?.[indicatorValueKey];
        config.indicatorConfigItem.value = indicatorValue ?? '0';
    }
    let simpleWidgetElement: HTMLElement;
    let titleElement: HTMLElement;
    let indicatorParentElement: HTMLElement;
    let indicatorElement: HTMLElement;
    let topSmallIconElement: HTMLElement;
    let bottomSmallIconElement: HTMLElement;

    let indicatorValue = '0';
    let isModalOpen: boolean = false;
    let modalMessage: string = '';

    const initialDataAndStyle = () => {
        resizeWidgetItems(indicatorElement, config.indicatorConfigItem.type, false, '');
        resizeWidgetItems(topSmallIconElement, config.topSmallIconConfigItem.type, true, 'top');
        resizeWidgetItems(bottomSmallIconElement, config.bottomSmallIconConfigItem.type, true, 'bottom');
        updateStyleForIndicator();
        updateStyleForSmallIconElement(topSmallIconElement, 'top');
        updateStyleForSmallIconElement(bottomSmallIconElement, 'bottom');
    };

    onMount(async () => {
        getUserFiltering();
        initialDataAndStyle();
        browserBroadcaster.sub(Channel.AOH_WIDGET_RESIZED, _ => {
            initialDataAndStyle();
        });
    });

    afterUpdate(() => {
        initialDataAndStyle();
    });

    const resizeWidgetItems = (
        widgetItem: HTMLElement,
        typeOfWidget: string,
        isIconWidget: boolean,
        positionOfIcon: string
    ) => {
        if (!widgetItem || !simpleWidgetElement) return;
        let minSize =
            Math.min(...[simpleWidgetElement.clientWidth, simpleWidgetElement.clientHeight].filter(m => m > 0)) - 20;

        if (isIconWidget) {
            widgetItem.style.width = widgetItem.style.height = minSize * 0.4 + 'px';
            const smallIconConfigItemPosition = positionOfIcon + 'SmallIconConfigItem';

            widgetItem.style.transform =
                config[smallIconConfigItemPosition].type !== 'prediction'
                    ? `scale(${config[smallIconConfigItemPosition].iconSize * 0.1})`
                    : '';
        } else {
            widgetItem.style.minWidth = 'auto';
            switch (typeOfWidget) {
                case 'circle':
                case 'square':
                    widgetItem.style.width = widgetItem.style.height = minSize * 0.7 + 'px';
                    break;
                case 'rectangle':
                    widgetItem.style.width = 'auto';
                    widgetItem.style.minWidth = minSize * 0.8 + 'px';
                    widgetItem.style.height = minSize * 0.5 + 'px';
                    break;
                case 'none':
                    widgetItem.style.width = widgetItem.style.height = '100%';
                    break;
            }
        }
    };

    function updateStyleForIndicator() {
        if (!indicatorElement) {
            return;
        }
        if (!config.indicatorThresholdConfig.isEnableThreshold) {
            indicatorElement.style.color = config.indicatorConfigItem.textColor;
            indicatorElement.style.backgroundColor = config.indicatorConfigItem.bgColor;
        }

        indicatorElement.className = '';
        simpleWidgetElement.style.backgroundColor = '';
        simpleWidgetElement.parentElement.parentElement.style.border = '';
        const borderStyle = config.indicatorConfigItem.isShowBorder
            ? `2px ${config.indicatorConfigItem.borderType} ` + config.indicatorConfigItem.borderColor
            : '';
        indicatorElement.style.fontSize = config.indicatorConfigItem.fontSize + 'px';
        indicatorElement.style.border = borderStyle;

        let classByTypeOfIndicator = '';
        switch (config.indicatorConfigItem.type) {
            case 'circle':
                classByTypeOfIndicator = 'h-full w-full rounded-full';
                break;
            case 'square':
                classByTypeOfIndicator = 'h-full w-full rounded-md';
                break;
            case 'rectangle':
                classByTypeOfIndicator = 'h-1/2 w-full rounded-md';
                break;
            case 'none':
                config.topSmallIconConfigItem.isEnable = false;
                config.bottomSmallIconConfigItem.isEnable = false;
                indicatorElement.style.border = '';
                indicatorElement.style.backgroundColor = 'transparent';
                simpleWidgetElement.style.backgroundColor = indicatorElement.style.backgroundColor;
                simpleWidgetElement.style.color = indicatorElement.style.color;
                simpleWidgetElement.parentElement.parentElement.style.border = borderStyle;
                break;
        }
        indicatorElement.className = 'relative flex items-center justify-center ' + classByTypeOfIndicator;
    }

    function updateStyleForSmallIconElement(smallIconElement: HTMLElement, positionOfIcon: string) {
        if (!smallIconElement || !simpleWidgetElement) {
            return;
        }
        let minSize =
            Math.min(...[simpleWidgetElement.clientWidth, simpleWidgetElement.clientHeight].filter(m => m > 0)) / 6;

        const smallIconConfigItemPosition = positionOfIcon + 'SmallIconConfigItem';
        const isPredictionIcon = config[smallIconConfigItemPosition].type === 'prediction';

        smallIconElement.className = '';
        smallIconElement.style.color = '';
        smallIconElement.style.backgroundColor = '';

        smallIconElement.style.fontSize = config[smallIconConfigItemPosition].fontSize + 'px';
        smallIconElement.style.color = config[smallIconConfigItemPosition].textColor;
        smallIconElement.style.backgroundColor = !isPredictionIcon ? config[smallIconConfigItemPosition].bgColor : '';
        const borderWidth = 2;
        smallIconElement.style.border =
            !isPredictionIcon && config[smallIconConfigItemPosition].isShowBorder
                ? borderWidth + 'px solid ' + config[smallIconConfigItemPosition].borderColor
                : '';

        let positonOfIconByPX =
            config.indicatorConfigItem.isShowBorder && isPredictionIcon
                ? minSize * -1 - 2 - borderWidth
                : minSize * -1 - 2;
        if (config.indicatorConfigItem.type === 'circle') {
            positonOfIconByPX =
                config.indicatorConfigItem.isShowBorder && isPredictionIcon
                    ? minSize * -0.5 - 2 - borderWidth
                    : minSize * -0.5 - 2;
        }
        smallIconElement.style[positionOfIcon] = smallIconElement.style.right = positonOfIconByPX + 'px';

        smallIconElement.className = `${
            config[smallIconConfigItemPosition].isEnable
                ? `absolute flex items-center justify-center text-center
             ${config[smallIconConfigItemPosition].type === 'circle' ? 'rounded-full' : 'rounded-md'}`
                : ' hidden '
        }`;
    }

    $: if (config) {
        resizeWidgetItems(indicatorElement, config.indicatorConfigItem.type, false, '');
        resizeWidgetItems(topSmallIconElement, config.topSmallIconConfigItem.type, true, 'top');
        resizeWidgetItems(bottomSmallIconElement, config.bottomSmallIconConfigItem.type, true, 'bottom');
        updateStyleForIndicator();
        updateStyleForSmallIconElement(topSmallIconElement, 'top');
        updateStyleForSmallIconElement(bottomSmallIconElement, 'bottom');
    }
    let unique = {};
    let openFilter = false;
    const onGearClick = () => {
        openFilter = true;
    };
    let form_fields_json;
    let key_value_json;
    let userFilteringData;
    let user_id = '';
    const client = get(gqlClientStore);
    let formModal;
    let dataCfgParameter: Record<string, string | boolean> = {};
    let titleFiltering;
    let subUserFiltering: OperationResultStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription> & Pausable;
    let customFilteringLabel: OperationResultStore<GetCfgFilteringLabelSubscription> & Pausable;

    $: if ($subUserFiltering?.data?.cc3_hoc_cfg_user_filtering) {
        key_value_json = $subUserFiltering.data?.cc3_hoc_cfg_user_filtering[0]?.key_value_json || {};
        if (userFilteringData)
            userFilteringData.filter_label = $subUserFiltering.data?.cc3_hoc_cfg_user_filtering[0]?.filter_label;
    }

    function getUserFiltering() {
        if (!config?.cfg_param) {
            return;
        }
        user_id = get(user).claims.hasura_access['x-hasura-user-id'];
        const queryCfgParams = queryStore<GetCfgParameterByIdQuery>({
            client,
            query: GetCfgParameterByIdDocument,
            variables: { id: config.cfg_param },
        });
        const unsubscriber = queryCfgParams.subscribe(res => {
            if (!res.fetching && !res.error) {
                const cfgParam = res.data?.cc3_hoc_cfg_parameter[0];
                form_fields_json = cfgParam?.form_fields_json || [];
                titleFiltering = cfgParam?.title || '';
                if (cfgParam) {
                    dataCfgParameter.id = cfgParam.id;
                    dataCfgParameter.name = cfgParam.name;
                    dataCfgParameter.is_custom_handler = cfgParam.is_custom_handler;
                }
                unsubscriber();
            } else if (!res.fetching && res.error) {
                unsubscriber();
            }
        });
        const queryUserFiltering = queryStore<GetCfgUserFilteringByCfgParamUserIdQuery>({
            client,
            query: GetCfgUserFilteringByCfgParamUserIdDocument,
            variables: { id: config.cfg_param, user_id },
        });
        const unsubscriberFiltering = queryUserFiltering.subscribe(res => {
            if (!res.fetching && !res.error) {
                userFilteringData = res.data?.cc3_hoc_cfg_user_filtering[0];
                key_value_json = res.data?.cc3_hoc_cfg_user_filtering[0]?.key_value_json || {};
                unsubscriberFiltering();
            } else if (!res.fetching && res.error) {
                unsubscriberFiltering();
            }
        });
        subUserFiltering = subscriptionStore<GetCfgUserFilteringByCfgParamUserIdSubscriptionSubscription>({
            client,
            query: GetCfgUserFilteringByCfgParamUserIdSubscriptionDocument,
            variables: { id: config.cfg_param, user_id },
        });

        if (config.showFilteringLabel && !config.label_use_filter_datasource && config.cfg_param_label) {
            const tenant_id = get(user).claims.active_tenant.tenant_id;
            customFilteringLabel = subscriptionStore<GetCfgFilteringLabelSubscription>({
                client,
                query: GetCfgFilteringLabelDocument,
                variables: { id: config.cfg_param_label, tenant_id },
            });
        }
    }

    const cfgSave = async data => {
        // Save to cfg_user_filtering
        let newData = data;
        let newFilterLabel;
        try {
            newData = JSON.stringify({ ...data, TimeZone: getCurrentTimezoneOffset() }).replaceAll(
                'T00:00:00.000Z',
                ''
            );
            newFilterLabel = JSON.stringify(data).replaceAll('T00:00:00.000Z', '');
        } catch (error) {}
        const userName = get(user)?.claims?.preferred_username;
        const tenant_id = get(user)?.claims?.active_tenant?.tenant_id;
        const dto: UpdateCfgUserFilteringDto = {
            user_id,
            tenant_id,
            filtering_id: config.cfg_param,
            key_value_json: newData,
            filter_label: newFilterLabel,
            updated_by: userName || '',
            created_by: userFilteringData?.created_by || userName,
        };

        // Specific case for SOC US
        const socFilteringData = JSON.parse(newData);
        const isSocUs = Object.values(SOC_CFG_POPUP_KEYS).some(key => socFilteringData[key] !== undefined);
        if (isSocUs) {
            const filterLabel = structuredClone(socFilteringData);
            for (const key of Object.keys(filterLabel)) {
                const info = SOC_CFG_POPUP_INFO.find(info => info.key === key);
                if (info?.key) {
                    const toggleStatus = await getToggleStatus(info.cfgName);
                    // handle for 1 toggle buttons, if >=2 => update here
                    const isOn = toggleStatus ? Object.values(toggleStatus)?.[0] || false : false;
                    const newValue = await mappingDataByKeys(socFilteringData[key], info, isOn);
                    filterLabel[key] = newValue[key]?.length ? newValue[key].join(', ') : filterLabel[key];
                }
            }
            dto.filter_label = JSON.stringify(standardizedFilterLabel(filterLabel, true));
            const keyValueJson = JSON.parse(dto.key_value_json);
            dto.key_value_json = JSON.stringify(convertDateMonthQuarterYearToDateString(keyValueJson, true));
        }
        // End of specific case for SOC US

        let res;
        if (!userFilteringData?.id) {
            res = await fetch(`/api/CfgUserFilteringService/upsert`, {
                method: 'POST',
                body: JSON.stringify(dto),
            });
        } else {
            res = await fetch(`/api/CfgUserFilteringService/${userFilteringData.id}`, {
                method: 'PATCH',
                body: JSON.stringify(dto),
            });
        }
        if (res.ok) {
            modalMessage = 'Configuration is saved successfully.';
            isModalOpen = true;
            openFilter = false;
            getUserFiltering();
        } else {
            modalMessage = 'Unable to save configuration successfully.';
            isModalOpen = true;
        }
        form_fields_json = undefined;
        unique = {};

        getUserFiltering();
        sendDataToDashboard({ cfgParamId: dataCfgParameter?.id, cfgParamName: dataCfgParameter?.name });
        openFilter = false;
    };

    function convertMinutesToFormat(minutes, formatKeys = []) {
        const minsInt = parseInt(minutes, 10);
        if (isNaN(minsInt) || minsInt < 0 || formatKeys.length === 0) return minutes;

        const totalSeconds = minsInt * 60;

        const labels = {
            day: 'd',
            hr: 'h',
            min: 'm',
            sec: 's',
        };

        const unitsInSeconds = {
            day: 86400,
            hr: 3600,
            min: 60,
            sec: 1,
        };

        if (formatKeys.length === 1) {
            const unit = formatKeys[0];
            if (unit === 'sec') return `${totalSeconds}s`;
            if (unit === 'min') return `${minsInt}m`;
            const decimal = minsInt / (unitsInSeconds[unit] / 60);
            return `${parseFloat(decimal.toFixed(2))}${labels[unit]}`;
        }

        let remainingSeconds = totalSeconds;
        const result = [];

        for (let i = 0; i < formatKeys.length; i++) {
            const unit = formatKeys[i];
            const isLast = i === formatKeys.length - 1;
            const label = labels[unit];
            const unitInSeconds = unitsInSeconds[unit];

            let value;

            if (isLast) {
                // Use decimal for the last unit
                value = remainingSeconds / unitInSeconds;
                if (value > 0 || result.length > 0) {
                    result.push(`${parseFloat(value.toFixed(2))}${label}`);
                }
                break;
            } else {
                value = Math.floor(remainingSeconds / unitInSeconds);
                if (value > 0) {
                    result.push(`${value}${label}`);
                } else {
                    const hasBefore = result.length > 0;
                    const remainingFormats = formatKeys.slice(i + 1);
                    const hasAfter = remainingFormats.some(u => {
                        const uSec = unitsInSeconds[u];
                        return Math.floor(remainingSeconds / uSec) > 0;
                    });
                    if (hasBefore && hasAfter) {
                        result.push(`0${label}`);
                    }
                }
                remainingSeconds %= unitInSeconds;
            }
        }

        const smallestUnit = formatKeys[formatKeys.length - 1];
        return result.join(' ') || `0${labels[smallestUnit]}`;
    }

    const formatValue = ({ valueFormat, timeDisplayFormat }: IndicatorConfigItem) => {
        if (
            ['', '-', null, undefined].includes(indicatorValue) ||
            (config.indicatorConfigItem.isShowDash && String(indicatorValue) === '0')
        ) {
            return '-';
        }
        let transformValue = indicatorValue;

        if (valueFormat === INDICATOR_VALUE_FORMAT.number.toLowerCase()) {
            transformValue = parseFloat(indicatorValue)
                .toFixed(2)
                .replace(/([0-9]+(\.[0-9]+[1-9])?)(\.?0+$)/, '$1');
        } else if (valueFormat === INDICATOR_VALUE_FORMAT.percentage.toLowerCase()) {
            transformValue = indicatorValue + '%';
        } else if (valueFormat === INDICATOR_VALUE_FORMAT.time.toLowerCase() && timeDisplayFormat) {
            transformValue = convertMinutesToFormat(indicatorValue, timeDisplayFormat);
        }

        return transformValue;
    };
</script>

<div class={'group flex h-full w-full flex-col p-2 '} bind:this={simpleWidgetElement}>
    <div class="invisible absolute w-full pr-4 pt-2 text-right group-hover:visible">
        {#if config?.showGear && !config.useParentFilter}
            <Button size="md" on:click={onGearClick}>
                <span>
                    {@html icon(faGear).html}
                </span>
            </Button>
        {/if}
    </div>
    {#if config.titleConfigItem.position !== 'top' && config.showFilteringLabel && !config.useParentFilter}
        <div class="mb-2">
            <CfgParameterViewer
                showFilteringLabel={config.showFilteringLabel}
                justify_items={JUSTIFY_iTEMS_OBJ[config?.filterLabelAlignment] || ''}
                filter_label={!config.label_use_filter_datasource
                    ? $customFilteringLabel?.data?.cc3_hoc_cfg_parameter?.[0]?.key_value_json
                    : userFilteringData?.filter_label} />
        </div>
    {/if}
    <div
        class={'flex flex-1 ' +
            (config.titleConfigItem.position === 'top' || config.titleConfigItem.position === 'bottom'
                ? 'flex-col'
                : '')}>
        <!-- Title -->
        <div
            class={'flex w-full items-center justify-center' +
                (config.titleConfigItem.isEnable || config.titleConfigItem.titleDatetimeItem.isEnable
                    ? ''
                    : ' hidden ') +
                (config.titleConfigItem.position === 'left' || config.titleConfigItem.position === 'top'
                    ? ' order-1 '
                    : ' order-last ') +
                (config.titleConfigItem.value ? ' p-2' : '')}
            bind:this={titleElement}>
            <div class="flex w-full flex-col items-center justify-center">
                <span
                    class={config.titleConfigItem.isEnable ? '' : 'hidden'}
                    style="color: {config.titleConfigItem.textColor}; font-size: {config.titleConfigItem.fontSize}px;">
                    {config.titleConfigItem.value}
                </span>
                <div
                    class=" {config.titleConfigItem.titleDatetimeItem.isEnable ? '' : 'hidden'}"
                    style="color: {config.titleConfigItem.titleDatetimeItem.textColor}; font-size: {config
                        .titleConfigItem.titleDatetimeItem.fontSize}px;">
                    <div class="flex items-center justify-center">
                        <div>
                            {@html icon(buttonIcon).html}
                        </div>
                        &nbsp;
                        <div>
                            {dayjs(config.titleConfigItem.titleDatetimeItem.value).format(
                                config.titleConfigItem.titleDatetimeItem.datetimeFormat
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {#if config.titleConfigItem.position === 'top' && config.showFilteringLabel && !config.useParentFilter}
            <div class="order-2 my-2">
                <CfgParameterViewer
                    showFilteringLabel={config.showFilteringLabel}
                    justify_items={JUSTIFY_iTEMS_OBJ[config?.filterLabelAlignment] || ''}
                    filter_label={!config.label_use_filter_datasource && config.cfg_param_label
                        ? $customFilteringLabel?.data?.cc3_hoc_cfg_parameter?.[0]?.key_value_json
                        : userFilteringData?.filter_label} />
            </div>
        {/if}

        <div
            class={'flex h-full w-full flex-col ' +
                (config.titleConfigItem.position === 'left' || config.titleConfigItem.position === 'top'
                    ? ' order-last'
                    : ' order-1')}>
            <!-- Indicator -->
            <div class={'flex h-full w-full items-center justify-center'} bind:this={indicatorParentElement}>
                <div bind:this={indicatorElement}>
                    <span class={TEXT_DECORATION[config.indicatorConfigItem.underlineStyle]}>
                        {formatValue(config.indicatorConfigItem)}
                    </span>

                    <!-- Indicator Top Icon -->

                    <div bind:this={topSmallIconElement}>
                        {#if config.topSmallIconConfigItem.isEnable && config.topSmallIconConfigItem.type === 'prediction'}
                            <span class="widget-cct-items-row-icon-target absolute" style="width: 20px; height:20px" />
                        {:else}
                            <span class={TEXT_DECORATION[config.topSmallIconConfigItem.underlineStyle]}>
                                {config.topSmallIconConfigItem.value}
                            </span>
                        {/if}
                    </div>
                    <!-- Indicator Bottom Icon -->
                    <div bind:this={bottomSmallIconElement}>
                        <span class={TEXT_DECORATION[config.bottomSmallIconConfigItem.underlineStyle]}>
                            {config.bottomSmallIconConfigItem.value}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{#key unique}
    {#if form_fields_json}
        {#if openFilter}
            <div
                id="large-modal-{userFilteringData?.id || ''}"
                tabindex="-1"
                class="fixed inset-0 z-50 flex max-h-full items-center justify-center overflow-y-auto overflow-x-hidden bg-black bg-opacity-80 p-4">
                <div class="relative max-h-full w-full max-w-4xl">
                    <!-- Modal content -->
                    <div class="relative rounded-lg bg-white shadow dark:bg-gray-700">
                        <!-- Modal header -->
                        <div
                            class="flex items-center justify-between rounded-t border-b p-4 md:p-4 dark:border-gray-600">
                            <h3 class="text-xl font-medium text-gray-900 dark:text-white">{titleFiltering}</h3>
                            <button
                                type="button"
                                class="ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
                                on:click={() => (openFilter = false)}>
                                <svg
                                    class="h-3 w-3"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 14 14">
                                    <path
                                        stroke="currentColor"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <!-- Modal body -->
                        <div class="space-y-4 bg-gray-900 bg-opacity-50 pt-4 dark:bg-opacity-80">
                            <FormModal
                                onSave={cfgSave}
                                {formModal}
                                data={{
                                    ...dataCfgParameter,
                                    key_value_json,
                                    fields: form_fields_json,
                                }} />
                        </div>
                    </div>
                </div>
            </div>
        {/if}
    {/if}
{/key}
<Notification bind:isOpen={isModalOpen} modalTitle="Notification" {modalMessage} />

<style>
    .widget-cct-items-row-icon-target {
        background: transparent url('$lib/icons/icon-target.svg') center/cover no-repeat;
        background-size: contain;
        vertical-align: middle;
    }
</style>
