<script lang="ts" context="module">
    import { logger } from '$lib/stores/Logger';

    export interface StatusConfig {
        dataSource?: string;
        statusTypes?: string;
        categoryTypes?: string;
        title?: string;
        subject?: string;
    }

    export const componentName = new URL(import.meta.url).pathname;
    const log = logger.child({ src: componentName });

    const DEFAULT_DATA_SOURCE = 'aoh_charts_status_sample';
    const DEFAULT_TITLE = 'Status Monitor';
    const DEFAULT_SUBJECT = 'subjects';

    export const configDefaults: StatusConfig = {
        dataSource: DEFAULT_DATA_SOURCE,
        title: DEFAULT_TITLE,
        subject: DEFAULT_SUBJECT,
        statusTypes: 'aoh_charts_enum_status_sample_type',
        categoryTypes: 'aoh_charts_enum_status_sample_type',
    };
</script>

<script lang="ts">
    import { dataSource } from '$lib/core/core';
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import TextField from '$lib/core/components/TextField/index.svelte';

    export let config: StatusConfig;

    const expectedFormat = [
        { name: 'name', type: 'String' },
        { name: 'status_type', type: 'String' },
        { name: 'category', type: 'String' },
    ];

    // List of data source names (valid GraphQL query roots)
    const validQueries: Array<DropdownItem> = [];
    const allEnumTypes: Array<DropdownItem> = [];

    // Look for aggregate > sum queries with expected format
    // aggregate > sum follows Hasura's aggregate sum schema
    $dataSource.__schema?.types?.forEach(query => {
        let isQueryValid = expectedFormat.every(format => {
            return query?.fields?.some(field => {
                return field?.name === format.name && format.type === field?.type?.ofType?.name;
            });
        });

        let isQueryEnum = query?.fields?.some(field => {
            return query.name.includes('enum_') && field.name === 'comment';
        });

        if (isQueryValid) validQueries.push({ label: query.name, value: query.name });
        if (isQueryEnum) allEnumTypes.push({ label: query.name, value: query.name });
    });

    const textClass = 'w-full text-on-background';
    const inputClass =
        textClass +
        ' w-full p-1 bg-transparent border border-outline transition-all duration-200 ease-in-out rounded focus:border-2 focus:border-primary font-light';
</script>

<div class="flex flex-col gap-2">
    <div>
        <TextField
            placeholder={'Title'}
            bind:value={config.title}
            setClass="{inputClass} h-10 text-xs"
            setLabelClass="text-primary" />
    </div>

    <div>
        <Dropdown
            title="Data Source"
            options={validQueries}
            setClass="border-b border-b-outline bg-transparent"
            bind:value={config.dataSource} />
    </div>

    <div>
        <Dropdown
            title="Categories"
            options={allEnumTypes}
            setClass="border-b border-b-outline bg-transparent"
            bind:value={config.categoryTypes} />
    </div>

    <div>
        <Dropdown
            title="Status"
            options={allEnumTypes}
            setClass="border-b border-b-outline bg-transparent"
            bind:value={config.statusTypes} />
    </div>
</div>
