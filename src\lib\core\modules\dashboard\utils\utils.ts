import { parseJSON } from '$lib/shared/util/dataConvert';
import { locations } from '$lib/stores/Locations';
import { get } from 'svelte/store';
import { convertWeekToDate } from '../../DynamicForm/utils';
import { WIDGET_GROUP_TYPE_ID } from '../components/WidgetViewer/index.svelte';

export function findWidgetById(loadedWidgetItems: LoadedWidgetItemConfig[], widgetId: string): LoadedWidgetItemConfig {
    let obj;
    if (loadedWidgetItems) {
        for (let i = 0; i < loadedWidgetItems.length; i++) {
            if (loadedWidgetItems[i].widgetId === widgetId) {
                obj = loadedWidgetItems[i];
                break;
            } else {
                obj = findWidgetById(loadedWidgetItems[i].widgetItems, widgetId);
                if (obj) break;
            }
        }
    }
    return obj;
}

export function filterWidgetByExcludeId(loadedWidgetItems: LoadedWidgetItemConfig[], widgetId) {
    if (loadedWidgetItems) {
        for (let i = 0; i < loadedWidgetItems.length; i++) {
            if (loadedWidgetItems[i].widgetId === widgetId) {
                loadedWidgetItems = loadedWidgetItems.filter(item => {
                    return widgetId !== item.widgetId;
                });
                break;
            } else {
                loadedWidgetItems[i].widgetItems = filterWidgetByExcludeId(loadedWidgetItems[i].widgetItems, widgetId);
            }
        }
    }
    return loadedWidgetItems;
}

export function pushDataToWidget(loadedWidgetItems: LoadedWidgetItemConfig[], widgetId, objToPush) {
    if (loadedWidgetItems) {
        for (let i = 0; i < loadedWidgetItems.length; i++) {
            if (loadedWidgetItems[i].widgetId === widgetId) {
                if (!loadedWidgetItems[i].widgetItems) loadedWidgetItems[i].widgetItems = [];
                loadedWidgetItems[i].widgetItems.push(objToPush);
                return loadedWidgetItems;
            } else pushDataToWidget(loadedWidgetItems[i].widgetItems, widgetId, objToPush);
        }
    }
}

// For SOC User Story
export const mappingDataByKeys = async (oldValue, info, isToggleOn = false) => {
    const { key, tableName, from, off, on } = info;
    const to = isToggleOn ? on : off;

    if (!oldValue) return { [key]: null };

    const fields = from === to ? from : [from, to]?.join(',');
    const path = `GetDataByCfgDataSource?table=cc3_hoc.${tableName}&fields=${fields}`;
    let res = await fetch(`${get(locations).sys_datasource_web}/${path}`);
    if (res.status !== 200) return { [key]: null };

    let response = await res.json();
    let arrayData = oldValue?.split(',') || [];
    const newValue = [
        ...new Set(
            arrayData.map(s => response.data?.find(c => c[from] === s)?.[to]).filter((v): v is string => Boolean(v))
        ),
    ];
    return { [key]: newValue };
};

export const getToggleStatus = async (
    toggleConfigName: string,
    key: string | null = null
): Promise<Record<string, boolean> | boolean | null> => {
    if (!toggleConfigName) return null;

    try {
        let response = await fetch(`/api/CfgParameterService/${toggleConfigName}`, { method: 'GET' });
        let json = await response.json();
        let cfgParameter = json.cfgParam;
        if (cfgParameter?.id) {
            const cfg = parseJSON(cfgParameter?.key_value_json);
            return key ? cfg[key] : cfg;
        }
    } catch (e) {
        console.error('Fail to get cfg parameter', e);
    }

    return null;
};

export const getCfgTableData = async (
    tableName: string,
    fields: string[]
): Promise<Record<string, any>[] | undefined> => {
    try {
        const res = await fetch(
            `${get(locations).sys_datasource_web}/GetDataByCfgDataSource?table=${tableName}&fields=${fields?.join(',')}`
        );

        if (!res.ok) throw new Error(res.statusText);

        return await res.json();
    } catch (error) {
        console.error('Failed to fetch table data:', error);
    }
};

export const getTriggerTables = async (id: string): Promise<string | undefined> => {
    if (!id) return;
    let tableTrigger;
    try {
        const response = await fetch(`${get(locations).sys_datasource_web}/${id}`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
        });
        const res = await response.json();
        tableTrigger = res?.data?.Agg_Tables || undefined;
    } catch (error) {
        console.error('Error:', error);
    }
    return tableTrigger;
};

export const standardizedFilterLabel = (object, isSocUs = false) => {
    let startDate = object['StartDate'];
    let endDate = object['EndDate'];
    let selectDate = object['SelectDate'];
    if (object['ViewType'] === 'Weekly' && !isSocUs) {
        startDate = convertWeekToDate(object['StartDate'], 'start');
        endDate = convertWeekToDate(object['EndDate'], 'end');
    }
    return {
        ...(object['ViewType'] && { 'View Type': object['ViewType'] }),
        ...(selectDate && { _SelectDate: selectDate }),
        ...(startDate && endDate && { _RangeDate: `${startDate} to ${endDate}` }),
        ...(object['Stage'] && { _Stage: object['Stage'] }),
        ...(object['ViewChartBy'] && { 'View by': object['ViewChartBy'] }),
        ...(object['MaterialList'] && { _MaterialList: object['MaterialList'].split(',').join(', ') }),
        ...(object['Location'] && { _Location: object['Location'].split(',').join(', ') }),
        ...(object['Equipment'] && { _Equipment: object['Equipment'].split(',').join(', ') }),
        ...(object['BloodProductList'] && { _BloodProductList: object['BloodProductList'].split(',').join(', ') }),
        ...(object['BloodTypeList'] && { _BloodTypeList: object['BloodTypeList'].split(',').join(', ') }),
        // for OT US
        ...(object['CasePriority'] && { _CasePriority: object['CasePriority'].split(',').join(', ') }),
        ...(object['OT_Location'] && { _OT_Location: object['OT_Location'].split(',').join(', ') }),
        // For SOC US
        ...(object['ServiceType'] && { _ServiceType: object['ServiceType'].split(',').join(', ') }),
        ...(object['AppointmentRationale'] && {
            _AppointmentRationale: object['AppointmentRationale'].split(',').join(', '),
        }),
        ...(object['StagesByService'] && { _StagesByService: object['StagesByService'].split(',').join(', ') }),
        ...(object['ClassOfPatient'] && { _ClassOfPatient: object['ClassOfPatient'].split(',').join(', ') }),
        ...(object['VisitTypeGroup'] && { _VisitTypeGroup: object['VisitTypeGroup'].split(',').join(', ') }),
        ...(object['Clinics'] && { _Clinics: object['Clinics'].split(',').join(', ') }),
        ...(object['Specialty'] && { _Specialty: object['Specialty'].split(',').join(', ') }),
        ...(object['CEStatus'] && { _CEStatus: object['CEStatus'].split(',').join(', ') }),
        ...(object['PriorityOfTest'] && { _PriorityOfTest: object['PriorityOfTest'].split(',').join(', ') }),
        ...(object['TypeOfPatient'] && { _TypeOfPatient: object['TypeOfPatient'].split(',').join(', ') }),
        ...(object['Doctor'] && { _Doctor: object['Doctor'].split(',').join(', ') }),
        ...(object['RadModality'] && { _RadModality: object['RadModality'].split(',').join(', ') }),
        ...(object['PendingResultFor'] && { _PendingResultFor: object['PendingResultFor'].split(',').join(', ') }),
        ...(object['OriginLocation'] && { _OriginLocation: object['OriginLocation'].split(',').join(', ') }),
        ...(object['PendingResultFor'] && { _PendingResultFor: object['PendingResultFor'].split(',').join(', ') }),
        ...(object['TypeOfLabTest'] && { _TypeOfLabTest: object['TypeOfLabTest'].split(',').join(', ') }),
        ...(object['Room'] && { _Room: object['Room'].split(',').join(', ') }),
        ...(object['SessionType'] && { _SessionType: object['SessionType'].split(',').join(', ') }),
        ...(object['AppointmentStatus'] && { _AppointmentStatus: object['AppointmentStatus'].split(',').join(', ') }),
        ...(object['Pharmacy'] && { _Pharmacy: object['Pharmacy'].split(',').join(', ') }),
    };
};

// Sortting widgets with type = WIDGET_GROUP at the top is required for nested layout
export function sortNestedGrid(items) {
    return items.sort((a, b) => {
        const aIsGroup = a.widget_type_id === WIDGET_GROUP_TYPE_ID;
        const bIsGroup = b.widget_type_id === WIDGET_GROUP_TYPE_ID;

        if (aIsGroup !== bIsGroup) {
            return aIsGroup ? -1 : 1;
        }

        const aHasParent = !!a.config?.parent_grid_id;
        const bHasParent = !!b.config?.parent_grid_id;

        if (aHasParent !== bHasParent) {
            return aHasParent ? 1 : -1;
        }

        return 0;
    });
}

export function deepSort(obj) {
    if (Array.isArray(obj)) {
        return obj.map(deepSort).sort((a, b) => {
            return JSON.stringify(a).localeCompare(JSON.stringify(b));
        });
    } else if (obj !== null && typeof obj === 'object') {
        return Object.keys(obj)
            .sort()
            .reduce((sorted, key) => {
                sorted[key] = deepSort(obj[key]);
                return sorted;
            }, {});
    }
    return obj;
}
