<script lang="ts" context="module">
    import ThresholdConfigComponent, {
        type ThresholdItem,
        type ThresholdConfig,
    } from '$lib/core/components/ThresholdConfig/index.svelte';
    import dayjs from 'dayjs';
    import { TYPEOF_COMPARATOR } from '$lib/core/modules/dashboard/widgets/utils';

    import { onMount } from 'svelte';
    import Flatpickr from 'svelte-flatpickr';
    import 'flatpickr/dist/flatpickr.css';

    export interface SegmentTimeMinsItem {
        name: string;
        fromValue: string;
        toValue: string;
        typeLineSeparator: string;
        bgColorSegment: string;
        valid: {
            status: boolean;
            message?: string;
        };
    }

    export interface CapsuleTimeMinsConfig {
        lastSegmentDisplay: boolean;
        maxWtDisplay: boolean;
        segment: SegmentTimeMinsItem[];
    }

    export interface SegmentTime24hItem {
        name: string;
        fromValue: string;
        toValue: string;
        typeLineSeparator: string;
        bgColorSegment: string;
        valid: {
            status: boolean;
            message?: string;
        };
    }

    export interface CapsuleTime24hConfig {
        lastSegmentDisplay: boolean;
        maxWtDisplay: boolean;
        segment: SegmentTime24hItem[];
    }

    export interface SegmentPercentItem {
        name: string;
        percentValue: number;
        timeValue: string;
        typeLineSegment: string;
        valid: {
            status: boolean;
            message?: string;
        };
    }

    export interface CapsulePercentConfig {
        movingtargetLineDisplay: boolean;
        movingTarget: {
            movingTargetValue: number;
            startTime: string;
            endTime: string;
            typeLineMovingTarget: string;
            movingTargetColor: string;
            valid: {
                status: boolean;
                message?: string;
            };
        };
        segment: SegmentPercentItem[];
        thresholdConfig: ThresholdConfig;
    }

    export interface CapsuleConfig {
        dataSource?: string;
        swapIndicatorAndTitle?: boolean;
        title?: string;
        titleAlign?: string;
        dateFormat?: string;
        titleDatetimeTextColorDefault?: string;
        titleDatetimeVisible?: boolean;
        titleVisible?: boolean;
        widgetHorizontal?: boolean;
        widgetTextCapsule?: boolean;
        widgetPassenger?: boolean;
        typeLineSeparator?: string;
        backgroundColorPercent?: string;
        radioTypeCapsule?: 'timemins' | 'time24h' | 'percentage';
        capsuleSegmentConfig: any;
        showTotal?: boolean;
    }

    export const configIndicatorThresholdItem: ThresholdItem = {
        titleThresholdItem: 'Critical',
        comparatorThreshold: TYPEOF_COMPARATOR[0].value,
        valueOfThreshold: 10,
        textColor: '#FFFFFF',
        bgColor: '#36A9E7',
        isValidItem: {
            isValid: true,
            errorMessage: '',
        },
    };

    const INDICATOR_THRESHOLD_CONFIG_ITEMS: Array<ThresholdItem> = [configIndicatorThresholdItem];

    export const configThresholdConfigs: ThresholdConfig = {
        isEnableThreshold: true,
        thresholdConfigItems: INDICATOR_THRESHOLD_CONFIG_ITEMS,
    };

    export let segmentTimeMinsDefault: Array<SegmentTimeMinsItem> = [
        {
            name: 'Segment1',
            fromValue: '1',
            toValue: '5',
            typeLineSeparator: 'solid',
            bgColorSegment: '#4d4d4d',
            valid: {
                status: true,
            },
        },
        {
            name: 'Segment2',
            fromValue: '6',
            toValue: '10',
            typeLineSeparator: 'solid',
            bgColorSegment: '#4d4d4d',
            valid: {
                status: true,
            },
        },
        {
            name: 'Segment3',
            fromValue: '11',
            toValue: '15',
            typeLineSeparator: 'solid',
            bgColorSegment: '#4d4d4d',
            valid: {
                status: true,
            },
        },
    ];

    export let capsuleTimeMinsConfigDefault: CapsuleTimeMinsConfig = {
        lastSegmentDisplay: true,
        maxWtDisplay: true,
        segment: segmentTimeMinsDefault,
    };

    let segmentTime24hDefault: Array<SegmentTime24hItem> = [
        {
            name: 'Segment1',
            fromValue: '00:00',
            toValue: '11:00',
            typeLineSeparator: 'solid',
            bgColorSegment: '#4d4d4d',
            valid: {
                status: true,
            },
        },
        {
            name: 'Segment2',
            fromValue: '11:01',
            toValue: '20:00',
            typeLineSeparator: 'solid',
            bgColorSegment: '#4d4d4d',
            valid: {
                status: true,
            },
        },
        {
            name: 'Segment3',
            fromValue: '20:01',
            toValue: '23:00',
            typeLineSeparator: 'solid',
            bgColorSegment: '#4d4d4d',
            valid: {
                status: true,
            },
        },
    ];

    let capsuleTime24hConfigDefault: CapsuleTime24hConfig = {
        lastSegmentDisplay: true,
        maxWtDisplay: true,
        segment: segmentTime24hDefault,
    };

    let segmentPercentDefault: Array<SegmentPercentItem> = [
        {
            name: 'Segment1',
            percentValue: 30,
            timeValue: '09:00',
            typeLineSegment: 'solid',
            valid: {
                status: true,
            },
        },
        {
            name: 'Segment2',
            percentValue: 80,
            timeValue: '22:00',
            typeLineSegment: 'solid',
            valid: {
                status: true,
            },
        },
    ];

    let capsulePercentConfigDefault: CapsulePercentConfig = {
        movingtargetLineDisplay: true,
        movingTarget: {
            movingTargetValue: 0,
            startTime: '08:00',
            endTime: '23:00',
            typeLineMovingTarget: 'dotted',
            movingTargetColor: '#E5E7EB',
            valid: {
                status: true,
            },
        },
        segment: segmentPercentDefault,
        thresholdConfig: JSON.parse(JSON.stringify(configThresholdConfigs)),
    };

    export const configDefaults: CapsuleConfig = {
        title: '',
        titleAlign: 'left',
        dataSource: '',
        dateFormat: 'YYYY/MM',
        titleDatetimeTextColorDefault: `#473674`,
        swapIndicatorAndTitle: false,
        titleDatetimeVisible: true,
        titleVisible: true,
        widgetHorizontal: true,
        widgetTextCapsule: true,
        widgetPassenger: true,
        typeLineSeparator: 'solid',
        backgroundColorPercent: '#36A9E7',
        radioTypeCapsule: 'timemins',
        capsuleSegmentConfig: capsuleTimeMinsConfigDefault,
    };
</script>

<script lang="ts">
    import Dropdown, { type DropdownItem } from '$lib/core/components/Dropdown/index.svelte';
    import { dataSource } from '$lib/core/core';
    import { icon } from '@fortawesome/fontawesome-svg-core';
    import { faCaretDown, faDatabase, faSliders } from '@fortawesome/free-solid-svg-icons';
    import { DROPDOWN_CLASS, INPUT_CLASS, LABEL_CLASS, type Query } from '../../utils';
    import { boolean, e, index, re } from 'mathjs';
    import {
        AccordionItem,
        Accordion,
        Label,
        Select,
        Input,
        Helper,
        Toggle,
        Button,
        Radio,
        Tooltip,
    } from 'flowbite-svelte';
    import WidgetGroupButton from '$lib/core/modules/dashboard/components/WidgetConfig/components/WidgetGroupButton/index.svelte';
    import { faPlus, faMinus, faClock } from '@fortawesome/free-solid-svg-icons';

    export let buttonfaPlus = faPlus;
    export let buttonfaMinus = faMinus;

    export let config: CapsuleConfig;
    export let datasources: Array<DropdownItem> = [];
    export let hasError: boolean = false;

    const datetimeFormatList: Array<String> = ['DD/MM', 'MMM YYYY', 'MM/DD/YY', 'DD MMM, YYYY'];

    const updateSourcesTitleAlign = () => {
        ['Left', 'Center', 'Right'].forEach(query => {
            sourcesTitleAlign.push({
                label: query,
                value: query,
                action: () => {},
            });
        });
    };

    const options = {
        enableTime: true,
        noCalendar: true,
        dateFormat: 'H:i',
        time_24hr: true,
    };
    let datetimeFormatValid: boolean = true;
    let sourcesTitleAlign: Array<DropdownItem> = [];
    let sourcesDatetimeFormat: Array<DropdownItem> = [];
    let currentDatasource: Query;
    let cssCheckbox1 = `w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600`;

    updateSourcesTitleAlign();

    function handleInput(event) {
        var inputValue = event.target.value;
        var regex = /^[aszhmdy\/\\\-:\s\.]*$/;
        if (!regex.test(inputValue)) {
            config.dateFormat = inputValue.replace(/[^aszhmdy\/\\\-:\s\.,]/gi, '');
        }
    }

    function addSegmentTimeMinsConfigItem() {
        let segmentTimeMins = config.capsuleSegmentConfig.segment;

        if (!segmentTimeMins.some(x => x.valid.status === false)) {
            if (segmentTimeMins.length === 0) {
                segmentTimeMins.push({
                    name: 'Segment' + 1,
                    fromValue: '1',
                    toValue: '2',
                    typeLineSeparator: 'solid',
                    bgColorSegment: '#4d4d4d',
                    valid: {
                        status: true,
                    },
                });
                config.capsuleSegmentConfig.segment = segmentTimeMins;
                config = config;
                return;
            }

            let lastItem = segmentTimeMins[segmentTimeMins.length - 1];

            const clone = {
                ...lastItem,
                name: 'Segment' + (segmentTimeMins.length + 1),
                fromValue: Number(lastItem.toValue) + 1,
                toValue: Number(lastItem.toValue) + 2,
                colorSegment: '#4d4d4d',
            };

            config.capsuleSegmentConfig.segment.push(clone);
            originCapsuleSegmentConfigTimemins = config.capsuleSegmentConfig;
            config = { ...config };
        }
    }

    function addSegmentTime24hConfigItem() {
        let segmentTime24h = config.capsuleSegmentConfig.segment;

        if (!segmentTime24h.some(x => x.valid.status === false)) {
            if (segmentTime24h.length === 0) {
                segmentTime24h.push({
                    name: 'Segment' + '1',
                    fromValue: '00:01',
                    toValue: '00:02',
                    typeLineSeparator: 'solid',
                    bgColorSegment: '#4d4d4d',
                    valid: {
                        status: true,
                    },
                });
                config.capsuleSegmentConfig.segment = segmentTime24h;
                config = config;
                return;
            }

            const lastItem = segmentTime24h[segmentTime24h.length - 1];
            const [hours, minutes] = lastItem.toValue.split(':').map(Number);

            const clone = {
                ...lastItem,
                name: 'Segment' + (segmentTime24h.length + 1),
                fromValue:
                    minutes <= 58 ? hours + ':' + (minutes < 9 ? '0' + (minutes + 1) : minutes + 1) : hours + 1 + ':00',
                toValue:
                    minutes <= 57 ? hours + ':' + (minutes < 8 ? '0' + (minutes + 2) : minutes + 2) : hours + 1 + ':00',
                colorSegment: '#4d4d4d',
                typeLineSegment: 'solid',
                bgColorSegment: '#4d4d4d',
                valid: {
                    status: true,
                },
            };

            config.capsuleSegmentConfig.segment.push(clone);
            originCapsuleSegmentConfigTime24h = config.capsuleSegmentConfig;
            config = { ...config };
        }
    }

    function addSegmentPercentConfigItem() {
        let segmentPercent = config.capsuleSegmentConfig.segment;

        for (let i = 0; i < segmentPercent.length; i++) {
            validationSegment(i);
        }

        if (!segmentPercent.some(x => x.valid.status === false)) {
            const [hoursInitial, minutesInitial] = config.capsuleSegmentConfig.movingTarget.startTime
                .split(':')
                .map(Number);

            if (segmentPercent.length === 0) {
                segmentPercent.push({
                    name: 'Segment' + '1',
                    percentValue: 0,
                    timeValue:
                        minutesInitial <= 58
                            ? (hoursInitial <= 9 ? '0' + hoursInitial : hoursInitial) +
                              ':' +
                              (minutesInitial < 9 ? '0' + minutesInitial : minutesInitial)
                            : hoursInitial + 1 + ':00',
                    typeLineSegment: 'solid',
                    valid: {
                        status: true,
                    },
                });
                config.capsuleSegmentConfig.segment = segmentPercent;
                config = config;
                return;
            }

            const lastItem = segmentPercent[segmentPercent.length - 1];
            const [hours, minutes] = lastItem.timeValue.split(':').map(Number);

            const clone = {
                ...lastItem,
                name: 'Segment' + (segmentPercent.length + 1),
                percentValue: Number(lastItem.percentValue) + 1,
                timeValue:
                    minutes <= 58
                        ? (hours <= 9 ? '0' + hours : hours) + ':' + (minutes < 9 ? '0' + (minutes + 1) : minutes + 1)
                        : hours + 1 + ':00',
                typeLineSeparator: 'solid',
                valid: {
                    status: true,
                },
            };

            segmentPercent.push(clone);
            for (let i = 0; i < segmentPercent.length; i++) {
                validationSegment(i);
            }
            originCapsuleSegmentConfigPercentage = config.capsuleSegmentConfig;
            config = { ...config };
        }
    }

    const segmentTypes = {
        timemins: {
            add: addSegmentTimeMinsConfigItem,
        },
        time24h: {
            add: addSegmentTime24hConfigItem,
        },
        percentage: {
            add: addSegmentPercentConfigItem,
        },
    };

    $: addSegment = segmentTypes[config.radioTypeCapsule].add;

    function removeItem(index) {
        let listItem = config.capsuleSegmentConfig.segment;
        const type = config.radioTypeCapsule;

        listItem = listItem.slice(0, index).concat(listItem.slice(index + 1));
        config.capsuleSegmentConfig.segment = listItem;

        if (listItem.length === 0) return;

        switch (type) {
            case 'timemins':
                listItem[listItem.length - 1].toValue = Number(listItem[listItem.length - 2]?.toValue || 0) + 2;
                originCapsuleSegmentConfigTimemins = config.capsuleSegmentConfig;
                config = { ...config };

                break;
            case 'time24h':
                const [hours, minutes] = listItem[listItem.length - 2].toValue.split(':').map(Number);
                listItem[listItem.length - 1].toValue =
                    minutes <= 57 ? hours + ':' + (minutes < 8 ? '0' + (minutes + 2) : minutes + 2) : hours + 1 + ':00';
                originCapsuleSegmentConfigTime24h = config.capsuleSegmentConfig;
                config = { ...config };

                break;

            default:
                originCapsuleSegmentConfigPercentage = config.capsuleSegmentConfig;
                config = { ...config };
                break;
        }
    }

    function updateSegmentTimMinsChanged() {
        let segmentList = config.capsuleSegmentConfig.segment;

        for (let i = 1; i < segmentList.length; i++) {
            segmentList[i].fromValue = Number(segmentList[i - 1].toValue) + 1;
        }

        segmentList[segmentList.length - 1].toValue = Number(segmentList[segmentList.length - 2]?.toValue || 0) + 2;

        segmentList = segmentList;
        originCapsuleSegmentConfigTimemins = config.capsuleSegmentConfig;
        config = { ...config };
    }

    function updateSegmentTim24hChanged() {
        let segmentList = config.capsuleSegmentConfig.segment;

        for (let i = 1; i < segmentList.length; i++) {
            const [hours, minutes] = segmentList[i - 1].toValue.split(':').map(Number);
            segmentList[i].fromValue =
                minutes <= 58 ? hours + ':' + (minutes < 9 ? '0' + (minutes + 1) : minutes + 1) : hours + 1 + ':00';
        }

        const [hours, minutes] = segmentList[segmentList.length - 2].toValue.split(':').map(Number);
        segmentList[segmentList.length - 1].toValue =
            minutes <= 57 ? hours + ':' + (minutes < 8 ? '0' + (minutes + 2) : minutes + 2) : hours + 1 + ':00';

        segmentList = segmentList;
        originCapsuleSegmentConfigTime24h = config.capsuleSegmentConfig;
        config = { ...config };
    }

    function validationSegment(index?) {
        const arraySegment = config.capsuleSegmentConfig.segment;

        for (let i = 0; i < arraySegment.length; i++) {
            arraySegment[i].valid = {
                status: true,
                message: '',
            };
        }

        if (config.radioTypeCapsule === 'timemins') {
            updateSegmentTimMinsChanged();
            for (let i = 0; i < arraySegment.length; i++) {
                arraySegment[i].valid = {
                    status: true,
                    message: '',
                };
                if (
                    Number(arraySegment[i].toValue) <= Number(arraySegment[i].fromValue) &&
                    i !== arraySegment.length - 1
                ) {
                    arraySegment[i].valid = {
                        status: false,
                        message: 'To value segment must be greater than From value',
                    };
                    config = config;
                    return;
                }
            }

            arraySegment[index].valid = {
                status: true,
                message: '',
            };
            originCapsuleSegmentConfigTimemins = config.capsuleSegmentConfig;
            config = { ...config };
            return;
        } else if (config.radioTypeCapsule === 'time24h') {
            updateSegmentTim24hChanged();
            for (let i = 0; i < arraySegment.length; i++) {
                arraySegment[i].valid = {
                    status: true,
                    message: '',
                };

                if (!isValidTimeFormat(arraySegment[i].toValue)) {
                    arraySegment[i].valid = {
                        status: false,
                        message: 'Invalid time format. Please use hh:mm format.',
                    };
                    config = config;
                    return;
                }
                const [fromHours, fromMinutes] = arraySegment[i].fromValue.split(':').map(Number);
                const [toHours, toMinutes] = arraySegment[i].toValue.split(':').map(Number);
                const fromTime = fromHours * 60 + fromMinutes;
                const toTime = toHours * 60 + toMinutes;

                if (toTime <= fromTime && i !== arraySegment.length - 1) {
                    arraySegment[i].valid = {
                        status: false,
                        message: 'To time value segment must be later than from time value.',
                    };
                    config = config;
                    return;
                }
            }

            arraySegment[index].valid = {
                status: true,
                message: '',
            };
            originCapsuleSegmentConfigTime24h = config.capsuleSegmentConfig;
            config = { ...config };
            return;
        } else {
            if (
                !isValidTimeFormat(config.capsuleSegmentConfig.movingTarget.startTime) ||
                !isValidTimeFormat(config.capsuleSegmentConfig.movingTarget.endTime)
            ) {
                config.capsuleSegmentConfig.movingTarget.valid = {
                    status: false,
                    message: 'Invalid time format. Please use hh:mm format.',
                };
                return;
            }

            if (
                config.capsuleSegmentConfig.movingTarget.startTime >= config.capsuleSegmentConfig.movingTarget.endTime
            ) {
                config.capsuleSegmentConfig.movingTarget.valid = {
                    status: false,
                    message: 'The Moving Target end time should be later than the Moving Target start time.',
                };
                return;
            }

            config.capsuleSegmentConfig.movingTarget.valid = {
                status: true,
                message: '',
            };

            for (let j = 0; j < arraySegment.length; j++) {
                arraySegment[j].valid = {
                    status: true,
                    message: '',
                };
                if (!isValidTimeFormat(arraySegment[j].timeValue)) {
                    arraySegment[j].valid = {
                        status: false,
                        message: 'Invalid time format. Please use hh:mm format.',
                    };
                    config = config;
                    return;
                }

                if (Number(arraySegment[j].percentValue) > 100) {
                    arraySegment[j].valid = {
                        status: false,
                        message: 'Percentage value segment must be less than 100',
                    };
                    config = config;
                    return;
                }

                if (
                    config.capsuleSegmentConfig.movingTarget.startTime > arraySegment[j].timeValue ||
                    arraySegment[j].timeValue > config.capsuleSegmentConfig.movingTarget.endTime
                ) {
                    arraySegment[j].valid = {
                        status: false,
                        message: 'The current time segment must be in between the Moving Target Time Range.',
                    };
                    config = config;
                    return;
                }
            }

            for (let i = 1; i < arraySegment.length; i++) {
                arraySegment[i].valid = {
                    status: true,
                    message: '',
                };

                if (Number(arraySegment[i].percentValue) <= Number(arraySegment[i - 1].percentValue)) {
                    arraySegment[i].valid = {
                        status: false,
                        message: 'Current percentage value segment must be greater than previous segment.',
                    };
                    config = config;
                    return;
                }

                if (arraySegment[i].timeValue <= arraySegment[i - 1].timeValue) {
                    arraySegment[i].valid = {
                        status: false,
                        message: 'The current time segment should be later than the previous segment.',
                    };
                    config = config;
                    return;
                }
            }

            arraySegment[index].valid = {
                status: true,
                message: '',
            };
            originCapsuleSegmentConfigPercentage = config.capsuleSegmentConfig;
            config = { ...config };
            return;
        }
    }

    function changeConfig() {
        originCapsuleSegmentConfigPercentage = config.capsuleSegmentConfig;
        config = { ...config };
    }

    let capsuleconfig: HTMLElement;

    // function UpdateSizeConfigWidget() {
    //     if (capsuleconfig) {
    //         let configurationPanel = capsuleconfig.parentElement.parentElement.parentElement;
    //         configurationPanel.classList.remove('w-1/5');
    //         configurationPanel.classList.add('overflow-y-auto');
    //         configurationPanel.classList.add('w-2/5');
    //     }
    // }
    onMount(() => {});

    function cloneDeep(params: any) {
        return JSON.parse(JSON.stringify(params));
    }

    let originCapsuleSegmentConfigTimemins, originCapsuleSegmentConfigTime24h, originCapsuleSegmentConfigPercentage;

    function handleChangeType() {
        const type = config.radioTypeCapsule;

        switch (type) {
            case 'timemins':
                config.capsuleSegmentConfig = cloneDeep({
                    ...capsuleTimeMinsConfigDefault,
                    ...originCapsuleSegmentConfigTimemins,
                });

                break;
            case 'time24h':
                config.capsuleSegmentConfig = cloneDeep({
                    ...capsuleTime24hConfigDefault,
                    ...originCapsuleSegmentConfigTime24h,
                });

                break;
            default:
                config.capsuleSegmentConfig = cloneDeep({
                    ...capsulePercentConfigDefault,
                    ...originCapsuleSegmentConfigPercentage,
                });
                break;
        }
    }

    $: if (config.capsuleSegmentConfig.thresholdConfig) {
        changeConfig();
    }

    function isValidTimeFormat(value) {
        const timeRegex = /^(?:2[0-3]|[01][0-9]):[0-5][0-9]$/;
        return timeRegex.test(value);
    }

    $: if (config.capsuleSegmentConfig?.segment?.length > 0) {
        const invalidSegment = config.capsuleSegmentConfig.segment.find(segment => !segment?.valid?.status);
        if (invalidSegment) {
            hasError = true;
        } else {
            hasError = false;
        }
    }
</script>

<div bind:this={capsuleconfig} class="text-on-surface flex flex-col gap-3">
    <Accordion>
        <AccordionItem open>
            <span slot="header">Properties</span>

            <section class="flex flex-col gap-2">
                <Dropdown
                    title="Data Source"
                    enableSearch={true}
                    options={datasources}
                    appendMenuClass="w-full"
                    setClass="border-b border-b-outline bg-transparent mb-4"
                    setLabelClass={LABEL_CLASS}
                    bind:value={config.dataSource} />
            </section>
            <div>
                <label for="default-input" class="mb-2 mt-2 block text-sm font-medium dark:text-gray-300">Title</label>
                <Input
                    type="text"
                    class="mb-2 block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                    bind:value={config.title} />
            </div>

            <section class="flex flex-col gap-2">
                <label class="relative inline-flex cursor-pointer items-center">
                    <input type="checkbox" bind:checked={config.titleDatetimeVisible} class="peer sr-only" />
                    <div class={cssCheckbox1} />
                    <span class="ms-3 text-sm font-medium dark:text-gray-300">Show Datetime</span>
                </label>

                {#if config.titleDatetimeVisible}
                    <section class="flex flex-col gap-2">
                        <Label class="text-white">Datetime Format</Label>
                        <Input
                            bind:value={config.dateFormat}
                            type="text"
                            required
                            placeholder="YYYY/MM"
                            on:input={event => {
                                handleInput(event);
                                datetimeFormatValid = dayjs(new Date(), config.dateFormat, true).isValid();
                            }} />
                        <Tooltip type="auto" arrow={false} placement="bottom" class="font-light">
                            {#each datetimeFormatList as dateFormat}
                                {dateFormat}<br />
                            {/each}
                        </Tooltip>
                    </section>
                {/if}

                <div>
                    <header class=" mb-2 flex w-full items-center gap-2 text-sm font-medium dark:text-gray-300">
                        Title Position
                    </header>

                    <WidgetGroupButton
                        bind:value={config.titleAlign}
                        labels={['Left', 'Center', 'Right']}
                        customClass=""
                        title="" />
                </div>

                <div>
                    <span class="text-sm dark:text-gray-300">Type Of Capsule</span>

                    <div class=" mt-2 flex flex-row">
                        <Radio on:change={handleChangeType} bind:group={config.radioTypeCapsule} value="timemins">
                            <p class="mr-2 text-sm font-medium dark:text-gray-300">Time (Mins)</p>
                        </Radio>
                        <Radio on:change={handleChangeType} bind:group={config.radioTypeCapsule} value="time24h">
                            <p class="mr-4 text-sm font-medium dark:text-gray-300">Time (24Hr)</p>
                        </Radio>
                        <Radio on:change={handleChangeType} bind:group={config.radioTypeCapsule} value="percentage">
                            <p class="mr-4 text-sm font-medium dark:text-gray-300">Percentage</p>
                        </Radio>
                    </div>
                </div>

                <label class="relative inline-flex cursor-pointer items-center">
                    <input type="checkbox" bind:checked={config.widgetHorizontal} class="peer sr-only" />
                    <div class={cssCheckbox1} />
                    <span class="ms-3 text-sm font-medium dark:text-gray-300">Show Horizontal</span>
                </label>

                <label
                    class="relative inline-flex cursor-pointer items-center"
                    style="display: {config.radioTypeCapsule !== 'percentage' && config.widgetHorizontal
                        ? ''
                        : 'none'}">
                    <input type="checkbox" bind:checked={config.widgetTextCapsule} class="peer sr-only" />
                    <div class={cssCheckbox1} />
                    <span class="ms-3 text-sm font-medium dark:text-gray-300">Show Percentile</span>
                </label>
                <label
                    class="relative inline-flex cursor-pointer items-center"
                    style="display: {config.radioTypeCapsule === 'percentage' ? '' : 'none'}">
                    <input type="checkbox" bind:checked={config.widgetPassenger} class="peer sr-only" />
                    <div class={cssCheckbox1} />
                    <span class="ms-3 text-sm font-medium dark:text-gray-300">Show Value</span>
                </label>
                <label class="relative inline-flex cursor-pointer items-center">
                    <input type="checkbox" bind:checked={config.showTotal} class="peer sr-only" />
                    <div class={cssCheckbox1} />
                    <span class="ms-3 text-sm font-medium dark:text-gray-300">Show Total</span>
                </label>
            </section>
        </AccordionItem>
        <AccordionItem>
            <span slot="header">Segments</span>

            <!-- Time -->
            {#if config.radioTypeCapsule === 'timemins' || config.radioTypeCapsule === 'time24h'}
                <div>
                    <div class="mb-2 flex flex-col gap-2">
                        <label class="relative inline-flex cursor-pointer items-center">
                            <input
                                type="checkbox"
                                bind:checked={config.capsuleSegmentConfig.lastSegmentDisplay}
                                on:change={config.radioTypeCapsule === 'timemins'
                                    ? updateSegmentTimMinsChanged
                                    : updateSegmentTim24hChanged}
                                class="peer sr-only" />
                            <div class={cssCheckbox1} />
                            <span class="ms-3 text-sm font-medium dark:text-gray-300">Show Dynamic (Last Segment)</span>
                        </label>
                        {#if config.capsuleSegmentConfig.lastSegmentDisplay}
                            <label class="relative inline-flex cursor-pointer items-center">
                                <input
                                    type="checkbox"
                                    bind:checked={config.capsuleSegmentConfig.maxWtDisplay}
                                    on:change={config.radioTypeCapsule === 'timemins'
                                        ? updateSegmentTimMinsChanged
                                        : updateSegmentTim24hChanged}
                                    class="peer sr-only" />
                                <div class={cssCheckbox1} />
                                <span class="ms-3 text-sm font-medium dark:text-gray-300"
                                    >Show Max WT (Segment Label)</span>
                            </label>
                        {/if}
                    </div>
                    <div class="text-sm font-medium dark:text-gray-300">
                        <div class="flex h-12">
                            <span class="mr-2 self-center text-white">Add Segment</span>

                            <Button pill={true} class=" w-12 bg-gray-500" on:click={() => addSegment()}
                                >{@html icon(buttonfaPlus).html}</Button>
                        </div>

                        {#each config.capsuleSegmentConfig?.segment || [] as item, index}
                            {@const timeMinsConfig = config.capsuleSegmentConfig}
                            {@const unit = config.radioTypeCapsule === 'timemins' ? 'mins' : ''}
                            {@const disabled = index === timeMinsConfig.segment.length - 1 ? '' : 'disabled'}
                            <div>
                                <section class="flex flex-row items-center gap-2 py-2 text-sm">
                                    <div class="flex flex-row">
                                        <span class="mr-1 w-20 self-center text-white">Segment{index + 1}</span>

                                        {#if config.radioTypeCapsule === 'time24h'}
                                            <div class="relative">
                                                {#key item.fromValue}
                                                    <Flatpickr
                                                        class="focus:border-primary-500 focus:ring-primary-500 dark:focus:border-primary-500 dark:focus:ring-primary-500 mr-1 block h-10 w-[100px] rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 disabled:cursor-not-allowed disabled:opacity-50 rtl:text-right dark:border-gray-500 dark:bg-gray-600 dark:text-white dark:placeholder-gray-400"
                                                        required
                                                        options={{
                                                            ...options,
                                                            defaultDate: item.fromValue,
                                                        }}
                                                        value={item.fromValue}
                                                        name="from"
                                                        disabled />
                                                {/key}
                                                <span class="pointer-events-none absolute right-3 top-3">
                                                    {@html icon(faClock).html}
                                                </span>
                                            </div>
                                        {:else}
                                            <Input
                                                class="mr-0.5 h-10 {config.radioTypeCapsule === 'timemins'
                                                    ? 'w-20'
                                                    : 'w-30'}"
                                                bind:value={item.fromValue}
                                                placeholder={item.fromValue}
                                                type={config.radioTypeCapsule === 'timemins' ? 'text' : 'time'}
                                                disabled />
                                        {/if}
                                        <span class="self-center text-white">{unit}</span>
                                    </div>
                                    <div class="ml-1 flex w-48 flex-row">
                                        <span class="mr-1 self-center text-white">To</span>
                                        <div class="flex flex-row">
                                            {#if index === timeMinsConfig.segment.length - 1}
                                                <div
                                                    class="{config.radioTypeCapsule === 'timemins'
                                                        ? 'mr-1 w-20 '
                                                        : 'w-[100px]'} text-center">
                                                    Max WT
                                                </div>
                                            {:else if config.radioTypeCapsule === 'timemins'}
                                                <Input
                                                    class="mr-1 h-10 w-20"
                                                    type="number"
                                                    bind:value={item.toValue}
                                                    on:change={() => validationSegment(index)}
                                                    placeholder={item.toValue}
                                                    min="1"
                                                    oninput="validity.valid||(value='');"
                                                    required />
                                            {:else if config.radioTypeCapsule === 'time24h'}
                                                <div class="relative">
                                                    <Flatpickr
                                                        class="focus:border-primary-500 focus:ring-primary-500 dark:focus:border-primary-500 dark:focus:ring-primary-500 mr-1 block h-10 w-[100px] rounded-lg border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 disabled:cursor-not-allowed disabled:opacity-50 rtl:text-right dark:border-gray-500 dark:bg-gray-600 dark:text-white dark:placeholder-gray-400"
                                                        required
                                                        options={{
                                                            ...options,
                                                            defaultDate: item.toValue,
                                                        }}
                                                        placeholder={item.toValue}
                                                        name="date"
                                                        on:change={e => {
                                                            if (e?.detail?.[1] && e.detail[1] !== item.toValue) {
                                                                item.toValue = e.detail[1];
                                                                validationSegment(index);
                                                            }
                                                        }} />

                                                    <span class="pointer-events-none absolute right-3 top-3">
                                                        {@html icon(faClock).html}
                                                    </span>
                                                </div>
                                            {/if}
                                        </div>
                                        <span class=" self-center text-white">{unit}</span>
                                    </div>

                                    <div
                                        class="{index === timeMinsConfig.segment.length - 1 &&
                                        config.radioTypeCapsule === 'time24h'
                                            ? ''
                                            : 'ml-1'} flex flex-row">
                                        <Radio
                                            bind:group={item.typeLineSeparator}
                                            on:change={config.radioTypeCapsule === 'timemins'
                                                ? updateSegmentTimMinsChanged
                                                : updateSegmentTim24hChanged}
                                            value="solid">
                                            <p class="mr-2 text-sm font-medium text-white">Solid</p>
                                        </Radio>
                                        <Radio
                                            bind:group={item.typeLineSeparator}
                                            on:change={config.radioTypeCapsule === 'timemins'
                                                ? updateSegmentTimMinsChanged
                                                : updateSegmentTim24hChanged}
                                            value="dotted">
                                            <p class="text-sm font-medium text-white">Dotted</p>
                                        </Radio>
                                    </div>

                                    <div class="ml-2 h-full w-1/12">
                                        <Input
                                            type="color"
                                            class="h-10 w-10 rounded-md p-0"
                                            placeholder="Background Color"
                                            bind:value={item.bgColorSegment} />
                                    </div>

                                    <div class="self-end">
                                        <Button
                                            class=" ml-2 h-12 w-12 bg-gray-500"
                                            pill={true}
                                            on:click={() => removeItem(index)}
                                            {disabled}>{@html icon(buttonfaMinus).html}</Button>
                                    </div>
                                </section>
                                {#if !item.valid.status}
                                    <Helper class="text-sm font-medium" color="red">
                                        <span>{item.valid.message}</span>
                                    </Helper>
                                {/if}
                            </div>
                        {/each}
                    </div>
                </div>
            {:else if config.capsuleSegmentConfig?.movingTarget}
                <!-- Percentage -->
                <div>
                    <div>
                        <div class="mb-2 flex flex-col gap-2">
                            <div>
                                <label class="relative inline-flex cursor-pointer items-center">
                                    <input
                                        type="checkbox"
                                        bind:checked={config.capsuleSegmentConfig.movingtargetLineDisplay}
                                        class="peer sr-only" />
                                    <div class={cssCheckbox1} />
                                    <span class="ms-3 text-sm font-medium dark:text-gray-300"
                                        >Show Moving Target Line</span>
                                </label>

                                <div class="flex flex-row">
                                    <span class="w-32 self-center text-white">Moving Target</span>
                                    <Input
                                        bind:value={config.capsuleSegmentConfig.movingTarget.startTime}
                                        type="time"
                                        on:change={() => validationSegment(index)}
                                        placeholder={config.capsuleSegmentConfig.movingTarget.startTime}
                                        class="mr-0.5 h-10 w-32"
                                        required />

                                    <span class="mx-1 w-4 self-center text-white">To</span>

                                    <Input
                                        bind:value={config.capsuleSegmentConfig.movingTarget.endTime}
                                        type="time"
                                        on:change={() => validationSegment(index)}
                                        placeholder={config.capsuleSegmentConfig.movingTarget.endTime}
                                        class=" w-30 mr-1 h-10"
                                        required />

                                    <div class="ml-4 flex flex-row">
                                        <Radio
                                            bind:group={config.capsuleSegmentConfig.movingTarget.typeLineMovingTarget}
                                            value="solid">
                                            <p class="mr-2 text-sm font-medium text-white">Solid</p>
                                        </Radio>
                                        <Radio
                                            bind:group={config.capsuleSegmentConfig.movingTarget.typeLineMovingTarget}
                                            value="dotted">
                                            <p class="text-sm font-medium text-white">Dotted</p>
                                        </Radio>
                                    </div>

                                    <Input
                                        type="color"
                                        class=" ml-4 h-10 w-10 rounded-md p-0"
                                        bind:value={config.capsuleSegmentConfig.movingTarget.movingTargetColor}
                                        placeholder="Background Color" />
                                </div>
                                {#if !config.capsuleSegmentConfig.movingTarget.valid.status}
                                    <Helper class="text-sm font-medium" color="red">
                                        <span>{config.capsuleSegmentConfig.movingTarget.valid.message}</span>
                                    </Helper>
                                {/if}
                            </div>
                            <div class="text-sm font-medium dark:text-gray-300">
                                <div class="flex h-12">
                                    <span class="mr-2 self-center text-white">Add Segment</span>
                                    <Button pill={true} class=" w-12 bg-gray-500" on:click={() => addSegment()}
                                        >{@html icon(buttonfaPlus).html}</Button>
                                </div>

                                {#each config.capsuleSegmentConfig?.segment || [] as item, index (index)}
                                    {@const disabled =
                                        index === config.capsuleSegmentConfig.segment.length - 1 ? '' : 'disabled'}
                                    <div>
                                        <div class="flex flex-row py-2">
                                            <span class="w-32 self-center text-white">Segment{index + 1}</span>
                                            <Input
                                                class="mr-0.5 h-10 w-32"
                                                type="number"
                                                bind:value={item.percentValue}
                                                on:change={() => validationSegment(index)}
                                                placeholder={item.percentValue}
                                                max="100"
                                                min="0"
                                                oninput="validity.valid||(value='');"
                                                required />

                                            <span class=" mx-1 w-4 self-center text-white">%</span>
                                            <Input
                                                bind:value={item.timeValue}
                                                type="time"
                                                on:change={() => validationSegment(index)}
                                                placeholder={item.timeValue}
                                                class=" w-30 mr-1 h-10"
                                                required />

                                            <div class="ml-4 flex flex-row">
                                                <Radio
                                                    bind:group={item.typeLineSegment}
                                                    value="solid"
                                                    on:change={() => validationSegment(index)}>
                                                    <p class="mr-2 text-sm font-medium text-white">Solid</p>
                                                </Radio>
                                                <Radio
                                                    bind:group={item.typeLineSegment}
                                                    value="dotted"
                                                    on:change={() => validationSegment(index)}>
                                                    <p class="text-sm font-medium text-white">Dotted</p>
                                                </Radio>
                                            </div>

                                            <Button
                                                class=" ml-3 h-12 w-12 bg-gray-500"
                                                pill={true}
                                                on:click={() => removeItem(index)}
                                                {disabled}>{@html icon(buttonfaMinus).html}</Button>
                                        </div>
                                        {#if !item.valid.status}
                                            <Helper class="text-sm font-medium" color="red">
                                                <span>{item.valid.message}</span>
                                            </Helper>
                                        {/if}
                                    </div>
                                {/each}
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
        </AccordionItem>

        <!-- Percentage Threshold-->
        {#if config.radioTypeCapsule === 'percentage' && config.capsuleSegmentConfig?.movingTarget}
            <AccordionItem>
                <span slot="header">Thresholds</span>

                <ThresholdConfigComponent
                    bind:configThresholdConfigs={config.capsuleSegmentConfig.thresholdConfig}
                    isDisplayTextColor={false}
                    isDisplayEnableButton={false} />
            </AccordionItem>
        {/if}
    </Accordion>

    <Accordion />
</div>

<style>
    :global(.flatpickr-calendar.hasTime.noCalendar) {
        width: 100px;
    }
</style>
