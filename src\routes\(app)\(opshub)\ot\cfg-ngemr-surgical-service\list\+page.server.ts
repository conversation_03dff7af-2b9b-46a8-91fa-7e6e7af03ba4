import { RESOURCE, SCOPE } from '$lib/shared/enum/general';
import { checkPermissions } from '$lib/shared/util/acl';
import { logger } from '$lib/stores/Logger';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

const log = logger.child({ src: new URL(import.meta.url).pathname });
export const load: PageServerLoad = async ({ parent, params, fetch, locals }) => {
    const specialtyRes = await fetch(`/api/soc/cfg-ngemr-specialty`);

    if (specialtyRes.status !== 200) {
        log.error({ status: specialtyRes.status });
        return error(404);
    }

    const specialty = await specialtyRes.json();
    const specialtyData = specialty?.data?.filter(s => s.service === 'SOC') || [];

    const userId = locals.user.claims?.sub;
    const tenantId = locals.user.claims?.active_tenant?.tenant_id;
    const token = locals.user.access_token;

    let permissions = [
        { scopeId: SCOPE.READ, resourceId: RESOURCE.OT_SURGERY_SERVICE },
        { scopeId: SCOPE.CREATE, resourceId: RESOURCE.OT_SURGERY_SERVICE },
        { scopeId: SCOPE.UPDATE, resourceId: RESOURCE.OT_SURGERY_SERVICE },
        { scopeId: SCOPE.DELETE, resourceId: RESOURCE.OT_SURGERY_SERVICE },
    ];
    const abilities = await checkPermissions({ token, tenantId, userId, permissions });

    return { specialtyData, abilities };
};
